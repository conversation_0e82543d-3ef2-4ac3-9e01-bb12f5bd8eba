/**
 * 根据用户拥有的权限过滤路由
 * @param { Object } routes
 * @param { Object } allAuths
 */
export function filterByAuth(routes, allAuths) {
  for (let i = 0; i < routes.length; i += 1) {
    const route = routes[i];
    if (!route.menuId || allAuths.indexOf(route.path) === -1) {
      routes.splice(i, 1);
      i -= 1;
    } else if (route.children && route.children.length > 0) {
      filterByAuth(route.children, allAuths);
    }
  }
}
