import React from 'react';
import { ValueTypeEnum } from '@yr/multi-view-table';
import { FormItemListProps, YRTableProps, YRButton, YRLink, YREasyUseModal, YRModal, YRMessage, YRDict } from 'yrantd';
import { M0105 } from '@permit/organ';
import RulesAddModal from './components/RulesAddModal';

const { delAuthInfo, loseEffectAuthInfo, pauseAuthInfo, restartAuthInfo } = M0105.interfaces;

const organData = [
  {
    organId: '1',
    sequence: '1',
    organName: '浙江省农村信用社联合社',
    organLevel: '001',
    organStatus: '002',
    organType: '001',
    organNature: '001',
    organPath: null,
    dataPermissionOrgan: null,
    parentOrganId: '0',
    parentOrganName: null,
    children: [
      {
        organId: '10003',
        sequence: '11',
        organName: '总行风险合规部',
        organLevel: '002',
        organStatus: '002',
        organType: '002',
        organNature: '001',
        organPath: null,
        dataPermissionOrgan: null,
        parentOrganId: '1',
        parentOrganName: '浙江省农村信用社联合社',
        children: null
      },
      {
        organId: '10007',
        sequence: '12',
        organName: '总行普惠金融部',
        organLevel: '002',
        organStatus: '002',
        organType: '002',
        organNature: '001',
        organPath: null,
        dataPermissionOrgan: null,
        parentOrganId: '1',
        parentOrganName: '浙江省农村信用社联合社',
        children: null
      },
      {
        organId: '10005',
        sequence: '14',
        organName: '总行公司金融部',
        organLevel: '002',
        organStatus: '002',
        organType: '002',
        organNature: '001',
        organPath: null,
        dataPermissionOrgan: null,
        parentOrganId: '1',
        parentOrganName: '浙江省农村信用社联合社',
        children: null
      },
      {
        organId: '10006',
        sequence: '15',
        organName: '总行零售金融部',
        organLevel: '002',
        organStatus: '002',
        organType: '002',
        organNature: '001',
        organPath: null,
        dataPermissionOrgan: null,
        parentOrganId: '1',
        parentOrganName: '浙江省农村信用社联合社',
        children: null
      },
      {
        organId: '109',
        sequence: '20',
        organName: '萧山农商行',
        organLevel: '004',
        organStatus: '002',
        organType: '001',
        organNature: '001',
        organPath: null,
        dataPermissionOrgan: null,
        parentOrganId: '1',
        parentOrganName: '浙江省农村信用社联合社',
        children: [
          {
            organId: '10910',
            sequence: '9010',
            organName: '零售银行',
            organLevel: '002',
            organStatus: '002',
            organType: '002',
            organNature: '002',
            organPath: null,
            dataPermissionOrgan: null,
            parentOrganId: '109',
            parentOrganName: '萧山农商行',
            children: [
              {
                organId: '10911',
                sequence: '90101',
                organName: '杭州',
                organLevel: '002',
                organStatus: '002',
                organType: '002',
                organNature: '001',
                organPath: null,
                dataPermissionOrgan: null,
                parentOrganId: '10910',
                parentOrganName: '零售银行',
                children: [
                  {
                    organId: '10912',
                    sequence: '901011',
                    organName: '滨江',
                    organLevel: '002',
                    organStatus: '002',
                    organType: '002',
                    organNature: '001',
                    organPath: null,
                    dataPermissionOrgan: null,
                    parentOrganId: '10911',
                    parentOrganName: '杭州',
                    children: [
                      {
                        organId: '10913',
                        sequence: '9010111',
                        organName: '西兴',
                        organLevel: '002',
                        organStatus: '001',
                        organType: '002',
                        organNature: '001',
                        organPath: null,
                        dataPermissionOrgan: null,
                        parentOrganId: '10912',
                        parentOrganName: '滨江',
                        children: null
                      }
                    ]
                  }
                ]
              }
            ]
          },
          {
            organId: '10901',
            sequence: '9011',
            organName: '公司金融部',
            organLevel: '002',
            organStatus: '002',
            organType: '002',
            organNature: '002',
            organPath: null,
            dataPermissionOrgan: null,
            parentOrganId: '109',
            parentOrganName: '萧山农商行',
            children: null
          },
          {
            organId: '10902',
            sequence: '9012',
            organName: '消费金融部',
            organLevel: '002',
            organStatus: '002',
            organType: '002',
            organNature: '002',
            organPath: null,
            dataPermissionOrgan: null,
            parentOrganId: '109',
            parentOrganName: '萧山农商行',
            children: null
          },
          {
            organId: '10903',
            sequence: '9013',
            organName: '贸易金融部',
            organLevel: '002',
            organStatus: '002',
            organType: '002',
            organNature: '002',
            organPath: null,
            dataPermissionOrgan: null,
            parentOrganId: '109',
            parentOrganName: '萧山农商行',
            children: null
          },
          {
            organId: '10904',
            sequence: '9014',
            organName: '供应链业务部',
            organLevel: '002',
            organStatus: '002',
            organType: '002',
            organNature: '002',
            organPath: null,
            dataPermissionOrgan: null,
            parentOrganId: '109',
            parentOrganName: '萧山农商行',
            children: null
          },
          {
            organId: '10905',
            sequence: '9015',
            organName: '金融市场部',
            organLevel: '002',
            organStatus: '002',
            organType: '002',
            organNature: '002',
            organPath: null,
            dataPermissionOrgan: null,
            parentOrganId: '109',
            parentOrganName: '萧山农商行',
            children: null
          },
          {
            organId: '10906',
            sequence: '9016',
            organName: '资产保全部',
            organLevel: '002',
            organStatus: '002',
            organType: '002',
            organNature: '002',
            organPath: null,
            dataPermissionOrgan: null,
            parentOrganId: '109',
            parentOrganName: '萧山农商行',
            children: null
          },
          {
            organId: '10907',
            sequence: '9017',
            organName: '投行业务部',
            organLevel: '002',
            organStatus: '002',
            organType: '002',
            organNature: '002',
            organPath: null,
            dataPermissionOrgan: null,
            parentOrganId: '109',
            parentOrganName: '萧山农商行',
            children: null
          },
          {
            organId: '10908',
            sequence: '9018',
            organName: '风险管理部',
            organLevel: '002',
            organStatus: '002',
            organType: '002',
            organNature: '002',
            organPath: null,
            dataPermissionOrgan: null,
            parentOrganId: '109',
            parentOrganName: '萧山农商行',
            children: null
          },
          {
            organId: '10909',
            sequence: '9019',
            organName: '授信管理部',
            organLevel: '002',
            organStatus: '002',
            organType: '002',
            organNature: '002',
            organPath: null,
            dataPermissionOrgan: null,
            parentOrganId: '109',
            parentOrganName: '萧山农商行',
            children: null
          },
          {
            organId: '10914',
            sequence: '9100',
            organName: '总行放款中心',
            organLevel: '002',
            organStatus: '002',
            organType: '002',
            organNature: '002',
            organPath: null,
            dataPermissionOrgan: null,
            parentOrganId: '109',
            parentOrganName: '萧山农商行',
            children: null
          },
          {
            organId: '10915',
            sequence: '9101',
            organName: '零售风险合规部',
            organLevel: '002',
            organStatus: '002',
            organType: '002',
            organNature: '002',
            organPath: null,
            dataPermissionOrgan: null,
            parentOrganId: '109',
            parentOrganName: '萧山农商行',
            children: null
          }
        ]
      },
      {
        organId: '1000004',
        sequence: '21',
        organName: '杭州农商联合银行',
        organLevel: '004',
        organStatus: '002',
        organType: '001',
        organNature: '001',
        organPath: null,
        dataPermissionOrgan: null,
        parentOrganId: '1',
        parentOrganName: '浙江省农村信用社联合社',
        children: [
          {
            organId: '1000004000001',
            sequence: '211',
            organName: '余杭区支行',
            organLevel: '006',
            organStatus: '002',
            organType: '002',
            organNature: '001',
            organPath: null,
            dataPermissionOrgan: null,
            parentOrganId: '1000004',
            parentOrganName: '杭州农商联合银行',
            children: null
          },
          {
            organId: '100000401',
            sequence: '212',
            organName: '上城区支行',
            organLevel: '006',
            organStatus: '002',
            organType: '002',
            organNature: '001',
            organPath: null,
            dataPermissionOrgan: null,
            parentOrganId: '1000004',
            parentOrganName: '杭州农商联合银行',
            children: null
          },
          {
            organId: '1000004000002',
            sequence: '213',
            organName: '滨江区支行',
            organLevel: '006',
            organStatus: '002',
            organType: '001',
            organNature: '001',
            organPath: null,
            dataPermissionOrgan: null,
            parentOrganId: '1000004',
            parentOrganName: '杭州农商联合银行',
            children: null
          },
          {
            organId: '1000004000003',
            sequence: '214',
            organName: '桐庐县支行',
            organLevel: '006',
            organStatus: '002',
            organType: '001',
            organNature: '001',
            organPath: null,
            dataPermissionOrgan: null,
            parentOrganId: '1000004',
            parentOrganName: '杭州农商联合银行',
            children: null
          },
          {
            organId: '1000004000004',
            sequence: '215',
            organName: '淳安县支行',
            organLevel: '006',
            organStatus: '002',
            organType: '001',
            organNature: '001',
            organPath: null,
            dataPermissionOrgan: null,
            parentOrganId: '1000004',
            parentOrganName: '杭州农商联合银行',
            children: null
          }
        ]
      },
      {
        organId: '105',
        sequence: '22',
        organName: '嘉兴农商行',
        organLevel: '004',
        organStatus: '002',
        organType: '002',
        organNature: '001',
        organPath: null,
        dataPermissionOrgan: null,
        parentOrganId: '1',
        parentOrganName: '浙江省农村信用社联合社',
        children: [
          {
            organId: '106',
            sequence: '10101',
            organName: '公司银行风险合规部',
            organLevel: '001',
            organStatus: '002',
            organType: '002',
            organNature: '001',
            organPath: null,
            dataPermissionOrgan: null,
            parentOrganId: '105',
            parentOrganName: '嘉兴农商行',
            children: [
              {
                organId: '107',
                sequence: '13',
                organName: 'HY三级机构',
                organLevel: '001',
                organStatus: '002',
                organType: '001',
                organNature: '001',
                organPath: null,
                dataPermissionOrgan: null,
                parentOrganId: '106',
                parentOrganName: '公司银行风险合规部',
                children: null
              }
            ]
          }
        ]
      },
      {
        organId: '1000005',
        sequence: '25',
        organName: '湖州农商行',
        organLevel: '004',
        organStatus: '002',
        organType: '001',
        organNature: '001',
        organPath: null,
        dataPermissionOrgan: null,
        parentOrganId: '1',
        parentOrganName: '浙江省农村信用社联合社',
        children: null
      },
      {
        organId: '1000002',
        sequence: '26',
        organName: '温州农商行',
        organLevel: '004',
        organStatus: '002',
        organType: '002',
        organNature: '001',
        organPath: null,
        dataPermissionOrgan: null,
        parentOrganId: '1',
        parentOrganName: '浙江省农村信用社联合社',
        children: [
          {
            organId: '100000201',
            sequence: '9020',
            organName: '烟台银行支行',
            organLevel: '004',
            organStatus: '002',
            organType: '002',
            organNature: '001',
            organPath: null,
            dataPermissionOrgan: null,
            parentOrganId: '1000002',
            parentOrganName: '温州农商行',
            children: [
              {
                organId: '100000202',
                sequence: '90201',
                organName: '支行营业部',
                organLevel: '004',
                organStatus: '002',
                organType: '002',
                organNature: '001',
                organPath: null,
                dataPermissionOrgan: null,
                parentOrganId: '100000201',
                parentOrganName: '烟台银行支行',
                children: null
              }
            ]
          },
          {
            organId: '1000003',
            sequence: '9021',
            organName: '分行营业部',
            organLevel: '003',
            organStatus: '002',
            organType: '002',
            organNature: '001',
            organPath: null,
            dataPermissionOrgan: null,
            parentOrganId: '1000002',
            parentOrganName: '温州农商行',
            children: null
          }
        ]
      },
      {
        organId: '100000001',
        sequence: '27',
        organName: '金华农商行',
        organLevel: '004',
        organStatus: '002',
        organType: '001',
        organNature: '001',
        organPath: null,
        dataPermissionOrgan: null,
        parentOrganId: '1',
        parentOrganName: '浙江省农村信用社联合社',
        children: [
          {
            organId: '100000002',
            sequence: '10001010',
            organName: '分行风险合规部',
            organLevel: '004',
            organStatus: '002',
            organType: '002',
            organNature: '001',
            organPath: null,
            dataPermissionOrgan: null,
            parentOrganId: '100000001',
            parentOrganName: '金华农商行',
            children: null
          },
          {
            organId: '10000000101',
            sequence: '100010101',
            organName: '中心支行',
            organLevel: '005',
            organStatus: '002',
            organType: '001',
            organNature: '001',
            organPath: null,
            dataPermissionOrgan: null,
            parentOrganId: '100000001',
            parentOrganName: '金华农商行',
            children: [
              {
                organId: '1000000010101',
                sequence: '10001010101',
                organName: '营业机构一',
                organLevel: '006',
                organStatus: '002',
                organType: '001',
                organNature: '001',
                organPath: null,
                dataPermissionOrgan: null,
                parentOrganId: '10000000101',
                parentOrganName: '中心支行',
                children: null
              },
              {
                organId: '1000000010102',
                sequence: '10001010102',
                organName: '营业机构二',
                organLevel: '006',
                organStatus: '002',
                organType: '001',
                organNature: '001',
                organPath: null,
                dataPermissionOrgan: null,
                parentOrganId: '10000000101',
                parentOrganName: '中心支行',
                children: null
              }
            ]
          }
        ]
      },
      {
        organId: '1000001',
        sequence: '28',
        organName: '丽水农商行',
        organLevel: '004',
        organStatus: '002',
        organType: '002',
        organNature: '001',
        organPath: null,
        dataPermissionOrgan: null,
        parentOrganId: '1',
        parentOrganName: '浙江省农村信用社联合社',
        children: null
      },
      {
        organId: '10008',
        sequence: '102',
        organName: '总行零售金融直销银行部',
        organLevel: '002',
        organStatus: '002',
        organType: '002',
        organNature: '001',
        organPath: null,
        dataPermissionOrgan: null,
        parentOrganId: '1',
        parentOrganName: '浙江省农村信用社联合社',
        children: null
      },
      {
        organId: '10009',
        sequence: '103',
        organName: '总行公司金融直销银行部',
        organLevel: '002',
        organStatus: '002',
        organType: '002',
        organNature: '001',
        organPath: null,
        dataPermissionOrgan: null,
        parentOrganId: '1',
        parentOrganName: '浙江省农村信用社联合社',
        children: null
      }
    ]
  }
];

const formItemList: FormItemListProps[] = [
  {
    placeholder: '状态',
    key: 'authStatus',
    type: 'dictSelect',
    position: 'show',
    dictkey: 'AUTH_STATUS'
  },
  //   {
  //     placeholder: '业务基线',
  //     key: 'bizLine',
  //     type: 'dictSelect',
  //     position: 'show',
  //     dictkey: 'BIZ_LINE'
  //   },
  {
    placeholder: '暂停截至日期',
    key: 'pauseDeadlineDate',
    type: 'datePicker',
    position: 'show'
  }
];

const tabs = [
  {
    tab: '公司银行',
    key: '001'
  },
  {
    tab: '零售公司',
    key: '002'
  },
  {
    tab: '数字银行',
    key: '003'
  },
  {
    tab: '金融市场',
    key: '004'
  }
];

const handleAction = (title, content, actionFn, successMessage, authBaseId, okCallback) => {
  YRModal.confirm({
    title,
    okText: '确定',
    cancelText: '取消',
    content,
    onOk: () => {
      actionFn({ authBaseId }).then((res) => {
        const { errorMessage } = res;
        if (!errorMessage) {
          YRMessage.success(successMessage, 0.5, () => {
            okCallback();
          });
        }
      });
    }
  });
};

// 右下角列表
const columns: (
  okCallback: () => void,
  submitCallback: any,
  submitLoading: boolean,
  mode: 'detail' | 'add'
) => YRTableProps['columns'] = (okCallback, submitCallback, submitLoading, mode) => {
  return [
    {
      title: '授权类型',
      dataIndex: 'authCategory',
      key: 'authCategory',
      render: (value) => <YRDict.Text dictkey={'AUTH_CATEGORY'} defaultValue={value} />
    },
    {
      title: '角色',
      dataIndex: 'roleName',
      key: 'roleName'
    },
    // {
    //   title: '授权维度',
    //   dataIndex: 'authDimension',
    //   key: 'authDimension'
    // },
    // {
    //   title: '结果类型',
    //   dataIndex: 'resultType',
    //   key: 'resultType'
    // },
    // {
    //   title: '结果',
    //   dataIndex: 'resultValue',
    //   key: 'resultValue'
    // },
    // {
    //   title: '客户类型',
    //   dataIndex: 'customerType',
    //   key: 'customerType',
    //   valueType: ValueTypeEnum.dict,
    //   dictkey: 'USER_TYPE'
    // },
    {
      title: '状态',
      dataIndex: 'authStatus',
      key: 'authStatus',
      valueType: ValueTypeEnum.dict,
      dictkey: 'AUTH_STATUS'
    },
    {
      title: '暂停截至日期',
      dataIndex: 'pauseDeadline',
      key: 'pauseDeadline',
      valueType: ValueTypeEnum.date
    },
    {
      title: '生效日期',
      dataIndex: 'effectBeginDate',
      key: 'effectBeginDate',
      valueType: ValueTypeEnum.date
    },
    {
      title: '失效日期',
      dataIndex: 'effectEndDate',
      key: 'effectEndDate',
      valueType: ValueTypeEnum.date
    },
    // {
    //   title: '登记日期',
    //   dataIndex: 'inputTime',
    //   key: 'inputTime',
    //   valueType: ValueTypeEnum.date
    // },
    {
      title: '登记人',
      dataIndex: 'operatorName',
      key: 'operatorName'
    },
    {
      title: '登记机构',
      dataIndex: 'orgName',
      key: 'orgName'
    },
    {
      title: '操作',
      dataIndex: 'operator',
      valueType: ValueTypeEnum.operation,
      fixed: 'right',
      render: (value, record) => {
        const { authBaseId, authCategory } = record as any;
        return (
          <YRButton.Overflow>
            <YRLink
              type="primary"
              onClick={() => {
                YREasyUseModal.show(RulesAddModal, {
                  mode: 'detail',
                  authBaseId,
                  authCategory
                });
              }}
            >
              详情
            </YRLink>
            {mode !== 'detail' && [
              <YRLink
                type="primary"
                onClick={() => {
                  YREasyUseModal.show(RulesAddModal, {
                    mode: 'edit',
                    authBaseId,
                    authCategory,
                    okCallback
                  });
                }}
              >
                修改
              </YRLink>,
              <YRLink
                type="primary"
                onClick={() => handleAction('删除', '确定要删除此授权吗?', delAuthInfo, '删除成功', authBaseId, okCallback)}
              >
                删除
              </YRLink>,
              <YRLink
                type="primary"
                onClick={() =>
                  handleAction('失效', '确定要失效此授权吗?', loseEffectAuthInfo, '失效成功', authBaseId, okCallback)
                }
              >
                失效
              </YRLink>,
              <YRLink
                type="primary"
                onClick={() => {
                  YRModal.confirm({
                    title: '提交复核',
                    okText: '确定',
                    cancelText: '取消',
                    content: '确定要提交复核吗？',
                    okButtonProps: {
                      loading: submitLoading
                    },
                    onOk: () => {
                      submitCallback({ authBaseId });
                    }
                  });
                }}
              >
                提交复核
              </YRLink>,
              <YRLink
                type="primary"
                onClick={() =>
                  handleAction('暂停', '确定要暂停此授权吗?', pauseAuthInfo, '暂停成功', authBaseId, okCallback)
                }
              >
                暂停
              </YRLink>,
              <YRLink
                type="primary"
                onClick={() =>
                  handleAction('重启', '确定要重启此授权吗?', restartAuthInfo, '重启成功', authBaseId, okCallback)
                }
              >
                重启
              </YRLink>
            ]}
          </YRButton.Overflow>
        );
      }
    }
  ];
};

export { organData, columns, tabs, formItemList };
