/**
 * @Author: wangyw26123
 * @Description: 基本信息-节点信息
 * @Date: Created in 2022-04-25 16:22:47
 * @Modifed By:
 */

import React, { useState } from 'react';
import { YRInput, YRForm } from 'yrantd';
import { nodeTypeNames, customNodeIdPrefix } from '@/pages/Flow/XFlow/constants';
import { uuid } from 'yr-loan-antd/lib/util';

const FormItem = YRForm.Item;

const NodeInfo = (props) => {
  const {
    globalDisabled,
    targetData: { nodeFormData, label, name }
  } = props;

  // 自动生成默认 id
  const [defaultId] = useState(() => {
    // 开始节点和结束节点的Id为固定值，不可变更
    if (name === nodeTypeNames['StartNoneEvent']) {
      return 'start';
    } else if (name === nodeTypeNames['EndNoneEvent']) {
      return 'end';
    }
    return `${customNodeIdPrefix[name]}_${uuid().substr(0, 7).toLowerCase()}`;
  });

  const isStartOrEndNode = name === nodeTypeNames['StartNoneEvent'] || name === nodeTypeNames['EndNoneEvent'];
  const isOrganiserNode = name === nodeTypeNames['Organiser'];

  return (
    <>
      {/* 声明表单 */}
      <FormItem name="displayName" initialValue={nodeFormData.displayName} hidden>
        <YRInput />
      </FormItem>
      <FormItem name="nodeType" initialValue={nodeFormData.nodeType} hidden>
        <YRInput />
      </FormItem>

      {/**  开始节点特有属性   */}
      {name === nodeTypeNames['StartNoneEvent'] && (
        <>
          <FormItem name={['properties', 'initiator']} initialValue={'INITIATOR'} hidden>
            <YRInput />
          </FormItem>
        </>
      )}

      {name === nodeTypeNames['Organiser'] && (
        <>
          <FormItem name={['properties', 'usertaskassignment', 'assignment', 'type']} initialValue={'static'} hidden>
            <YRInput />
          </FormItem>
          <FormItem
            name={['properties', 'usertaskassignment', 'assignment', 'assignee']}
            initialValue={'${INITIATOR}'}
            hidden
          >
            <YRInput />
          </FormItem>
        </>
      )}

      <FormItem
        name="id"
        label="节点编号"
        rules={[
          {
            required: true,
            whitespace: true,
            message: '请输入节点编号'
          }
        ]}
        initialValue={nodeFormData.id || defaultId}
      >
        <YRInput disabled={globalDisabled || isStartOrEndNode || isOrganiserNode} placeholder="请输入" maxLength={32} />
      </FormItem>
      <FormItem
        name="name"
        label="节点名称"
        rules={[
          {
            required: true,
            whitespace: true,
            message: '请输入节点名称'
          }
        ]}
        initialValue={label}
      >
        <YRInput disabled={globalDisabled || isStartOrEndNode || isOrganiserNode} placeholder="请输入" maxLength={32} />
      </FormItem>
    </>
  );
};

export default NodeInfo;
