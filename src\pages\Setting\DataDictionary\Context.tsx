/*
 * @Author: DJscript
 * @Date: 2023-03-07 10:12:54
 * @LastEditTime: 2023-03-29 16:41:39
 * @FilePath: /kylin-admin/src/pages/Setting/DataDictionary/Context.tsx
 * @Description: ...
 */
import { Result } from 'ahooks/lib/useRequest/src/types';
import React from 'react';

import { TreeNode } from './Interface';

type ProviderValueType = {
  antdTreeProps: Result<TreeNode[], any> & { originalList: TreeNode[] };
} & {
  selectedState: [Partial<TreeNode>, React.Dispatch<React.SetStateAction<TreeNode>>];
};
const AntdContext = React.createContext<ProviderValueType>(null as unknown as ProviderValueType);

export const AntdContextProvider: React.FC<React.PropsWithChildren<{ value: ProviderValueType }>> = (props) => {
  return <AntdContext.Provider {...props} />;
};

export const useAntd = () => React.useContext(AntdContext);
