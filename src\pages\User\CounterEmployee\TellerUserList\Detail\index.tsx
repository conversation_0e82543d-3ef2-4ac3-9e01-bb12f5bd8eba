/**
 * @页面描述: 柜员管理详情
 * @文件名 index.tsx
 * @Path src\pages\User\CounterEmployee\Detail\index.tsx
 * @Date 2023-03-15 14:03:28
 * <AUTHOR>
 */

import React, { useState } from 'react';
import { YRDatePicker, YRDict, YRForm, YRInput, YRText, YRTooltip, YREasyUseModal, YRModal } from 'yrantd';
import { antdModal, setDict } from '../../../../../utils/utils';
import { useRequest } from 'ahooks';
import { queryUserDetailWithIdentify } from '../../../../../services/user';
import moment from 'moment';

setDict('EnumRegtype', [
  {
    itemKey: '0',
    itemName: '自主注册'
  },
  {
    itemKey: '1',
    itemName: '人工新增'
  },
  {
    itemKey: '2',
    itemName: '自动同步'
  }
]);

setDict('EnumBoolean', [
  {
    itemKey: 'Y',
    itemName: '是'
  },
  {
    itemKey: 'N',
    itemName: '否'
  }
]);

const Index = YREasyUseModal.create(({ userId }) => {
  const modal = YREasyUseModal.useModal();
  const [form] = YRForm.useForm();
  const [RoleList, setRoleList] = useState([]);
  const { data } = useRequest(
    () =>
      queryUserDetailWithIdentify({ userId }).then((res) => {
        if (res?.success) {
          return res.data;
        } else {
          return {};
        }
      }),
    {
      onSuccess: (detailInfo) => {
        form.setFieldsValue(detailInfo);
        setRoleList(detailInfo.userIdentifyDtoList);
      }
    }
  );

  return (
    <YRModal {...antdModal(modal)} title="核心柜员详情" size="middle">
      <YRForm form={form} mode="readPretty">
        <YRForm.Row>
          <YRForm.Item
            label="用户姓名"
            initialValue={data?.username}
            name={['userName']}
            rules={[{ required: true, message: '请输入' }]}
          >
            <YRInput />
          </YRForm.Item>
          <YRForm.Item label="登录账号" name={['accountNo']} rules={[{ required: true, message: '请输入' }]}>
            <YRInput />
          </YRForm.Item>
          <YRForm.Item label="用户状态" name={['userStatus']} rules={[{ required: true, message: '请输入' }]}>
            <YRDict.Select dictkey="EnumGuardUserStatus" />
          </YRForm.Item>
          <YRForm.Item label="是否本行员工" name={['ownBankFlag']}>
            <YRDict.Select dictkey="EnumBoolean" />
          </YRForm.Item>
          <YRForm.Item label="手机号码" name={['mobile']} rules={[{ required: true, message: '请输入' }]}>
            <YRInput />
          </YRForm.Item>
          <YRForm.Item label="所属机构" name={['organName']} rules={[{ required: true, message: '请输入' }]}>
            <YRInput />
          </YRForm.Item>
          {RoleList.map((item: { [key: string]: any }) => {
            return (
              <>
                <YRForm.Item label="身份" name={['身份']} rules={[{ required: true, message: '请输入' }]}>
                  <YRTooltip title={item.organName}>{item.identityName}</YRTooltip>
                </YRForm.Item>
                <YRForm.Item label="角色" name={['角色']} rules={[{ required: true, message: '请输入' }]}>
                  <YRText>
                    {item.roleDtoList?.map((v, i) =>
                      i !== item?.roleDtoList.length - 1 ? `${v.roleName}、` : v.roleName
                    )}
                  </YRText>
                </YRForm.Item>
              </>
            );
          })}
          <YRForm.Item label="电子邮箱" name={['email']} rules={[{ required: true, message: '请输入' }]}>
            <YRInput />
          </YRForm.Item>
          <YRForm.Item label="岗位" name={['post']}>
            <YRInput />
          </YRForm.Item>
          <YRForm.Item label="客户经理级别" name={['level']}>
            <YRInput />
          </YRForm.Item>
          <YRForm.Item label="所属HR机构" name={['hrOrganName']}>
            <YRInput />
          </YRForm.Item>
          <YRForm.Item label="是否柜员" name={['tellerFlag']}>
            <YRDict.Select dictkey="EnumBoolean" />
          </YRForm.Item>
          <YRForm.Item label="注册类型" name={['注册类型']}>
            <YRDict.Select dictkey="EnumRegtype" />
          </YRForm.Item>
          <YRForm.Item
            label="用户有效期"
            name={['accountEffectiveDate']}
            rules={[{ required: true, message: '请输入' }]}
            getValueProps={(v) => {
              return { value: v ? moment(v).format('YYYY-MM-DD') : undefined };
            }}
          >
            <YRDatePicker />
          </YRForm.Item>
          <YRForm.Item label="登记人" name={['registerUserName']} rules={[{ required: true, message: '请输入' }]}>
            <YRInput />
          </YRForm.Item>
          <YRForm.Item label="登记机构" name={['registerOrganName']} rules={[{ required: true, message: '请输入' }]}>
            <YRInput />
          </YRForm.Item>
          <YRForm.Item
            label="登记日期"
            name={['registerDate']}
            rules={[{ required: true, message: '请输入' }]}
            getValueProps={(v) => {
              return { value: v ? moment(v).format('YYYY-MM-DD') : undefined };
            }}
          >
            <YRDatePicker />
          </YRForm.Item>
          <YRForm.Item label="更新人" name={['updateUserName']} rules={[{ required: true, message: '请输入' }]}>
            <YRInput />
          </YRForm.Item>
          <YRForm.Item label="更新机构" name={['updateOrganName']} rules={[{ required: true, message: '请输入' }]}>
            <YRInput />
          </YRForm.Item>
          <YRForm.Item
            label="更新日期"
            name={['updateDate']}
            rules={[{ required: true, message: '请输入' }]}
            getValueProps={(v) => {
              return { value: v ? moment(v).format('YYYY-MM-DD') : undefined };
            }}
          >
            <YRDatePicker />
          </YRForm.Item>
        </YRForm.Row>
      </YRForm>
    </YRModal>
  );
});

export default Index;
