/*
 * @Author: liaokt
 * @Description:  全局-参数库
 * @Date: 2023-05-09 15:55:38
 * @LastEditors: liaokt
 * @LastEditTime: 2023-05-11 10:08:19
 */
import React, { useState } from 'react';
import { YREasyUseModal, YRForm, YRInput, YRMessage } from 'yrantd';
import BandingParamLIbraryModal from './BandingParamLIbraryModal';
import { useDebounceEffect, useRequest } from 'ahooks';
import { addFlowParamLibraryRef } from '@/services/flow';

const ParamsLibrary = (props: any) => {
  const {
    globalDisabled,
    targetData: { nodeFormData },
    globalFormData,
    targetData
  } = props;
  const [currentParam, setCurrentParam] = useState<any>({});
  const [currentId, setCurrentId] = useState<[]>([]);
  const form = YRForm.useFormInstance();

  useDebounceEffect(() => {
    const { paramLibraryId } = globalFormData;
    setCurrentId(paramLibraryId);
  }, [globalFormData]);

  /** 绑定参数库接口 */
  const { loading: suspendLaoding, run: suspendRequest } = useRequest(addFlowParamLibraryRef, {
    manual: true,
    onSuccess: (result) => {
      const { errorMessage, data } = result;
      if (errorMessage === null) {
        YRMessage.success('绑定成功');
        const { libraryName, id } = currentParam;
        form.setFieldsValue({ paramLibraryName: libraryName, paramLibraryId: id });
      }
    }
  });

  /** 选择参数库事件 */
  const selectParam = (values: any) => {
    if (values) {
      setCurrentParam(values[0]);
      bandingLibrary(values[0]?.id);
    }
  };

  /** 绑定参数库 */
  const bandingLibrary = (id: string) => {
    const params = {
      paramLibraryId: id,
      relationId: globalFormData?.flowNumber,
      relationType: globalFormData?.sceneType
    };
    suspendRequest(params);
  };

  return (
    <YRForm.Row>
      <YRForm.Item name={'paramLibraryName'} label={'参数库绑定'} initialValue={globalFormData?.paramLibraryName}>
        <YRInput
          placeholder="请选择要绑定的参数库"
          onClick={() => {
            YREasyUseModal.show(BandingParamLIbraryModal, { globalFormData, onOk: selectParam, currentId });
          }}
        />
      </YRForm.Item>
      <YRForm.Item name={'paramLibraryId'} label={'参数库Id'} initialValue={globalFormData?.paramLibraryId} hidden>
        <YRInput />
      </YRForm.Item>
    </YRForm.Row>
  );
};

export default ParamsLibrary;
