# 机构树选择功能API集成说明

## 概述

为已复核-单笔授权管理页面和待复核-单笔授权管理页面实现了机构树选择功能的API集成，使用真实的后端接口获取机构树数据。

## API接口信息

### 机构树查询接口
- **接口路径**: `/hsjry/corp/authCatalogQuery/queryAuthCatalogOrgTree`
- **请求方法**: POST
- **功能**: 查询机构树数据用于机构选择器
- **调用时机**: 页面初始化时

### API调用配置
```typescript
export function queryAuthCatalogOrgTree(param?: any) {
  return request('/hsjry/corp/authCatalogQuery/queryAuthCatalogOrgTree', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '查询机构树数据'
  });
}
```

### API请求参数
```typescript
{
  "queryAuthFlag": "1",                    // 查询授权标识
  "logPermitId": "kylin-admin-M0105I1",   // 日志权限ID
  "logOperateName": "查询所有一级机构"      // 日志操作名称
}
```

#### 参数说明
- **queryAuthFlag**: 查询授权标识，固定值 "1"
- **logPermitId**: 日志权限ID，用于权限控制和日志记录，固定值 "kylin-admin-M0105I1"
- **logOperateName**: 日志操作名称，用于操作日志记录，固定值 "查询所有一级机构"

## 数据结构处理

### API响应数据结构
API响应中的机构树数据可能存在于以下路径：
- `$.data.categoryList` - 机构分类列表
- `$.data.children` - 直接的子机构列表
- `$.data` - 直接的机构数组

### 数据映射函数
```typescript
const mapOrgTreeData = (orgData: any): OrganTreeNode[] => {
  if (!orgData) return [];
  
  const mapNode = (node: any): OrganTreeNode => {
    return {
      orgId: node.orgId || node.id || '',
      orgName: node.orgName || node.name || node.title || '',
      children: node.children && node.children.length > 0 
        ? node.children.map(mapNode) 
        : undefined
    };
  };

  // 处理API响应中的机构树数据
  if (Array.isArray(orgData)) {
    return orgData.map(mapNode);
  } else if (orgData.categoryList && Array.isArray(orgData.categoryList)) {
    return orgData.categoryList.map(mapNode);
  } else if (orgData.children && Array.isArray(orgData.children)) {
    return orgData.children.map(mapNode);
  }
  
  return [];
};
```

### 目标数据结构
```typescript
interface OrganTreeNode {
  orgId: string;        // 机构ID
  orgName: string;      // 机构名称
  children?: OrganTreeNode[];  // 子机构列表
}
```

## 实现的页面

### 1. 已复核-单笔授权管理页面
- **文件路径**: `src/pages/Auth/ReviewedSingleAuth/index.tsx`
- **页面路由**: `/organ-group/auth/reviewed-single`
- **功能**: 查询条件中的机构树选择器

### 2. 待复核-单笔授权管理页面
- **文件路径**: `src/pages/Auth/PendingSingleAuth/index.tsx`
- **页面路由**: `/organ-group/auth/pending-single`
- **功能**: 查询条件中的机构树选择器

## 修改的文件

### 1. API服务文件
- **文件**: `src/services/batchAuth.ts`
- **新增**: `queryAuthCatalogOrgTree` 函数

### 2. 页面组件文件
- **文件**: `src/pages/Auth/ReviewedSingleAuth/index.tsx`
- **修改**: 添加机构树API集成和数据映射
- **文件**: `src/pages/Auth/PendingSingleAuth/index.tsx`
- **修改**: 添加机构树API集成和数据映射

## 功能特性

### 1. 自动加载
- 页面初始化时自动调用机构树API
- 获取完整的机构树结构数据

### 2. 数据映射
- 智能识别API响应中的机构树数据位置
- 自动映射为TreeSelect组件需要的格式
- 支持多级嵌套的机构结构

### 3. 错误处理
- API调用失败时显示友好提示
- 提供默认的机构树数据作为备选
- 不影响页面其他功能的正常使用

### 4. 用户体验
- 支持多级机构展开和选择
- 保持现有的交互逻辑和样式
- 提供清晰的加载状态反馈

## 错误处理机制

### 1. API调用示例
```typescript
// 调用机构树查询接口
const orgTreeRes = await queryAuthCatalogOrgTree({
  queryAuthFlag: "1",
  logPermitId: "kylin-admin-M0105I1",
  logOperateName: "查询所有一级机构"
});

if (orgTreeRes?.rpcResult === 'SUCCESS' && orgTreeRes?.data) {
  // 成功处理
  const mappedOrgTree = mapOrgTreeData(orgTreeRes.data);
  setOrganTreeData(mappedOrgTree);
} else {
  // 失败处理
  console.warn('机构树数据加载失败:', orgTreeRes?.errorMessage);
  YRMessage.warning('机构树数据加载失败，请刷新页面重试');
  // 设置默认数据
}
```

### 2. 异常捕获
```typescript
try {
  // API调用
} catch (error) {
  console.error('初始化下拉框数据失败:', error);
  YRMessage.error('初始化数据失败，请刷新页面重试');
  // 设置默认数据
}
```

### 3. 默认数据
当API调用失败时，提供默认的机构树数据：
```typescript
setOrganTreeData([
  {
    orgId: '1',
    orgName: '总行',
    children: [
      { orgId: '101', orgName: '风险管理部' },
      { orgId: '102', orgName: '信贷管理部' }
    ]
  }
]);
```

## 调试信息

### 1. 成功日志
```typescript
console.log('机构树数据加载成功:', mappedOrgTree);
```

### 2. 警告日志
```typescript
console.warn('机构树数据加载失败:', orgTreeRes?.errorMessage);
```

### 3. 错误日志
```typescript
console.error('初始化下拉框数据失败:', error);
```

## 使用说明

### 1. 机构选择
- 点击机构选择器下拉箭头
- 展开机构树结构
- 选择目标机构
- 支持清空选择

### 2. 查询功能
- 选择机构后可进行查询
- 支持与其他查询条件组合使用
- 查询结果会根据选择的机构进行过滤

## 注意事项

1. **API依赖**: 确保后端API接口正常可用
2. **数据格式**: API响应数据格式可能变化，需要相应调整映射逻辑
3. **性能考虑**: 大型机构树可能影响加载性能
4. **权限控制**: 确保用户只能看到有权限的机构
5. **缓存策略**: 考虑添加机构树数据缓存以提高性能

## 更新日志

- 2024-12-19: 初始版本实现
- 集成机构树查询API
- 实现数据映射和错误处理
- 更新两个单笔授权管理页面
- 添加完整的调试和错误处理机制
