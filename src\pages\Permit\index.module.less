/**
 * @Author: wangyw26123
 * @Description: 功能权限
 * @Date: Created in 2022-12-14 13:14:41
 * @Modifed By:
 */
@import 'src/common';

// 功能权限-权限组
.permitGroupLoading {
  height: 100%;

  :global {
    .ant-spin-container {
      height: inherit;
    }
  }

  .permitGroupList {
    max-height: calc(100vh - 169px);
    overflow-y: auto;
    overflow-x: hidden;
  }
}

// 权限树
.permitTreeNodeIcon {
  :global {
    .ant-tree-iconEle {
      width: 45px !important;
    }
  }
}

// 权限组列表
.groupListItem {
  padding: @yr-padding-xss @yr-padding-sm;
  cursor: pointer;

  &.active {
    background: @yr-primary-1;
  }

  &:hover {
    background: @yr-primary-1;
  }
}
