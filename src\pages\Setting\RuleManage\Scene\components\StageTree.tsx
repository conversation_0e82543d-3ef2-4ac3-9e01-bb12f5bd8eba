/**
 * 页面描述:阶段及环节目录
 * @文件名 StageTree.tsx
 * @filePath \src\pages\Setting\RuleManage\Scene\components\StageTree.tsx
 * @Date 2023-08-21 10:45:31
 * @Email <EMAIL>
 * <AUTHOR>
 */
import React, { useMemo, useState } from 'react';
import { YREasyUseModal, YRSpace, YRTree, YREmpty, YRItem } from 'yrantd';
import { CaretDownOutlined } from '@ant-design/icons';
import { businessStageNames, tacheNameAndStage } from '../config';

const StageTree = (props) => {
  const { setActiveNode, stageList, tacheList } = props as any;
  /** 当前选中节点 */
  const [selectedIds, setSelectedIds] = useState<any>([]);

  /** 树的格式化 */
  const tree: any = useMemo(() => {
    const treeData = [] as any;
    if (stageList.length > 0 && tacheList.length > 0) {
      treeData.push({
        title: '所有阶段',
        key: 'all',
        nodeType: 'all',
        children: stageList.map((item) => {
          return {
            title: businessStageNames[item],
            key: businessStageNames[item],
            nodeType: 'stage',
            children: tacheList
              .filter((v) => {
                return tacheNameAndStage[v]?.stage === item;
              })
              .map((i) => {
                return {
                  title: tacheNameAndStage[i].name,
                  key: i,
                  nodeType: 'tache'
                };
              })
          };
        })
      });
      setSelectedIds(['all']);
      setActiveNode({
        title: '所有阶段',
        key: 'all',
        nodeType: 'all'
      });
    }
    const loop = (data: any, parent?: any) =>
      data?.map((item: any) => {
        const { title, key } = item;
        const sonTitle = (
          <YRItem
            title={title}
            key={key}
            onClick={() => {
              setSelectedIds([key]);
              setActiveNode(item);
            }}
          />
        );
        return {
          ...item,
          title: sonTitle,
          key,
          children: loop(item.children, item),
          parent: parent || null
        };
      });
    return loop(treeData);
  }, [stageList, tacheList]);

  return (
    <YREasyUseModal.Provider>
      <YRSpace direction="vertical">
        {tree?.length > 0 ? (
          <YRTree
            blockNode
            defaultExpandAll
            selectedKeys={selectedIds}
            treeData={tree}
            switcherIcon={<CaretDownOutlined />}
          />
        ) : (
          <YREmpty />
        )}
      </YRSpace>
    </YREasyUseModal.Provider>
  );
};

export default StageTree;
