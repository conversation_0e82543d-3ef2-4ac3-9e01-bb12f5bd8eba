/**
 * @页面描述:
 * @文件名 AddPartJobModal.tsx
 * @Path src\pages\User\components\AddPartJobModal.tsx
 * @Date 2023-03-27 16:04:58
 * <AUTHOR>
 */

import React, { useState } from 'react';
import {
  YRClassificationLayout,
  YRDict,
  YREasyUseModal,
  YRForm,
  YRInput,
  YRMessage,
  YRModal,
  YRTransfer,
  YRTreeSelect
} from 'yrantd';
import { antdModal } from '../../../utils/utils';
import { allocateUserRoles } from '../../../services/user';

const AddPartJobModal = YREasyUseModal.create(
  ({ partInfo, treeData, mockData, filterOption, targetKeys, queryDetail }) => {
    const modal = YREasyUseModal.useModal();
    const [form] = YRForm.useForm();
    const [targetList, setTargetList] = useState(targetKeys || []);
    const handleChange = (newTargetKeys) => {
      setTargetList(newTargetKeys);
    };
    const submit = () => {
      form.validateFields().then((values: { [key: string]: any }) => {
        const param = {
          userId: partInfo.userId,
          identityId: partInfo.identifyId,
          identityName: values.identityName,
          organId: values.organId,
          bizLines: values.bizLines.join(','),
          roleIdList: targetList
        };
        allocateUserRoles(param).then((res) => {
          if (res && res.success) {
            queryDetail();
            YRMessage.success('修改成功');
            modal.hide();
          }
        });
      });
    };
    return (
      <YRModal {...antdModal(modal)} size="middle" title="兼职角色" maskClosable={false} onOk={submit}>
        <YRForm form={form}>
          <YRForm.Item
            label="兼职身份"
            wrapperCol={{ span: 24 }}
            name="identityName"
            rules={[{ required: true, message: '请输入' }]}
            initialValue={partInfo?.identityName}
          >
            <YRInput placeholder="请输入兼职身份" />
          </YRForm.Item>
          <YRForm.Item
            label="关联机构名称"
            name="organId"
            rules={[{ required: true, message: '请选择' }]}
            initialValue={partInfo?.organId}
          >
            <YRTreeSelect
              showSearch
              treeData={treeData}
              treeNodeFilterProp="organName"
              fieldNames={{ label: 'organName', value: 'organId' }}
              placeholder="请选择合同签约机构"
            />
          </YRForm.Item>
          <YRForm.Item
            name="bizLines"
            label="所属条线"
            rules={[{ required: true, message: '所属条线' }]}
            initialValue={partInfo?.bizLines}
          >
            <YRDict.CheckboxGroup dictkey="EnumUserBizLine" />
          </YRForm.Item>
        </YRForm>
        <YRClassificationLayout title="分配角色">
          <YRTransfer
            listStyle={{
              width: '50%',
              height: 400
            }}
            dataSource={mockData}
            showSearch
            filterOption={filterOption}
            targetKeys={targetList}
            onChange={handleChange}
            render={(item: { key: string; title: string }) => item.title}
          />
        </YRClassificationLayout>
      </YRModal>
    );
  }
);

export default AddPartJobModal;
