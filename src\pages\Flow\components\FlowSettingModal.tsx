/*
 * @Author: liaokt
 * @E-mail: <EMAIL>
 * @Date: 2023-04-04 10:07:17
 * @Description: 流程定义配置弹窗
 */

import React, { useState } from 'react';
import { YRDict, YREasyUseModal, YRMessage, YRModal, YRSelect, YRSwitch, YRTable, YRTableProps } from 'yrantd';
import { antdModal } from '@/utils/utils';
import Dict from '../../Organ/mock/getDict';
import { useAntdTable, useDebounceEffect, useRequest } from 'ahooks';
import {
  activateProcessDefinitionByDeployId,
  queryFlowState,
  suspendProcessDefinitionByDeployId
} from '@/services/flow';

/**
 * @param flowNumber 流程编号
 */
const FlowSettingModal = YREasyUseModal.create(({ flowNumber }) => {
  const modal = YREasyUseModal.useModal();
  const [currentOpen, setCurrentOpen] = useState(false);

  /** 查询流程版本状态列表 */
  const { tableProps, run, params, refresh } = useAntdTable(
    (p) => {
      delete p.extra;
      delete p.filters;
      return queryFlowState({
        ...p,
        pageNum: p.current
      }).then((res) => {
        return res.success ? { list: res.data, total: res.data?.length } : { list: [], total: 0 };
      });
    },
    {
      manual: true
    }
  );

  /** 挂起流程版本 */
  const { loading: suspendLaoding, run: suspendRequest } = useRequest(suspendProcessDefinitionByDeployId, {
    manual: true,
    onSuccess: (result) => {
      const { errorMessage, data } = result;
      if (errorMessage === null) {
        YRMessage.success('挂起成功');
      }
    }
  });

  /** 激活流程版本 */
  const { loading: activateLaoding, run: activateRequest } = useRequest(activateProcessDefinitionByDeployId, {
    manual: true,
    onSuccess: (result) => {
      const { errorMessage, data } = result;
      if (errorMessage === null) {
        YRMessage.success('激活成功');
      }
    }
  });

  useDebounceEffect(() => {
    flowNumber && run({ ...params[0], flowNumbers: [flowNumber] } as any);
  }, [flowNumber]);

  //  确认事件
  const submit = () => {};

  // 开关事件
  const openOperate = (checked, row) => {
    const { procDefId } = row;
    if (checked) {
      // 激活操作
      activateRequest({ procDefId });
    } else {
      // 挂起操作
      suspendRequest({ procDefId });
    }
  };

  const columns: YRTableProps['columns'] = [
    {
      title: '版本号',
      dataIndex: 'version',
      width: 120,
      render: (value: string) => value || CONST.null
    },
    {
      title: '状态',
      dataIndex: 'state',
      width: 120,
      render: (value: string, row) => {
        return (
          (
            <YRSwitch
              unCheckedChildren={'挂起'}
              checkedChildren={'激活'}
              defaultChecked={value === '1'}
              loading={suspendLaoding || activateLaoding}
              onClick={(checked, e) => {
                openOperate(checked, row);
              }}
            />
          ) || CONST.null
        );
      }
    }
  ];

  return (
    <YRModal {...antdModal(modal)} title="配置" width={'30%'}>
      <YRTable rowKey={(row) => row.procDefId} columns={columns} {...tableProps} />
    </YRModal>
  );
});

export default Dict(['EnumVersoinStatus'])(FlowSettingModal);
