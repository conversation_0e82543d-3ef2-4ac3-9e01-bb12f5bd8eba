/*
 * @Description: file content
 * @Author: your name
 * @Date: 2020-10-24 14:46:20
 * @LastEditors: your name
 * @LastEditTime: 2020-11-17 17:50:17
 */
import React, { useEffect, useState } from 'react';
import { YRForm, YRInput, YRSelect, YRRow, YRCol, YRSwitch, YRTooltip } from 'yrantd';
import LittleModal from '@/components/LittleModal';
import RegexRules from '@/utils/regex';
import { enumParamType, enumSdkParamType, enumParamWay } from '@/utils/data-dictionary';

const FormItem = YRForm.Item;
const { Option } = YRSelect;

const formItemLayout = {
  labelCol: {
    span: 7
  },
  wrapperCol: {
    span: 16,
    offset: 1
  }
};

const formItemLayoutWithoutLabel = {
  labelCol: {
    span: 0
  },
  wrapperCol: {
    span: 24
  }
};
const ModalRequest = (props) => {
  const [form] = YRForm.useForm();
  const { type, initValues, title, cacheKey = '', children, sysFieldList = [], clientType } = props;
  const [isCache, setIsCache] = useState(false);
  const [isRequired, setIsRequired] = useState(false);
  const [isInParam, setIsInParam] = useState(false);
  const selectedValWay = YRForm.useWatch('valWay', form);
  const selectedType = YRForm.useWatch('type', form);

  useEffect(() => {
    if (cacheKey) {
      const index = cacheKey.split(',').findIndex((val) => {
        return val === initValues.paramName;
      });
      setIsCache(index !== -1);
    } else {
      setIsCache(false);
    }
  }, [cacheKey, initValues]);

  const onParamTypeChange = (value) => {
    const formRef = React.createRef();
  };

  useEffect(() => {
    if (initValues && initValues.isRequired) {
      setIsRequired(initValues.isRequired === '1');
    }
  }, [initValues]);

  useEffect(() => {
    if (initValues && initValues.isInParam) {
      setIsInParam(initValues.isInParam === '1');
    }
  }, [initValues]);

  const cancel = () => {
    if (cacheKey) {
      const index = cacheKey.split(',').findIndex((val) => {
        return val === initValues.paramName;
      });
      setIsCache(index !== -1);
    } else {
      setIsCache(false);
    }
    setIsRequired(initValues.isRequired === '1');
    setIsInParam(initValues.isInParam === '1');
    form.resetFields();
  };

  // 提交方法
  const submit = ({ callback }) => {
    const { handleOk } = props;
    form.validateFields().then((values) => {
      callback(false);
      handleOk(type, {
        ...values,
        isCache,
        id: initValues.id,
        isRequired: isRequired ? '1' : '0',
        isInParam: isInParam ? '1' : '0'
      });
      cancel();
    });
  };

  const handleChange = () => {
    form.setFieldsValue({ val: undefined });
  };

  const generateFormFields = () => {
    let layout = formItemLayoutWithoutLabel;
    let item;
    if (clientType === 'http') {
      if (selectedValWay === 0) {
        item = (
          <YRSelect placeholder="参数值">
            {sysFieldList.map((info) => {
              return (
                <Option value={info.itemVal} key={info.itemVal}>
                  <YRTooltip title={info.itemVal}>{info.itemName}</YRTooltip>
                </Option>
              );
            })}
          </YRSelect>
        );
      } else {
        item = <YRInput placeholder="参数值" />;
      }
    } else {
      layout = formItemLayout;
      if (selectedValWay === 0) {
        item = (
          <YRSelect placeholder="参数值">
            {sysFieldList.map((info) => {
              return (
                <Option value={info.itemVal} key={info.itemVal}>
                  <YRTooltip title={info.itemVal}>{info.itemName}</YRTooltip>
                </Option>
              );
            })}
          </YRSelect>
        );
      } else {
        item = <YRInput placeholder="参数值" />;
      }
    }

    return (
      <YRForm form={form}>
        <YRRow gutter={16}>
          {clientType === 'http' ? (
            <YRCol span={8}>
              <FormItem
                label="参数"
                {...formItemLayout}
                name="type"
                initialValue={initValues?.type || 0}
                rules={[{ required: true }]}
              >
                <YRSelect onChange={onParamTypeChange} disabled={type !== 'add'}>
                  {Object.keys(enumParamType).map((value) => {
                    return (
                      <Option value={Number(value)} key={value}>
                        {enumParamType[value]}
                      </Option>
                    );
                  })}
                </YRSelect>
              </FormItem>
            </YRCol>
          ) : null}
          {clientType !== 'http' ? (
            <YRCol span={8}>
              <FormItem
                label="参数"
                {...formItemLayout}
                name="type"
                initialValue={initValues?.type || 2}
                rules={[
                  {
                    required: true,
                    message: '请输入参数'
                  },
                  {
                    message: '不能有空格！',
                    pattern: RegexRules.unSpace
                  }
                ]}
              >
                <YRSelect onChange={onParamTypeChange}>
                  {Object.keys(enumSdkParamType).map((value) => {
                    return (
                      <Option value={Number(value)} key={value}>
                        {enumSdkParamType[value]}
                      </Option>
                    );
                  })}
                </YRSelect>
              </FormItem>
            </YRCol>
          ) : null}
          <YRCol span={7}>
            <FormItem
              label={clientType === 'http' ? undefined : '参数'}
              {...layout}
              name="paramName"
              initialValue={initValues?.paramName || ''}
              rules={[
                {
                  required: true,
                  message: '请输入参数'
                },
                {
                  pattern: RegexRules.uncode2 || RegexRules.unchinese,
                  message: '只能输入英文数字和下划线'
                }
              ]}
            >
              <YRInput placeholder="参数名" maxLength={254} disabled={type !== 'add'} />
            </FormItem>
          </YRCol>
          <YRCol span={clientType === 'http' ? 8 : 8}>
            <FormItem
              {...formItemLayoutWithoutLabel}
              name="paramNameCn"
              initialValue={initValues?.paramNameCn || ''}
              rules={[
                {
                  required: true,
                  message: '请输入参数说明'
                },
                {
                  message: '不能有空格！',
                  pattern: RegexRules.unSpace
                }
              ]}
            >
              <YRInput placeholder="参数说明" />
            </FormItem>
          </YRCol>
          <hr style={{ height: '51px', visibility: 'hidden' }} />
          {selectedType !== 3 ? (
            <YRCol span={8}>
              <FormItem
                label="值"
                {...formItemLayout}
                name="valWay"
                initialValue={initValues?.valWay || 8}
                rules={[
                  {
                    required: true,
                    message: '请输入参数值'
                  },
                  {
                    message: '不能有空格！',
                    pattern: RegexRules.unSpace
                  }
                ]}
              >
                <YRSelect showSearch onChange={handleChange}>
                  {Object.keys(enumParamWay).map((value) => {
                    return (
                      <Option value={Number(value)} key={value}>
                        {enumParamWay[value]}
                      </Option>
                    );
                  })}
                </YRSelect>
              </FormItem>
            </YRCol>
          ) : null}
          {selectedType !== 3 ? (
            <YRCol span={16}>
              <FormItem
                {...formItemLayoutWithoutLabel}
                name="val"
                initialValue={initValues?.val || ''}
                rules={[
                  {
                    required: true,
                    message: '请输入参数值'
                  },
                  {
                    message: '不能有空格！',
                    pattern: RegexRules.unSpace
                  }
                ]}
              >
                {item}
              </FormItem>
            </YRCol>
          ) : null}
        </YRRow>
      </YRForm>
    );
  };

  const handleSwitch = (switchType, checked) => {
    if (switchType === '1') {
      setIsCache(checked);
    } else if (switchType === '2') {
      setIsRequired(checked);
    } else {
      setIsInParam(checked);
    }
  };

  const getActions = () => {
    return (
      <div>
        {selectedType === 3 ? (
          <span style={{ verticalAlign: 'middle' }}>
            必传：
            <YRSwitch
              checkedChildren="是"
              unCheckedChildren="否"
              checked={isRequired}
              onChange={(checked) => {
                handleSwitch('2', checked);
              }}
              style={{ marginRight: '24px' }}
            />
          </span>
        ) : null}
        {/* {selectedType !== 3 ? (
          <span style={{ verticalAlign: "middle" }}>
            入参：
            <YRSwitch
              checkedChildren="是"
              unCheckedChildren="否"
              checked={isInParam}
              onChange={checked => {
                handleSwitch("3", checked);
              }}
              style={{ marginRight: "24px" }}
            />
          </span>
        ) : null} */}

        {/* {selectedType !== 3 ? (
          <span style={{ verticalAlign: 'middle' }}>
            缓存参数：
            <YRSwitch
              checkedChildren="是"
              unCheckedChildren="否"
              checked={isCache}
              onChange={(checked) => {
                handleSwitch('1', checked);
              }}
            />
          </span>
        ) : null} */}
      </div>
    );
  };
  return (
    <LittleModal
      title={title}
      onOk={submit}
      onCancel={cancel}
      width={700}
      content={generateFormFields()}
      actions={getActions()}
    >
      {children}
    </LittleModal>
  );
};

export default ModalRequest;
