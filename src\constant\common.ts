/**
 * @Author: wangyw26123
 * @Description: 全局规则
 * @Date: Created in 2022-12-20 10:06:34
 * @Modifed By:
 */
export {
  generate,
  presetPalettes,
  presetDarkPalettes,
  presetPrimaryColors,
  red,
  volcano,
  orange,
  gold,
  yellow,
  lime,
  green,
  cyan,
  blue,
  geekblue,
  purple,
  magenta,
  grey,
  gray
} from '@ant-design/colors';
/**
 * 枚举状态
 */
enum EnumStatus {
  success = 'success',
  processing = 'processing',
  default = 'default',
  error = 'error',
  warning = 'warning'
}

/**
 * 枚举常用提示语
 */
enum EnumMsg {
  add = '新增成功',
  modify = '修改成功',
  delete = '删除成功',
  save = '保存成功',
  enable = '启用成功',
  disable = '停用成功',
  operator = '操作成功',
  update = '更新成功',
  copy = '复制成功',
  submit = '提交成功',
  restore = '恢复成功',
  reset = '重置成功',
  handover = '移交成功'
}

/**
 * 日期格式
 */
enum EnumDateFormat {
  date = 'YYYY-MM-DD',
  time = 'YYYY-MM-DD HH:mm:ss'
}

export { EnumStatus, EnumMsg, EnumDateFormat };
