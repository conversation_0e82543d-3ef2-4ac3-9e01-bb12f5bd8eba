/**
 * @param {Object} Object
 * @returns {Array} Array
 */
export function objectToArray(object, { nameKey, valueKey } = { nameKey: 'key', valueKey: 'value' }) {
  return Object.entries(object).map(([value, key]) => {
    return { [nameKey]: key, [valueKey]: value };
  });
}

export const menuIconList = {
  default: 'madfont-pub-ic-set',
  '/': 'fkicon-qidongshouye1',
  '/index': 'fkicon-qidongshouye1',
  '/workbench': 'fkicon-gongzuotai',
  '/businesscenter': 'fkicon-yewuzhongxin',
  '/data': 'madfont-list-ic-thirddata',
  '/monitor': 'fkicon-baogaozhongxin',
  '/system': 'fkicon-guanlizhongxin',
  '/strategy': 'fkicon-celvezhongxin',
  '/riskReport': 'madfont-list-ic-monitor',
  '/externalList': 'madfont-list-ic-namelist',
  '/ml': 'madfont-list-ic-book',
  '/indicator': 'fkicon-bianliangzhongxin',
  '/case': 'madfont-list-ic-servicedata',
  '/three': 'madfont-list-ic-thirddata',
  '/category': 'fkicon-yewupinleiguanli',
  '/datacenter': 'fkicon-shujuzhongxin',
  '/gateway': 'fkicon-wangguanzhongxin',
  '/userrights': 'fkicon-zuzhiguanli',
  '/decisionPackage': 'fkicon-juece1'
};
export const statusMap = ['default', 'success', 'error', 'processing'];
export const userStatus = {
  '00': '全部',
  1: '启用中',
  0: '已停用'
};
export const jobWorkStatus = {
  0: '启动',
  1: '停止',
  2: '待启动',
  3: '启动中',
  4: '启动失败'
};
export const idType = {
  0: '身份证'
};
export const sexType = {
  1: '男',
  2: '女',
  0: '其它'
};
// 操作员状态
export const operatorStatus = {
  '-1': '全部',
  0: '停用',
  1: '启用',
  2: '过期',
  3: '注销'
};
export const AccountChargeType = {
  10: '计数子账户',
  20: '包月子账户',
  30: '包季子账户',
  40: '包年子账户'
};
export const WhetherSuccess = {
  '': '全部',
  1: '计费成功',
  0: '计费失败',
  2: '登记成功'
};
export const WhetherSuccessArr = objectToArray(WhetherSuccess);
export const accountLocks = {
  0: '锁定',
  1: '正常'
};
export const computeType = {
  0: '流处理',
  1: '批处理'
};
export const menu = [
  {
    id: 'permission0001',
    name: '客户管理'
  },
  {
    id: 'permission0002',
    name: '授信申请查询'
  },
  {
    id: 'permission0003',
    name: '额度管理'
  },
  {
    id: 'permission0004',
    name: '借据申请查询'
  },
  {
    id: 'permission0005',
    name: '明细查询',
    child: [
      {
        id: 'permission0010',
        name: '还款明细'
      },
      {
        id: 'permission0011',
        name: '减免明细'
      },
      {
        id: 'permission0012',
        name: '放款明细'
      },
      {
        id: 'permission0013',
        name: '利息计提明细'
      },
      {
        id: 'permission0021',
        name: '费用计费明细'
      }
    ]
  },
  {
    id: 'permission0006',
    name: '业务报表',
    child: [
      {
        id: 'permission0014',
        name: '总账报表总览'
      },
      {
        id: 'permission0015',
        name: '资金平衡对账表'
      },
      {
        id: 'permission0016',
        name: '备付金账户流水'
      }
    ]
  },
  {
    id: 'permission0007',
    name: '异常管理'
  },
  {
    id: 'permission0008',
    name: '数据回滚'
  },
  {
    id: 'permission0009',
    name: '用户权限',
    child: [
      {
        id: 'permission0017',
        name: '用户管理'
      },
      {
        id: 'permission0018',
        name: '机构管理'
      },
      {
        id: 'permission0019',
        name: '角色管理'
      }
    ]
  },
  {
    id: 'permission0020',
    name: '总账配置'
  },
  {
    id: 'permission0022',
    name: '黑名单配置'
  }
];
export const applicationResult = {
  A: '通过',
  D: '拒绝',
  R: '转人工',
  W: '警告',
  DATA: '数据源',
  DATA_ASYNC: '异步数据源',
  ERR: '异常单'
};
export const computeSwitch = {
  '00': '全部',
  0: '关闭',
  1: '正常'
};
export const listLibraryType = {
  '-1': '全部',
  1: '黑名单',
  2: '白名单',
  3: '灰名单',
  4: '红名单'
};
export const listLibraryTypeArr = objectToArray(listLibraryType);

export const listType = {
  '-1': '全部',
  1: '手机号',
  2: '身份证号',
  3: '设备ID',
  4: 'IP',
  5: '邮箱',
  6: 'QQ',
  7: '微信',
  8: '地址',
  9: '公司名称',
  10: '支付宝',
  11: '短信过滤手机号',
  12: '三要素',
  13: '行业'
};
export const listTypeArr = objectToArray(listType);

// 角色类型
export const roleType = {
  1: '管理员角色',
  2: '普通角色',
  3: '其它'
};

export const roleStatus = {
  0: '未生效',
  1: '生效',
  2: '失效'
};

export const approvalStatus = {
  ADOPT: '通过',
  REJECT: '驳回',
  REPULSE: '打回',
  REFUSE: '拒绝',
  AUTOREFUSEFK: '自动拒绝',
  BACKPATH: '退补件'
};
export const listStatus = {
  1: '待审核',
  2: '通过',
  3: '否决'
};
export const dateType = {
  day: '日报',
  week: '周报',
  month: '月报',
  default: '自定义'
};
export const dateTypeButDay = {
  week: '周报',
  month: '月报',
  default: '自定义'
};
export const dateTypeButDayArr = objectToArray(dateTypeButDay);
export const datePickerType = {
  day: 0,
  week: 1,
  month: 2,
  default: 3
};
export const datePickerTypeArr = objectToArray(datePickerType);

export const dateType1 = {
  day: '日报',
  week: '周报',
  month: '月报'
};
export const applyStatus = {
  0: '提交',
  1: '处理中',
  2: '完成',
  3: '异常'
};

export const decisionStatusName = {
  A: '通过',
  D: '拒绝',
  W: '提醒',
  ERR: '异常'
};

export const decisionStatusBadgeMap = {
  A: 'success',
  D: 'error',
  W: 'processing',
  ERR: 'error'
};

export const statusBadgeMap = ['default', 'processing', 'success', 'error'];

export const constantType = {
  String: 'String',
  Integer: 'Integer',
  Char: 'Char',
  Double: 'Double',
  Long: 'Long',
  Float: 'Float',
  BigDecimal: 'BigDecimal',
  Boolean: 'Boolean',
  Date: 'Date',
  List: 'List',
  Set: 'Set',
  Map: 'Map',
  Enum: 'Enum',
  Object: 'Object'
};

export const creditType = {
  ALL: '全部',
  BQTL_GETDURATION: '百川联通在网时长',
  TD: '同盾',
  BQS: '白骑士',
  JIAO_RSL_D3: '集奥手机号码月消费档次',
  JIAO_RSL_A3: '集奥手机号码在网时长',
  JIAO_RSL_B13: '集奥手机号码最近三月停机次数',
  ZM_VERIFY: '欺诈关注清单',
  RH_CREDIT: '人行征信'
};

export const ruleSource = {
  全部: '全部',
  白骑士: '白骑士',
  同盾: '同盾'
};

export const userType = {
  0: '居民身份证',
  1: '户口簿',
  2: '护照',
  3: '军官证',
  4: '士兵证'
};

export const applyType = {
  CREDIT: '授信',
  LOAN: '支用',
  CREDIT_LOAN: '授信支用一体',
  default: '未定义'
};

export const comSwitch = {
  0: '关',
  1: '开'
};
export const threeClientType = {
  // "0": "全部",
  http: 'HTTP',
  sdk: 'SDK'
};
export const threeClientTypeArr = objectToArray(threeClientType);

export const enumParamType = {
  0: '请求头',
  1: '路径参数'
};
export const paramType = [
  { label: '字符串', value: 'String' },
  { label: '整型', value: 'Integer' },
  { label: '双精度浮点型', value: 'Double' },
  { label: '长整型', value: 'Long' },
  { label: '布尔型', value: 'Boolean' },
  { label: '单精度浮点型', value: 'Float' },
  { label: '短整型', value: 'Short' },
  { label: '基本整型', value: 'int' },
  { label: '基本长整型', value: 'long' },
  { label: '基本双精度浮点型', value: 'double' },
  { label: '基本单精度浮点型', value: 'float' },
  { label: '基本布尔型', value: 'boolean' },
  { label: '基本短整型', value: 'short' },
  { label: '对象类型', value: 'object' }
];
export const methodType = [
  { label: '构造器参数', value: 'constructor' },
  { label: '方法参数', value: 'method' }
];

export const sourceMap = {
  risk: '决策',
  compute: '计算',
  model: '模型',
  three: '三方'
};
export const sourceMapArr = objectToArray(sourceMap);

export const challengerType = {
  1: '分流',
  2: '并行'
};

export const ifSure = {
  0: '否',
  1: '是'
};

export const units = {
  0: '天',
  1: '月',
  2: '年'
};

export const decisionUserStatus = {
  '00': '全部',
  1: '启用',
  0: '停用'
};
export const decisionUserStatusArr = objectToArray(decisionUserStatus);
export const samplingMethod = { 0: '随机抽样', 1: '分层抽样' };
export const modeOptions = { 0: '模型训练', 1: '参数寻优' };
export const featureModeOptions = { 0: '特征工程', 1: '参数寻优' };

export const approveStatusCode = {
  0: '未审核',
  1: '通过',
  2: '拒绝'
};
export const approveStatusCodeArr = objectToArray(approveStatusCode);

export const checkResult = {
  0: '未审核',
  1: '审核通过',
  2: '审核拒绝'
};

export const modelType = {
  0: '未审核',
  1: '审核通过',
  2: '审核拒绝'
};

export const highRuleType = {
  hit_rule_num: '命中规则数',
  score: '评分',
  un_limit: '不限制'
};

export const valueCompareType = {
  gt: '大于',
  lt: '小于',
  eq: '等于',
  gte: '大于等于',
  lte: '小于等于'
};

export const highRiskOrEvenType = {
  0: '或',
  1: '且',
  2: '无'
};

export const WarnStatus = {
  1: '启用',
  2: '停用',
  3: '删除'
};

// 是否
export const Whether = {
  1: '是',
  0: '否'
};

export const ChargePoint = {
  200: '查得',
  300: '查询',
  400: '查询(查得+查询)'
};

export const ChargeType = {
  10: '按次数据计费',
  20: '包月',
  30: '包季度',
  40: '包年'
};
export const LimitType = {
  1: '无上限',
  2: '有上限',
  3: '超过上限按次数'
};
export const AccountType = {
  1: '正常账号',
  2: '临时账号'
};

// 批量决策状态
export const batchDecisionStatus = {
  '00': '全部',
  1: '执行中',
  2: '已完成',
  3: '异常'
};
export const batchDecisionStatusArr = objectToArray(batchDecisionStatus);

// 批量决策>结果分析>是否预警
export const decisionWarnStatus = {
  '00': '全部',
  1: '预警',
  0: '不预警'
};

// 录制状态
export const videoRecordStatus = {
  '00': '全部',
  1: '未开始',
  2: '录制中',
  3: '已完成'
};
export const videoRecordStatusArr = objectToArray(videoRecordStatus);

// 逾期状态
export const overdueStatus = {
  r7: '逾期7天以上',
  r15: '逾期15天以上',
  r30: '逾期30天以上',
  r60: '逾期60天以上',
  r90: '逾期90天以上'
};

export const routerIcon = {
  default: 'madfont-pub-ic-set',
  '/strategy/decisionPackage/library': 'madfont-pub_ic_parameter',
  '/strategy/decisionPackage/decisionModel/decisionSet': 'madfont-pub_ic_ruleset',
  '/strategy/decisionPackage/decisionFlow': 'madfont-pub_ic_decisionflow',
  '/strategy/decisionPackage/decisionModel': 'column-width',
  '/strategy/decisionPackage/decisionHitRule': 'radius-setting',
  '/strategy/decisionPackage/decisionModel/scoreCard': 'madfont-con-ic-decision-scor',
  '/strategy/decisionPackage/decisionModel/decisionTree': 'madfont-pub_ic_decisiontree',
  '/strategy/decisionPackage/decisionModel/decisionTable': 'madfont-pub_ic_decisiontable',
  '/strategy/decisionPackage/decisionModel/pmml': 'madfont-pub_ic_mo_ppml',

  '/data/externalList/manage': 'madfont-pub_ic_listna_name',
  '/data/externalList/memberin': 'madfont-pub_ic_listna_in',
  '/data/externalList/memberout': 'madfont-pub_ic_listna_out',
  '/data/three/datasource': 'madfont-pub_ic_link_store',
  '/data/three/threeConf': 'madfont-pub_ic_link_port',
  '/data/three/batchtask': 'madfont-pub_ic_mo_batch',
  '/data/three/costAllocation': 'madfont-pub_ic_link_cost',
  '/cost/configure': 'madfont-pub_ic_link_cost',
  '/data/three/dataBilling': 'madfont-pub_ic_link_calculator',
  '/cost/statistics': 'madfont-pub_ic_link_calculator',
  '/monitor/business/flowonline': 'madfont-pub_ic_decisionflow',
  '/monitor/business/risksetting': 'madfont-pub_ic_mo_report',
  '/data/indicator/raw': 'madfont-pub_ic_var_set',
  '/data/indicator/derivative': 'madfont-pub_ic_var_derivative',
  '/data/indicator/ParamMapping': 'madfont-pub_ic_mapping',
  '/data/indicator/GroupVar': 'madfont-pub_ic_var_group',
  '/ml/map/variableResult': 'madfont-pub_ic_var_portrait',
  '/data/three/valueconfig': 'madfont-line_data_programming_set',
  '/data/trajectory/vars': 'madfont-line_data_var',

  '/monitor/business/application': 'madfont-pub_ic_mo_filelook',
  '/monitor/business/appStatistics': 'madfont-line_file_page_6dot',
  '/monitor/business/decisionResult': 'madfont-pub_ic_mo_result',
  '/monitor/business/denialReason': 'madfont-pub_ic_mo_ratiochart',
  '/monitor/business/tripartiteRule': 'madfont-pub_ic_mo_hit',
  '/monitor/business/highrisklist': 'madfont-pub_ic_warning',
  '/monitor/business/champion': 'madfont-pub_ic_mo_cup',
  '/monitor/business/decisionNodePassingRate': 'madfont-percentage_adopt',
  '/monitor/business/decisionNodeRefusingLoan': 'madfont-percentage_fail',
  '/monitor/business/ruleHitRate': 'madfont-hit_percentage',
  '/monitor/business/ruleHitMonthlyDetails': 'madfont-hit_month',

  '/monitor/model/ks': 'madfont-pub_ic_mo_ks',
  '/monitor/model/gini': 'madfont-pub_ic_mo_gini',
  '/monitor/model/psi': 'madfont-pub_ic_mo_psi',
  '/monitor/threedata/three': 'madfont-pub_ic_mo_adjust',
  '/monitor/threedata/masterdata': 'madfont-line_english_ehr',
  '/monitor/threedata/paymentrequest': 'madfont-line_money_monitor',

  '/system/compute/flow': 'madfont-pub_ic_mo_modleset',
  '/system/compute/batch': 'madfont-pub_ic_mo_moremod',
  '/system/compute/job': 'madfont-pub_ic_mo_textlist',
  '/system/compute/data_conf': 'madfont-pub_ic_mo_textset',

  '/strategy/systemconf/productconf': 'madfont-pub_ic_mo_model',
  '/strategy/systemConf/DecisionIntervene': 'madfont-pub_ic_mo_access',
  '/strategy/systemConf/interveneReviewe': 'madfont-pub_ic_obtained',
  '/strategy/systemConf/Application': 'madfont-pub_ic_mo_filelook',

  '/strategy/case/eventCase': 'madfont-line_computer_file',
  '/strategy/case/decisionCase': 'madfont-line_computer_connection',
  '/strategy/batchDecision/batchDecision': 'madfont-pub_ic_mo_batch',

  '/system/userrights/agency': 'madfont-pub_ic_mo_organization',
  '/system/userrights/operator': 'madfont-pub_ic_mo_user',
  '/system/userrights/role': 'madfont-pub_ic_mo_role',
  '/system/userrights/manpower': 'madfont-line_english_ehr_data',
  '/strategy/batchDecision/videoRecorder': 'madfont-pub_ic_mo_camera',
  '/strategy/batchDecision/sandBox': 'madfont-pub_ic_mo_box',

  '/ml/map/relationship': 'share-alt',
  '/cost/account': 'madfont-pub_ic_obtained',
  '/cost/details': 'madfont-pub_ic_mo_batch',

  '/workbench/task/todo': 'madfont-line_fold_listno',
  '/workbench/task/done': 'madfont-line_fold_list',
  '/workbench/task/mine': 'madfont-line_user',

  '/datasource/accessphysics': 'madfont-line_data',
  '/datasource/storagephysics': 'madfont-line_chip',
  '/datasource/engine': 'madfont-line_arithmetic',
  '/metadata/standard-definition': 'madfont-line_file_rule',
  '/metadata/structure-definition': 'madfont-line_model_inside',
  '/metadata/table': 'madfont-line_table',
  '/metadata/monitor': 'madfont-line_file_protect',
  '/metadata/overview': 'madfont-line_file_audit',
  '/metadata/rule-manage': 'madfont-line_set_rule',
  '/metadata/data_overview': 'madfont-line_report',
  '/metadata/all_data': 'madfont-line_chart_pie',
  '/development/handle-data': 'madfont-line_cloud_server',
  '/development/resource': 'madfont-line_jigsaw',
  '/development/inquiry': 'madfont-line_search',
  '/development/release': 'madfont-line_paperplane',
  '/datasever/theme': 'madfont-line_theme',
  '/datasever/apiserver': 'madfont-line_api',

  '/ml/featureengineering': 'madfont-line_dot_inside',
  '/ml/modeltraining': 'madfont-line_model_function'
};
export const accessDatasource = {
  0: 'MYSQL',
  // "1": "POSTGRESQL",
  // "2": "HIVE",
  5: 'ORACLE',
  // "3": "GP",
  7: 'MONGO',
  // "4": "HBASE",
  // "6": "SQLSERVER",
  10: 'FTP',
  11: 'HDFS'
};
export const hivedatasourceTypes = {
  12: 'KAFKA'
};
export const loutDataType = {
  JSON: 'JSON',
  CSV: 'CSV'
};
export const outDataType = {
  JSON: 'JSON',
  XML: 'XML',
  CSV: 'CSV',
  TEXT: 'TEXT',
  LOG: 'LOG'
};
export const offlineWork = {
  4: 'SQL',
  // 5: "子流程",
  3: '数据同步',
  6: '数据质量'
};
export const onlineWork = {
  7: 'SOURCE KAFKA',
  8: 'SINK MYSQL',
  9: 'FLINK SQL'
};
export const monitorType = {
  0: '失败',
  1: '成功'
};
export const processScope = {
  0: '数据处理'
  // 1: "纬度建模"
};
export const releaseState = {
  0: '下线',
  1: '上线'
};
export const releaseMode = {
  0: '离线',
  1: '实时'
};
export const storangeDatasourceKeys = [
  { value: '0', key: 'MYSQL' },
  // "1": "POSTGRESQL",
  { value: '2', key: 'HIVE' },
  { value: '5', key: 'ORACLE' },
  // "3": "GP",
  { value: '7', key: 'MONGO' }
  // "4": "HBASE"
];
export const storangeDatasource = {
  0: 'MYSQL',
  // "1": "POSTGRESQL",
  2: 'HIVE',
  5: 'ORACLE',
  // "3": "GP",
  7: 'MONGO'
  // "4": "HBASE"
};
export const dataTypes = {
  STRING: 'STRING',
  LONG: 'LONG',
  DOUBLE: 'DOUBLE',
  BYTE: 'BYTE',
  DATE: 'DATE',
  INTEGER: 'INTEGER',
  BIG_DECMAIL: 'BIG_DECMAIL'
};
export const tableIsCreate = {
  0: '未发布',
  1: '已发布'
};
export const controlNode = {
  EXAMINATION: '上会审查',
  SIGNING: '签约',
  PAYMENT: '投放'
};
export const idNoType = {
  0: '居民身份证',
  1: '户口簿',
  2: '护照',
  3: '军官证',
  4: '士兵证',
  5: '港澳居民往来内地通行证',
  6: '台湾同胞往来大陆通行证',
  7: '临时居民身份证',
  8: '外国人居留证',
  9: '警官证',
  A: '香港身份证',
  B: '澳门身份证',
  C: '台湾身份证',
  X: '其他证件',
  C0: '统一信用代码'
};
export const noticeType = { 0: '官方公告', 1: '公司动态', 2: '新闻资讯' };
export const noticeTypeArr = objectToArray(noticeType);
export const cautionStatus = {
  '-1': '全部',
  0: '未生效',
  1: '已生效',
  2: '已结束'
};
export const caution = {
  '-1': '全部',
  0: 'N',
  1: 'Y'
};
export const enumParamWay = {
  8: '表达式',
  9: '固定值 '
};
export const enumSdkParamType = {
  2: 'sdk参数'
  // 3: "调用方入参"
};
export const isAsyncType = {
  0: '同步',
  1: '异步'
};
export const isDependentType = {
  0: '非依赖',
  1: '依赖'
};
export const isScreatType = {
  0: '不加密',
  1: '加密'
};
export const responseType = {
  0: '表达式'
  // 1: '脚本'
  // 2: "预配置"
};
export const returnType = { json: 'json', xml: 'xml' };
export const interfaceReturnType = { 1: 'json' };
export const threeComPathName = {
  GET: 'GET',
  POST: 'POST'
};
// 决策结果状态颜色
export const decisionResultColorMap = {
  A: 'green',
  ERR: 'red',
  D: 'magenta',
  W: 'orange',
  R: 'blue'
};
export const decisionResultMap = {
  A: {
    color: '#52C41A',
    text: '通过',
    icon: 'fkicon-tongyi'
  },
  ERR: {
    color: '#FF7875',
    text: '异常',
    icon: 'fkicon-yichang'
  },
  D: {
    color: '#FF4D4F',
    text: '拒绝',
    icon: 'fkicon-jujue'
  },
  W: {
    color: '#FAAD14',
    text: '提醒',
    icon: 'fkicon-tixing'
  },
  R: {
    color: '#1890ff',
    text: '转人工',
    icon: 'fkicon-tixing'
  },
  null: {
    color: '#ccc',
    text: '提交',
    icon: 'fkicon-tixing'
  }
};
export const cautionArr = objectToArray(caution);
export const groupTypeMap = {
  base_var: '衍生',
  bus_var: '业务',
  three_var: '三方',
  list_var: '名单'
};
export const sysFieldList = [
  {
    itemName: '身份证号',
    itemVal: 'idNo'
  },
  {
    itemName: '用户名',
    itemVal: 'userName'
  },
  {
    itemName: '邮箱',
    itemVal: 'email'
  },
  {
    itemName: '手机号',
    itemVal: 'mobile'
  },
  {
    itemName: '联系人手机号',
    itemVal: 'relationMobile'
  },
  {
    itemName: '证件类型',
    itemVal: 'idNoKind'
  },
  {
    itemName: '银行卡号',
    itemVal: 'cardNo'
  },
  {
    itemName: '单位名称',
    itemVal: 'companyName'
  },
  {
    itemName: '单位地址',
    itemVal: 'companyAddress'
  },
  {
    itemName: '秘钥',
    itemVal: 'interface_code'
  },
  {
    itemName: '内部用户ID',
    itemVal: 'access_user_id'
  },
  {
    itemName: '申请号',
    itemVal: 'tax_no'
  },
  {
    itemName: '纳税人识别号',
    itemVal: 'taxpayer_id'
  },
  {
    itemName: '显示数量',
    itemVal: 'showCount'
  },
  {
    itemName: '当前页',
    itemVal: 'currentPage'
  },
  {
    itemName: '纳税人识别号2',
    itemVal: 'taxpayerId'
  },
  {
    itemName: '家庭地址',
    itemVal: 'homeAddress'
  }
];
