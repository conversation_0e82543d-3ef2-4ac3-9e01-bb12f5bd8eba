// yrcard 的body样式
const cardBodyStyle = {
  padding: 0
};

// 画布节点类型枚举
const nodeTypeNames = {
  // 开始节点
  StartNoneEvent: 'CUSTOM-NODE-START',
  // 结束节点
  EndNoneEvent: 'CUSTOM-NODE-END',
  // 任务节点
  UserTask: 'CUSTOM-NODE-TASK',
  // 发起人
  Organiser: 'CUSTOM-NODE-ORGAN',
  // 排他网关
  ExclusiveGateway: 'CUSTOM-NODE-JUDGE',
  // 并行节点
  ParallelGateway: 'CUSTOM-NODE-PARALLEL',
  // 中断节点
  IntermediateThrowEvent: 'CUSTOM-NODE-INTERRUPT',
  // 决策表
  DecisionTask: 'CUSTOM-NODE-DECISION',
  // 决策网关
  DecisionGateway: 'CUSTOM-NODE-DECISIONGATEWAY',
  // 包容网关
  InclusiveGateway: 'CUSTOM-NODE-INCLUSION',
  // 事件网关
  EventGateway: 'CUSTOM-NODE-EVENTBASED',
  // 信号抛出节点
  ThrowSignalEvent: 'CUSTOM-NODE-SIGNALTHROW',
  // 信号捕获节点
  CatchSignalEvent: 'CUSTOM-NODE-SIGNALCATCH',
  // 服务节点
  ServiceTask: 'CUSTOM-NODE-SERVICE',
  // 子流程
  CallActivity: 'CUSTOM-NODE-SUBPROCESS',
  // 定时器
  CatchTimerEvent: 'CUSTOM-NODE-TIMER',
  SequenceFlow: 'CUSTOM-EDGE',
  // 画布面板
  Canvas: 'CANVAS'
};

/**
 * 自定义节点 icon 枚举
 */
const enumIconName = {
  'CUSTOM-NODE-START': 'yunrongkaishi',
  'CUSTOM-NODE-END': 'yunrongjieshu',
  'CUSTOM-NODE-TASK': 'yunrongrenwu',
  'CUSTOM-NODE-ORGAN': 'yunrongfaqiren',
  'CUSTOM-NODE-JUDGE': 'yunrongpaitawangguan',
  'CUSTOM-NODE-PARALLEL': 'yunrongbinghangwangguan',
  'CUSTOM-NODE-INTERRUPT': 'yunrongzhongduanjiedian',
  // 决策表
  'CUSTOM-NODE-DECISION': 'yunrongjuecebiao',
  // 决策网关
  'CUSTOM-NODE-DECISIONGATEWAY': 'yunrongshujuchulizhongxin',
  // 包容网关
  'CUSTOM-NODE-INCLUSION': 'yunrongbaorongwangguan',
  'CUSTOM-NODE-EVENTBASED': 'yunrongshijianwangguan1',
  'CUSTOM-NODE-SIGNALTHROW': 'yunrongxinhaofachu',
  'CUSTOM-NODE-SIGNALCATCH': 'yunrongxinhaobuhuo',
  // 服务节点
  'CUSTOM-NODE-SERVICE': 'yunrongfuwujiedian',
  'CUSTOM-NODE-SUBPROCESS': 'yunrongziliucheng',
  'CUSTOM-NODE-TIMER': 'yunrongdingshiqi'
};

/** <YRClassificationLayout.Space>
        <ResolutionMethod form={form} step={step} contractType={contractType} />
      </YRClassificationLayout.Space>
 * 节点编号 默认id前缀枚举
 */
const customNodeIdPrefix = {
  'CUSTOM-NODE-START': 'StartNoneEvent',
  'CUSTOM-NODE-END': 'EndNoneEvent',
  'CUSTOM-NODE-TASK': 'UserTask',
  'CUSTOM-NODE-ORGAN': 'Organiser',
  'CUSTOM-NODE-JUDGE': 'ExclusiveGateway',
  'CUSTOM-NODE-PARALLEL': 'ParallelGateway',
  'CUSTOM-NODE-INTERRUPT': 'IntermediateThrowEvent',
  'CUSTOM-EDGE': 'SequenceFlow',
  // 决策表
  'CUSTOM-NODE-DECISION': 'DecisionTask',
  'CUSTOM-NODE-DECISIONGATEWAY': 'DecisionGateway',
  'CUSTOM-NODE-INCLUSION': 'InclusiveGateway',
  'CUSTOM-NODE-EVENTBASED': 'EventGateway',
  'CUSTOM-NODE-SIGNALTHROW': 'ThrowSignalEvent',
  'CUSTOM-NODE-SIGNALCATCH': 'CatchSignalEvent',
  'CUSTOM-NODE-SERVICE': 'ServiceTask',
  'CUSTOM-NODE-SUBPROCESS': 'CallActivity',
  'CUSTOM-NODE-TIMER': 'CatchTimerEvent'
};

// switch开关属性
const enumSwitchStatus = {
  Y: true,
  N: false
};

// 监听器类型
const listenType = {
  1: 'java class'
};

// 事件类型
const eventType = {
  start: 'start',
  end: 'end'
};

// 全局-相同组配置，文字提示
const sameGroupTootipText =
  '互斥组下每个节点分配审批人时，不能与该组下其他节点最近一次审批人相同（同一节点多次审批时，以最后一次审批人为准）';

// 全局-互斥组配置，文字提示
const mutuallyExclusiveGroupTootipText =
  '互斥组下每个节点分配审批人时，其他节点存在审批记录时，' +
  '按每个节点的最后一次审批人中，启用状态的用户、最新审批时间的审批人；无审批人时不执行此规则；';

// 审批结束通知，文字提示
const approvalEndNoticeTootipText =
  '审批结束时（含通过、拒绝、取消），需要通知的对应系统处理的审批结果、业务信息，由对应系统进行后续处理';

// 工单业务所属变更通知，文字提示
const businessChangeNoticeTooltipText =
  '工单业务所属变更时，需要通知的对应系统处理的变更结果，由对应系统进行后续处理；' +
  '相关操作：指派时的强制业务移交、审批提交时自动更新业务所属场景';

// 业务变更通知，文字提示
const infoChangeNoticeTooltipText =
  '工单信息变更时，需要通知的对应系统处理的变更结果，由对应系统进行后续处理；' +
  '相关操作：提交时检查（检查进件的影像信息、风控信息、业务信息是否存在修改未通知的数据）';

// 优先规则-业务所属人
const businessOwnerTooltipText =
  '应用场景：该节点是否为业务所属人节点，开启表示是，关闭表示非业务所属人节点；\n影响：系统派件、人工指派、强制指派';

// 优先规则-退回件
const returnItemTooltipText = '应用场景：进件为退补件、驳回件、退回件时派件规则；';

export {
  cardBodyStyle,
  nodeTypeNames,
  sameGroupTootipText,
  mutuallyExclusiveGroupTootipText,
  approvalEndNoticeTootipText,
  businessChangeNoticeTooltipText,
  infoChangeNoticeTooltipText,
  businessOwnerTooltipText,
  returnItemTooltipText,
  enumSwitchStatus,
  enumIconName,
  listenType,
  eventType,
  customNodeIdPrefix
};
