/*
 * @Author: 伍晶晶
 * @Description: 节假日context
 * @Date: 2023-03-22 09:54:02
 * @LastEditors: 伍晶晶
 * @LastEditTime: 2023-03-29 17:07:40
 */

import { Result } from 'ahooks/lib/useRequest/src/types';
import React from 'react';
import { IData, TreeNode } from './Interface';

type ProviderValueType = {
  antdTreeProps: Result<IData[], any>;
} & {
  selectedState: [Partial<TreeNode>, React.Dispatch<React.SetStateAction<TreeNode>>];
};
const AntdContext = React.createContext<ProviderValueType>(null as unknown as ProviderValueType);

export const AntdContextProvider: React.FC<React.PropsWithChildren<{ value: ProviderValueType }>> = (props) => {
  return <AntdContext.Provider {...props} />;
};

export const useAntd = () => React.useContext(AntdContext);
