/*
 * @Author: DJscript
 * @Date: 2023-02-20 14:26:36
 * @LastEditTime: 2023-03-02 09:59:53
 * @FilePath: /kylin-admin/src/models/user.ts
 * @Description: ...
 */
import { formatter } from '@/utils/micro-utils';
import part from '@/routes/part';
import { filterByAuth } from '@/utils/routerFilter';
import { userQueryMyPermit } from '@/services/common';
import { joinRoutePath } from '@/utils/utils';
import packageInfo from '../../package.json';

const partData = joinRoutePath(part);

export default {
  state: {
    menuData: [] // 菜单数据
  },
  effects: (dispatch) => ({
    async getMenuData() {
      let data = Array.isArray(partData) ? partData : [];
      data = formatter(partData);
      // 获取当前登录用户的所有权限数组, 过滤出有权限的路由
      if (sessionStorage.allAuths) {
        // filterByAuth(data, JSON.parse(sessionStorage.allAuths));
        dispatch.user.setMenu(data);
      } else {
        const response = await userQueryMyPermit();
        const permitList = response.data.allPermitUriMap?.[packageInfo.businessSign] || [];
        const permits = permitList.map((p) => {
          if (/\/hsjry.*(\/v\d{1,2}.*)/g.test(p)) {
            return p.replace(/\/hsjry.*(\/v\d{1,2}.*)/g, '$1');
          }
          const moduleKeys = Object.keys(MODULES).filter((i) => p.indexOf(MODULES[i].prefix) > -1) || [];
          return moduleKeys[0] ? p.replace(MODULES[moduleKeys[0]].prefix, '') : p;
        });
        sessionStorage.allAuths = JSON.stringify(permits);
        // filterByAuth(data, permits);
        dispatch.user.setMenu(data);
      }
    }
  }),
  reducers: {
    setMenu(prevState, payload) {
      prevState.menuData = payload;
    },
    clearAll() {
      return {
        menuData: []
      };
    }
  }
};
