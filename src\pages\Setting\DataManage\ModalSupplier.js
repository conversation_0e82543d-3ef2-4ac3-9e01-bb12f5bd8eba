/*
 * @Description: file content
 * @Author: zy
 * @Date: 2020-07-20 17:10:13
 * @LastEditors: your name
 * @LastEditTime: 2020-11-13 10:26:52
 */

import React from 'react';
import { YRModal, YRForm, YRInput } from 'yrantd';
import RegexRules from '@/utils/regex';

const FormItem = YRForm.Item;
const { TextArea } = YRInput;
/**
 * 新增编辑
 */
const ModalSupplier = (props) => {
  const { handleCancel, visible, initValues, type, handleOk } = props;
  const [form] = YRForm.useForm();

  // 提交方法
  const submit = () => {
    form.validateFields().then((values) => {
      const params = values;
      if (initValues) {
        params.id = initValues.id;
      }
      handleOk(type, params);
    });
  };

  const generateFormFields = () => {
    return (
      <YRForm layout="vertical" form={form}>
        <FormItem
          label="厂商名称"
          name={'comName'}
          initialValue={initValues?.comName || ''}
          rules={[
            { required: true, message: '请输入厂商名称' },
            { max: 20, message: '长度不能超过20！' },
            { message: '请勿输入空格！', pattern: RegexRules.unSpace }
          ]}
        >
          <YRInput placeholder="请输入" disabled={type !== 'add'} />
        </FormItem>
        <FormItem
          label="厂商CODE"
          name={'comCode'}
          initialValue={initValues?.comCode || ''}
          rules={[
            {
              required: true,
              message: '请输入英文字母、数字或下划线',
              pattern: RegexRules.code1
            },
            { max: 60, message: '长度不能超过60！' }
          ]}
        >
          <YRInput placeholder="请输入" disabled={type !== 'add'} />
        </FormItem>
        <FormItem
          label="描述"
          name={'description'}
          initialValue={initValues?.description || ''}
          rules={[{ max: 200, message: '长度不能超过200!' }]}
        >
          <TextArea rows={4} placeholder="请输入" />
        </FormItem>
      </YRForm>
    );
  };
  const title = type === 'add' ? '新增供应商' : '编辑供应商';

  return (
    <YRModal title={title} visible={visible} onOk={submit} onCancel={handleCancel} maskClosable={false} width={400}>
      <div>{type && generateFormFields()}</div>
    </YRModal>
  );
};
export default ModalSupplier;
