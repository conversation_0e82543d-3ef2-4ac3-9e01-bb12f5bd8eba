import { createStore } from '@ice/store';
import login from './models/login';
import global from './models/global';
import micro from './models/micro';
import user from './models/user';
import layout from './models/layout';
import flow from './models/flow';
import organ from './models/organ';
import rule from './models/rule';

const store = createStore({
  login,
  global,
  micro,
  user,
  layout,
  flow,
  organ,
  rule
});

export default store;
