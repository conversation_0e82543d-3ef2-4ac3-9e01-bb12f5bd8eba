/**
 * @Author: AI Assistant
 * @Description: 批量授权管理相关接口
 * @Date: 2024-12-19
 */

import { MODULES, request, SCENES } from '@yr/util';

/**
 * 分页查询批量授权导入信息列表
 * @param param 查询参数
 */
export function queryBatchAuthInfoPage(param?: any) {
  return request('/authBaseInfoBatchQueryController/queryInfoPage', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '查询批量授权信息列表'
  });
}

/**
 * 批量授权导入
 * @param param 导入参数
 */
export function batchImportAuthInfo(param?: any) {
  return request('/authBaseInfoBatchController/batchImport', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.apply,
    serviceName: '批量授权导入'
  });
}

/**
 * 批量授权提交复核
 * @param param 提交参数
 */
export function submitBatchAuthForReview(param?: any) {
  return request('/authBaseInfoBatchController/submitForReview', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.apply,
    serviceName: '批量授权提交复核'
  });
}

/**
 * 删除批量授权
 * @param param 删除参数
 */
export function deleteBatchAuthInfo(param?: any) {
  return request('/authBaseInfoBatchController/delete', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.apply,
    serviceName: '删除批量授权'
  });
}

/**
 * 批量失效授权
 * @param param 失效参数
 */
export function invalidateBatchAuthInfo(param?: any) {
  return request('/authBaseInfoBatchController/invalidate', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.apply,
    serviceName: '批量失效授权'
  });
}

/**
 * 下载批量授权导入模板
 * @param param 下载参数
 */
export function downloadBatchAuthTemplate(param?: any) {
  return request('/authBaseInfoBatchController/downloadTemplate', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '下载批量授权导入模板'
  });
}

/**
 * 查询待复核批量授权列表
 * @param param 查询参数
 */
export function queryPendingBatchAuthList(param?: any) {
  return request('/authBaseInfoBatchQueryController/queryPendingPage', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '查询待复核批量授权列表'
  });
}

/**
 * 批量授权复核通过
 * @param param 复核参数
 */
export function approveBatchAuth(param?: any) {
  return request('/authBaseInfoBatchController/approve', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.apply,
    serviceName: '批量授权复核通过'
  });
}

/**
 * 批量授权复核退回
 * @param param 复核参数
 */
export function rejectBatchAuth(param?: any) {
  return request('/authBaseInfoBatchController/reject', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.apply,
    serviceName: '批量授权复核退回'
  });
}

/**
 * 查询批量授权详情
 * @param param 查询参数
 */
export function queryBatchAuthDetail(param?: any) {
  return request('/authBaseInfoBatchQueryController/queryDetail', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '查询批量授权详情'
  });
}

/**
 * 查询已复核单笔授权列表
 * @param param 查询参数
 */
export function queryReviewedSingleAuthList(param?: any) {
  return request('/authBaseInfoQueryController/queryReviewedPage', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '查询已复核单笔授权列表'
  });
}

/**
 * 查询单笔授权详情
 * @param param 查询参数
 */
export function querySingleAuthDetail(param?: any) {
  return request('/authBaseInfoQueryController/queryDetail', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '查询单笔授权详情'
  });
}

/**
 * 查询机构树数据
 * @param param 查询参数
 */
export function queryAuthCatalogOrgTree(param?: any) {
  return request('/authCatalogQuery/queryAuthCatalogOrgTree', {
    param,
    method: 'POST',
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '查询机构树数据',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  });
}
