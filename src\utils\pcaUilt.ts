/**
 * @Author: wangyw26123
 * @Description: 用于操作省市区的相关方法
 * @Date: Created in 2023-01-04 20:31:56
 * @Modifed By:
 */

import pcaData from 'yrantd/lib/pcadata';

interface TreeNode {
  code: string;
  name: string;
  children?: TreeNode[];
}

/**
 * 操作省市区的相关方法
 */
class PcaUilt {
  private pcaTreeData: TreeNode[];
  private fieldNames: { children: string; label: string; value: string };

  constructor(data) {
    this.pcaTreeData = data;
    this.fieldNames = {
      label: 'name',
      value: 'code',
      children: 'children'
    };
  }

  /**
   * 根据key值获取全部省市区code(补全操作，比如根据区id，获取省和市id)
   * @param key
   */
  public getCodes(key: string): string[] {
    const [, pca] = this.getNodeByKey(key);

    if (pca.length) {
      return pca.map((item) => item.code);
    }

    return [];
  }

  /**
   * 根据key值获取全部省市区text(补全操作，比如根据区id，获取省和市text)
   * @param key
   */
  public getText(key: string): string[] {
    const [, pca] = this.getNodeByKey(key);

    if (pca.length) {
      return pca.map((item) => item.name);
    }

    return [];
  }

  /**
   * 根据key值获取text
   * @param key
   */
  public getValue(key: string): string {
    const [node] = this.getNodeByKey(key);

    return node ? node.name : '';
  }

  /**
   * 根据codes列表获取相对应的text
   * @param key
   */
  public getPca(codes: string[]): string[] {
    return codes.map((key) => {
      const [node] = this.getNodeByKey(key);

      return node?.name ?? '';
    });
  }

  /**
   * 根据key值获取树节点
   * @param key
   */
  private getNodeByKey(key: string): [TreeNode | null, TreeNode[]] {
    const { pcaTreeData, fieldNames } = this;
    let result = null;
    let pca: TreeNode[] = [];
    const loop = (data, k, _pca) => {
      for (let i = 0; i < data.length; i++) {
        if (data[i][fieldNames.value] === k) {
          result = data[i];
          pca = [..._pca, data[i]];
          return;
        } else if (data[i][fieldNames.children]) {
          loop(data[i][fieldNames.children], k, [..._pca, data[i]]);
        }
      }
    };

    loop(pcaTreeData, key, []);

    return [result, pca];
  }
}

export default new PcaUilt(pcaData);
