/*
 * @Author: liaokt
 * @E-mail: <EMAIL>
 * @Date: 2023-03-10 10:20:38
 * @Description:
 */
/**
 * @Author: wangyw26123
 * @Description: 节点属性面板tabs集合
 * @Date: Created in 2022-04-25 16:18:51
 * @Modifed By:
 */

import React, { useEffect, useState } from 'react';
import { YRTabs, YRUtils } from 'yrantd';
import { checkAuth } from '@/utils/utils';
import BaseInfoIndex from './BaseInfo/BaseInfoIndex';
import HeightLevelIndex from './HeighLevel';

const { TabPane } = YRTabs;

const styles = {
  height: 'calc(100vh - 200px)',
  overflow: 'auto'
};

let nodeTabPanels = [
  {
    title: '基本信息',
    key: 'baseInfo',
    check: null,
    component(props) {
      return (
        <TabPane forceRender tab={this.title} key={this.key} style={{ margin: '-16px 0 0 0' }}>
          <div style={styles}>
            <BaseInfoIndex {...props} />
          </div>
        </TabPane>
      );
    }
  },
  {
    title: '高级配置',
    key: 'highLevel',
    check: null,
    component(props) {
      return (
        <TabPane forceRender tab={this.title} key={this.key} style={{ margin: '-16px 0 0 0' }}>
          <div style={styles}>
            <HeightLevelIndex {...props} />
          </div>
        </TabPane>
      );
    }
  }
];

nodeTabPanels = nodeTabPanels.filter((item) => (item.check ? checkAuth(item.check) : true));

const FormIndex = (props) => {
  const [activeKey, setActiveKey] = useState();
  const {
    targetData: { nodeTabs = [], id },
    form
  } = props;

  useEffect(() => {
    setActiveKey('baseInfo');
  }, [id]);
  const newNodeTabs = nodeTabPanels.filter((item) => nodeTabs.includes(item.key));
  const renderNodeTab = newNodeTabs.map((com) => com.component(props)) || [];

  return (
    <YRTabs size="small" defaultActiveKey="baseInfo" activeKey={activeKey} onChange={(e) => setActiveKey(e)}>
      {renderNodeTab}
    </YRTabs>
  );
};

export default FormIndex;
