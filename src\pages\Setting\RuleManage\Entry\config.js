export const operatorItem = [
  {
    label: '小于',
    name: '<'
  },
  {
    label: '小于或等于',
    name: '<='
  },
  {
    label: '等于',
    name: '=='
  },
  {
    label: '不等于',
    name: '!='
  },
  {
    label: '大于',
    name: '>'
  },
  {
    label: '大于或等于',
    name: '>='
  },
  {
    label: '在集合中',
    name: '在集合中'
  },
  {
    label: '不在集合中',
    name: '不在集合中'
  },
  {
    label: '为空',
    name: '为空'
  },
  {
    label: '不为空',
    name: '不为空'
  },
  {
    label: '包含',
    name: '包含'
  },
  {
    label: '不包含',
    name: '不包含'
  }
];
export const operatorNameItem = {
  '<': {
    label: '小于',
    name: 'LessThen',
    symbol: '<',
    desc: ''
  },
  '<=': {
    label: '小于或等于',
    name: 'LessThenEquals',
    symbol: '<=',
    desc: ''
  },
  '==': {
    label: '等于',
    name: 'Equals',
    symbol: '==',
    desc: ''
  },
  '!=': {
    label: '不等于',
    name: 'NotEquals',
    symbol: '!=',
    desc: ''
  },
  '>': {
    label: '大于',
    name: 'GreaterThen',
    symbol: '>',
    desc: ''
  },
  '>=': {
    label: '大于或等于',
    name: 'GreaterThenEquals',
    symbol: '>=',
    desc: ''
  },
  在集合中: {
    label: '在集合中',
    name: 'In',
    symbol: '在集合中',
    desc: '之中'
  },
  不在集合中: {
    label: '不在集合中',
    name: 'NotIn',
    symbol: '不在集合中',
    desc: '之中'
  },
  为空: {
    label: '为空',
    name: 'Null',
    symbol: '为空',
    desc: ''
  },
  不为空: {
    label: '不为空',
    name: 'NotNull',
    symbol: '不为空',
    desc: ''
  },
  包含: {
    label: '包含',
    name: 'Contain',
    symbol: '包含',
    desc: ''
  },
  不包含: {
    label: '不包含',
    name: 'NotContain',
    symbol: '不包含',
    desc: ''
  }
};
export const arithmeticMenuData = [
  {
    label: '+',
    name: 'Add'
  },
  {
    label: '-',
    name: 'Sub'
  },
  {
    label: 'x',
    name: 'Mul'
  },
  {
    label: '÷',
    name: 'Div'
  },
  {
    label: '%',
    name: 'Mod'
  },
  {
    label: '删除',
    name: 'Delete'
  }
];
export const arithmeticMenuData2 = [
  {
    label: '值',
    name: 'Value'
  },
  {
    label: '括号',
    name: 'Paren'
  }
];
export const arithmeticMap = {
  Add: '+',
  Sub: '-',
  Mul: 'x',
  Div: '÷',
  Mod: '%',
  Delete: '删除'
};
// 值类型
export const inputValueMenu = [
  {
    label: '输入值',
    name: 'Input'
  },
  {
    label: '选择常量',
    name: 'Constant'
  },
  {
    label: '选择参数',
    name: 'Parameter'
  }
];
export const noValueItem = ['Null', 'NotNull'];
export const defaultLeft = 69;
export const defaultHeight = 32;
export const defaultSpace = 85;
export const defaultTop = 16;
export const defaultWidth = 72;
export const defaultWidth2 = defaultWidth / 2;
export const defaultTopSpace = 0;
