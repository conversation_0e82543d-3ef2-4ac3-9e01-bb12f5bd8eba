{"name": "kylin-admin", "businessSign": "kylin-admin", "defaultOpen": "NORMAL", "envType": "LOCAL", "status": "ONLINE", "appEntry": "//127.0.0.1:8111", "version": "4.0.0", "description": "后台配置", "scripts": {"start": "cross-env envType=LOCAL publicPath=kylin-admin icejs start --mode local  --port 8111  --config build.config.js", "start:no-mock": "cross-env envType=LOCAL publicPath=/child/kylin-admin/ icejs start --mode local  --disable-mock  --port 8111  --config build.config.js", "start:permit-sql": "cross-env startMode=PERMIT_SQL envType=LOCAL publicPath=/child/kylin-admin/ icejs start --mode local  --disable-mock  --port 8111 --config build.config.js", "build": "cross-env envType=LOCAL publicPath=/child/kylin-admin/ icejs build --config build.config.js", "build:product": "cross-env gitBranch=$branch envType=PRODUCT publicPath=/child/kylin-admin/  icejs build  --config build.config.js", "build:project": "cross-env gitBranch=$branch envType=PROJECT publicPath=/child/kylin-admin/ icejs build  --config build.config.js", "build:micro": "icejs build --mode micro --config build.config.js", "lint": "npm run eslint && npm run stylelint", "eslint": "eslint --cache --ext .js,.jsx ./", "eslint:fix": "npm run eslint -- --fix", "stylelint": "stylelint \"**/*.{css,scss,less}\"", "eslint:lint-staged": "eslint --cache --ext .js,.jsx,.ts,.tsx", "prettier:lint-staged": "prettier --write", "stylelint:lint-staged": "stylelint", "prepare": "husky install"}, "dependencies": {"@ant-design/colors": "^7.0.0", "@ant-design/icons": "^4.6.2", "@babel/core": "^7.19.3", "@ice/stark-app": "^1.4.1", "@ice/stark-data": "^0.1.2", "@types/styled-components": "^5.1.26", "@yr/multi-view-table": "0.0.41", "@yr/pro-layout": "^0.1.9", "@yr/util": "^1.1.39", "@yr/xflow": "^1.0.7", "ahooks": "^3.7.4", "build-plugin-antd": "^0.1.1", "build-plugin-icestark": "^2.0.7", "classnames": "^2.2.6", "cross-env": "^7.0.3", "crypto-browserify": "^3.12.0", "ice.js": "^2.6.5", "less": "^4.2.0", "lodash-decorators": "^6.0.1", "lodash-es": "^4.17.21", "moment": "^2.24.0", "node-polyfill-webpack-plugin": "^2.0.1", "omit.js": "^2.0.2", "path-to-regexp": "^6.2.0", "postcss": "^8.4.16", "postcss-nested": "^5.0.6", "postcss-preset-env": "^7.8.1", "query-string": "^7.0.0", "react": "^16.14.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^16.14.0", "sm-crypto": "^0.2.7", "styled-components": "^5.3.8", "virtualizedtableforantd4": "^1.3.0", "webpack": "^5.74.0", "yr-loan-antd": "^1.1.2", "yrantd": "^4.2.99"}, "devDependencies": {"@babel/plugin-proposal-decorators": "^7.19.1", "@stagewise/toolbar-react": "^0.4.4", "@types/react": "^18.0.26", "@types/react-dom": "^16.9.17", "@yr/spec": "^1.2.2", "eslint": "7.32.0", "husky": "^7.0.4", "lint-staged": "12.3.8", "prettier": "2.6.2", "re-resizable": "6.9.0", "stylelint": "13.2.0"}, "engines": {"node": ">=8.0.0"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx,less,css}": ["npm run prettier:lint-staged"], "src/**/*.{js,jsx,ts,tsx}": ["npm run eslint:lint-staged"], "src/**/*.{less,css}": ["npm run stylelint:lint-staged"]}, "private": true, "originTemplate": "micro-appV2"}