/*
 * @Author: liaokt
 * @Description: 转授权管理
 * @Date: 2024-10-16 09:21:54
 * @LastEditors: liaokt
 * @LastEditTime: 2024-10-25 11:01:39
 */
import {
  YRButton,
  YRFlexPageLayout,
  YREmpty,
  YRTable,
  YRTabs,
  YREasyUseModal,
  YRForm,
  YRMessage,
  YRClassificationLayout,
  YRSpace
} from 'yrantd';
import { columns, tabs, formItemList } from './useIndex';
import { M0105 } from '@permit/organ';
import { useRequest } from 'ahooks';
import { useDict } from '@yr/util';
import { CopyOutlined } from '@ant-design/icons';
import React, { useEffect, useRef, useState } from 'react';
import OrganTree from './components/OrganTree';
import RoleTree from './components/RoleTree';
import RulesTransferModal from './components/RulesTransferModal';

const AuthPage = () => {
  useDict(['AUTH_STATUS', 'AUTH_CATEGORY', 'USER_TYPE', 'AUTH_METHOD', 'BIZ_LINE']);

  const {
    queryFirstLvlOrgList,
    queryAuthInfoRuleList,
    queryTransferAuthInfoRuleList,
    queryTransferAuthRoleList,
    submitAuthRuleInfo
  } = M0105.interfaces;

  const [form] = YRForm.useForm();
  const [orgForm] = YRForm.useForm();

  const [activeKey, setActiveKey] = useState('001');

  const [organData, setOrganData] = useState([]);
  const [roleData, setRoleData] = useState([]);
  const [rulesData, setRulesData] = useState([]);
  const [orgRulesData, setOrgRulesData] = useState([]);

  // 选中机构节点的数据
  const [currentOrganParams, setCurrentOrganParams] = useState<{ orgId: string; authCategory: string }>({
    orgId: '',
    authCategory: ''
  });
  // 选中的角色的编号
  const [currentRole, setCurrentRole] = useState({ roleId: '', authNo: '', authCatalogRoleId: '' });
  const currentRoleId = useRef('');

  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [selectedRows, setSelectedRows] = useState<Array<{ authNo: string; authCategory: string; authBaseId: string }>>([]);
  const containerRef = useRef(null);

  const [listHeight, setListHeight] = useState(window.innerHeight);

  useEffect(() => {
    const handleResize = () => {
      setListHeight(window.innerHeight); // 更新列表高度为当前窗口高度
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // 查询机构树
  const { loading: queryOrganTreeLoading, refresh: queryOrgan } = useRequest(queryFirstLvlOrgList, {
    onSuccess: (res: any) => {
      if (res?.errorMessage === null) {
        const { data } = res as { data: never };
        setOrganData([data]);
      }
    },
    defaultParams: [
      {
        queryAuthFlag: '1'
      }
    ]
  });

  // 查询角色树
  const {
    loading: queryAuthRoleListLoading,
    run: queryRelationRole,
    refresh: refreshRoleTree
  } = useRequest(queryTransferAuthRoleList, {
    onSuccess: (res: any) => {
      if (res?.errorMessage === null) {
        const { data } = res;
        setRoleData(data || []);
      }
    },
    manual: true
  });

  // 查询角色关联授权规则
  const {
    loading: queryAuthListLoading,
    run: queryAuthList,
    refresh: reQueryAuthList,
    params
  } = useRequest(queryTransferAuthInfoRuleList, {
    onSuccess: (res: any) => {
      if (res?.errorMessage === null) {
        const { data } = res as { data: {list: never} };
        if (data.list) {
          setRulesData(data.list);
        } else {
          setRulesData([]);
        }
      }
    },
    manual: true
  });

  // 查询机构关联基础授权
  const {
    loading: queryOrgRulesListLoading,
    run: queryOrgRulesList,
    params: orgRulesParams
  } = useRequest(queryAuthInfoRuleList, {
    onSuccess: (res: any) => {
      if (res?.errorMessage === null) {
        const { data } = res as { data: {list: never} };
        if (data.list) {
          setOrgRulesData(data.list);
        } else {
          setOrgRulesData([]);
        }
      }
    },
    manual: true
  });

  // 提交复核成功
  const { run: submitRun, loading: submitLoading } = useRequest(submitAuthRuleInfo, {
    onSuccess: (res: any) => {
      if (res?.errorMessage === null) {
        YRMessage.success('提交复核成功', 0.5, () => {
          reQueryAuthList();
        });
      }
    },
    manual: true
  });

  // 查询授权关联角色
  useEffect(() => {
    const { orgId, authCategory } = currentOrganParams;
    setRoleData([]);
    setCurrentRole({ roleId: '', authNo: '', authCatalogRoleId: '' });
    currentRoleId.current = '';
    setRulesData([]);
    if (orgId && authCategory) {
      queryRelationRole(currentOrganParams);
      // queryOrgRulesList({ authCategory, orgId,  bizLine: activeKey, authCatalogRoleId: currentRole.authCatalogRoleId });
      // queryAuthList({ authCategory, orgId, transferAuthFlag: '1', bizLine: activeKey });
    }
  }, [currentOrganParams]);

  // 查询角色关联授权规则
  useEffect(() => {
    const { orgId, authCategory } = currentOrganParams;

    if (currentRoleId.current) {
      queryOrgRulesList({ authCategory, orgId, authCatalogRoleId: currentRole.authCatalogRoleId });
      queryAuthList({ roleId: currentRoleId.current, authCategory, orgId, transferAuthFlag: '1', authCatalogRoleId: currentRole.authCatalogRoleId });
    }
  }, [currentRoleId.current, currentOrganParams]);

  const handleSearch = (type: 'base' | 'transfer') => {
    if (!currentRole.authCatalogRoleId) {
      YRMessage.warning('请先选择角色');
      return;
    }
    if (type === 'transfer') {
      form.validateFields().then((res: any) => {
        queryAuthList({ ...params[0], ...res, transferAuthFlag: '1' });
      });
    } else {
      orgForm.validateFields().then((res: any) => {
        queryOrgRulesList({ ...orgRulesParams[0], ...res, authCatalogRoleId: currentRole.authCatalogRoleId });
      });
    }
  };

  const rowSelection: {
    type: 'checkbox' | 'radio';
    onChange: (selectedRowKeys: any, rows: any) => void;
    selectedRowKeys: string[];
  } = {
    type: 'radio',
    onChange: (selectedRowKeys, rows) => {
      setSelectedKeys(selectedRowKeys);
      setSelectedRows(rows);
    },
    selectedRowKeys: selectedKeys
  };

  const baseOperationRender = (
    <YRButton.Space>
      <YRButton
        disabled={selectedKeys.length === 0}
        icon={<CopyOutlined />}
        type="primary"
        onClick={() => {
          const { authNo, authCategory, authBaseId } = selectedRows[0];
          YREasyUseModal.show(RulesTransferModal, {
            mode: 'edit',
            authNo,
            authCategory,
            authBaseId,
            okCallback: () => {
              reQueryAuthList();
              refreshRoleTree();
              queryOrgan();
            }
          });
        }}
      >
        新增转授权
      </YRButton>
    </YRButton.Space>
  );

  return (
    <YRFlexPageLayout ref={containerRef}>
      <YRFlexPageLayout.Sider title="机构列表">
        <OrganTree
          okCallback={refreshRoleTree}
          height={listHeight}
          loading={queryOrganTreeLoading}
          data={organData}
          setData={setCurrentOrganParams}
        />
      </YRFlexPageLayout.Sider>
      <YRFlexPageLayout.Main contentNoPadding>
        <YRFlexPageLayout>
          <YRFlexPageLayout.Sider
            title="角色列表"
            // extra={
            //   <YRButton
            //     type="text"
            //     onClick={() => {
            //       YREasyUseModal.show(AddRoleModal, {
            //         params: currentOrganParams,
            //         okCallback: refreshRoleTree
            //       });
            //     }}
            //   >
            //     新增授权角色
            //   </YRButton>
            // }
          >
            <RoleTree
              okCallback={refreshRoleTree}
              data={roleData}
              loading={queryAuthRoleListLoading}
              setData={(e) => {
                const { roleId } = e;
                currentRoleId.current = roleId;
                setCurrentRole(e);
              }}
            />
          </YRFlexPageLayout.Sider>
          <YRFlexPageLayout.Main>
            {currentOrganParams.orgId ? (
              <>
                <YRTabs
                  defaultActiveKey={activeKey}
                  type="card"
                  onChange={(e) => {
                    setActiveKey(e);
                    const { orgId, authCategory } = currentOrganParams;

                    queryOrgRulesList({ authCategory, orgId, authCatalogRoleId: currentRole.authCatalogRoleId });
                    if (!currentRole.roleId) {
                      queryAuthList({ authCategory, orgId, transferAuthFlag: '1', authCatalogRoleId: currentRole.authCatalogRoleId });
                    } else {
                      queryAuthList({
                        roleId: currentRoleId.current,
                        authCategory,
                        orgId,
                        transferAuthFlag: '1',
                        authCatalogRoleId: currentRole.authCatalogRoleId
                      });
                    }
                  }}
                  items={tabs!.map((item) => {
                    const { tab, key } = item;
                    return {
                      label: tab,
                      key,
                      children: null
                    };
                  })}
                />
                <YRSpace direction="vertical" size={'large'}>
                  <div style={{ height: (listHeight ?? 600) / 3 }}>
                    <YRClassificationLayout title="机构授权" />
                    <YRTable
                      key={'authBaseId'}
                      rowKey={'authBaseId'}
                      form={orgForm}
                      rowSelection={rowSelection}
                      dataSource={orgRulesData}
                      handleSearch={() => {
                        handleSearch('base');
                      }}
                      loading={queryOrgRulesListLoading}
                      columns={columns(reQueryAuthList, submitRun, submitLoading, 'detail')}
                      formItemList={formItemList}
                      operationRender={baseOperationRender}
                      pagination={false}
                      autoScrollHeight
                    />
                  </div>
                  <div style={{ marginTop: '20px' }}>
                    <YRClassificationLayout title="转授权" />
                    <YRTable
                      rowKey={'authBaseId'}
                      form={form}
                      dataSource={rulesData}
                      handleSearch={() => {
                        handleSearch('transfer');
                      }}
                      loading={queryAuthListLoading}
                      columns={columns(reQueryAuthList, submitRun, submitLoading, 'add')}
                      formItemList={formItemList}
                      //   operationRender={operationRender}
                      pagination={false}
                      autoScrollHeight
                    />
                  </div>
                </YRSpace>
              </>
            ) : (
              <YREmpty style={{ marginTop: '110px' }} />
            )}
          </YRFlexPageLayout.Main>
        </YRFlexPageLayout>
      </YRFlexPageLayout.Main>
    </YRFlexPageLayout>
  );
};

export default AuthPage;
