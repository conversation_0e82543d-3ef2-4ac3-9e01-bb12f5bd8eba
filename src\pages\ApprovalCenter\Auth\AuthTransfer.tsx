/*
 * @Author: liaokt
 * @Description: 转授权审批
 * @Date: 2023-06-18 16:12:23
 * @LastEditors: liaokt
 * @LastEditTime: 2023-06-26 11:06:33
 */
import PostOpinion from '@/pages/ApprovalCenter/components/PostOpinion';
import { queryAllUserList } from '@/services/common';
import { approveSubmit } from '@/services/auth';
import { router } from '@/utils/utils';
import { useDict } from '@yr/util';
import { useDebounceEffect, useRequest } from 'ahooks';
import { useLocation } from 'ice';
import React, { useEffect, useState } from 'react';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import {
  YRFullPageLayout,
  YRButton,
  YRSplitScreenLayout,
  YRAnchor,
  YRCard,
  YRForm,
  YRMessage,
  YRClassificationLayout,
  YRCol,
  YRDatePicker,
  YRDict,
  YREmpty,
  YRInput,
  YRList,
  YRRow,
  YRSpace,
  YRText
} from 'yrantd';
import Link from 'yrantd/lib/yr-anchor/Link';
import { EnumPlacement } from 'yrantd/lib/yr-split-screen-layout';
import { queryAuthInfoByAuthNo } from '@/services/auth'

const PermissionTransfer = () => {
  useDict(['CUSTOMER_PERMIT', 'CERTIFICATE_KIND', 'BIZ_LINE']);
  const location = useLocation();
  const { query } = location;
  const [form] = YRForm.useForm();
  const [splitScreenActiveKeys, setSplitScreenActiveKeys] = useState(['left-1', 'right-1']);
  const [recipientList, setRecipientList] = useState([]);

  // 详情
  const [detailInfo, setDetailInfo] = useState<any>({});

  // 是否已办
  const { dealPolicy } = query;

  // 判断是否有已选客户的管户权
  useEffect(() => {
    queryAllUserList().then((res) => {
      if (res?.data && res?.data.length > 0) {
        setRecipientList(res?.data || []);
      }
    });
  }, []);

  /** 详情查询接口 */
  const { loading: detailLoading, run: detailRequest } = useRequest(queryAuthInfoByAuthNo, {
    manual: true,
    onSuccess: (result) => {
      const { data, errorMessage } = result;
      debugger
      if (errorMessage === null) {
        setDetailInfo(data);
        form.setFieldsValue({ ...data });
      }
    }
  });

  /** 审批提交接口 */
  const { loading: approveSubmitLoading, run: approveSubmitRequest } = useRequest(approveSubmit, {
    manual: true,
    onSuccess: (result) => {
      const { data, errorMessage } = result;
      if (errorMessage === null) {
        YRMessage.success('提交成功', 1, () => {
          window.close();
          if (window.opener && window.opener.location) {
            window.opener.location.reload();
          }
        });
      }
    }
  });

  // 保存事件
  const onSave = () => {
    const params: any = form.getFieldsValue();
    const { approve, remark } = params;
    if (!approve) {
      YRMessage.info('请填写审批信息');
    } else {
      const { instanceId, taskId, taskName, businessKey } = query;
      approveSubmitRequest({ opinion: approve, opinionDesc: remark, authBaseId: businessKey, instanceId, taskId, taskName });
    }
  };

  useDebounceEffect(() => {
    const { businessKey } = query;
    businessKey && detailRequest({ authBaseId: businessKey });
  }, [query]);

  return (
    <YRFullPageLayout
      left={{
        title: '转授权审批',
        goBack: () => router.goBack()
      }}
      loading={detailLoading}
      right={
        dealPolicy === 'W' && (
          <YRButton
            type="primary"
            onClick={() => {
              onSave();
            }}
            loading={approveSubmitLoading}
          >
            提交
          </YRButton>
        )
      }
    >
      <YRSplitScreenLayout
        bodyStyle={{ paddingTop: 0 }}
        initialState={['left-1', 'right-1']}
        activeKeys={splitScreenActiveKeys}
        onSelect={(activeKeys) => {
          setSplitScreenActiveKeys(activeKeys);
        }}
        tabs={[
          {
            label: '导航',
            key: 'left-1',
            initPlacement: EnumPlacement.leftTop,
            children: (
              <YRAnchor
                affix={false}
                showInkInFixed
                getContainer={() => window.document.querySelector('.yr-split-screen-layout-body') as HTMLHtmlElement}
              >
                <Link href="#001" title="转授权" />
              </YRAnchor>
            )
          },
          {
            label: '审批意见',
            key: 'right-1',
            children: <PostOpinion form={form} />,
            initPlacement: EnumPlacement.rightTop
          }
        ]}
        siderbarProps={{ right: { width: 500 } }}
      >
        <YRCard id={'001'}>
          <YRForm mode={'readPretty'} form={form}>
            <YRClassificationLayout.Space>
                <YRClassificationLayout title="基本信息">
                    <YRForm.Row column={2}>
                    <YRForm.Item label="机构" name="orgName">
                        <YRInput placeholder="请选择机构" disabled />
                    </YRForm.Item>
                    <YRForm.Item label="角色" name="roleName">
                        <YRInput placeholder="请选择角色" disabled />
                    </YRForm.Item>
                    <YRForm.Item label="业务条线" name="bizLine" rules={[{ required: true, message: '请选择业务条线' }]}>
                        <YRDict.Select dictkey="BIZ_LINE" placeholder="请选择业务条线" />
                    </YRForm.Item>
                    <YRForm.Item
                        label="生效日期"
                        name="effectBeginDate"
                        rules={[{ required: true, message: '请输入生效日期' }]}
                    >
                        <YRDatePicker placeholder="请输入生效日期"  />
                    </YRForm.Item>
                    <YRForm.Item
                        label="失效日期"
                        name="effectEndDate"
                        rules={[{ required: true, message: '请输入失效日期' }]}
                    >
                        <YRDatePicker placeholder="请输入失效日期"  />
                    </YRForm.Item>
                    {/* <YRForm.Item label="登记日期" name="signDate">
                        <YRDatePicker placeholder="请输入登记日期" disabled />
                    </YRForm.Item> */}
                    <YRForm.Item label="登记人" name="operatorName">
                        <YRInput placeholder="请输入登记人" disabled />
                    </YRForm.Item>
                    <YRForm.Item label="登记机构" name="orgName">
                        <YRInput placeholder="请输入登记机构" disabled />
                    </YRForm.Item>
                    <YRForm.Item label="授权编号" name="authSourceType" hidden>
                        <YRInput />
                    </YRForm.Item>
                    </YRForm.Row>
                </YRClassificationLayout>
                <YRClassificationLayout title="维度名称">
                    <YRSpace direction="vertical">
                    {detailInfo.dimensions?.length ? (
                        <YRList bordered size="small">
                            {detailInfo.dimensions.map((item, index) => (
                            <YRList.Item>
                                <YRRow gutter={24} style={{ width: '100%' }}>
                                <YRCol span={3} style={{ display: 'flex', alignItems: 'center' }}>
                                    <YRText>维度名称</YRText>
                                </YRCol>
                                <YRCol span={7}>{(item as any)?.authCodeName}</YRCol>
                                <YRCol span={7}>{(item as any)?.authValueName}</YRCol>
                                <YRCol span={7}>{(item as any)?.resultType}</YRCol>
                                </YRRow>
                            </YRList.Item>
                            ))}
                        </YRList>
                        ) : 
                        (
                        <YREmpty style={{ border: '1px solid #ddd', padding: '10px', margin: '0px -1px' }} />
                    )}
                    </YRSpace>
                </YRClassificationLayout>
                </YRClassificationLayout.Space>
            </YRForm>
        </YRCard>
      </YRSplitScreenLayout>
    </YRFullPageLayout>
  );
};

export default PermissionTransfer;
