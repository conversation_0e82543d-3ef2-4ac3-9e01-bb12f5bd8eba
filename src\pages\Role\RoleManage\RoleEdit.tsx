/**
 * @Author: wangyw26123
 * @Description: 角色编辑/详情
 * @Date: Created in 2022-12-21 10:49:38
 * @Modifed By:
 */
import React, { useEffect, useState } from 'react';
import { YRButton, YRForm, YRSpace, YRMessage, YRSplitScreenLayout, YRAnchor, YRInput } from 'yrantd';
import type { YRSplitScreenLayoutTabs } from 'yrantd';
import { closeWin, QueryString } from '@yr/util';
import { router } from '@/utils/utils';
import { addOrUpdateRole, queryRoleDetail } from '@/services/setting';
import FullPageLayout from '@/components/FullPageLayout';
import RoleInfo from './components/RoleInfo';
import FnMenu from './components/FnMenu';
import PermitData from './components/PermitData';
import Dict from '../../Organ/mock/getDict';
import { EnumMsg } from '@/constant/common';

const { Link } = YRAnchor;

interface RoleEditProps {
  query: {
    mode: 'add' | 'edit' | 'readPretty';
    roleId?: string;
    acrossTenantFlag?: string;
  };
}

const RoleEdit = (props: RoleEditProps) => {
  const [pageLoading, setPageLoading] = useState<boolean>(false);
  const [btnLoading, setBtnLoading] = useState<boolean>(false);
  const [activeKeys, setActiveKeys] = useState<string[]>([]);
  const [roleDetail, setRoleDetail] = useState<any>({});
  const [forceRenderForm, setForceRenderForm] = useState(0);
  const [pageTitle, setPageTitle] = useState<string>('角色新增');
  const { roleId, mode, acrossTenantFlag } = props.query;
  const [form] = YRForm.useForm();

  useEffect(() => {
    if (mode === 'edit') {
      setPageTitle('角色编辑');
    } else if (mode === 'readPretty') {
      setPageTitle('角色详情');
    }

    if (!roleId) return;
    setPageLoading(true);
    queryRoleDetail({ roleId }).then((res) => {
      setPageLoading(false);
      if (res.success) {
        setRoleDetail(res.data || {});
        setForceRenderForm(1);
      }
    });
  }, [roleId]);

  const onsubmit = () => {
    form.validateFields().then((values: any) => {
      setBtnLoading(true);
      const params = { ...values };

      if (mode === 'edit') {
        params.roleId = roleId;
      }

      params.bizLine = params.bizLine.toString();
      params.guardRoleExcludeDtos = params.guardRoleExcludeDtos.map((item) => ({ excludeRoleId: item.excludeRoleId }));

      addOrUpdateRole(params).then((res) => {
        setBtnLoading(false);

        if (res.success) {
          YRMessage.success(EnumMsg.save);
          closeWin();
        }
      });
    });
  };

  const left = {
    title: pageTitle,
    goBack: () => router.goBack()
  };

  const right = () => {
    if (mode !== 'readPretty') {
      return (
        <YRButton type="primary" onClick={onsubmit} loading={btnLoading} disabled={btnLoading}>
          保存
        </YRButton>
      );
    }
    if (acrossTenantFlag !== 'Y') {
      return (
        <YRButton
          type="primary"
          onClick={() => {
            router.replace({
              pathname: '/setting/role/role-manage/edit',
              query: {
                mode: 'edit',
                roleId,
                newtab: '1'
              }
            });
            window.location.reload();
          }}
        >
          修改
        </YRButton>
      );
    }
    return null;
  };

  const anchorList = [
    {
      title: '角色信息',
      href: '#roleInfo'
    },
    {
      title: '功能菜单',
      href: '#fnMenu'
    },
    {
      title: '数据权限',
      href: '#permitData'
    }
  ];

  const renderIcon = (val) => {
    // todo: 暂时不使用表单状态
    // return <CheckCircleOutlined style={{ color: anchorId === val ? 'red' : 'green' }} />;
    return null;
  };

  const tabs: YRSplitScreenLayoutTabs[] = [
    {
      key: 'anchor',
      label: '目录',
      initPlacement: 'leftTop',
      children: (
        <YRAnchor
          targetOffset={20}
          getContainer={() => window.document.querySelector('.yr-split-screen-layout-body') as HTMLElement}
        >
          {anchorList.map((link) => (
            <Link key={link.href} title={link.title} href={link.href} icon={renderIcon(link.href)} />
          ))}
        </YRAnchor>
      )
    }
  ];

  return (
    <FullPageLayout loading={pageLoading} left={left} right={right()}>
      <YRSplitScreenLayout activeKeys={activeKeys} tabs={tabs} onSelect={(keys) => setActiveKeys(keys)}>
        <YRSpace key={forceRenderForm} direction="vertical" block>
          <RoleInfo form={form} mode={mode} roleDetail={roleDetail} />
          <FnMenu
            mode={mode}
            roleDetail={roleDetail}
            getPermitGroupIds={(keys) => {
              form.setFieldValue('permitGroupIdList', keys);
            }}
          />
          <PermitData form={form} mode={mode} roleDetail={roleDetail} />

          <YRForm form={form} style={{ display: 'none' }}>
            <YRForm.Item label="权限组列表" name="permitGroupIdList">
              <YRInput />
            </YRForm.Item>
          </YRForm>
        </YRSpace>
      </YRSplitScreenLayout>
    </FullPageLayout>
  );
};

export default Dict([
  'EnumRoleLevel',
  'EnumGuardRoleType',
  'EnumGuardRoleStatus',
  'EnumRoleBizLine',
  'EnumGuardAccessRoleType',
  'EnumFilterGroup',
  'EnumGuardRoleStatus'
])(QueryString(RoleEdit));
