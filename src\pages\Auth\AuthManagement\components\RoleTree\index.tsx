/*
 * @Author: liaokt
 * @Description: 角色树
 * @Date: 2024-10-16 10:04:40
 * @LastEditors: liaokt
 * @LastEditTime: 2024-10-17 14:51:17
 */
import { useRequest } from 'ahooks';
import React, { forwardRef, useImperativeHandle, useState, useEffect } from 'react';
import { YRSpin, YRTree, YRItem, YRModal, YREmpty, YRButton, YRMessage } from 'yrantd';
import { M0105 } from '@permit/organ';

const RoleTree = forwardRef((props: { data: any[]; loading: boolean; setData: any; okCallback: () => void }, ref) => {
  const { data, loading, setData, okCallback } = props;
  const [currentRoleNodeInfo, setCurrentRoleNodeInfo] = useState({});
  
  useImperativeHandle(ref, () => ({
    getCurrentRoleNodeInfo: () => {
      return currentRoleNodeInfo;
    }
  }));

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      // 清理可能的异步操作
    };
  }, []);
  const { delAuthCategoryRoleInfo } = M0105.interfaces;

  const { loading: deleteLoading, run: onDelete } = useRequest(delAuthCategoryRoleInfo, {
    manual: true,
    onSuccess: (res: any) => {
      if (res?.errorMessage === null) {
        YRMessage.success('删除成功', 0.5, () => {
          okCallback();
        });
      }
    }
  });

  return (
    <>
      <YRSpin spinning={loading} style={{ marginTop: '20px' }}>
        {data && data.length > 0 ? (
          <YRTree.DirectoryTree
            treeData={(data || []).filter(Boolean).map(item => ({
              ...item,
              key: item.key || item.roleId || item.id
            }))}
            showIcon={false}
            titleRender={(nodeData) => {
              const { roleId, roleName, authCategory, authNo, orgId, authCatalogRoleId, key } = nodeData as any;
              return (
                <YRItem
                  title={roleName || roleId}
                  key={key || roleId}
                  onClick={() => {
                    setData({ roleId, authNo, roleName });
                    setCurrentRoleNodeInfo({ nodeData });
                  }}
                  operateRender={[
                    <YRButton
                      key={`delete-${key || roleId}`}
                      type={'text'}
                      onClick={() => {
                        YRModal.confirm({
                          title: '删除',
                          okText: '确定',
                          cancelText: '取消',
                          content: '确定要删除此角色吗?',
                          okButtonProps: {
                            loading: deleteLoading
                          },
                          onOk: () => {
                            onDelete({ roleId, authCategory, authNo, orgId, authCatalogRoleId });
                          }
                        });
                      }}
                    >
                      删除
                    </YRButton>
                  ]}
                />
              );
            }}
          />
        ) : (
          <YREmpty style={{ marginTop: '80px' }} />
        )}
      </YRSpin>
    </>
  );
});

export default RoleTree;
