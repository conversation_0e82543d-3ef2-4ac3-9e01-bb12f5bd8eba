import React, { useEffect, useState } from 'react';
import { YRModal, YREasyUseModal, YRClassificationLayout, YRForm, YRInput, YRDict, YRMessage } from 'yrantd';
import { OpenModalType } from '../type';
import { antdModal, openNewTab } from '@/utils/utils';
import { addFlowDiagramResource, modifyFlowDiagramResource } from '@/services/flow';

const MODAL_TYPE_ENMU = {
  FLOWCHART: 1, // 流程图
  DECISIONABLE: 2 // 决策表
};

const AddModal = (props) => {
  const modal = YREasyUseModal.useModal();
  const [form] = YRForm.useForm();
  const { type, id } = props;
  form.setFieldsValue({ ...props });
  const getTitle = () => {
    switch (type) {
      case OpenModalType.add:
        return '新增';
      case OpenModalType.edit:
        return '修改';
      default:
        return '新增';
    }
  };
  const handleSave = async () => {
    const values: any = await form.validateFields();
    const request = type === 'add' ? addFlowDiagramResource : modifyFlowDiagramResource;
    values.id = id;
    values.modelType = MODAL_TYPE_ENMU.DECISIONABLE;
    try {
      request(values).then((res) => {
        if (res?.success) {
          YRMessage.success('保存成功');
          modal.hide();
          if (type === 'add') {
            openNewTab({
              pathname: `/decision-page/${type}`,
              query: { id: res?.data }
            });
          }
        }
      });
    } catch (error) {}
  };
  return (
    <YRModal {...antdModal(modal)} size="middle" title={getTitle()} onOk={handleSave}>
      <YRClassificationLayout.Space>
        <YRForm labelAlign="left" form={form}>
          <YRForm.Row>
            <YRForm.Item name={'modelName'} label="决策表名称">
              <YRInput disabled={type !== 'add'} />
            </YRForm.Item>
            <YRForm.Item name={'modelNumber'} label="决策表编号">
              <YRInput disabled={type !== 'add'} />
            </YRForm.Item>
            <YRForm.Item name={'sceneType'} label="场景类型">
              <YRDict.Select dictkey="flow_scene_type" disabled={type !== 'add'} />
            </YRForm.Item>
            <YRForm.Item name={'modelDesc'} label="描述">
              <YRInput />
            </YRForm.Item>
          </YRForm.Row>
        </YRForm>
      </YRClassificationLayout.Space>
    </YRModal>
  );
};

export default YREasyUseModal.create(AddModal);
