import React, { useEffect } from 'react';
import { Y<PERSON>utton, YRDict, YRForm, YRInput, YRMessage, YRModal } from 'yrantd';
import Dict from '@/pages/Organ/mock/getDict';
import { useRequest } from 'ahooks';
import { addFlowParamItem, modifyFlowParamItem } from '@/services/flow';
import { isObjectEmpty } from '@/utils/utils';

const ParamAddModal = (props: any) => {
  const { visible, changeVisible, okCallback, activity, currentParam, setCurrentParam } = props;
  const [form] = YRForm.useForm();
  const isDict = YRForm.useWatch('isDict', form);

  /** 新增参数项接口 */
  const { loading: addLaoding, run: addRequest } = useRequest(addFlowParamItem, {
    manual: true,
    onSuccess: (result: any) => {
      const { errorMessage, data } = result;
      if (errorMessage === null) {
        YRMessage.success('新增成功', 0.5, () => {
          okCallback();
          onCancel();
        });
      }
    }
  });

  /** 修改参数项接口 */
  const { loading: modifyLaoding, run: modifyRequest } = useRequest(modifyFlowParamItem, {
    manual: true,
    onSuccess: (result: any) => {
      const { errorMessage, data } = result;
      if (errorMessage === null) {
        YRMessage.success('修改成功', 0.5, () => {
          okCallback();
          onCancel();
        });
      }
    }
  });

  /** ==== 副作用 ==== */

  // 获取参数库 id
  useEffect(() => {
    activity && form.setFieldsValue({ paramLibraryId: activity });
  }, [activity]);

  // 修改时反显数据
  useEffect(() => {
    if (!isObjectEmpty(currentParam) && visible) {
      form.setFieldsValue({ ...currentParam });
    }
  }, [currentParam, visible]);

  /** ==== 方法 ==== */

  // 取消事件
  const onCancel = () => {
    changeVisible(false);
    setCurrentParam({});
    form.resetFields(['paramItemKey', 'paramValueType', 'paramItemName']);
  };

  // 确认事件
  const onOk = () => {
    form
      .validateFields()
      .then((values: any) => {
        const { id } = currentParam;
        if (!isObjectEmpty(currentParam)) {
          delete values.paramItemKey;
          delete values.paramLibraryId;
          delete values.paramValueType;
          modifyRequest({ ...values, id });
        } else {
          addRequest({ ...values });
        }
      })
      .catch((errorInfo) => {});
  };

  return (
    <YRModal
      width={'50%'}
      destroyOnClose
      onCancel={() => {
        onCancel();
      }}
      open={visible}
      title={!isObjectEmpty(currentParam) ? '修改参数项' : '新增参数项'}
      footer={[
        <YRButton key="back" onClick={() => onCancel()}>
          取消
        </YRButton>,
        <YRButton key="submit" type="primary" onClick={() => onOk()} loading={addLaoding || modifyLaoding}>
          {!isObjectEmpty(currentParam) ? '修改' : '新增'}
        </YRButton>
      ]}
    >
      <YRForm form={form}>
        <YRForm.Row>
          <YRForm.Item
            label={'参数库ID'}
            name={'paramLibraryId'}
            rules={[{ required: true, message: '参数库ID不能为空' }]}
          >
            <YRInput disabled placeholder={'请输入参数库ID'} />
          </YRForm.Item>
          <YRForm.Item label={'参数key'} name={'paramItemKey'} rules={[{ required: true, message: '参数key不能为空' }]}>
            <YRInput placeholder={'请输入参数key'} disabled={!isObjectEmpty(currentParam)} />
          </YRForm.Item>
          <YRForm.Item
            label={'参数名称'}
            name={'paramItemName'}
            rules={[{ required: true, message: '参数名称不能为空' }]}
          >
            <YRInput placeholder={'请输入参数名称'} />
          </YRForm.Item>
          <YRForm.Item label={'值类型'} name={'paramValueType'} rules={[{ required: true, message: '值类型不能为空' }]}>
            <YRDict.Select
              dictkey={'param_value_type'}
              placeholder={'请选择值类型'}
              disabled={!isObjectEmpty(currentParam)}
            />
          </YRForm.Item>
          <YRForm.Item
            label={'是否字典'}
            name={'isDict'}
            rules={[{ required: true, message: '是否字典不能为空' }]}
            initialValue={'0'}
          >
            <YRDict.Select disabled dictkey={'EnumBoolean'} placeholder={'请选择是否字典'} />
          </YRForm.Item>
          {isDict === '1' && (
            <YRForm.Item label={'字典key'} name={'dictKey'} rules={[{ required: true, message: '字典key不能为空' }]}>
              <YRInput placeholder={'请输入字典key'} />
            </YRForm.Item>
          )}
        </YRForm.Row>
      </YRForm>
    </YRModal>
  );
};

export default Dict(['param_value_type', 'EnumBoolean'])(ParamAddModal);
