/*
 * @Author: liaokt
 * @Description:
 * @Date: 2024-10-22 16:39:24
 * @LastEditors: liaokt
 * @LastEditTime: 2024-10-22 17:36:16
 */
import React, { useState, useEffect } from 'react';
import { useRequest } from 'ahooks';
import { M0105 } from '@permit/organ';
import { YRTreeSelect } from 'yrantd';

const ChooseRoleTree = (props: {
  organParams: { orgId: string; orgLvlList: string[] };
  selectBack: (values: any) => void;
}) => {
  const [roleData, setRoleData] = useState([] as any[]);

  const { organParams, selectBack } = props;

  const { queryRole } = M0105.interfaces;

  const { loading: queryRoleLoading, run: queryRoleRequest } = useRequest(queryRole, {
    manual: true,
    onSuccess: (res: any) => {
      if (res?.errorMessage === null) {
        const { data } = res;
        setRoleData(data || []);
      }
    }
  });

  useEffect(() => {
    const { orgId, orgLvlList = [] } = organParams;
    if (orgId && orgLvlList.length) {
      queryRoleRequest({ orgId, orgLvlList });
    }
  }, [organParams]);

  return (
    <YRTreeSelect
      multiple
      loading={queryRoleLoading}
      style={{ width: '100%' }}
      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
      treeData={roleData}
      onChange={selectBack}
      fieldNames={{ label: 'roleName', value: 'roleNo' }}
      placeholder="请选择角色"
      treeDefaultExpandAll
      allowClear
    />
  );
};

export default ChooseRoleTree;
