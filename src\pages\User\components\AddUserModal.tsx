/**
 * @Author: 刘文强
 * @Description: 新增/修改 用户对话框
 * @Date: Created in
 * @Modifed By:
 */
import React, { useEffect, useMemo, useState } from 'react';
import { YRForm, YRInput, YRModal, YRTreeSelect, YRRadio, YRDatePicker, YRMessage, YRDict } from 'yrantd';
import { queryUserOrganTree } from '@/services/setting';
import { regex } from '@yr/util';
import debounce from 'lodash-es/debounce';
import moment from 'moment';
import queryAcctOrganTreeJson from '@/pages/Organ/mock/queryAcctOrganTree.json';
import { addUser, modifyUser, queryThirdUser } from '../../../services/user';
import { md5Crypto } from '@/utils/utils';

const queryAcctOrganTree = () => Promise.resolve(queryAcctOrganTreeJson);

const filterOrganTree = (organTree, calllback): any[] => {
  let newOrganTree = [];

  if (typeof calllback !== 'function') return newOrganTree;

  const loop = (data, fn) => {
    const list: any = [];

    for (let i = 0; i < data.length; i++) {
      const getItem = fn(data[i]);

      if (!getItem) continue;

      if (data[i].children) {
        const subItem = loop(data[i].children, fn);

        if (subItem.length) {
          getItem.children = subItem;
        }
      }

      list.push(getItem);
    }

    return list;
  };

  newOrganTree = loop(organTree, calllback);

  return newOrganTree;
};

const AddUserModal = (props) => {
  const [form] = YRForm.useForm();
  const [treeData, setTreeData] = useState<any>([]);
  const [parentOrganTree, setParentOrganTree] = useState<any>([]);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [isCustomerManger, setIsCustomerManger] = useState(false);
  const [isSelfBankMemberFlag, setIsSelfBankMemberFlag] = useState(false);
  const [acctOrganTreeData, setAcctOrganTreeData] = useState<any[]>([]);
  const { setVisible, addVisible, type, detail, handleSearch } = props;

  useEffect(() => {
    addVisible && form.resetFields();
  }, [addVisible, detail]);

  useEffect(() => {
    queryUserOrganTree({}).then((res) => {
      if (res.success && res.data) {
        const organTreeDto = res.data.organTreeDto ? [res.data.organTreeDto] : [];
        setTreeData(organTreeDto);
      }
    });

    queryAcctOrganTree().then((res: any) => {
      if (res.success && res.data) {
        const organTreeDto = res.data.organTreeDto ? [res.data.organTreeDto] : [];
        setAcctOrganTreeData(organTreeDto);
      }
    });
  }, []);

  useEffect(() => {
    setParentOrganTree(
      filterOrganTree(treeData, (val) => {
        return {
          ...val,
          // 不为启用时禁止选择
          disabled: val.organStatus !== '002'
        };
      })
    );
  }, [treeData]);

  const handleOk = () => {
    form.validateFields().then((values) => {
      const { password } = values as any;
      const param = {
        ...values,
        password: md5Crypto(password),
        tenantId: sessionStorage.tenantId,
        ownOrganId: form.getFieldValue('organId'),
        accountEffectiveDate: moment(form.getFieldValue('accountEffectiveDate')).format('YYYY-MM-DD')
      };
      setConfirmLoading(true);
      if (type === 'add') {
        addUser(param).then((res) => {
          setConfirmLoading(false);
          if (res && res.success) {
            if (res.data.result === true) {
              YRMessage.success('新增成功!');
              handleSearch();
              handleCancel();
            } else {
              YRMessage.error(res.data.errorMessage);
            }
          }
        });
      } else {
        modifyUser({ ...param, userId: detail.userId }).then((res) => {
          setConfirmLoading(false);
          if (res && res.success) {
            YRMessage.success('修改成功!');
            handleSearch();
            handleCancel();
          }
        });
      }
    });
  };

  const handleCancel = () => {
    setVisible(false);
  };

  // 名称和账号变化时，调三方接口查询用户信息
  const handleAccountNo = useMemo(() => {
    return debounce(() => {
      const userName = form.getFieldValue('userName') || undefined;
      const accountNo = form.getFieldValue('accountNo') || undefined;
      if (userName && accountNo) {
        const param = {
          userName,
          accountNo
        };
        queryThirdUser(param).then((res) => {
          if (res && res.success && res.data?.length > 0) {
            const { isOwn } = res.data[0];
            form.setFieldValue('selfBankMemberFlag', isOwn ? 'Y' : 'N');
            setIsSelfBankMemberFlag(isOwn);
          } else {
            form.setFieldValue('selfBankMemberFlag', 'N');
            setIsSelfBankMemberFlag(false);
          }
        });
      }
    }, 1000);
  }, []);

  const handlePostChange = (e) => {
    // 岗位值为客户经理岗位时，显示客户经理级别
    setIsCustomerManger(e.target.value === '客户经理');
  };

  const selfBankMemberFlag = YRForm.useWatch('selfBankMemberFlag', form);
  const bankTellerFlag = YRForm.useWatch('bankTellerFlag', form);

  return (
    <YRModal
      confirmLoading={confirmLoading}
      title={type === 'add' ? '新增用户' : '修改用户'}
      open={addVisible}
      onCancel={handleCancel}
      onOk={handleOk}
      destroyOnClose
      bodyStyle={{ maxHeight: '65vh', overflow: 'auto' }}
    >
      <YRForm form={form} mode={'edit'}>
        <YRForm.Row>
          <YRForm.Item
            initialValue={detail.userName}
            name="userName"
            label="用户名"
            rules={[{ required: true, message: '用户名必填' }]}
          >
            {/* <YRInput maxLength={30} onChange={() => handleAccountNo()} /> */}
            <YRInput maxLength={30} />
          </YRForm.Item>
          <YRForm.Item
            initialValue={detail.accountNo}
            name="accountNo"
            label="登录账号"
            rules={[{ required: true, message: '登录账号必填' }]}
          >
            {/* <YRInput maxLength={30} onChange={() => handleAccountNo()} /> */}
            <YRInput maxLength={30} />
          </YRForm.Item>
          {type === 'add' && (
            <YRForm.Item
              name="password"
              label="登录密码"
              rules={[
                { required: true, message: '登录密码必填' },
                {
                  pattern:
                    /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,}$/,
                  message: '登录密码长度8-16位，必须包含中英文大小写、数字、符号中三种或以上'
                }
              ]}
            >
              <YRInput.Password maxLength={16} />
            </YRForm.Item>
          )}
          <YRForm.Item
            initialValue={detail.mobile}
            name="mobile"
            label="手机号码"
            rules={[{ required: true, message: '手机号码必填' }]}
          >
            <YRInput />
          </YRForm.Item>
          <YRForm.Item
            initialValue={detail.organId}
            name="organId"
            label="所属机构"
            rules={[{ required: true, message: '所属机构必填' }]}
          >
            <YRTreeSelect
              treeData={parentOrganTree}
              fieldNames={{ label: 'organName', value: 'organId' }}
              placeholder="请选择机构"
            />
          </YRForm.Item>
          <YRForm.Item initialValue={detail.certificateKind} name="certificateKind" label="证件类型">
            <YRDict.Select dictkey="EnumCertificateKind" />
          </YRForm.Item>
          <YRForm.Item
            initialValue={detail.certificateNo}
            name="certificateNo"
            label="证件号码"
            rules={[{ pattern: regex.idCard, message: '身份证格式错误' }]}
          >
            <YRInput />
          </YRForm.Item>
          <YRForm.Item
            initialValue={detail.email}
            name="email"
            label="电子邮箱"
            rules={[{ pattern: regex.email, message: '邮箱格式错误' }]}
          >
            <YRInput />
          </YRForm.Item>
          <YRForm.Item
            initialValue={detail.post}
            name="post"
            label="岗位"
            rules={[{ required: true, message: '请选择岗位' }]}
          >
            <YRRadio.Group onChange={handlePostChange}>
              <YRRadio key="客户经理" value="客户经理">
                客户经理
              </YRRadio>
              <YRRadio key="其他" value="其他">
                其他
              </YRRadio>
            </YRRadio.Group>
          </YRForm.Item>
          {/* 岗位为客户经理时，才可以选择客户经理级别 */}
          {isCustomerManger && (
            <YRForm.Item initialValue={detail.level} name="level" label="客户经理级别">
              <YRInput maxLength={30} />
            </YRForm.Item>
          )}
          {/* 调完三方接口后自动带出，不可以编辑。 */}
          <YRForm.Item initialValue={detail.ownBankFlag || 'N'} name="selfBankMemberFlag" label="是否本行行员">
            <YRRadio.Group disabled>
              <YRRadio key="Y" value="Y">
                是
              </YRRadio>
              <YRRadio key="N" value="N">
                否
              </YRRadio>
            </YRRadio.Group>
          </YRForm.Item>
          {(isSelfBankMemberFlag || selfBankMemberFlag) && (
            <YRForm.Item initialValue={detail.bankTellerFlag} name="bankTellerFlag" label="是否柜员">
              <YRRadio.Group>
                <YRRadio key="Y" value="Y">
                  是
                </YRRadio>
                <YRRadio key="N" value="N">
                  否
                </YRRadio>
              </YRRadio.Group>
            </YRForm.Item>
          )}
          <YRForm.Item
            initialValue={detail.accountEffectiveDate ? moment(detail.accountEffectiveDate) : moment().add(3, 'month')}
            name="accountEffectiveDate"
            label="账户有效期"
            rules={[{ required: true, message: '账户有效期必填' }]}
          >
            <YRDatePicker
              disabledDate={(current) => {
                return current && current < moment().startOf('day');
              }}
            />
          </YRForm.Item>
          {/*{(isSelfBankMemberFlag || selfBankMemberFlag) && type === 'add' && (
            <YRForm.Item name="ownHrOrganId" label="所属机构(HR)" initialValue={moment(detail.ownHrOrganId)}>
              <YRTreeSelect
                treeData={parentOrganTree}
                fieldNames={{ label: 'organName', value: 'organId' }}
                placeholder="请选择机构"
              />
            </YRForm.Item>
          )}*/}
          {type === 'edit' && (isSelfBankMemberFlag || selfBankMemberFlag) && bankTellerFlag === 'Y' && (
            <YRForm.Item name="acctOrganId" label="账务机构" initialValue={detail.acctOrganId}>
              <YRTreeSelect
                disabled={type === 'edit'}
                treeData={acctOrganTreeData}
                fieldNames={{ label: 'acctOrganName', value: 'acctOrganId' }}
              />
            </YRForm.Item>
          )}
        </YRForm.Row>
      </YRForm>
    </YRModal>
  );
};

export default AddUserModal;
