const YRMicroUtils = require('yrantd/lib/yr-micro-utils');
const NodePolyfillPlugin = require('node-polyfill-webpack-plugin');
const { execSync } = require('child_process');
const path = require('path');

module.exports = (microConfig) => {
  const { onGetWebpackConfig, modifyUserConfig, context } = microConfig;
  const { NODE_ENV } = process.env;
  const { pkg } = context;

  onGetWebpackConfig((config) => {
    // 组件库样式在生产模式下将会被排除, 更新组件库时请注意同步更新基座中的组件版本
    if (NODE_ENV === 'production') {
      config.module
        .rule('ignore-style')
        .test([/(antd|yrantd|yr-loan-antd).*\.(less|css)$/])
        .before('jsx')
        .use('empty-loader')
        .loader(path.resolve('./empty-loader.js'));
      config.optimization.splitChunks({
        chunks: 'all', // 同时分割同步和异步代码
        cacheGroups: {
          vendor: {
            name: 'highlight',
            chunks: 'all',
            test: /[\\/]node_modules[\\/](highlight)/,
            priority: 10
          }
        }
      });
    }
    config.resolve.alias.set('@permit', path.resolve(__dirname, 'permit'));
    config.resolve.fallback = {
      crypto: require.resolve('crypto-browserify')
    };
    config.plugin('node-polyfill-webpack-plugin').use(NodePolyfillPlugin);
  });

  modifyUserConfig('define', {
    ...context.userConfig.define,
    packageInfo: {
      name: pkg.name
    }
  });

  YRMicroUtils.setDeployInfo({ ...microConfig, execSync });
};
