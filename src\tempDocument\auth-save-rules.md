# 授权规则保存校验规则文档

## 概述

本文档详细描述了授权规则保存时的各项校验规则，包括基础校验、维度校验、互斥校验等，确保授权规则配置的正确性和完整性。

---

## 1. 基础校验规则

### 1.1 结果类型校验

**适用范围**: 授信类别为"授信业务审批授权"、"放款审查授权"

**校验规则**:
- 结果类型为必选项
- 选择结果类型后需填入金额，默认为0
- 若没有选择结果类型，则不允许保存

**错误提示**: 
```
没有结果类型的授权不允许保存！
```

### 1.2 条件维度校验

**校验规则**:
- 必须存在至少一项条件维度
- 若不存在任何条件维度，则保存不成功

**错误提示**: 
```
请至少增加一项条件维度！
```

### 1.3 维度重复校验

**校验规则**:
- 系统校验本条授权规则是否有选择重复的维度名称
- 若有重复则保存不成功

**错误提示**: 
```
条件维度xx重复
```

---

## 2. 条件维度互斥校验规则

### 2.1 客户类型校验

| 规则编号 | 校验条件 | 错误提示 |
|---------|---------|----------|
| ① | 条件维度[客户类型]必选 | 条件维度[客户类型]必输，请核实 |

### 2.2 基于客户类型的维度校验

| 规则编号 | 客户类型条件 | 可配置维度 | 错误提示 |
|---------|-------------|-----------|----------|
| ② | 个人客户 | 贷款用途 | [客户类型]为"个人客户"时，才可配置条件维度[贷款用途]，请核实 |
| ③ | 公司客户 | 贷款组织形式 | [客户类型]为"公司客户"时，才可配置条件维度[贷款组织形式]，请核实 |
| ④ | 个人客户 或 公司客户 | 是否存量 | [客户类型]为"个人客户"或"公司客户"时，才可配置条件维度[是否存量]，请核实 |
| ⑤ | 个人客户 或 公司客户 | 是否重组 | [客户类型]为"个人客户"或"公司客户"时，才可配置条件维度[是否重组]，请核实 |
| ⑥ | 个人客户 或 公司客户 | 主担保方式 | [客户类型]为"个人客户"或"公司客户"时，才可配置条件维度[主担保方式]，请核实 |
| ⑦ | 公司客户 | 是否担保额度 | [客户类型]为"公司客户"时，才可配置条件维度[是否担保额度]，请核实 |
| ⑧ | 合作方 | 合作类型 | [客户类型]为"合作方"时，才可配置条件维度[合作类型]，请核实 |

### 2.3 基于授权类型和客户类型的维度校验

| 规则编号 | 授权类型条件 | 客户类型条件 | 可配置维度 | 错误提示 |
|---------|-------------|-------------|-----------|----------|
| ⑨ | 专项阈值 | 个人客户 或 公司客户 | 合作项目 | 授权类型为"专项阈值"，且条件维度[客户类型]为"个人客户"或"公司客户"时，才可配置条件维度[合作项目]，请核实 |

---

## 3. 结果维度互斥校验规则

### 3.1 产品阈值类型结果维度校验

| 规则编号 | 授权类型 | 客户类型条件 | 可配置结果维度 | 错误提示 |
|---------|---------|-------------|---------------|----------|
| ⑩ | 产品阈值 | 个人客户 | 授信金额（用途+担保） | [授权类型]为"产品阈值"且条件维度[客户类型]为"个人客户"时，才可配置结果维度[授信金额（用途+担保）]，请核实 |
| ⑪ | 产品阈值 | 公司客户 | 授信金额（贷款组织形式+担保） | [授权类型]为"产品阈值"且条件维度[客户类型]为"公司客户"时，才可配置结果维度[授信金额（贷款组织形式+担保）]，请核实 |
| ⑫ | 产品阈值 | 公司客户 | 授信金额（产品组+担保） | [授权类型]为"产品阈值"且条件维度[客户类型]为"公司客户"时，才可配置结果维度[授信金额（产品组+担保）]，请核实 |

### 3.2 综合阈值类型结果维度校验

| 规则编号 | 授权类型 | 客户类型条件 | 其他条件 | 可配置结果维度 | 错误提示 |
|---------|---------|-------------|---------|---------------|----------|
| ⑬ | 综合阈值 | 个人客户 或 公司客户 | - | 授信金额（担保） | [授权类型]为"综合阈值"且条件维度[客户类型]为"个人客户"或"公司客户"时，才可配置结果维度[授信金额（担保）]，请核实 |
| ⑮ | 综合阈值 | 个人客户 或 公司客户 | 是否重组=是 | 重组授信总金额 | [授权类型]为"综合阈值"，条件维度[客户类型]为"个人客户"或"公司客户"且条件维度[是否重组]为"是"时，才可配置结果维度[重组授信总金额]，请核实 |
| ⑯ | 综合阈值 | - | - | 客户授信总金额 | [授权类型]为"综合阈值"时，才可配置结果维度[客户授信总金额]，请核实 |

### 3.3 专项阈值类型结果维度校验

| 规则编号 | 授权类型 | 客户类型条件 | 可配置结果维度 | 错误提示 |
|---------|---------|-------------|---------------|----------|
| ⑭ | 专项阈值 | 个人客户 或 公司客户 | 专项阈值金额 | [授权类型]为"专项阈值"且条件维度[客户类型]为"个人客户"或"公司客户"时，才可配置结果维度[专项阈值金额]，请核实 |

---

## 4. 特殊业务逻辑校验

### 4.1 重组与存量关联校验

**校验规则**:
- 当[是否重组]配置为"是"时，[是否存量]一定要配置为"是"

**错误提示**: 
```
[是否重组]配置为"是"时，[是否存量]一定要配置为"是"，请核实
```

---

## 5. 校验规则汇总表

### 5.1 授权类型分类

| 授权类型 | 适用客户类型 | 可配置条件维度 | 可配置结果维度 |
|---------|-------------|---------------|---------------|
| 产品阈值 | 个人客户 | 客户类型、贷款用途、是否存量、是否重组、主担保方式 | 授信金额（用途+担保） |
| 产品阈值 | 公司客户 | 客户类型、贷款组织形式、是否存量、是否重组、主担保方式、是否担保额度 | 授信金额（贷款组织形式+担保）、授信金额（产品组+担保） |
| 综合阈值 | 个人客户/公司客户 | 客户类型、是否存量、是否重组、主担保方式 | 授信金额（担保）、客户授信总金额、重组授信总金额 |
| 专项阈值 | 个人客户/公司客户 | 客户类型、是否存量、是否重组、主担保方式、合作项目 | 专项阈值金额 |
| 所有类型 | 合作方 | 客户类型、合作类型 | - |

### 5.2 客户类型分类

| 客户类型 | 专属条件维度 | 通用条件维度 |
|---------|-------------|-------------|
| 个人客户 | 贷款用途 | 是否存量、是否重组、主担保方式 |
| 公司客户 | 贷款组织形式、是否担保额度 | 是否存量、是否重组、主担保方式 |
| 合作方 | 合作类型 | - |

---

## 6. 实施建议

### 6.1 前端校验
- 在用户选择授权类型和客户类型后，动态显示/隐藏相应的维度选项
- 实时校验用户输入，提前提示错误信息
- 在保存前进行完整性校验

### 6.2 后端校验
- 实现完整的服务端校验逻辑
- 返回详细的错误信息和错误码
- 记录校验失败的日志便于问题排查

### 6.3 测试建议
- 针对每个校验规则编写单元测试
- 覆盖所有错误场景的集成测试
- 边界条件和异常情况的测试

---

*本文档版本：v1.0*  
*最后更新：2024年12月*
