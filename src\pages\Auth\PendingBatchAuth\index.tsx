/*
 * @Author: AI Assistant
 * @Description: 待复核-批量授权管理页面
 * @Date: 2024-12-19
 */
import React, { useState, useEffect } from 'react';
import { YRButton, YRTable, YRForm, YRMessage, YRIndexPageLayout, YRModal } from 'yrantd';
import { useRequest } from 'ahooks';
import { useDict } from '@yr/util';
import { M0105 } from '@permit/organ';
import { columns, formItemList } from './useIndex';
import type { 
  PendingBatchQueryParams, 
  ApiResponse, 
  PageResponse, 
  PendingBatchAuthInfo, 
  PendingBatchAuthDto,
  BatchReviewParams,
  BatchAuthDetailInfo
} from './types';
import { 
  queryPendingBatchAuthList, 
  approveBatchAuth, 
  rejectBatchAuth,
  queryBatchAuthDetail
} from '@/services/batchAuth';

const PendingBatchAuth: React.FC = () => {
  // 初始化数据字典
  useDict(['AUTH_STATUS']);

  // 数据映射函数：将API返回的数据映射为前端使用的数据结构
  const mapApiDataToFrontend = (apiData: PendingBatchAuthDto): PendingBatchAuthInfo => {
    // 安全的日期格式化
    const formatDate = (dateString: string): string => {
      if (!dateString) return '';
      try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return '';
        return date.toISOString().split('T')[0];
      } catch (error) {
        console.warn('日期格式化失败:', dateString, error);
        return '';
      }
    };

    return {
      batchAuthId: apiData.batchAuthBaseId || '',
      batchAuthNo: apiData.batchAuthBaseId || '',
      fileName: apiData.fileName || '',
      authStatus: apiData.authStatus || '',
      operatorName: apiData.registerName || '',
      operatorId: apiData.registerId || '',
      ownOrganName: apiData.registerOrgName || '',
      ownOrganId: apiData.registerOrgId || '',
      inputTime: formatDate(apiData.registerTime),
      updateTime: apiData.registerTime || '',
      totalCount: (apiData.successNum || 0) + (apiData.failNum || 0),
      successCount: apiData.successNum || 0,
      failCount: apiData.failNum || 0,
      flowInstanceId: apiData.flowInstanceId,
      remark: apiData.remark || ''
    };
  };

  const [form] = YRForm.useForm();
  const [dataSource, setDataSource] = useState<PendingBatchAuthInfo[]>([]);

  // 分页数据
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) =>
      `共 ${total} 条记录，当前显示第 ${range[0]}-${range[1]} 条`
  });

  // 查询待复核批量授权列表
  const {
    loading: queryLoading,
    run: queryPendingBatchList,
    refresh: refreshList
  } = useRequest(
    async (params: PendingBatchQueryParams) => {
      // 添加状态过滤条件，只查询复核中的记录
      const queryParams = {
        ...params,
        authStatus: params.authStatus || '020' // 默认查询复核中状态
      };
      return await queryPendingBatchAuthList(queryParams);
    },
    {
      onSuccess: (res: ApiResponse<PageResponse<PendingBatchAuthDto>>) => {
        if (res?.rpcResult === 'SUCCESS' && !res?.errorMessage) {
          const { data } = res;
          // 将API返回的数据映射为前端使用的数据结构
          const mappedData = (data.list || []).map(mapApiDataToFrontend);
          setDataSource(mappedData);
          setPagination((prev) => ({
            ...prev,
            current: data.pageNum,
            total: data.total
          }));
        } else {
          YRMessage.error(res?.errorMessage || '查询失败');
        }
      },
      onError: (error) => {
        console.error('查询待复核批量授权列表失败:', error);
        YRMessage.error('查询失败，请稍后重试');
        setDataSource([]);
        setPagination((prev) => ({
          ...prev,
          current: 1,
          total: 0
        }));
      },
      manual: true
    }
  );

  // 复核通过
  const { run: approveRun, loading: approveLoading } = useRequest(
    async (params: BatchReviewParams) => {
      return await approveBatchAuth({ 
        batchAuthId: params.batchAuthId,
        reviewRemark: params.reviewRemark 
      });
    },
    {
      onSuccess: (res: any) => {
        if (res?.rpcResult === 'SUCCESS') {
          YRMessage.success('复核通过成功', 0.5, () => {
            refreshList();
          });
        } else {
          YRMessage.error(res?.errorMessage || '复核通过失败');
        }
      },
      manual: true
    }
  );

  // 复核退回
  const { run: rejectRun, loading: rejectLoading } = useRequest(
    async (params: BatchReviewParams) => {
      return await rejectBatchAuth({ 
        batchAuthId: params.batchAuthId,
        reviewRemark: params.reviewRemark 
      });
    },
    {
      onSuccess: (res: any) => {
        if (res?.rpcResult === 'SUCCESS') {
          YRMessage.success('复核退回成功', 0.5, () => {
            refreshList();
          });
        } else {
          YRMessage.error(res?.errorMessage || '复核退回失败');
        }
      },
      manual: true
    }
  );

  // 初始化查询
  useEffect(() => {
    handleSearch();
  }, []);

  // 查询处理
  const handleSearch = () => {
    form.validateFields().then((formValues: any) => {
      const queryParams: PendingBatchQueryParams = {
        pageNum: 1,
        pageSize: pagination.pageSize,
        batchAuthNo: formValues.batchAuthNo,
        authStatus: formValues.authStatus
      };

      console.log('查询参数:', queryParams);
      setPagination((prev) => ({ ...prev, current: 1 }));
      queryPendingBatchList(queryParams);
    }).catch((error) => {
      console.error('表单验证失败:', error);
      YRMessage.error('查询条件验证失败');
    });
  };

  // 分页变化处理
  const handlePaginationChange = (page: number, pageSize?: number) => {
    const newPagination = {
      ...pagination,
      current: page,
      pageSize: pageSize || pagination.pageSize
    };
    setPagination(newPagination);

    form.validateFields().then((formValues: any) => {
      const queryParams: PendingBatchQueryParams = {
        pageNum: page,
        pageSize: pageSize || pagination.pageSize,
        batchAuthNo: formValues.batchAuthNo,
        authStatus: formValues.authStatus
      };
      console.log('分页查询参数:', queryParams);
      queryPendingBatchList(queryParams);
    }).catch((error) => {
      console.error('分页查询表单验证失败:', error);
      YRMessage.error('查询条件验证失败');
    });
  };

  // 详情处理
  const handleDetail = (record: PendingBatchAuthInfo) => {
    // 查询详情数据
    queryBatchAuthDetail({ batchAuthId: record.batchAuthId })
      .then((res: ApiResponse<BatchAuthDetailInfo>) => {
        if (res?.rpcResult === 'SUCCESS') {
          const detailData = res.data;
          YRModal.info({
            title: '批量授权详情',
            width: 800,
            content: (
              <div>
                <p><strong>批量授权编号：</strong>{detailData.batchAuthNo}</p>
                <p><strong>导入文件名称：</strong>{detailData.fileName}</p>
                <p><strong>授权状态：</strong>{detailData.authStatus}</p>
                <p><strong>登记人：</strong>{detailData.operatorName}</p>
                <p><strong>登记机构：</strong>{detailData.ownOrganName}</p>
                <p><strong>登记时间：</strong>{detailData.inputTime}</p>
                <p><strong>总记录数：</strong>{detailData.totalCount}</p>
                <p><strong>成功记录数：</strong>{detailData.successCount}</p>
                <p><strong>失败记录数：</strong>{detailData.failCount}</p>
              </div>
            )
          });
        } else {
          YRMessage.error(res?.errorMessage || '查询详情失败');
        }
      })
      .catch((error) => {
        console.error('查询批量授权详情失败:', error);
        // 如果详情接口失败，显示基本信息
        YRModal.info({
          title: '批量授权详情',
          width: 800,
          content: (
            <div>
              <p><strong>批量授权编号：</strong>{record.batchAuthNo}</p>
              <p><strong>导入文件名称：</strong>{record.fileName}</p>
              <p><strong>授权状态：</strong>{record.authStatus}</p>
              <p><strong>登记人：</strong>{record.operatorName}</p>
              <p><strong>登记机构：</strong>{record.ownOrganName}</p>
              <p><strong>登记时间：</strong>{record.inputTime}</p>
              <p><strong>总记录数：</strong>{record.totalCount}</p>
              <p><strong>成功记录数：</strong>{record.successCount}</p>
              <p><strong>失败记录数：</strong>{record.failCount}</p>
            </div>
          )
        });
      });
  };

  // 复核处理
  const handleReview = (params: BatchReviewParams) => {
    if (params.reviewResult === 'PASS') {
      approveRun(params);
    } else {
      rejectRun(params);
    }
  };

  return (
    <YRIndexPageLayout>
      <YRTable
        rowKey="batchAuthId"
        form={form}
        dataSource={dataSource}
        handleSearch={handleSearch}
        loading={queryLoading}
        columns={columns(handleDetail, handleReview, approveLoading || rejectLoading)}
        formItemList={formItemList}
        pagination={{
          ...pagination,
          onChange: handlePaginationChange,
          onShowSizeChange: handlePaginationChange
        }}
        scroll={{
          x: 1200
        }}
      />
    </YRIndexPageLayout>
  );
};

export default PendingBatchAuth;
