/**
 * @Author: wangyw26123
 * @Description: 新增/修改 机构信息
 * @Date: Created in 2022-12-13 20:46:38
 * @Modifed By:
 */
import React, { useEffect, useState } from 'react';
import {
  YRClassificationLayout,
  YRForm,
  YRInput,
  YRDict,
  YRTreeSelect,
  YRPcaSelect,
  YRDatePicker,
  YRTextAreaPlus,
  YRSpace,
  YRCard,
  YRSelect
} from 'yrantd';
import type { FormInstance, YRFormProps } from 'yrantd';
import { jsonParse, regex } from '@yr/util';
import moment from 'moment';
import Dict from '@/pages/Organ/mock/getDict';
import MappingMechanismInfo from './MappingMechanismInfo';

import store from '@/store';
import pcaUilt from '@/utils/pcaUilt';
import { EnumDateFormat } from '@/constant/common';
import { setDict } from '../../../../utils/utils';

/**
 * 根据提供的回调函数过滤机构树
 * @param organTree 机构树
 * @param calllback 回调函数
 */
const filterOrganTree = (organTree, calllback): any[] => {
  let newOrganTree = [];

  if (typeof calllback !== 'function') return newOrganTree;

  const loop = (data, fn) => {
    const list: any = [];

    for (let i = 0; i < data.length; i++) {
      const getItem = fn(data[i]);

      if (!getItem) continue;

      if (data[i].children) {
        const subItem = loop(data[i].children, fn);

        if (subItem.length) {
          getItem.children = subItem;
        }
      }

      list.push(getItem);
    }

    return list;
  };

  newOrganTree = loop(organTree, calllback);

  return newOrganTree;
};

interface OrganInfoEditProps {
  form: FormInstance;
  mode: YRFormProps['mode'];
  parentOrganId: string;
  detail: any;
  /**
   * 复制机构标识
   */
  isCopy: boolean;
}

setDict('EnumBool', [
  {
    itemKey: 'Y',
    itemName: '是'
  },
  {
    itemKey: 'N',
    itemName: '否'
  }
]);

const OrganInfoEdit = (props: OrganInfoEditProps) => {
  const { form, mode, parentOrganId, detail: d, isCopy } = props;
  const list = d.organRelationList || [];
  const detail = d?.organInfo || {};

  const [parentOrganTree, setParentOrganTree] = useState<any>([]);
  const [formDisabled] = useState<boolean>(mode === 'edit');
  const [organState] = store.useModel('organ');
  const { organTreeList: treeData, acctOrganTreeList, hrList } = organState;
  const userInfo = jsonParse(window.sessionStorage.getItem('userInfo') || '') || {};
  useEffect(() => {
    setParentOrganTree(
      filterOrganTree(treeData, (val) => {
        return {
          ...val,
          // 不为启用时禁止选择
          disabled: val.organStatus !== '002'
        };
      })
    );
  }, [treeData]);

  useEffect(() => {
    const { areaCode, ownArea } = detail;
    if (areaCode) {
      setTimeout(() => {
        // 解决查看详情时，反显问题
        form.setFieldValue('pca', pcaUilt.getCodes(areaCode));
      }, 300);
    }

    if (ownArea?.trim()) {
      const codeList = ownArea.split(',');
      setTimeout(() => {
        // 解决查看详情时，反显问题
        form.setFieldValue(
          'ownArea',
          codeList.map((code) => pcaUilt.getCodes(code))
        );
      }, 300);
    }
  }, [detail]);

  const initDate = (value) => {
    if (mode === 'readPretty') {
      return value ? moment(value) : undefined;
    }

    return value ? moment(value) : moment();
  };

  // 机构复制时，相关登记信息(登记人、机构、时间)都应与新增一致
  const initOrganInfo = (value, secondVal) => {
    if (isCopy) return value;
    return secondVal;
  };

  const watchOrganNature = YRForm.useWatch('organNature', form);
  const watchOrganType = YRForm.useWatch('organType', form);
  const acctOrganId = YRForm.useWatch('acctOrganId', form);
  return (
    <YRForm form={form} mode={mode}>
      <YRSpace direction="vertical" block>
        <YRCard>
          <YRClassificationLayout title="机构信息">
            <YRForm.Row>
              {mode !== 'add' && (
                <YRForm.Item
                  name="organId"
                  label="机构编号"
                  initialValue={detail.organId}
                  rules={[
                    { required: true, message: '请输入机构编号' },
                    { pattern: regex.LettersAndNumbers, message: '格式不正确,请输入数字' }
                  ]}
                >
                  <YRInput maxLength={30} disabled placeholder="请输入机构编号" />
                </YRForm.Item>
              )}
              <YRForm.Item
                name="sequence"
                label="排序号"
                initialValue={detail.sequence}
                rules={[
                  { required: true, message: '请输入排序号' },
                  { pattern: regex.naturalNumber, message: '格式不正确' }
                ]}
              >
                <YRInput maxLength={30} placeholder="请输入排序号" />
              </YRForm.Item>
              <YRForm.Item
                name="organName"
                label="机构名称"
                initialValue={detail.organName}
                rules={[{ required: true, message: '请输入机构名称' }]}
              >
                <YRInput maxLength={60} placeholder="请输入机构名称" />
              </YRForm.Item>
              <YRForm.Item name="organShortName" label="机构简称" initialValue={detail.organShortName}>
                <YRInput placeholder="请输入机构简称" maxLength={20} />
              </YRForm.Item>
              <YRForm.Item
                name="organLevel"
                label="级别"
                initialValue={detail.organLevel}
                rules={[{ required: true, message: '请选择级别' }]}
              >
                <YRDict.Select dictkey="EnumOrganLevel" placeholder="请选择级别" />
              </YRForm.Item>
              <YRForm.Item name="bizLine" label="所属条线" initialValue={detail.bizLine}>
                <YRDict.Select dictkey="EnumOrganBizLine" placeholder="请选择级别" />
              </YRForm.Item>
              <YRForm.Item
                name="organType"
                label="机构类型"
                initialValue={detail.organType}
                rules={[{ required: true, message: '请选择机构类型' }]}
              >
                <YRDict.Select dictkey="EnumOrganType" placeholder="请选择机构类型" />
              </YRForm.Item>
              <YRForm.Item
                name="organNature"
                label="机构性质"
                initialValue={detail.organNature}
                rules={[{ required: true, message: '请选择机构性质' }]}
              >
                <YRDict.Select dictkey="EnumOrganNature" placeholder="请选择机构性质" />
              </YRForm.Item>
              {watchOrganNature === '001' && (
                <YRForm.Item name="loanOrganId" label="所属放款中心" initialValue={detail.loanOrganId}>
                  <YRTreeSelect
                    showSearch
                    allowClear
                    treeData={parentOrganTree}
                    treeNodeFilterProp="organName"
                    fieldNames={{ label: 'organName', value: 'organId' }}
                    placeholder="请选择所属放款中心"
                  />
                </YRForm.Item>
              )}
              <YRForm.Item
                name="archiveMgrOrganId"
                label="档案管理机构"
                initialValue={detail.archiveMgrOrganId || '本机构'}
                rules={[{ required: true, message: '请选择档案管理机构' }]}
              >
                <YRTreeSelect
                  showSearch
                  treeData={treeData}
                  treeNodeFilterProp="organName"
                  fieldNames={{ label: 'organName', value: 'organId' }}
                  placeholder="请选择档案管理机构"
                />
              </YRForm.Item>
              <YRForm.Item
                name="parentOrganId"
                label="上级机构"
                initialValue={detail.parentOrganId || parentOrganId}
                rules={[{ required: true, message: '请选择上级机构' }]}
              >
                <YRTreeSelect
                  showSearch
                  treeNodeFilterProp="organName"
                  treeData={parentOrganTree}
                  disabled={!!parentOrganId || formDisabled}
                  fieldNames={{ label: 'organName', value: 'organId' }}
                  placeholder="请选择上级机构"
                />
              </YRForm.Item>
              {/*<YRForm.Item
                name="hrOrganId"
                label="对应HR机构"
                initialValue={detail.hrOrganId}
                rules={[{ required: true, message: '请选择上级机构' }]}
              >
                <YRSelect
                  disabled={mode !== 'add'}
                  onSelect={(_, node) => {
                    form.setFieldsValue({
                      hrOrganName: node.acctOrganName
                    });
                  }}
                  showSearch
                  allowClear
                  placeholder="请选择对应核心机构"
                >
                  {(hrList || []).map((item) => (
                    <YRSelect.Option value={item.hrOrganId} key={item.hrOrganId}>
                      {item.hrOrganName}
                    </YRSelect.Option>
                  ))}
                </YRSelect>
              </YRForm.Item>*/}
              <YRForm.Item
                name="acctOrganId"
                label="对应核心机构"
                initialValue={detail.acctOrganId}
                rules={[{ required: watchOrganType === '001' }]}
              >
                <YRTreeSelect
                  onChange={(v) => {
                    form.setFieldValue('pbcOrganCode', v);
                    // run({ acctOrganId: v })
                  }}
                  showSearch
                  allowClear
                  treeData={acctOrganTreeList}
                  treeNodeFilterProp="acctOrganName"
                  fieldNames={{ label: 'acctOrganName', value: 'acctOrganId' }}
                  placeholder="请选择对应核心机构"
                />
              </YRForm.Item>
              <YRForm.Item
                name="pbcOrganCode"
                label="人行金融机构代码"
                initialValue={detail.pbcOrganCode}
                rules={[
                  { required: acctOrganId || watchOrganType === '001', message: '请输入人行金融机构代码' },
                  { pattern: regex.LettersAndNumbers, message: '格式不正确,请输入字母或数字' },
                  {
                    validator(rule, value, callback) {
                      if (value && regex.LettersAndNumbers.test(value) && value?.length !== 14) {
                        callback('请输入14位人行金融机构代码');
                      } else {
                        callback();
                      }
                    }
                  }
                ]}
              >
                <YRInput maxLength={14} placeholder="人行金融机构代码" />
              </YRForm.Item>
              <YRForm.Item
                name="dataPermissionOrgan"
                label="数据权限机构"
                initialValue={detail.dataPermissionOrgan}
                rules={[{ required: true, message: '请选择数据权限机构' }]}
              >
                <YRDict.Select dictkey="EnumBool" placeholder="请选择数据权限机构" />
              </YRForm.Item>
              <YRForm.Item
                name="legalRepresentativeName"
                label="所属法人"
                initialValue={detail.legalRepresentativeName || '苏州银行'}
                rules={[{ required: true, message: '请输入所属法人' }]}
              >
                <YRInput disabled placeholder="请输入所属法人" />
              </YRForm.Item>
              <YRForm.Item name="pca" label="所属行政区划" rules={[{ required: true }]}>
                <YRPcaSelect level="3" />
              </YRForm.Item>
              <YRForm.Item
                name="signOrganId"
                label="合同签约机构"
                initialValue={detail.signOrganId}
                rules={[{ required: true, message: '请输入合同签约机构' }]}
              >
                <YRTreeSelect
                  showSearch
                  treeData={treeData}
                  treeNodeFilterProp="organName"
                  fieldNames={{ label: 'organName', value: 'organId' }}
                  placeholder="请选择合同签约机构"
                />
              </YRForm.Item>
              <YRForm.Item name="businessCert" label="营业执照" initialValue={detail.businessCert}>
                <YRInput maxLength={64} placeholder="请输入营业执照" />
              </YRForm.Item>
              <YRForm.Item name="financialCert" label="金融机构许可证" initialValue={detail.financialCert}>
                <YRInput maxLength={64} placeholder="请输入金融机构许可证" />
              </YRForm.Item>
              <YRForm.Item name="organAddress" label="机构地址" initialValue={detail.organAddress}>
                <YRInput maxLength={60} placeholder="请输入机构地址" />
              </YRForm.Item>
              <YRForm.Item name="ownArea" label="机构辖区">
                <YRPcaSelect level="3" multiple />
              </YRForm.Item>
              <YRForm.Item
                name="zipCode"
                label="邮政编码"
                initialValue={detail.zipCode}
                rules={[
                  { pattern: regex.naturalNumber, message: '格式不正确,请输入数字' },
                  {
                    validator(rule, value, callback) {
                      if (regex.naturalNumber.test(value) && value.length !== 6) {
                        callback('请输入6位邮政编码');
                      } else {
                        callback();
                      }
                    }
                  }
                ]}
              >
                <YRInput maxLength={6} placeholder="请输入邮政编码" />
              </YRForm.Item>
              <YRForm.Item name="organUsedName" label="机构曾用名" initialValue={detail.organUsedName}>
                <YRInput maxLength={60} placeholder="请输入机构曾用名" />
              </YRForm.Item>
              <YRForm.Item
                name="organEstablishDate"
                label="成立时间"
                initialValue={initDate(detail.organEstablishDate)}
              >
                <YRDatePicker format={EnumDateFormat.time} />
              </YRForm.Item>
              <YRForm.Item name="chargePerson" label="负责人" initialValue={detail.chargePerson}>
                <YRInput maxLength={32} placeholder="请输入负责人" />
              </YRForm.Item>
              <YRForm.Item
                name="contactTel"
                label="联系电话"
                initialValue={detail.contactTel}
                rules={[{ pattern: regex.phone, message: '格式不正确' }]}
              >
                <YRInput placeholder="请输入联系电话" />
              </YRForm.Item>
              <YRForm.Item
                name="registerOrganId"
                label="登记机构"
                initialValue={initOrganInfo(userInfo.organId, detail.registerOrganId || userInfo.organId)}
                rules={[{ required: true, message: '请选择登记机构' }]}
              >
                <YRTreeSelect
                  showSearch
                  disabled
                  treeNodeFilterProp="organName"
                  treeData={treeData}
                  fieldNames={{ label: 'organName', value: 'organId' }}
                  placeholder="请选择登记机构"
                />
              </YRForm.Item>
              <YRForm.Item
                name="registerUserName"
                label="登记人"
                initialValue={initOrganInfo(userInfo.operatorName, detail.registerUserName || userInfo.operatorName)}
                rules={[{ required: true, message: '请输入登记人' }]}
              >
                <YRInput disabled placeholder="请输入登记人" />
              </YRForm.Item>
              <YRForm.Item
                name="registerDate"
                label="登记日期"
                initialValue={initOrganInfo(moment(), initDate(detail.registerDate))}
                rules={[{ required: true, message: '请选择登记日期' }]}
              >
                <YRDatePicker disabled placeholder="请选择登记日期" format={EnumDateFormat.date} />
              </YRForm.Item>
              <YRForm.Item
                name="updateOrganId"
                label="更新机构"
                initialValue={initOrganInfo(userInfo.organId, detail.updateOrganId || userInfo.organId)}
                rules={[{ required: true, message: '请选择更新机构' }]}
              >
                <YRTreeSelect
                  showSearch
                  disabled
                  treeNodeFilterProp="organName"
                  treeData={treeData}
                  fieldNames={{ label: 'organName', value: 'organId' }}
                  placeholder="请选择更新机构"
                />
              </YRForm.Item>
              <YRForm.Item
                name="updateUserName"
                label="更新人"
                initialValue={initOrganInfo(userInfo.operatorName, detail.updateUserName || userInfo.operatorName)}
                rules={[{ required: true, message: '请输入更新人' }]}
              >
                <YRInput disabled placeholder="请输入更新人" />
              </YRForm.Item>
              <YRForm.Item
                name="updateDate"
                label="更新日期"
                initialValue={mode === 'edit' ? moment() : initOrganInfo(moment(), initDate(detail.updateDate))}
                rules={[{ required: true, message: '请选择更新日期' }]}
              >
                <YRDatePicker disabled placeholder="请选择更新日期" format={EnumDateFormat.date} />
              </YRForm.Item>
              <YRForm.Item name="remark" label="备注" initialValue={detail.remark}>
                <YRTextAreaPlus maxLength={255} placeholder="请输入备注" />
              </YRForm.Item>

              {/* 隐藏域 */}
              <YRForm.Item
                name="registerUserId"
                label="登记人"
                style={{ display: 'none' }}
                initialValue={initOrganInfo(userInfo.operatorId, detail.registerUserId || userInfo.operatorId)}
              >
                <YRInput disabled placeholder="请输入登记人" />
              </YRForm.Item>
              <YRForm.Item
                name="updateUserId"
                label="更新人"
                style={{ display: 'none' }}
                initialValue={initOrganInfo(userInfo.operatorId, detail.updateUserId || userInfo.operatorId)}
              >
                <YRInput disabled placeholder="请输入更新人" />
              </YRForm.Item>
            </YRForm.Row>
          </YRClassificationLayout>
        </YRCard>
        {/*<YRCard>
          <MappingMechanismInfo mode={mode} list={list} />
        </YRCard>*/}
      </YRSpace>
    </YRForm>
  );
};

export default Dict(['对应HR机构', '映射机构类型'])(OrganInfoEdit);
