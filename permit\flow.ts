/**
 * 功能权限
 */
import { YRLoanPermit } from 'yr-loan-antd';
import packageInfo from '../package.json';
import {
  selectPageForMaxVersion,
  addFlowDiagramResource,
  deleteFlowDiagramResource,
  queryFlowDiagramResourceDetail,
  modifyFlowDiagramResource,
  deployProcess,
  queryFlowApprovalStationList,
  queryFlowDiagramResourceVersion
} from '@/services/flow';
import { queryUserList } from '@/services/setting';

const { buildPermit, EnumPermitType } = YRLoanPermit;

/**
 * 功能权限
 */
const M02 = buildPermit('M02', packageInfo, {
  interfaces: {
    selectPageForMaxVersion,
    queryFlowDiagramResourceDetail,
    queryFlowApprovalStationList,
    queryUserList
  },
  E01: {
    name: '新增流程图',
    path: '/hsjry/flow-designer/v1/flowModelResourceQuery/selectPageForMaxVersion',
    type: EnumPermitType.Element,
    interfaces: {
      addFlowDiagramResource
    }
  },
  E02: {
    name: '删除流程图',
    path: '/hsjry/flow-designer/v1/flowDiagramResource/deleteFlowDiagramResource',
    type: EnumPermitType.Element,
    interfaces: {
      deleteFlowDiagramResource
    }
  },
  E03: {
    name: '更新流程图',
    path: '/hsjry/flow-designer/v1/flowDiagramResource/modifyFlowDiagramResource',
    type: EnumPermitType.Element,
    interfaces: {
      modifyFlowDiagramResource,
      addFlowDiagramResource
    }
  },
  E04: {
    name: '部署流程图',
    path: '/hsjry/flow-designer/v1/manage-process/deployProcess',
    type: EnumPermitType.Element,
    interfaces: {
      deployProcess
    }
  },
  E05: {
    name: '查询流程版本',
    path: '/hsjry/flow-designer/v1/flowDiagramResourceQuery/queryFlowDiagramResourceVersion',
    type: EnumPermitType.Element,
    interfaces: {
      queryFlowDiagramResourceVersion
    }
  }
});

const M0201 = buildPermit('M0201', packageInfo, {
  interfaces: {
    selectPageForMaxVersion,
    queryFlowDiagramResourceDetail,
    queryFlowApprovalStationList,
    queryUserList
  },
  E01: {
    name: '新增流程图',
    path: '/hsjry/flow-designer/v1/flowModelResourceQuery/selectPageForMaxVersion',
    type: EnumPermitType.Element,
    interfaces: {
      addFlowDiagramResource
    }
  },
  E02: {
    name: '删除流程图',
    path: '/hsjry/flow-designer/v1/flowDiagramResource/deleteFlowDiagramResource',
    type: EnumPermitType.Element,
    interfaces: {
      deleteFlowDiagramResource
    }
  },
  E03: {
    name: '更新流程图',
    path: '/hsjry/flow-designer/v1/flowDiagramResource/modifyFlowDiagramResource',
    type: EnumPermitType.Element,
    interfaces: {
      modifyFlowDiagramResource,
      addFlowDiagramResource
    }
  },
  E04: {
    name: '部署流程图',
    path: '/hsjry/flow-designer/v1/manage-process/deployProcess',
    type: EnumPermitType.Element,
    interfaces: {
      deployProcess
    }
  },
  E05: {
    name: '查询流程版本',
    path: '/hsjry/flow-designer/v1/flowDiagramResourceQuery/queryFlowDiagramResourceVersion',
    type: EnumPermitType.Element,
    interfaces: {
      queryFlowDiagramResourceVersion
    }
  }
});

const M0202 = buildPermit('M0202', packageInfo, {
  interfaces: {}
});
const M0203 = buildPermit('M0203', packageInfo, {
  interfaces: {}
});

export { M02, M0201, M0202, M0203 };
