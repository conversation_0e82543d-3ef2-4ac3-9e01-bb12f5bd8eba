/**
 * 页面描述:连接组件
 * @文件名 Join.tsx
 * @filePath \src\pages\Setting\RuleManager\Entry\components\Join.tsx
 * @Date 2023-08-08 14:32:01
 * @Email <EMAIL>
 * <AUTHOR>
 */
import React, { useEffect, useState } from 'react';
import { useUnmount } from 'ahooks';
import { YRMenu, YRDropdown, YRMessage, YRIcon } from 'yrantd';
import { uuid } from '@yr/util';
import store from '@/store';
import { defaultLeft, defaultSpace, defaultWidth2, defaultTop, defaultHeight, defaultTopSpace } from '../config';
import style from '../index.module.less';

const SVG_NS = 'http://www.w3.org/2000/svg';

const Join = (props) => {
  const { level, number, parentNumber, svgId = 'ifSvg', id, addItem, isSingle, parentId, removeItem } = props;
  const [ruleState, ruleDispatch] = store.useModel('rule');
  const { type, ruleDataMap } = ruleState;
  const itemData = ruleDataMap[id] || {};
  const [path, setPath] = useState(null) as any;
  const left = (level - 1) * defaultSpace + defaultLeft - 44;

  useEffect(() => {
    if (number > 1) {
      draw(level, parentNumber, number);
    }
  }, [level, parentNumber, number]);

  useUnmount(() => {
    const svg = document.getElementById(`${svgId}`);
    if (path && path.parentNode === svg) {
      svg && svg.removeChild && svg.removeChild(path);
    }
  });

  // 绘制路径
  const draw = (drawLevel, drawParentNumber, drawNumber) => {
    const start = {
      x: (drawLevel - 2) * defaultSpace + defaultWidth2 + defaultLeft,
      y: (drawParentNumber - 1) * defaultHeight + defaultTop + defaultTopSpace
    };
    const end = {
      x: (drawLevel - 1) * defaultSpace + defaultLeft,
      y: (drawNumber - 1) * defaultHeight + defaultHeight / 2 + defaultTopSpace
    };
    const pos = getPos(start, end);
    if (path === null) {
      const newPath = document.createElementNS(SVG_NS, 'path');
      newPath.setAttribute('fill', 'none');
      newPath.setAttribute('stroke-width', '2px');
      newPath.setAttribute('stroke', 'rgba(207, 219, 230, 1)');
      const svgDom = document.getElementById(`${svgId}`);
      svgDom && svgDom.appendChild(newPath);
      newPath.setAttribute('d', pos);
      setPath(newPath);
    } else {
      path.setAttribute('d', pos);
    }
  };

  // 获取连线坐标
  const getPos = (start, end) => {
    return `M${start.x},${start.y} C${start.x},${end.y} ${start.x},${end.y} ${end.x},${end.y}`;
  };

  // 改变类型
  const changeType = (logicType) => {
    if (itemData?.junctionType === logicType) return;
    const newData = {
      ...(ruleDataMap || {}),
      [id]: {
        ...(itemData || {}),
        junctionType: logicType
      }
    };
    ruleDispatch.setRuleDataMap(newData);
  };

  // 增加条件
  const add = (status) => {
    let obj;
    // 生成唯一标识
    const key = uuid();
    if (status) {
      obj = {
        key,
        junctionType: 'and'
      };
    } else {
      obj = {
        key,
        leftVariableRuleDto: {},
        op: ''
      };
    }
    // 新增节点
    addItem(obj, id);
    // 节点映射新增
    const newData = {
      ...(ruleDataMap || {}),
      [key]: obj
    };
    ruleDispatch.setRuleDataMap(newData);
  };

  // 删除节点
  const remove = () => {
    if (parentId === 'root') return;
    if (!isSingle) {
      YRMessage.error('请选删除子节点');
      return;
    }
    removeItem(parentId, id);
  };

  const itemMenu = (
    <YRMenu>
      <YRMenu.Item onClick={() => changeType('and')}>并且</YRMenu.Item>
      <YRMenu.Item onClick={() => changeType('or')}>或者</YRMenu.Item>
    </YRMenu>
  );
  const addMenu = (
    <YRMenu>
      <YRMenu.Item onClick={() => add(false)}>添加条件</YRMenu.Item>
      <YRMenu.Item onClick={() => add(true)}>添加联合条件</YRMenu.Item>
    </YRMenu>
  );

  return (
    <div className={style['if-item']}>
      <span className={style['if-item-rownum']}>{number}</span>
      <span style={{ paddingLeft: left }}>
        <span className={style['ant-btn-sm-span']}>
          <YRDropdown disabled={type === 'detail'} overlay={itemMenu} placement="bottomLeft">
            <span>{itemData.junctionType === 'and' ? '并且' : '或者'}</span>
          </YRDropdown>
          <YRDropdown disabled={type === 'detail'} overlay={addMenu} placement="bottomLeft">
            <YRIcon style={{ paddingLeft: '5px' }} icon="yunrongicon_add" fontSize={'14px'} />
          </YRDropdown>
        </span>
      </span>
      {type !== 'detail' && (
        <span className={style['if-item-remove']} onClick={remove}>
          <YRIcon icon="yunrongXXclose" fontSize={'14px'} />
        </span>
      )}
    </div>
  );
};

export default Join;
