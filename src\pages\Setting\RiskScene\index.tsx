/*
 * @Author: ducf
 * @E-mail: <EMAIL>
 * @Date: 2023-05-10 16:56:08
 * @Description: 风险探测场景配置查询列表
 */
import { useAntdTable } from 'ahooks';
import React from 'react';
import { deleteFlowDiagramResource } from '@/services/flow';
import {
  FormItemListProps,
  YRTableProps,
  YRButton,
  YRForm,
  YRIndexPageLayout,
  YRTable,
  YRDict,
  YRTooltip
} from 'yrantd';
import { openNewTab } from '@/utils/utils';
import { queryProbeScene } from '@/services/setting';
import { Dict } from '@yr/util';

interface Props {}

const RiskScene: React.FC<Props> = (props) => {
  const [form] = YRForm.useForm();

  const { tableProps, search, run } = useAntdTable(
    (p) => {
      return queryProbeScene({
        ...p,
        pageNum: p?.current
      })
        .then((res) => {
          return { list: res?.data?.list, total: res?.data?.total || 0 };
        })
        .catch(() => {});
    },
    {
      defaultParams: [
        {
          pageNum: 1,
          pageSize: 10,
          approveStatus: 0
        }
      ]
    }
  );

  const formItemList: FormItemListProps[] = [
    {
      placeholder: '场景名称',
      key: 'sceneName',
      type: 'input',
      position: 'show'
    }
  ];

  const onDel = (row) => {
    deleteFlowDiagramResource({ id: row?.id }).then((res) => {
      if (res?.success) {
        run({ current: 1, pageSize: 10 });
      }
    });
  };
  const getTableColumns: YRTableProps['columns'] = [
    {
      title: '场景名称',
      dataIndex: 'sceneName'
      // render: (value, row) => {
      //   if (value) {
      //     return (
      //       <YRLink type="primary" onClick={() => toPage('readPretty', row.id)}>
      //         {value || '4'}
      //       </YRLink>
      //     );
      //   }
      //   return CONST.null;
      // }
    },
    {
      title: '场景描述',
      dataIndex: 'sceneDesc'
    }
  ];

  const expandedRowRender = (record) => {
    const expandableColumns = [
      {
        title: '探测项',
        dataIndex: 'probeItemName',
        width: 200,
        fixed: 'left',
        render: (value, row, index) => {
          return (
            <YRTooltip onlyShow title={value}>
              <div className="ellipsis">{value}</div>
            </YRTooltip>
          );
        }
      },
      {
        title: '探测申请阶段',
        dataIndex: 'applyStage',
        children: [
          {
            title: '发起',
            dataIndex: ['applyStage', 'start'],
            width: 120,
            render: (value, row, index) => {
              return (
                <YRDict.Select
                  defaultValue={value || '4'}
                  style={{ width: '100%' }}
                  dictkey="PROBE_HIT_POLICY"
                  disabled
                />
              );
            }
          },
          {
            title: '认领',
            dataIndex: ['applyStage', 'accept'],
            width: 120,
            render: (value, row, index) => {
              return (
                <YRDict.Select
                  defaultValue={value || '4'}
                  style={{ width: '100%' }}
                  dictkey="PROBE_HIT_POLICY"
                  disabled
                />
              );
            }
          },
          {
            title: '受理',
            dataIndex: ['applyStage', 'claim'],
            width: 120,
            render: (value, row, index) => {
              return (
                <YRDict.Select
                  defaultValue={value || '4'}
                  style={{ width: '100%' }}
                  dictkey="PROBE_HIT_POLICY"
                  disabled
                />
              );
            }
          },
          {
            title: '提交',
            dataIndex: ['applyStage', 'submit'],
            width: 120,
            render: (value, row, index) => {
              return (
                <YRDict.Select
                  defaultValue={value || '4'}
                  style={{ width: '100%' }}
                  dictkey="PROBE_HIT_POLICY"
                  disabled
                />
              );
            }
          },
          {
            title: '审查',
            dataIndex: ['applyStage', 'check'],
            width: 120,
            render: (value, row, index) => {
              return (
                <YRDict.Select
                  defaultValue={value || '4'}
                  style={{ width: '100%' }}
                  dictkey="PROBE_HIT_POLICY"
                  disabled
                />
              );
            }
          },
          {
            title: '审批',
            dataIndex: ['applyStage', 'checkup'],
            width: 120,
            render: (value, row, index) => {
              return (
                <YRDict.Select
                  defaultValue={value || '4'}
                  style={{ width: '100%' }}
                  dictkey="PROBE_HIT_POLICY"
                  disabled
                />
              );
            }
          }
        ]
      },
      {
        title: '探测出账阶段',
        dataIndex: 'accountStage',
        children: [
          {
            title: '发起',
            dataIndex: ['accountStage', 'start'],
            width: 120,
            render: (value, row, index) => {
              return (
                <YRDict.Select
                  defaultValue={value || '4'}
                  style={{ width: '100%' }}
                  dictkey="PROBE_HIT_POLICY"
                  disabled
                />
              );
            }
          },
          {
            title: '提交',
            dataIndex: ['accountStage', 'submit'],
            width: 120,
            render: (value, row, index) => {
              return (
                <YRDict.Select
                  defaultValue={value || '4'}
                  style={{ width: '100%' }}
                  dictkey="PROBE_HIT_POLICY"
                  disabled
                />
              );
            }
          },
          {
            title: '审查',
            dataIndex: ['accountStage', 'check'],
            width: 120,
            render: (value, row, index) => {
              return (
                <YRDict.Select
                  defaultValue={value || '4'}
                  style={{ width: '100%' }}
                  dictkey="PROBE_HIT_POLICY"
                  disabled
                />
              );
            }
          },
          {
            title: '审批',
            dataIndex: ['accountStage', 'checkup'],
            width: 120,
            render: (value, row, index) => {
              return (
                <YRDict.Select
                  defaultValue={value || '4'}
                  style={{ width: '100%' }}
                  dictkey="PROBE_HIT_POLICY"
                  disabled
                />
              );
            }
          }
        ]
      }
    ];
    return (
      <YRTable
        bordered
        rowKey="probeItemId"
        columns={expandableColumns}
        pagination={false}
        dataSource={record?.probeSceneItemRelaInfoDtos || []}
      />
    );
  };

  const toPage = (type: string, id?: string) => {
    openNewTab({
      pathname: `/decision-page/${type}`,
      query: { id }
    });
  };

  const renderExtAction = (
    <YRButton type="primary" onClick={() => toPage('add')}>
      新增
    </YRButton>
  );

  return (
    <YRIndexPageLayout>
      <YRTable
        scroll={{ y: undefined, x: '1000px' }}
        form={form}
        rowKey="id"
        columns={getTableColumns}
        formItemList={formItemList}
        // extAction={renderExtAction}
        handleSearch={(reqParams) => {
          run({ ...reqParams, current: 1, pageSize: 10 });
        }}
        expandable={{ expandedRowRender, defaultExpandedRowKeys: ['0'] }}
        // onRow={(row) => {
        //   return {
        //     onDoubleClick: () => toDetail(row)
        //   };
        // }}
        {...tableProps}
      />
    </YRIndexPageLayout>
  );
};

export default Dict(['PROBE_HIT_POLICY', 'PROBE_ACCOUNT_STAGE'])(RiskScene);
