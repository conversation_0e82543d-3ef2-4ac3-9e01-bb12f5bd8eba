/**
 * @页面描述: 柜员管理列表
 * @文件名 index.tsx
 * @Path src\pages\User\CounterEmployee\index.tsx
 * @Date 2023-03-15 10:08:04
 * <AUTHOR>
 */

import React, { useMemo } from 'react';
import { YREasyUseModal, YRFlexPageLayout } from 'yrantd';
import { useRequest } from 'ahooks';
import { queryUserOrganTree } from '../../../services/setting';
import { DefaultNode, KeyEnum, TreeNode } from './Interface';
import { AntdContextProvider } from './Context';
import OrganizationTree from './OrganizationTree';
import TellerUserList from './TellerUserList';

function Index() {
  type AntdContextProviderValue = React.ComponentProps<typeof AntdContextProvider>['value'];
  const [selectedKey, setSelectedKey] = React.useState<Partial<TreeNode>>(DefaultNode);

  const antdTreeState = useRequest<TreeNode[], any>(
    (p) => queryUserOrganTree(p).then((res) => (res.success ? [res.data.organTreeDto] : [])),
    {
      defaultParams: [{ current: 1, pageSize: 10, pageNum: 1 }]
    }
  );

  const memoProviderValue = useMemo<AntdContextProviderValue>(
    () => ({
      antdTreeProps: { ...antdTreeState },
      selectedState: [selectedKey, setSelectedKey]
    }),
    [selectedKey, setSelectedKey, antdTreeState]
  );

  return (
    <YREasyUseModal.Provider>
      <YRFlexPageLayout>
        <AntdContextProvider value={memoProviderValue}>
          <YRFlexPageLayout.Sider title="机构列表">
            <OrganizationTree />
          </YRFlexPageLayout.Sider>
          <YRFlexPageLayout.Main
            breadcrumb={{
              items: [DefaultNode, ...(selectedKey[KeyEnum.KEY] !== DefaultNode[KeyEnum.KEY] ? [selectedKey] : [])],
              fieldNames: { label: KeyEnum.LABEL, key: KeyEnum.KEY },
              disabled: (item) => item === selectedKey,
              onClick: (item) => {
                setSelectedKey(item);
              }
            }}
          >
            <TellerUserList />
          </YRFlexPageLayout.Main>
        </AntdContextProvider>
      </YRFlexPageLayout>
    </YREasyUseModal.Provider>
  );
}

export default Index;
