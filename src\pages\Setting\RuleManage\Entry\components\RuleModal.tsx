/**
 * 页面描述: 规则条目配置弹窗
 * @文件名 RuleModal.tsx
 * @filePath \src\pages\Setting\RuleManage\Entry\components\RuleModal.tsx
 * @Date 2023-08-30 10:11:20
 * @Email <EMAIL>
 * <AUTHOR>
 */
import React, { useState, useEffect, useMemo } from 'react';
import {
  YRClassificationLayout,
  YREasyUseModal,
  YRForm,
  YRModal,
  YRInput,
  YRMenu,
  YRTextAreaPlus,
  YRDropdown,
  YRButton,
  YRMessage,
  YRInputNumber
} from 'yrantd';
import store from '@/store';
import { useRequest } from 'ahooks';
import RuleContext from './RuleContext';
import { saveOrModifyRuleItem } from '@/services/setting';
import { getRuleData, formatRuleData } from '../util/helper';

const titleOption = {
  add: '新增',
  edit: '修改',
  detail: '查看',
  copy: '复制'
};

const RuleModal = (props) => {
  const { defaultData, refresh, type } = props;
  const modal = YREasyUseModal.useModal();
  const [form] = YRForm.useForm();
  const [ruleState, ruleDispatch] = store.useModel('rule');
  const { ruleData, ruleDataMap, parameterList } = ruleState;
  const [menuVisible, setMenuVisible] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const hitReminder = YRForm.useWatch('hitReminder', form) || '';

  // 保存规则条目
  const { run: submitRun, loading: submitLoading } = useRequest(saveOrModifyRuleItem, {
    manual: true,
    onSuccess: (result, params: any) => {
      const { errorMessage } = result as any;
      if (errorMessage === null) {
        YRMessage.success('保存成功');
        modal.remove();
        refresh();
      }
    }
  });

  useEffect(() => {
    const newDefaultData = { ...defaultData };
    const treeId = newDefaultData?.treeId || '';
    const express = newDefaultData?.express ? JSON.parse(newDefaultData?.express) : '';
    const { treeData, treeDataMap } = getRuleData(express);
    if (treeId) {
      if (type === 'detail') {
        newDefaultData.version = `${newDefaultData?.version}.0`;
      }
      form.setFieldsValue(newDefaultData);
      ruleDispatch.setType(type);
      ruleDispatch.setRuleData(treeData);
      ruleDispatch.setRuleDataMap(treeDataMap);
      ruleDispatch.getDictList();
      ruleDispatch.getParameterList({ treeId, pageNum: 1, pageSize: 9999 });
    }
  }, [defaultData, type]);

  // 根据搜索框筛选可选参数
  const filterParameterList = useMemo(() => {
    return searchValue !== ''
      ? parameterList.filter((item: any) => item.variableName.includes(searchValue)).slice(0, 150)
      : parameterList;
  }, [parameterList, searchValue]);

  // 选择参数
  const selectMenu = ({ item }) => {
    if (!item || !item?.props) return;
    // 选中项
    const data = item.props?.data || {};
    const variableCode = data?.variableCode || '';
    const newHitReminder = `${hitReminder}\${${variableCode}}`;
    setMenuVisible(false);
    form.setFieldValue('hitReminder', newHitReminder);
  };

  const parameterMenu = (
    <>
      <YRInput.Search
        placeholder="最多显示150条,输入值查询"
        style={{
          width: '100%',
          padding: '5px 7px',
          position: 'sticky',
          top: 0,
          background: '#fff',
          boxShadow:
            '0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)'
        }}
        onSearch={(value) => setSearchValue(value)}
      />
      <YRMenu
        onClick={selectMenu}
        className="parameter-value parameter-value-max"
        style={{ maxHeight: 300, overflowY: 'scroll' }}
      >
        {filterParameterList.map((item: any) => {
          const valueIndex = item.variableName.indexOf(searchValue);
          const beforeStr = item.variableName.substr(0, valueIndex);
          const afterStr = item.variableName.substr(valueIndex + searchValue.length);
          const title =
            searchValue && valueIndex > -1 ? (
              <span>
                {beforeStr}
                <span style={{ color: '#f50' }}>{searchValue}</span>
                {afterStr}
              </span>
            ) : (
              item.variableName
            );
          return (
            <YRMenu.Item
              key={item.variableId}
              data={{ variableCode: item?.variableCode || '', variableName: item?.variableName || '' }}
            >
              {title}
            </YRMenu.Item>
          );
        })}
      </YRMenu>
    </>
  );

  // 保存
  const submit = () => {
    const newData = formatRuleData(ruleData, ruleDataMap);
    if (!newData) {
      return;
    }
    form.validateFields().then((value) => {
      const params = {
        ...defaultData,
        ...(value || {}),
        express: JSON.stringify(newData)
      };
      submitRun(params);
    });
  };

  return (
    <YRModal
      title={`${titleOption[type]}规则条目`}
      onOk={submit}
      onCancel={modal.remove}
      footer={
        type === 'detail'
          ? null
          : [
            <YRButton key="cancel" onClick={() => modal.remove()}>
              取消
            </YRButton>,
            <YRButton key="confirm" type="primary" loading={submitLoading} onClick={submit}>
              确定
            </YRButton>
          ]
      }
      open={modal.visible}
      destroyOnClose
      maskClosable={false}
      width={'80%'}
    >
      <YRForm form={form} mode={type === 'detail' ? 'readPretty' : 'edit'}>
        <YRClassificationLayout.Space>
          <YRClassificationLayout title="基本信息">
            <YRForm.Item name="treeId" label="树编号" hidden>
              <YRInput />
            </YRForm.Item>
            <YRForm.Item name="treeName" label="树名称" hidden>
              <YRInput />
            </YRForm.Item>
            <YRForm.Row>
              {!['add', 'copy'].includes(type) && (
                <YRForm.Item name="itemId" label="规则条目编号">
                  <YRInput disabled />
                </YRForm.Item>
              )}
              <YRForm.Item name="version" label="规则条目版本">
                <YRInputNumber style={{ width: '100%' }} precision={1} disabled />
              </YRForm.Item>
              <YRForm.Item
                name="itemName"
                label="规则条目名称"
                rules={[
                  { required: true },
                  {
                    max: 128,
                    message: '长度不能超过128字节!'
                  }
                ]}
              >
                <YRInput />
              </YRForm.Item>
              <YRForm.Item
                name="description"
                label="规则条目描述"
                rules={[
                  { required: true },
                  {
                    max: 1000,
                    message: '长度不能超过1000字节!'
                  }
                ]}
                column={'block'}
                labelCol={{ span: 4 }}
                wrapperCol={{ span: 20 }}
              >
                <YRTextAreaPlus maxLength={1000} />
              </YRForm.Item>
            </YRForm.Row>
          </YRClassificationLayout>
          <YRClassificationLayout title="规则脚本">
            <YRClassificationLayout type="secondary" title="条件">
              <RuleContext />
            </YRClassificationLayout>
            <YRClassificationLayout type="secondary" title="命中提示">
              {type !== 'detail' && (
                <YRDropdown
                  overlay={parameterMenu}
                  trigger={['click']}
                  placement="bottomLeft"
                  visible={menuVisible}
                  onVisibleChange={(value) => setMenuVisible(value)}
                >
                  <YRButton type="primary">添加参数</YRButton>
                </YRDropdown>
              )}

              <YRForm.Item
                name="hitReminder"
                rules={[{ required: true, message: '请输入命中提示' }]}
                column={'block'}
                labelCol={{ span: 0 }}
                wrapperCol={{ span: 24 }}
              >
                <YRTextAreaPlus maxLength={300} />
              </YRForm.Item>
            </YRClassificationLayout>
          </YRClassificationLayout>
        </YRClassificationLayout.Space>
      </YRForm>
    </YRModal>
  );
};

export default YREasyUseModal.create(RuleModal);
