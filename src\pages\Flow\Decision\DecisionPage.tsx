/*
 * @Author: ducf
 * @E-mail: <EMAIL>
 * @Date: 2023-05-05 20:26:39
 * @Description: 决策表配置页面
 */
import { MinusCircleOutlined, MinusSquareOutlined, PlusCircleOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
import {
  YRButton,
  YRDatePicker,
  YRDict,
  YREasyUseModal,
  YRForm,
  YRFullPageLayout,
  YRFullPageLayoutProps,
  YRIndexPageLayout,
  YRInput,
  YRInputNumber,
  YRLink,
  YRMessage,
  YRSelect,
  YRSpace,
  YRTable
} from 'yrantd';
import EditColumnsLIstModal from './components/EditColumnsLIstModal';
import {
  addFlowDiagramResource,
  addFlowParamLibraryRef,
  deployDmn,
  modifyFlowDiagramResource,
  queryFlowDiagramResourceDetail,
  selectListByParamLibraryId
} from '@/services/flow';
import { closeWin, uuid } from '@yr/util';
import { useDebounceEffect, useRequest, useSetState } from 'ahooks';
import { useLocation, useParams } from 'ice';
import { router } from '@/utils/utils';
import styles from './index.module.less';
import { M02 } from '@permit/flow';
import BandingParamLIbraryModal from '../XFlow/XFlowEdit/CanvasAttrPanelForm/GlobalConfig/ParamsLibrary/BandingParamLIbraryModal';

interface Props {}
const InInputExpressions = [
  {
    variableId: 'none',
    variableType: null,
    type: 'none',
    label: 'New Input',
    entries: [],
    newVariable: false,
    complexExpression: false
  }
];
const INOutputExpressions = [
  {
    variableId: 'none',
    variableType: null,
    type: 'none',
    label: 'New Output',
    entries: [],
    newVariable: false,
    complexExpression: false
  }
];
const InRules = [
  {
    2: '2',
    '1_operator': '==',
    '1_expression': '总行'
  }
];

const typeDict = [
  { key: 'string', value: 'string' },
  { key: 'number', value: 'number' },
  { key: 'boolean', value: 'boolean' },
  { key: 'date', value: 'date' },
  { key: 'collection', value: 'collection' }
];
const HITDict = [
  { key: 'First', value: '第一条' },
  { key: 'Any', value: '任意一条' },
  { key: 'Unique', value: '唯一一条' },
  { key: 'Priority', value: '优先级' },
  { key: 'Rule Order', value: '规则顺序' },
  { key: 'Output Order', value: '输出顺序' },
  { key: 'Collect', value: '收集' }
];
const operatorObj = {
  string: ['==', '!=', 'IS IN', 'IS NOT', 'IN'],
  number: ['==', '!=', '>', '<', '>=', '<=', 'IS IN', 'IS NOT', 'IN'],
  boolean: ['==', '!='],
  none: []
};

const expressionDOM = {
  string: <YRInput style={{ width: '100%' }} />,
  number: <YRInput style={{ width: '100%' }} defaultValue={'-'} />,
  boolean: (
    <YRSelect style={{ width: '100%' }}>
      {['true', 'false'].map((ele) => (
        <YRSelect.Option key={ele} value={ele}>
          {ele}
        </YRSelect.Option>
      ))}
    </YRSelect>
  ),
  date: <YRDatePicker style={{ width: '100%' }} />,
  none: null
};

const DecisionPage: React.FC<any> = (props) => {
  const { history } = props;
  const [inputExpressions, setInputExpressions] = useState([]);
  const [outputExpressions, setOutputExpressions] = useState([]);
  // 当前选择参数库
  const [currentParamList, setCurrentParamList] = useState<any>({});
  // 当前参数库的参数项
  const [paramItemList, setParamItemList] = useState<any>([]);
  const [idx, setIdx] = useState(0);
  const { query } = useLocation();
  const { mode } = useParams();
  const [editRow, setEditRow] = useState({});
  const [rules, setRules] = useState([]);
  const [cloumnsVisible, setCloumnsVisible] = useState(false);
  const [form] = YRForm.useForm();
  const { queryFlowDiagramResourceVersion } = M02.E05.interfaces;
  const _paramId = YRForm.useWatch('paramId', form);
  const _paramName = YRForm.useWatch('paramName', form);

  const [state, setState] = useSetState<any>({
    isDisabled: false,
    btnLoading: false,
    pageLoading: false,
    modelName: '',
    modelNumber: '',
    id: '',
    modelDesc: '',
    sceneType: '',
    status: '',
    updateTime: '',
    version: '',
    modelResource: '',
    graphData: {
      edges: [],
      nodes: []
    }
  });

  // 查询决策表版本
  const { data: versions = [] } = useRequest(
    (params) => {
      return queryFlowDiagramResourceVersion({ modelType: '2', ...params }).then((res) => res.data || {});
    },
    {
      ready: !!state.modelNumber,
      defaultParams: [
        {
          modelNumber: state.modelNumber
        }
      ]
    }
  );

  /** 查询流程表详情 */
  const { data, run, loading } = useRequest((params) =>
    queryFlowDiagramResourceDetail({
      modelType: '2',
      id: query?.id,
      ...params
    }).then((res) => {
      if (res?.success) {
        form.setFieldsValue(res?.data);
        setState(res?.data);
      }

      const _data = JSON.parse(res?.data?.modelResource || '1');
      setInputExpressions(_data?.inputExpressions || InInputExpressions);
      setOutputExpressions(_data?.outputExpressions || INOutputExpressions);
      setRules(_data?.rules || InRules);
      const { hitIndicator, paramId, paramName } = _data || {};
      form.setFieldsValue({
        hitIndicator,
        paramId,
        paramName,
        formRules: _data?.rules || InRules
      });
      if (paramId) {
        selectListRequest({ paramLibraryId: paramId });
      }

      return res?.data || {};
    })
  );

  /** 绑定参数库接口 */
  const {
    loading: bandingLaoding,
    params,
    run: bandingRequest
  } = useRequest(addFlowParamLibraryRef, {
    manual: true,
    onSuccess: (result) => {
      const { errorMessage } = result;
      if (errorMessage === null) {
        YRMessage.success('绑定成功');
        const { paramLibraryId } = params[0];
        selectListRequest({ paramLibraryId });
      }
    }
  });

  /** 查询参数项 */
  const { loading: selectListLaoding, run: selectListRequest } = useRequest(selectListByParamLibraryId, {
    manual: true,
    onSuccess: (result: any) => {
      const { errorMessage } = result;
      if (errorMessage === null) {
        const _data = result?.data;
        setParamItemList(_data);
      }
    }
  });

  const handleChangeVersion = (value) => {
    run({ id: value });
    history.replace({
      pathname: '/decision-page/edit',
      search: `?id=${value}`
    });
  };

  // 选择参数库
  const selectParam = (value) => {
    console.log(data);
    if (value) {
      const { id, libraryName } = value[0];
      const params = {
        paramLibraryId: id,
        relationId: data?.modelNumber,
        relationType: data?.sceneType
      };
      bandingRequest(params);
      setCurrentParamList(value[0]);
      form.setFieldsValue({ paramName: libraryName, paramId: id });
    }
  };

  const extInfo = [
    {
      key: '决策表编号',
      value: data?.modelNumber || CONST.null
    },
    {
      key: '决策表名称',
      value: data?.modelName || CONST.null
    },
    {
      key: '决策表状态',
      value: <YRDict.Select type="text" defaultValue={data?.status} dictkey="flow_diagram_status" /> || CONST.null
    },
    {
      key: '决策表版本',
      value: (
        <YRSelect
          style={{ width: 100 }}
          value={data?.version || CONST.null}
          size="small"
          onChange={handleChangeVersion}
        >
          {versions.map((item) => {
            return <YRSelect.Option value={item?.first}>{item?.second}</YRSelect.Option>;
          })}
        </YRSelect>
      )
    },
    {
      key: '参数库',
      value: (
        <YRInput
          style={{ width: 100 }}
          size="small"
          value={_paramName ?? currentParamList?.libraryName}
          placeholder="请选择参数库"
          onClick={() => {
            YREasyUseModal.show(BandingParamLIbraryModal, {
              onOk: selectParam,
              globalFormData: data,
              currentId: _paramId
            });
          }}
        />
      )
    },
    {
      key: '更新时间',
      value: data?.updateTime || CONST.null
    }
  ];

  const left: YRFullPageLayoutProps['left'] = {
    title: '决策配置',
    extInfo
  };

  // 查看模式隐藏操作按钮
  const showButton = (element) => {
    if (mode === 'readPretty') {
      return null;
    } else {
      return element;
    }
  };

  // 添加规则
  const addRules = () => {
    const index = rules.length + 1;
    const outIndex = inputExpressions.length + 1;
    const newRules = [...rules];
    const newobj = {
      [outIndex]: '-',
      [`${index}_operator`]: '-',
      [`${index}_expression`]: '-'
    };
    newRules.push(newobj);
    setRules(newRules);
    form.setFieldValue('formRules', newRules);
  };

  // 修改对象的key
  const modifyObj = (obj, num, index) => {
    const isInput = index <= inputExpressions.length;
    const keys = Object.keys(obj);
    for (let i = 0; i < keys.length; i++) {
      const k = keys[i];
      if (num > 0) {
        // 修改rules
        if (isInput) {
          const newKey = /^\d+$/.test(k) ? parseInt(k) + num : k; // 如果是数字就加 1，否则不变
          obj[newKey] = obj[k];
          if (newKey !== k) {
            // 如果 key 发生了变化，则删除原始键
            delete obj[k];
          }
        } else {
          const newKey = /^\d+$/.test(k) && index < 1 + parseInt(k) ? parseInt(k) + num : k; // 如果是数字就加 1，否则不变
          obj[newKey] = obj[k];
          if (newKey !== k) {
            // 如果 key 发生了变化，则删除原始键
            delete obj[k];
          }
        }
      } else {
        // 删除输入列
        if (isInput) {
          if (keys[i] === `${index}_expression` || keys[i] === `${index}_operator`) {
            delete obj[k];
          }
        }
        // 删除输出列
        if (!isInput) {
          if (k == index) {
            delete obj[k];
          }
        }
      }
    }

    return obj;
  };
  // 插入列
  const editColumns = (data) => {
    const params = { ...data, variableType: null, newVariable: false, complexExpression: false };
    const isInput = idx < inputExpressions.length;

    const newData = isInput ? [...inputExpressions] : [...outputExpressions];
    if (editRow?.variableId) {
      // 修改
      if (isInput) {
        newData.splice(idx, 1, params);
        setInputExpressions(newData);
      } else {
        newData.splice(idx - inputExpressions.length, 1, params);
        setOutputExpressions(newData);
      }
    } else {
      // 新增
      const _rules = form.getFieldValue('formRules');
      // 输入
      if (isInput) {
        newData.splice(idx + 1, 0, params);
        setInputExpressions(newData);
        // 修改数据
        const newRules = _rules.map((ele) => {
          return modifyObj(ele, 1, idx);
        });
        form.setFieldValue('formRules', newRules);
        setRules(newRules);
      } else {
        // 输出
        newData.splice(idx - inputExpressions.length + 1, 0, params);
        setOutputExpressions(newData);
        // 修改数据
        const newRules = _rules.map((ele) => {
          return modifyObj(ele, 1, idx);
        });
        form.setFieldValue('formRules', newRules);
        setRules(newRules);
      }
    }
    setEditRow({});
    setCloumnsVisible(false);
  };
  // 删除列
  const delColumns = (index) => {
    const isInput = index < inputExpressions.length;
    if (isInput) {
      const dataList = [...inputExpressions];
      dataList.splice(index, 1);
      setInputExpressions(dataList);
    } else {
      const dataList = [...outputExpressions];
      dataList.splice(index - inputExpressions.length, 1);
      setOutputExpressions(dataList);
    }
    // 修改数据
    const _rules = [...rules];
    const newRules = _rules.map((ele) => {
      return modifyObj(ele, -1, index + 1);
    });
    setRules(newRules);
  };
  // 删除规则
  const delRule = (index) => {
    const newRule = [...(form.getFieldValue('formRules') || [])];
    newRule.splice(index, 1);
    setRules(newRule);
    form.setFieldValue('formRules', newRule);
  };

  const columns = () => {
    const columnsData = [...inputExpressions, ...outputExpressions];
    const _columns = columnsData.map((ele: any, index) => {
      const isInput = index < inputExpressions.length;
      const dataList = isInput ? inputExpressions : outputExpressions;
      return {
        title: (
          <div className={styles.tableHeaderBox}>
            <YRSpace
              className={!isInput ? styles['outTableHeader'] : styles['inputTableHeader']}
              style={{ width: '100%', justifyContent: 'space-around' }}
            >
              {dataList.length > 1 &&
                showButton(
                  <a>
                    <MinusCircleOutlined onClick={() => delColumns(index)} style={{ fontSize: '24px' }} />
                  </a>
                )}
              <div style={{ textAlign: 'center' }}>
                <h3>
                  <a
                    onClick={() => {
                      setIdx(index);
                      setCloumnsVisible(true);
                      setEditRow(ele);
                    }}
                  >
                    {ele?.label}
                  </a>
                </h3>
                <p>{ele?.variableId}</p>
                <p>{ele?.type}</p>
              </div>
              {showButton(
                <a>
                  <PlusCircleOutlined
                    onClick={() => {
                      setIdx(index);
                      setCloumnsVisible(true);
                    }}
                    style={{ fontSize: '24px' }}
                  />
                </a>
              )}
            </YRSpace>
          </div>
        ),
        dataIndex: isInput ? `${index + 1}_operator` : index + 1,
        render: (v, r, i) => {
          return (
            <div style={{ width: '100%', display: 'flex' }}>
              {isInput && (
                <YRForm.Item noStyle name={['formRules', i, isInput ? `${index + 1}_operator` : index + 1]}>
                  {ele?.type !== 'none' && (
                    <YRSelect style={{ width: '120px' }}>
                      {operatorObj[ele?.type].map((v) => (
                        <YRSelect.Option key={v} value={v}>
                          {v}
                        </YRSelect.Option>
                      ))}
                    </YRSelect>
                  )}
                </YRForm.Item>
              )}
              <YRForm.Item noStyle name={['formRules', i, isInput ? `${index + 1}_expression` : index + 1]}>
                {expressionDOM[ele.type]}
              </YRForm.Item>
            </div>
          );
        }
      };
    });
    const delColumnsItem = [
      {
        title: '操作',
        key: 'del111',
        width: 80,
        render: (v, r, i) => <YRLink onClick={() => delRule(i)}>删除</YRLink>
      }
    ];
    return mode !== 'readPretty' ? [..._columns, ...delColumnsItem] : _columns;
  };
  // 提交
  const submit = async () => {
    const request = mode === 'add' || state.status === '1' ? addFlowDiagramResource : modifyFlowDiagramResource;
    try {
      const values: any = await form.validateFields();
      console.log(values, 'xxxxxx');
      const _outputExpressions = [...outputExpressions].map((ele, i) => ({
        ...ele,
        id: inputExpressions.length + i + 1
      }));
      const _inputExpressions = [...inputExpressions].map((ele, i) => ({ ...ele, id: i + 1 }));
      const params = {
        modelType: '2',
        ...values,
        modelResource: JSON.stringify({
          hitIndicator: values?.hitIndicator,
          name: values.modelName,
          key: values.modelNumber,
          description: values.modelDesc,
          paramId: values?.paramId,
          paramName: values?.paramName,
          id: uuid(),
          modelVersion: '2',
          inputExpressions: _inputExpressions,
          outputExpressions: _outputExpressions,
          rules: values.formRules
        }),
        id: query?.id
      };
      delete params.formRules;
      delete params.hitIndicator;
      request(params).then((res) => {
        if (res?.success) {
          YRMessage.success('保存成功');
          //   router.push(`/decision-page/edit?newtab=1&id=${res?.data}`);
          location.reload();
        }
      });
    } catch (error) {}
  };

  // 部署
  const deploy = () => {
    deployDmn({ id: query?.id }).then((res) => {
      res?.success && YRMessage.success('部署成功');
      res?.success && location.reload();
    });
  };
  return (
    <YRFullPageLayout
      left={left}
      right={
        <>
          {showButton(
            <YRButton onClick={submit} type="primary">
              {state.status === '0' || mode === 'add' ? '保存' : '保存新版本'}
            </YRButton>
          )}
          <YRButton onClick={deploy} disabled={state.status !== '0'}>
            部署
          </YRButton>
        </>
      }
      loading={loading}
    >
      <YRIndexPageLayout>
        <YRForm mode={mode} labelAlign="left" form={form}>
          <YRForm.Row>
            <YRForm.Item name={'modelName'} label="决策表名称">
              <YRInput disabled={mode !== 'add'} />
            </YRForm.Item>
            <YRForm.Item name={'modelNumber'} label="决策表编号">
              <YRInput disabled={mode !== 'add'} />
            </YRForm.Item>
            <YRForm.Item name={'sceneType'} label="场景类型">
              <YRDict.Select dictkey="flow_scene_type" disabled={mode !== 'add'} />
            </YRForm.Item>
            <YRForm.Item name={'modelDesc'} label="描述">
              <YRInput />
            </YRForm.Item>
            <YRForm.Item name={'paramId'} label="参数库Id" hidden>
              <YRInput />
            </YRForm.Item>
            <YRForm.Item name={'paramName'} label="参数库名称" hidden>
              <YRInput />
            </YRForm.Item>
            <YRForm.Item name={'hitIndicator'} label="命中策略">
              <YRSelect>
                {HITDict.map((ele) => (
                  <YRSelect.Option key={ele.key} value={ele.key}>
                    {ele.value}
                  </YRSelect.Option>
                ))}
              </YRSelect>
            </YRForm.Item>
          </YRForm.Row>
          <YRTable
            className={styles['table']}
            size="small"
            pagination={false}
            bordered
            columns={columns()}
            dataSource={rules}
          />
        </YRForm>
        {showButton(
          <YRButton style={{ width: '100%' }} type="dashed" onClick={addRules}>
            新增
          </YRButton>
        )}
      </YRIndexPageLayout>
      <EditColumnsLIstModal
        typeDict={typeDict}
        onCancel={() => {
          setCloumnsVisible(false);
        }}
        onOk={(data) => {
          editColumns(data);
        }}
        paramItemList={paramItemList}
        visible={cloumnsVisible}
        editRow={editRow}
      />
    </YRFullPageLayout>
  );
};

export default DecisionPage;
