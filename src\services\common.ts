/**
 * 公共api(不需要校验权限的接口)
 */
import { request } from '@yr/util';

const oldMdata = { cname: 'old数据中心', prefix: '/hsjry/old/mdata' };

export function upload(file) {
  let param = file;
  if (file instanceof File) {
    param = new FormData();
    param.append('file', file);
  }
  return request('/v1/uploadFile/upLoad', {
    param,
    module: MODULES.admin,
    serviceScene: SCENES.apply,
    serviceName: '文件上传'
  });
}

/* -------------------------------------- 新的公用的-----------------------------  */

export function userQueryMyPermit(param?: any) {
  return request('/base/IPermitQuery/queryMyPermitList', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '查询当前操作人的权限'
  });
}

export function userQueryList(param) {
  return request('/v1/guardUserQuery/queryGuardUserInfoWithRoleInfoNoFilter', {
    param,
    module: MODULES.admin,
    serviceScene: SCENES.query,
    serviceName: '查询用户列表'
  });
}

export function getDictItems(param) {
  return request('/v1/dictItemBatchQuery/batchQueryDictItem', {
    param,
    // module: MODULES.admin,
    module: { cname: '管理后台', prefix: '/mock/hsjry/guard/management' },
    serviceScene: SCENES.query,
    serviceName: '查询数据字典'
  });
}

/**
 * 仅查询当前登录人下的机构树
 * @param param
 * @returns {Promise<void>}
 */
export function organQueryTree(param) {
  return request('/v1/organQuery/queryUserOrganTree', {
    param,
    module: MODULES.admin,
    serviceScene: SCENES.query,
    serviceName: '查询当前登录人下的机构树'
  });
}

/**
 * 查询系统营业时间
 * @param param
 */
export function querySystemTime(param) {
  return request('/v1/navigationBar/querySystemTime', {
    param,
    module: MODULES.admin,
    serviceScene: SCENES.query,
    serviceName: '查询系统营业时间'
  });
}

/**
 * @description 查询产品列表
 */
export function productQueryList(param) {
  return request('/productQuery/queryPage', {
    param,
    module: oldMdata,
    serviceScene: SCENES.query,
    serviceName: '查询产品列表'
  });
}

// 查询所有的用户
export function queryAllUserList(param?: any) {
  return request('/base/IUserQuery/queryAllUserList', {
    param,
    module: { cname: '查询所有的用户', prefix: '/hsjry/guard' },
    serviceScene: SCENES.query,
    serviceName: '查询所有的用户'
  });
}