/**
 * @Author: wangyw26123
 * @Description: 角色成员列表弹窗
 * @Date: Created in 2022-12-20 16:20:34
 * @Modifed By:
 */
import React, { useEffect, useState } from 'react';
import { YRButton, YRSpace, YRModal, YRTransfer, YRMessage } from 'yrantd';
import styles from './index.module.less';
import { EnumMsg } from '@/constant/common';
import { M010301 } from '@permit/role';
import { checkAuth } from '@/utils/utils';

interface RecordType {
  userId: string;
  userName: string;
  accountNo: string;
}

const { queryRoleDetail } = M010301.E09.interfaces;
const { allotRole, queryUserList } = M010301.E07.interfaces;

const UserListModal = (props) => {
  const { visible, onCancel, onOk, roleId } = props;
  const [targetKeys, setTargetKeys] = useState<string[]>([]);
  const [btnLoading, setBtnLoading] = useState<boolean>(false);
  const [userList, setUserList] = useState([]);

  useEffect(() => {
    queryUserList().then((res) => {
      if (res.success) {
        setUserList(res.data);
      }
    });
  }, []);

  useEffect(() => {
    if (!roleId || !visible) return;
    queryRoleDetail({ roleId }).then((res) => {
      if (res.success) {
        const userDtoList = res.data?.userDtoList || [];
        setTargetKeys(userDtoList.map((item) => item.userId));
      }
    });
  }, [roleId, visible]);

  const onConfirm = () => {
    if (!checkAuth(M010301.E07)) return;

    setBtnLoading(true);
    allotRole({
      roleId,
      userIds: targetKeys
    }).then((res) => {
      setBtnLoading(false);
      if (res.success) {
        YRMessage.success(EnumMsg.save);
        onOk();
      }
    });
  };

  const filterOption = (inputValue: string, option: RecordType) => option.userName.indexOf(inputValue) > -1;

  const handleChange = (newTargetKeys: string[]) => {
    setTargetKeys(newTargetKeys);
  };

  return (
    <YRModal
      title="添加成员"
      size="middle"
      open={visible}
      onCancel={onCancel}
      footer={[
        <YRButton key="cancel" onClick={() => onCancel()}>
          取消
        </YRButton>,
        <YRButton
          key="confirm"
          type="primary"
          check={M010301.E07}
          disabled={btnLoading}
          loading={btnLoading}
          onClick={() => onConfirm()}
        >
          确定
        </YRButton>
      ]}
    >
      <YRTransfer
        showSearch
        titles={['未选', '已选']}
        dataSource={userList}
        className={styles['roleMutexModalTransfer']}
        filterOption={filterOption}
        targetKeys={targetKeys}
        onChange={handleChange}
        rowKey={(record) => record.userId}
        render={(item) => (
          <YRSpace>
            <span>{item.userName}</span>
            <span>|</span>
            <span>{item.accountNo}</span>
          </YRSpace>
        )}
      />
    </YRModal>
  );
};

export default UserListModal;
