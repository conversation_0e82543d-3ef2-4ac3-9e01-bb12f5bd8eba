import React from 'react';
import { YRLoanTaskDetailLayout } from 'yr-loan-antd';
import packageInfo from '../../../package.json';
import {
  taskDetail,
  queryApproveProcess,
  doApprove,
  transfer,
  queryFlowStepInfo,
  queryTaskApprovalAuthority,
  snakerDetail
} from '../../services/workplat';
import { userQueryList, organQueryTree } from '../../services/common';
import { batchQueryDictItem } from '@services/setting';
import { previewProcessTaskNode } from '@/services/flow';
import { Link } from 'ice';

function TaskDetailLayout(props) {
  // 服务
  const services = {
    taskDetail,
    queryApproveProcess,
    doApprove,
    transfer,
    queryFlowStepInfo,
    queryTaskApprovalAuthority,
    previewProcessTaskNode,
    snakerDetail,
    userQueryList,
    organQueryTree,
    getDictItems: batchQueryDictItem
  };
  const { children } = props;

  return (
    <YRLoanTaskDetailLayout {...props} services={services} packageName={packageInfo.businessSign} Link={Link}>
      {children}
    </YRLoanTaskDetailLayout>
  );
}

export default TaskDetailLayout;
