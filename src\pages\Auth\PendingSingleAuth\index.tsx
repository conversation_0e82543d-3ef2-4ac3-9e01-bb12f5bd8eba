/*
 * @Author: AI Assistant
 * @Description: 待复核-单笔授权管理页面
 * @Date: 2024-12-19
 */
import React, { useState, useEffect } from 'react';
import { YRButton, YRTable, YRForm, YRMessage, YRIndexPageLayout, YRModal, YRTreeSelect, YRSelect } from 'yrantd';
import { useRequest } from 'ahooks';
import { useDict } from '@yr/util';
import { M0105 } from '@permit/organ';
import { columns, formItemList } from './useIndex';
import type {
  PendingSingleQueryParams,
  ApiResponse,
  PageResponse,
  PendingSingleAuthInfo,
  ReviewParams,
  OrganTreeNode,
  DeptInfo,
  RoleInfo
} from './types';
import { queryAuthCatalogOrgTree } from '@/services/batchAuth';

const PendingSingleAuth: React.FC = () => {
  // 初始化数据字典
  useDict(['AUTH_STATUS', 'AUTH_CATEGORY']);

  // 机构树数据映射函数：将API返回的数据映射为TreeSelect组件需要的格式
  const mapOrgTreeData = (orgData: any): OrganTreeNode[] => {
    if (!orgData) return [];

    const mapNode = (node: any): OrganTreeNode => {
      return {
        orgId: node.orgId || node.id || '',
        orgName: node.orgName || node.name || node.title || '',
        children: node.children && node.children.length > 0
          ? node.children.map(mapNode)
          : undefined
      };
    };

    // 处理API响应中的机构树数据
    if (Array.isArray(orgData)) {
      return orgData.map(mapNode);
    } else if (orgData.categoryList && Array.isArray(orgData.categoryList)) {
      return orgData.categoryList.map(mapNode);
    } else if (orgData.children && Array.isArray(orgData.children)) {
      return orgData.children.map(mapNode);
    }

    return [];
  };

  // 获取权限接口（这里暂时使用现有的接口，实际应该有专门的待复核接口）
  const { queryAuthInfoRuleList, approvePassAuthRuleInfo, approveRejectAuthRuleInfo } = M0105.interfaces;

  const [form] = YRForm.useForm();
  const [dataSource, setDataSource] = useState<PendingSingleAuthInfo[]>([]);
  
  // 下拉框数据
  const [organTreeData, setOrganTreeData] = useState<OrganTreeNode[]>([]);
  const [deptOptions, setDeptOptions] = useState<DeptInfo[]>([]);
  const [roleOptions, setRoleOptions] = useState<RoleInfo[]>([]);

  // 分页数据
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) =>
      `共 ${total} 条记录，当前显示第 ${range[0]}-${range[1]} 条`
  });

  // 查询待复核授权列表
  const {
    loading: queryLoading,
    run: queryPendingAuthList,
    refresh: refreshList
  } = useRequest(
    async (params: PendingSingleQueryParams) => {
      // TODO: 这里应该调用实际的待复核授权查询接口
      // 暂时使用现有接口，添加状态过滤条件
      const queryParams = {
        ...params,
        authStatus: '020' // 复核中状态
      };
      return await queryAuthInfoRuleList(queryParams);
    },
    {
      onSuccess: (res: ApiResponse<PageResponse<PendingSingleAuthInfo>>) => {
        if (res?.rpcResult === 'SUCCESS' && !res?.errorMessage) {
          const { data } = res;
          setDataSource(data.list || []);
          setPagination((prev) => ({
            ...prev,
            current: data.pageNum,
            total: data.total
          }));
        } else {
          YRMessage.error(res?.errorMessage || '查询失败');
        }
      },
      manual: true
    }
  );

  // 复核通过
  const { run: approvePassRun, loading: approvePassLoading } = useRequest(
    async (params: ReviewParams) => {
      return await approvePassAuthRuleInfo({ authBaseId: params.authBaseId });
    },
    {
      onSuccess: (res: any) => {
        if (res?.errorMessage === null) {
          YRMessage.success('复核通过成功', 0.5, () => {
            refreshList();
          });
        } else {
          YRMessage.error(res?.errorMessage || '复核通过失败');
        }
      },
      manual: true
    }
  );

  // 复核退回
  const { run: approveRejectRun, loading: approveRejectLoading } = useRequest(
    async (params: ReviewParams) => {
      return await approveRejectAuthRuleInfo({ authBaseId: params.authBaseId });
    },
    {
      onSuccess: (res: any) => {
        if (res?.errorMessage === null) {
          YRMessage.success('复核退回成功', 0.5, () => {
            refreshList();
          });
        } else {
          YRMessage.error(res?.errorMessage || '复核退回失败');
        }
      },
      manual: true
    }
  );

  // 初始化查询
  useEffect(() => {
    handleSearch();
    // 初始化下拉框数据
    initSelectData();
  }, []);

  // 初始化下拉框数据
  const initSelectData = async () => {
    try {
      // 调用机构树查询接口
      const orgTreeRes = await queryAuthCatalogOrgTree({
        queryAuthFlag: "1",
        logPermitId: "kylin-admin-M0105I1",
        logOperateName: "查询所有一级机构"
      });
      if (orgTreeRes?.rpcResult === 'SUCCESS' && orgTreeRes?.data) {
        const mappedOrgTree = mapOrgTreeData(orgTreeRes.data);
        setOrganTreeData(mappedOrgTree);
        console.log('机构树数据加载成功:', mappedOrgTree);
      } else {
        console.warn('机构树数据加载失败:', orgTreeRes?.errorMessage);
        YRMessage.warning('机构树数据加载失败，请刷新页面重试');
        // 设置默认的机构树数据
        setOrganTreeData([
          {
            orgId: '1',
            orgName: '总行',
            children: [
              { orgId: '101', orgName: '风险管理部' },
              { orgId: '102', orgName: '信贷管理部' }
            ]
          }
        ]);
      }

      // TODO: 调用实际的接口获取部门、角色数据
      setDeptOptions([
        { deptId: '1001', deptName: '风险合规部', orgId: '101' },
        { deptId: '1002', deptName: '授信审批部', orgId: '102' }
      ]);

      setRoleOptions([
        { roleId: 'R001', roleName: '风险经理', roleCode: 'RISK_MANAGER' },
        { roleId: 'R002', roleName: '授信经理', roleCode: 'CREDIT_MANAGER' }
      ]);
    } catch (error) {
      console.error('初始化下拉框数据失败:', error);
      YRMessage.error('初始化数据失败，请刷新页面重试');
      // 设置默认的机构树数据
      setOrganTreeData([
        {
          orgId: '1',
          orgName: '总行',
          children: [
            { orgId: '101', orgName: '风险管理部' },
            { orgId: '102', orgName: '信贷管理部' }
          ]
        }
      ]);
    }
  };

  // 查询处理
  const handleSearch = () => {
    form.validateFields().then((formValues: any) => {
      const queryParams: PendingSingleQueryParams = {
        pageNum: 1,
        pageSize: pagination.pageSize,
        authNo: formValues.authNo,
        orgId: formValues.orgId,
        deptId: formValues.deptId,
        roleId: formValues.roleId,
        authCategory: formValues.authCategory
      };

      setPagination((prev) => ({ ...prev, current: 1 }));
      queryPendingAuthList(queryParams);
    });
  };

  // 分页变化处理
  const handlePaginationChange = (page: number, pageSize?: number) => {
    const newPagination = {
      ...pagination,
      current: page,
      pageSize: pageSize || pagination.pageSize
    };
    setPagination(newPagination);

    form.validateFields().then((formValues: any) => {
      const queryParams: PendingSingleQueryParams = {
        pageNum: page,
        pageSize: pageSize || pagination.pageSize,
        authNo: formValues.authNo,
        orgId: formValues.orgId,
        deptId: formValues.deptId,
        roleId: formValues.roleId,
        authCategory: formValues.authCategory
      };
      queryPendingAuthList(queryParams);
    });
  };

  // 详情处理
  const handleDetail = (record: PendingSingleAuthInfo) => {
    YRModal.info({
      title: '授权详情',
      width: 800,
      content: (
        <div>
          <p><strong>授权编号：</strong>{record.authNo}</p>
          <p><strong>机构：</strong>{record.orgName}</p>
          <p><strong>部门：</strong>{record.deptName}</p>
          <p><strong>角色：</strong>{record.roleName}</p>
          <p><strong>授权类别：</strong>{record.authCategory}</p>
          <p><strong>授信类别：</strong>{record.creditCategory}</p>
          <p><strong>生效日期：</strong>{record.effectBeginDate}</p>
          <p><strong>失效日期：</strong>{record.effectEndDate}</p>
          <p><strong>登记人：</strong>{record.operatorName}</p>
          <p><strong>登记机构：</strong>{record.ownOrganName}</p>
        </div>
      )
    });
  };

  // 复核处理
  const handleReview = (params: ReviewParams) => {
    if (params.reviewResult === 'PASS') {
      approvePassRun(params);
    } else {
      approveRejectRun(params);
    }
  };

  // 动态更新表单项的组件属性
  const dynamicFormItemList = formItemList.map(item => {
    if (item.key === 'orgId') {
      return {
        ...item,
        component: (
          <YRTreeSelect
            style={{ width: '100%' }}
            dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
            placeholder="请选择机构"
            allowClear
            treeDefaultExpandAll
            treeData={organTreeData}
            fieldNames={{ label: 'orgName', value: 'orgId' }}
          />
        )
      };
    }
    if (item.key === 'deptId') {
      return {
        ...item,
        component: (
          <YRSelect
            style={{ width: '100%' }}
            placeholder="请选择部门"
            allowClear
            options={deptOptions.map(dept => ({ label: dept.deptName, value: dept.deptId }))}
          />
        )
      };
    }
    if (item.key === 'roleId') {
      return {
        ...item,
        component: (
          <YRSelect
            style={{ width: '100%' }}
            placeholder="请选择角色"
            allowClear
            options={roleOptions.map(role => ({ label: role.roleName, value: role.roleId }))}
          />
        )
      };
    }
    return item;
  });

  return (
    <YRIndexPageLayout>
      <YRTable
        rowKey="authBaseId"
        form={form}
        dataSource={dataSource}
        handleSearch={handleSearch}
        loading={queryLoading}
        columns={columns(handleDetail, handleReview, approvePassLoading || approveRejectLoading)}
        formItemList={dynamicFormItemList}
        pagination={{
          ...pagination,
          onChange: handlePaginationChange,
          onShowSizeChange: handlePaginationChange
        }}
        scroll={{
          x: 1400
        }}
      />
    </YRIndexPageLayout>
  );
};

export default PendingSingleAuth;
