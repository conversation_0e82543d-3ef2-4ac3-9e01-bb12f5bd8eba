/**
 * @Author: wangyw26123
 * @Description: 机构管理-账务机构信息
 * @Date: Created in 2022-12-15 11:18:18
 * @Modifed By:
 */
import React from 'react';
import { YRClassificationLayout, YRDict, YRTable, YRTag } from 'yrantd';
import type { YRTableProps } from 'yrantd';
import { EnumAcctOrganTypelColor } from '../../../../constant/StyleConst';

const FinancialOrganInfo = (props) => {
  const { list } = props;

  const columns: YRTableProps['columns'] = [
    {
      title: '账务机构编号',
      dataIndex: 'acctOrganId',
      render: (value) => value || CONST.null
    },
    {
      title: '账务机构名称',
      dataIndex: 'acctOrganName',
      render: (value) => value || CONST.null
    },
    {
      title: '账务机构类型',
      dataIndex: 'acctOrganType',
      render: (value: string) => {
        return value ? (
          <YRTag color={EnumAcctOrganTypelColor[value]}>
            <YRDict.Text dictkey="EnumAcctOrganType" defaultValue={value} />
          </YRTag>
        ) : (
          CONST.null
        );
      }
    }
  ];

  return (
    <YRClassificationLayout title="账务机构信息">
      <YRTable columns={columns} dataSource={list} />
    </YRClassificationLayout>
  );
};

export default FinancialOrganInfo;
