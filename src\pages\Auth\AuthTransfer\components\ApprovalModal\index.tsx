/*
 * @Author: liaokt
 * @Description: 新增角色
 * @Date: 2024-02-27 16:10:32
 * @LastEditors: liaokt
 * @LastEditTime: 2024-10-17 19:14:03
 */
import { FormItemListProps, YREasyUseModal, YRForm, YRInput, YRMessage, YRModal, YRTable, YRTreeSelect } from 'yrantd';
import React, { useEffect, useState } from 'react';
import { useRequest } from 'ahooks';
import { columns } from '../../useIndex';

const AddRoleTree = (props: { params: any; okCallback: () => void }) => {
  const { params, okCallback } = props;

  const [form] = YRForm.useForm();
  const modal = YREasyUseModal.useModal();

  const [roleData, setRoleData] = useState([] as any[]);

  //   const { loading: queryRoleLoading, run: queryRoleRequest } = useRequest(queryRole, {
  //     manual: true,
  //     onSuccess: (res: any) => {
  //       if (res?.errorMessage === null) {
  //         const { data } = res;
  //         setRoleData(data || []);
  //       }
  //     }
  //   });

  // 提交事件
  const onSubmit = () => {};

  const formItemList: FormItemListProps[] = [
    {
      placeholder: '授权类型',
      key: 'authStatus',
      type: 'dictSelect',
      position: 'show',
      dictkey: 'AUTH_CATEGORY'
    },
    {
      placeholder: '授权编号',
      key: 'authNo',
      type: 'input',
      position: 'show'
    }
  ];

  return (
    <YRModal
      title={'授权复核'}
      open={modal.visible}
      onCancel={modal.hide}
      afterClose={modal.remove}
      confirmLoading={false}
      okText="确定"
      onOk={onSubmit}
      destroyOnClose
    >
      <YRTable
        dataSource={[]}
        formItemList={formItemList}
        columns={columns(() => {})?.filter((item) => {
          return item?.dataIndex !== 'operator';
        })}
        pagination={false}
      />
    </YRModal>
  );
};

export default YREasyUseModal.create(AddRoleTree);
