/*
 * @Author: 伍晶晶
 * @Description: 记录
 * @Date: 2023-03-20 11:14:48
 * @LastEditors: 伍晶晶
 * @LastEditTime: 2023-03-27 16:21:55
 */
import React from 'react';
import { YRLink, YRTable, YRTableProps } from 'yrantd';
import { useAntd } from '../Context';

const Dict = () => {
  const antd = useAntd();

  const [, setSelectState] = antd.selectedState;

  const columns: YRTableProps['columns'] = [
    {
      dataIndex: 'holidayName',
      title: '假期种类',
      render: (value, record) => {
        if (value) {
          return (
            <YRLink type="primary" onClick={() => setSelectState(record)}>
              {value}
            </YRLink>
          );
        }
        return CONST.null;
      }
    },
    { dataIndex: 'operatorName', title: '操作人名称' },
    { dataIndex: 'updateTime', title: '更新时间' }
  ];

  return (
    <YRTable
      {...antd.antdTreeProps}
      rowKey="holidayType"
      dataSource={antd.antdTreeProps.data || []}
      onRow={(record) => ({
        onDoubleClick: () => {
          setSelectState(record);
        },
        style: { cursor: 'pointer' }
      })}
      columns={columns}
      scroll={{ y: 600, x: 1200 }}
    />
  );
};

export default Dict;
