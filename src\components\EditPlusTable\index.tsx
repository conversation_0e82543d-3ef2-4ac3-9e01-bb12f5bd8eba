/* eslint-disable @typescript-eslint/consistent-type-definitions */
/* eslint-disable @typescript-eslint/array-type */
/* eslint-disable @typescript-eslint/indent */
/*
 * @Author: DJscript
 * @Date: 2023-02-09 09:46:39
 * @LastEditTime: 2023-02-20 17:08:28
 * @FilePath: /kylin-admin/src/components/EditPlusTable/index.tsx
 * @Description: 可编辑表格
 */
import { FieldData } from 'rc-field-form/lib/interface';
import React, { useImperativeHandle } from 'react';
import { YRTable, YRForm, YRTableProps, YRInputProps, YRSelectProps, YRDatePickerProps, YRRadioProps } from 'yrantd';
import { YRFormInternalPros } from 'yrantd/lib/yr-form/Form';
import { InternalFormItemProps } from 'yrantd/lib/yr-form/FormItem';
import Cell from './Cell';

type ColumnType = NonNullable<YRTableProps['columns']>[number];

type RenderType = Parameters<NonNullable<ColumnType['render']>>;
type RenderRetrunType = ReturnType<NonNullable<ColumnType['render']>>;

export interface EditPlusTableProps<T = { [key: string]: any }> {
  IEditPlusTableProps: IEditPlusTableProps<T>;
  EditRefType: {
    add: (arg?: T) => void;
    del: (arg: number) => void;
  };
}

type ComponentType<T, U> = U | ((params: { record: T; rowIndex: number; dataIndex: string }) => U);
type ShowColumn<T> = Omit<ColumnType, 'render'> & {
  dataIndex?: string;
  render?: (
    value: RenderType['0'],
    record: T,
    index: RenderType['2'],
    fn: {
      del: () => void;
      add: (arg?: T) => void;
    }
  ) => RenderRetrunType;
  formItemProps?:
    | InternalFormItemProps
    | ((params: { record: T; rowIndex: number; dataIndex: string }) => InternalFormItemProps);
} & (
    | {
        valueType?: 'input';
        componentProps?: ComponentType<T, YRInputProps>;
      }
    | {
        valueType: 'select';
        options: { label: string; value: string }[];
        componentProps?: ComponentType<T, YRSelectProps>;
      }
    | {
        valueType: 'dictSelect';
        dictkey: string;
        componentProps?: ComponentType<T, YRSelectProps>;
      }
    | {
        valueType: 'datePicker';
        componentProps?: ComponentType<T, YRDatePickerProps>;
      }
    | {
        valueType: 'radio';
        componentProps?: ComponentType<T, YRRadioProps>;
      }
    | {
        valueType: 'component';
        component: (arg: any) => React.ReactNode;
      }
  );

type IEditPlusTableProps<T> = {
  id?: string;
  onChange?: (value: T[]) => void;
  value?: T[];
  editRef?: any;
  mode?: YRFormInternalPros['mode'];
  columns?: ShowColumn<T>[];
} & Omit<YRTableProps, 'columns'>;

export default function EditableTable<T extends { [key: string]: any } = { [key: string]: any }>({
  id,
  onChange,
  value = [],
  columns = [],
  editRef,
  mode = 'edit',
  rowKey,
  ...rest
}: EditPlusTableProps<T>['IEditPlusTableProps']) {
  const [_form] = YRForm.useForm();
  const formInstance = YRForm.useFormInstance();
  const form = formInstance || _form;

  const ids = id ? id.split('_') : (id as FieldData['name']);
  const handleDelte = (index) => {
    const list = form.getFieldValue(ids) || [];
    const newList = list.filter((_, i) => i !== index);
    form.setFieldValue(ids, newList);
    onChange?.(newList);
  };
  const handleAdd = (item) => {
    const list = form.getFieldValue(ids) || [];
    form.setFieldValue(ids, [...list, item || {}]);
  };
  useImperativeHandle(editRef as any, () => ({
    form,
    del: handleDelte,
    add: handleAdd
  }));

  return (
    <YRForm
      mode={mode}
      component={false}
      form={form}
      onValuesChange={(_, values) => {
        if (!formInstance) {
          (ids as string[]).reduce((__, cur, i) => {
            if (i === (ids as string[]).length - 1) {
              onChange?.(values[cur]);
            } else {
              values = values[cur];
            }
            return cur;
          });
        }
      }}
      bordered={false}
    >
      <YRTable
        components={{
          body: {
            cell: Cell
          },
          header: {
            cell: (p) => {
              const rules = p?.formItemProps?.rules || [];
              const required = rules.some((r) => r.required);
              return (
                <th {...p}>
                  {required && <span style={{ color: 'red' }}>*</span>}
                  {p.children}
                </th>
              );
            }
          }
        }}
        dataSource={value}
        columns={
          (columns || []).map((column) => ({
            ...column,
            render: (...arg) => {
              return (
                <div style={{ marginTop: 5 }}>
                  {
                    (column.render
                      ? column.render(...arg, {
                          del: () => handleDelte(arg[2]),
                          add: handleAdd
                        })
                      : (t) => t) as any
                  }
                </div>
              );
            },
            onHeaderCell: (record, rowIndex) => ({
              record,
              rowIndex,
              fieldName: ids,
              ...column
            }),
            onCell: (record, rowIndex) => ({
              ...column,
              dataIndex: column.dataIndex || column.key,
              record,
              rowIndex,
              fieldName: ids,
              mode
            })
          })) as YRTableProps['columns']
        }
        rowKey={(rowKey || ((_, i) => i)) as YRTableProps['rowKey']}
      />
    </YRForm>
  );
}
