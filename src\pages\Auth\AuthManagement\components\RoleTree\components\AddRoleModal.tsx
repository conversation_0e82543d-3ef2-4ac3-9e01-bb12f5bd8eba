/*
 * @Author: liaokt
 * @Description: 新增角色
 * @Date: 2024-02-27 16:10:32
 * @LastEditors: liaokt
 * @LastEditTime: 2024-10-19 16:16:02
 */
import { YREasyUseModal, YRForm, YRInput, YRMessage, YRModal, YRTreeSelect } from 'yrantd';
import React, { useEffect, useState } from 'react';
import { useRequest } from 'ahooks';
import { M0105 } from '@permit/organ';
// import RoleAddModal from '../../RoleAddModal/index';

const AddRoleTree = (props: { params: any; okCallback: () => void }) => {
  const { params, okCallback } = props;

  const [form] = YRForm.useForm();
  const modal = YREasyUseModal.useModal();

  const [roleData, setRoleData] = useState([] as any[]);

  const { queryRole, addAuthCategoryRoleInfo } = M0105.interfaces;

  const { loading: queryRoleLoading, run: queryRoleRequest } = useRequest(queryRole, {
    manual: true,
    onSuccess: (res: any) => {
      if (res?.errorMessage === null) {
        const { data } = res;
        setRoleData(data || []);
      }
    }
  });

  const { loading: createRoleLoading, run: createRole } = useRequest(addAuthCategoryRoleInfo, {
    manual: true,
    onSuccess: (res: any) => {
      if (res?.errorMessage === null) {
        YRMessage.success('新增角色成功', 0.5, () => {
          okCallback();
          modal.remove();
        });
      }
    }
  });

  useEffect(() => {
    const { orgId, orgLvl } = params as { orgId: string; orgLvl: string };
    if (orgId) {
      queryRoleRequest({ orgId, orgLvlList: [orgLvl] });
    }
  }, [params]);

  // 提交事件
  const onSubmit = () => {
    form.validateFields().then((values: any) => {
      const { orgId, authCategory, authCatalogId } = params;
      const createParams = { ...values, orgId, authCategory, authCatalogId };
      createParams.roleList = values.roleList.map((item: any) => {
        return {
          roleId: item.value,
          roleName: item.label
        };
      });
      createRole(createParams);
    });
  };

  return (
    <YRModal
      title={'新增授权角色'}
      open={modal.visible}
      onCancel={modal.hide}
      afterClose={modal.remove}
      confirmLoading={createRoleLoading}
      okText="确定"
      onOk={onSubmit}
      destroyOnClose
    >
      <YRForm mode={'add'} form={form}>
        <YRForm.Row column={2}>
          <YRForm.Item label="角色" name="roleList" required>
            <YRTreeSelect
              multiple
              loading={queryRoleLoading}
              style={{ width: '100%' }}
              dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
              treeData={roleData}
              fieldNames={{ label: 'roleName', value: 'roleNo' }}
              placeholder="请选择角色"
              treeDefaultExpandAll
              allowClear
              labelInValue
            />
          </YRForm.Item>
          <YRForm.Item label="角色编号" name="roleId" hidden>
            <YRInput />
          </YRForm.Item>
        </YRForm.Row>
      </YRForm>
    </YRModal>
  );
};

export default YREasyUseModal.create(AddRoleTree);
