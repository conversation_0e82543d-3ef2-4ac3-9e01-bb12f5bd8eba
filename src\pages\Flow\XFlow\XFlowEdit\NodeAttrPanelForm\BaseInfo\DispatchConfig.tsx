/**
 * @Author: wangyw26123
 * @Description: 高级-派件配置
 * @Date: Created in 2022-04-27 14:08:32
 * @Modifed By:
 */

import React, { useEffect, useState } from 'react';
import { YRSelect, YRForm, YRRadio } from 'yrantd';

const FormItem = YRForm.Item;
const { Option } = YRSelect;

const radioStyle = {
  display: 'block',
  height: '30px',
  lineHeight: '30px'
};

const DispatchConfig = (props) => {
  const {
    targetData: { nodeFormData },
    globalDisabled
  } = props;

  return (
    <>
      <FormItem
        name={['properties', 'assignModel']}
        label="派单模式"
        initialValue={nodeFormData?.properties?.assignModel || '1'}
        rules={[
          {
            required: true,
            whitespace: true,
            message: '请选择会签或签'
          }
        ]}
      >
        <YRRadio.Group disabled={globalDisabled}>
          <YRRadio style={radioStyle} value="1">
            自动派单
          </YRRadio>
          <YRRadio style={radioStyle} value="2">
            人工抢单
          </YRRadio>
          <YRRadio style={radioStyle} value="3">
            审批指派
          </YRRadio>
        </YRRadio.Group>
      </FormItem>
    </>
  );
};

export default DispatchConfig;
