/**
 * @Author: wangyw26123
 * @Description: 节点-基本信息表单
 * @Date: Created in 2022-04-27 13:49:31
 * @Modifed By:
 */

import React from 'react';
import store from '@/store';
import { checkAuth } from '@/utils/utils';
import { nodeTypeNames } from '@/pages/Flow/XFlow/constants';

import XFlowCollapse from '@/pages/Flow/XFlow/components/XFlowCollapse';
import BaseInfo from '@/pages/Flow/XFlow/XFlowEdit/NodeAttrPanelForm/BaseInfo/NodeInfo';
import ApproverConfig from '@/pages/Flow/XFlow/XFlowEdit/NodeAttrPanelForm/BaseInfo/ApproverConfig';
import ApproverProcessConfig from '@/pages/Flow/XFlow/XFlowEdit/NodeAttrPanelForm/BaseInfo/ApproverProcessConfig';
import DispatchConfig from '@/pages/Flow/XFlow/XFlowEdit/NodeAttrPanelForm/BaseInfo/DispatchConfig';
import FormConfig from '@/pages/Flow/XFlow/XFlowEdit/NodeAttrPanelForm/BaseInfo/FormConfig';
import NodeConfig from '@/pages/Flow/XFlow/XFlowEdit/NodeAttrPanelForm/BaseInfo/NodeConfig';
import AuthConfig from './AuthConfig';

const { Panel } = XFlowCollapse;

let components = [
  {
    title: '节点信息',
    key: 'nodeInfo',
    check: null,
    component(props) {
      return (
        <Panel forceRender key={this.key} header={this.title}>
          <BaseInfo {...props} />
        </Panel>
      );
    }
  },
  {
    title: '表单配置',
    key: 'formConfig',
    check: null,
    component(props) {
      return (
        <Panel forceRender key={this.key} header={this.title}>
          <FormConfig {...props} />
        </Panel>
      );
    }
  },
  {
    title: '派单策略',
    key: 'dispatchConfig',
    check: null,
    component(props) {
      return (
        <Panel forceRender key={this.key} header={this.title}>
          <ApproverProcessConfig {...props} />

          <ApproverConfig {...props} />

          <DispatchConfig {...props} />

          <AuthConfig {...props} />
        </Panel>
      );
    }
  },
  {
    title: '节点配置',
    key: 'nodeConfig',
    check: null,
    component(props) {
      return (
        <Panel forceRender key={this.key} header={this.title}>
          <NodeConfig {...props} />
        </Panel>
      );
    }
  }
  // {
  //   title: '审批人配置',
  //   key: 'approverConfig',
  //   check: null,
  //   component(props) {
  //     return (
  //       <Panel forceRender key={this.key} header={this.title}>
  //         <ApproverConfig {...props} />
  //       </Panel>
  //     );
  //   }
  // },
  // {
  //   title: '审批处理配置',
  //   key: 'approverProcessConfig',
  //   check: null,
  //   component(props) {
  //     return (
  //       <Panel forceRender key={this.key} header={this.title}>
  //         <ApproverProcessConfig {...props} />
  //       </Panel>
  //     );
  //   }
  // }
];

components = components.filter((item) => (item.check ? checkAuth(item.check) : true));

const BaseInfoIndex = (props: any) => {
  const {
    targetData: { nodePanel, name }
  } = props;
  const [state] = store.useModel('flow');
  const { processBaseInfo } = state;
  const { processType } = processBaseInfo;
  const panels = [...nodePanel];

  // 目前只有任务节点有时效配置, 进件(信审)支持时效配置，非进件不支持时效配置
  if (nodeTypeNames['UserTask'] === name && processType !== '0') {
    const i = panels.indexOf('timeConfig');

    if (i > -1) {
      panels.splice(i, 1);
    }
  }

  const coms = components.filter((item) => panels.includes(item.key)) || [];
  const nodePanels = coms.map((com) => com.component(props)) || [];

  return <XFlowCollapse defaultActiveKey={coms[0].key}>{nodePanels}</XFlowCollapse>;
};

export default BaseInfoIndex;
