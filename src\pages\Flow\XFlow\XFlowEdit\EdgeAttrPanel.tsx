/**
 * @Author: wangyw26123
 * @Description: 边属性面板
 * @Date: Created in 2022-04-24 15:23:15
 * @Modifed By:
 */

import React, { useCallback, useEffect, forwardRef, useImperativeHandle, useRef } from 'react';
import { YRCard, YRForm } from 'yrantd';
import { cardBodyStyle } from '@/pages/Flow/XFlow/constants';
import FormIndex from './EdgeAttrPanelForm/FormIndex';
import XflowInstance from '@/pages/Flow/XFlow/XflowInstance';
import store from '@/store';
import { useDebounceEffect } from 'ahooks';

export default forwardRef((props, ref) => {
  const [form] = YRForm.useForm();
  const { targetData = {}, updateEdge } = props;
  const [state, dispatchers] = store.useModel('flow');
  const username = YRForm.useWatch('name', form);

  const { globalFormData } = state;

  useImperativeHandle(ref, () => {
    return {
      form,
      targetData: { ...targetData }
    };
  });

  useDebounceEffect(() => {
    onChange();
    XflowInstance.setRealTimeSave();
  }, [username]);

  const onChange = useCallback(() => {
    const timer = setTimeout(() => {
      const values = form.getFieldsValue();
      console.log('更新节点的边数据', props, values);
      updateEdge({
        // 更新节点的边数据
        ...targetData,
        label: values.name,
        nodeFormData: {
          ...values,
          nodeType: 'SequenceFlow'
        }
      });
      clearTimeout(timer);
    }, 100);
  }, []);

  useEffect(() => {
    // 表单初始化完成后，进行一次初始值保存，因为表单注册时值是不会同步到节点的。
    onChange();
  }, []);

  return (
    <YRCard size="small" title={targetData?.label} bodyStyle={cardBodyStyle}>
      {/* id变化时会重新渲染表单 */}
      <YRForm
        onFieldsChange={() => {
          onChange();
          XflowInstance.setRealTimeSave();
        }}
        form={form}
      >
        <FormIndex {...props} globalFormData={globalFormData} key={targetData?.id} form={form} />
      </YRForm>
    </YRCard>
  );
});
