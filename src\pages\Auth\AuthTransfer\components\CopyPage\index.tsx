/*
 * @Author: liaokt
 * @Description:
 * @Date: 2024-09-24 18:02:53
 * @LastEditors: liaokt
 * @LastEditTime: 2024-10-17 18:52:46
 */
import { YRFullPageLayout, YRButton, YRSplitScreenLayout, YRForm, YRSpace, YRAnchor } from 'yrantd';
import { useRequest } from 'ahooks';
import { closeWin, useDict } from '@yr/util';
import { EnumPlacement } from 'yrantd/lib/yr-split-screen-layout';
import React, { useEffect, useState } from 'react';
import { useLocation } from 'ice';
import { M0105 } from '@permit/organ';
import * as PropTypes from 'prop-types';
import Link from 'yrantd/lib/yr-anchor/Link';
import OriginInfo from './components/OrginInfo';
import TargetInfo from './components/TargetInfo';

const MockPage = () => {
  useDict(['AUTH_STATUS', 'AUTH_CATEGORY']);

  const { queryAuthInfoByAuthNo } = M0105.interfaces;

  const [form] = YRForm.useForm();
  const { query } = useLocation();

  const { authNoList } = query;

  const [splitScreenActiveKeys, setSplitScreenActiveKeys] = useState<string[]>(['top-1']);

  const componentsList = [
    {
      id: 'origin-info',
      components: <OriginInfo id={'origin-info'} title={'引入源信息'} form={form} />,
      name: '引入源信息'
    },
    {
      id: 'target-info',
      components: <TargetInfo id={'target-info'} title={'目标信息'} form={form} />,
      name: '目标信息'
    }
  ];

  //   const { run, loading } = useRequest(fddRecvPoolApply, {
  //     manual: true,
  //     onSuccess: (res) => {
  //       if (res.success) {
  //         YRMessage.success('提交成功');
  //         closeWin();
  //       }
  //     }
  //   });

  const { run: queryDetail, loading: detailLoading } = useRequest(queryAuthInfoByAuthNo, {
    onSuccess: (res) => {
      if (!res.success) {
        const { data } = res;
        form.setFieldsValue(data);
      }
    },
    manual: true
  });

  useEffect(() => {
    if (authNoList) {
      const list = JSON.parse(authNoList);
      queryDetail({ authNo: list[0] });
    }
  }, [authNoList]);

  const pageTabs = [
    {
      label: '目录',
      key: 'top-1',
      children: (
        <YRAnchor
          affix={false}
          showInkInFixed
          getContainer={() => window.document.querySelector('.yr-split-screen-layout-body') as any}
        >
          {componentsList.map((tab) => (
            <YRAnchor.Link key={tab.id} href={`#${tab.id}`} title={tab.name} />
          ))}
        </YRAnchor>
      ),
      initPlacement: EnumPlacement.leftTop
    }
  ];

  const onSubmit = () => {
    form.validateFields().then((values: any) => {});
  };

  return (
    <YRFullPageLayout
      left={{
        title: '复制基本授权',
        goBack: () => window.close()
      }}
      loading={detailLoading}
      Link={Link as PropTypes.ReactComponentLike}
      right={
        <YRButton.Space>
          <YRButton type="primary" onClick={onSubmit} loading={false}>
            提交
          </YRButton>
        </YRButton.Space>
      }
    >
      <YRSplitScreenLayout
        tabs={pageTabs}
        activeKeys={splitScreenActiveKeys}
        onSelect={(activeKeys) => {
          setSplitScreenActiveKeys(activeKeys);
        }}
      >
        <YRForm form={form} labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} size="middle" labelWrap>
          <YRSpace direction="vertical" style={{ width: '100%' }} block>
            {componentsList?.map((item) => {
              return item.components;
            })}
          </YRSpace>
        </YRForm>
      </YRSplitScreenLayout>
    </YRFullPageLayout>
  );
};

export default MockPage;
