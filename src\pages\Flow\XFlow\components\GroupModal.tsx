/**
 * @Author: wangyw26123
 * @Description: 机构管理-机构复制
 * @Date: Created in 2022-12-15 16:58:45
 * @Modifed By:
 */
import React, { useEffect } from 'react';
import { YRForm, YRModal, YRButton, YRTreeSelect, YRTable, YRTableProps } from 'yrantd';
import { findIndex } from 'lodash-es';
import { uuid } from 'yr-loan-antd/lib/util';

import { useSetState } from 'ahooks';

const { SHOW_PARENT } = YRTreeSelect;

interface AdjustmentOrderProps {
  data: string[];
  visible: boolean;
  initialValue: any;
  onCancel: () => void;
  onOk: (value: string[]) => void;
}

const GroupModal = (props: AdjustmentOrderProps) => {
  const { visible, onCancel, initialValue, onOk, data } = props;
  const [form] = YRForm.useForm();

  const [state, setState] = useSetState({
    pageLoading: false,
    dataSource: [],
    selectedIds: []
  });

  useEffect(() => {
    setState({ selectedIds: initialValue });
  }, [initialValue]);

  const onSubmit = () => {
    if (state.selectedIds?.length > 0) {
      onOk(state.selectedIds);
      onCancel();
    }
  };

  const onChange = (newValue: string[]) => {
    console.log('onChange ', newValue);
  };

  const getTableColumns: YRTableProps['columns'] = [
    {
      title: '岗位编号',
      dataIndex: 'id'
    },
    {
      title: '岗位名称',
      dataIndex: 'stationName'
    }
  ];

  const rowSelection = {
    onChange: (selectedRowKeys, rows) => {
      setState({
        selectedIds: selectedRowKeys
      });
    },
    selectedRowKeys: state.selectedIds
  };

  return (
    <YRModal
      title="审批"
      open={visible}
      onCancel={onCancel}
      footer={[
        <YRButton key="cancel" onClick={onCancel}>
          取消
        </YRButton>,
        <YRButton key="submit" type="primary" onClick={onSubmit}>
          确定
        </YRButton>
      ]}
    >
      <YRTable
        form={form}
        rowKey="id"
        loading={state.pageLoading}
        dataSource={data}
        columns={getTableColumns}
        pagination={false}
        rowSelection={rowSelection}
        showIndex={false}
      />
    </YRModal>
  );
};

export default GroupModal;
