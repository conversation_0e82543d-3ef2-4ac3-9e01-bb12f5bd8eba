import React from 'react';
import { ValueTypeEnum } from '@yr/multi-view-table';
import { FormItemListProps, YRTableProps, YRButton, YRLink, YRModal, YRMessage, YRDict, YRTreeSelect, YRSelect } from 'yrantd';
import type { PendingSingleAuthInfo, ReviewParams } from './types';

// 处理复核操作的通用函数
const handleReviewAction = (
  actionName: string,
  reviewResult: 'PASS' | 'REJECT',
  authBaseId: string,
  reviewCallback: (params: ReviewParams) => void
) => {
  YRModal.confirm({
    title: actionName,
    okText: '确定',
    cancelText: '取消',
    content: `确定要${actionName}此授权吗？`,
    onOk: () => {
      reviewCallback({
        authBaseId,
        reviewResult,
        reviewRemark: actionName === '复核通过' ? '复核通过' : '复核退回'
      });
    }
  });
};

// 表单项配置
const formItemList: FormItemListProps[] = [
  {
    placeholder: '授权编号',
    key: 'authNo',
    type: 'input',
    position: 'show'
  },
  {
    placeholder: '机构',
    key: 'orgId',
    type: 'component',
    position: 'show',
    component: (
      <YRTreeSelect
        style={{ width: '100%' }}
        dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
        placeholder="请选择机构"
        allowClear
        treeDefaultExpandAll
        // treeData 需要从props传入
      />
    )
  },
  {
    placeholder: '部门',
    key: 'deptId',
    type: 'component',
    position: 'show',
    component: (
      <YRSelect
        style={{ width: '100%' }}
        placeholder="请选择部门"
        allowClear
        // options 需要从props传入
      />
    )
  },
  {
    placeholder: '角色',
    key: 'roleId',
    type: 'component',
    position: 'show',
    component: (
      <YRSelect
        style={{ width: '100%' }}
        placeholder="请选择角色"
        allowClear
        // options 需要从props传入
      />
    )
  },
  {
    placeholder: '授权类别',
    key: 'authCategory',
    type: 'dictSelect',
    position: 'show',
    dictkey: 'AUTH_CATEGORY'
  }
];

// 表格列定义
const columns: (
  detailCallback: (record: PendingSingleAuthInfo) => void,
  reviewCallback: (params: ReviewParams) => void,
  reviewLoading: boolean
) => YRTableProps['columns'] = (
  detailCallback,
  reviewCallback,
  reviewLoading
) => {
  return [
    {
      title: '授权编号',
      dataIndex: 'authNo',
      key: 'authNo',
      width: 180,
      fixed: 'left'
    },
    {
      title: '机构',
      dataIndex: 'orgName',
      key: 'orgName',
      width: 150,
      ellipsis: true
    },
    {
      title: '部门',
      dataIndex: 'deptName',
      key: 'deptName',
      width: 120,
      ellipsis: true
    },
    {
      title: '角色',
      dataIndex: 'roleName',
      key: 'roleName',
      width: 120,
      ellipsis: true
    },
    {
      title: '授权分类',
      dataIndex: 'authCategory',
      key: 'authCategory',
      width: 120,
      render: (value) => <YRDict.Text dictkey={'AUTH_CATEGORY'} defaultValue={value} />
    },
    {
      title: '授信类别',
      dataIndex: 'creditCategory',
      key: 'creditCategory',
      width: 120,
      ellipsis: true
    },
    {
      title: '授权状态',
      dataIndex: 'authStatus',
      key: 'authStatus',
      width: 120,
      valueType: ValueTypeEnum.dict,
      dictkey: 'AUTH_STATUS'
    },
    {
      title: '生效日期',
      dataIndex: 'effectBeginDate',
      key: 'effectBeginDate',
      width: 120,
      valueType: ValueTypeEnum.date
    },
    {
      title: '失效日期',
      dataIndex: 'effectEndDate',
      key: 'effectEndDate',
      width: 120,
      valueType: ValueTypeEnum.date
    },
    {
      title: '操作',
      dataIndex: 'operator',
      valueType: ValueTypeEnum.operation,
      fixed: 'right',
      width: 200,
      render: (value, record) => {
        const { authBaseId } = record as PendingSingleAuthInfo;
        
        return (
          <YRButton.Space size="small">
            <YRLink
              type="primary"
              onClick={() => detailCallback(record as PendingSingleAuthInfo)}
            >
              详情
            </YRLink>
            <YRLink
              type="primary"
              onClick={() => 
                handleReviewAction('复核退回', 'REJECT', authBaseId, reviewCallback)
              }
            >
              复核退回
            </YRLink>
            <YRLink
              type="primary"
              onClick={() => 
                handleReviewAction('复核通过', 'PASS', authBaseId, reviewCallback)
              }
            >
              复核通过
            </YRLink>
          </YRButton.Space>
        );
      }
    }
  ];
};

export { columns, formItemList };
