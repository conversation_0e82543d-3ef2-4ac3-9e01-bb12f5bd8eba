/*
 * @Author: ducf
 * @E-mail: <EMAIL>
 * @Date: 2023-05-05 16:56:08
 * @Description: 决策表列表
 */
import { useAntdTable } from 'ahooks';
import React from 'react';
import { deleteFlowDiagramResource, selectPageForMaxVersion } from '@/services/flow';
import {
  FormItemListProps,
  YRTableProps,
  YRButton,
  YRForm,
  YRIndexPageLayout,
  YRTable,
  YRLink,
  YRConfirmBtn,
  YRSpace,
  YRTag,
  YRDict,
  YREasyUseModal
} from 'yrantd';
import { openNewTab } from '@/utils/utils';
import { flowSceneTypeColor } from '@/constant/StyleConst';
import AddModal from './components/AddModal';
import { Dict } from '@yr/util';
import { OpenModalType } from './type';

interface Props {}

const Decision: React.FC<Props> = (props) => {
  const [form] = YRForm.useForm();

  const { tableProps, search, run } = useAntdTable(
    (p) => {
      return selectPageForMaxVersion({
        ...p,
        pageNum: p?.current,
        modelType: '2'
      })
        .then((res) => {
          return { list: res?.data?.list, total: res?.data?.total || 0 };
        })
        .catch(() => {});
    },
    {
      defaultParams: [
        {
          pageNum: 1,
          pageSize: 10,
          approveStatus: 0
        }
      ]
    }
  );

  const formItemList: FormItemListProps[] = [
    {
      placeholder: '决策表编号',
      key: 'modelNumber',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '决策表名称',
      key: 'modelName',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '流程类型',
      key: 'sceneType',
      type: 'dictSelect',
      dictkey: 'SCENE_TYPE',
      position: 'show'
    }
  ];

  const onDel = (row) => {
    deleteFlowDiagramResource({ id: row?.id }).then((res) => {
      if (res?.success) {
        run({ current: 1, pageSize: 10 });
      }
    });
  };
  const getTableColumns: YRTableProps['columns'] = [
    {
      title: '决策表编号',
      dataIndex: 'modelNumber',
      width: 120,
      render: (value, row) => {
        if (value) {
          return (
            <YRLink type="primary" onClick={() => toPage('readPretty', row.id)}>
              {value}
            </YRLink>
          );
        }
        return CONST.null;
      }
    },
    {
      title: '决策表名称',
      dataIndex: 'modelName',
      width: 220
    },
    {
      title: '场景类型',
      dataIndex: 'sceneType',
      width: 80,
      render: (value: string) => {
        if (!value) return CONST.null;
        return (
          <YRTag color={flowSceneTypeColor[value]}>
            <YRDict.Text dictkey="SCENE_TYPE" defaultValue={value} />
          </YRTag>
        );
      }
    },
    {
      title: '描述',
      dataIndex: 'modelDesc',
      width: 220
    },
    // {
    //   title: '最新版本',
    //   dataIndex: 'version',
    //   width: 80
    // },
    {
      title: '操作',
      dataIndex: 'operator',
      width: 100,
      fixed: 'right',
      render: (value, row: any) => {
        return (
          <div>
            <YRSpace>
              <YRLink type="primary" onClick={() => handleModal({ ...row, type: OpenModalType.edit, id: row.id })}>
                修改
              </YRLink>
              <YRLink
                type="primary"
                onClick={() => {
                  toPage('edit', row.id);
                }}
              >
                配置
              </YRLink>
              <YRLink>
                <YRConfirmBtn msg="确定删除吗？" type="pop" onConfirm={() => onDel(row)}>
                  删除
                </YRConfirmBtn>
              </YRLink>
            </YRSpace>
          </div>
        );
      }
    }
  ];

  const toPage = (type: string, id?: string) => {
    openNewTab({
      pathname: `/decision-page/${type}`,
      query: { id }
    });
  };
  // http://kylin-base-web.loan4-0-szyh-dev.svc.cluster.local:8889/kylin-admin/flow/decision
  const handleModal = (row: { type: OpenModalType; id?: string }) => YREasyUseModal.show(AddModal, row);
  const renderExtAction = (
    <YRButton type="primary" onClick={() => handleModal({ type: OpenModalType.add })}>
      新增
    </YRButton>
  );

  return (
    <YRIndexPageLayout>
      <YRTable
        form={form}
        rowKey="id"
        columns={getTableColumns}
        formItemList={formItemList}
        extAction={renderExtAction}
        handleSearch={() => {
          form.validateFields().then((res) => {
            run({ ...res, current: 1, pageSize: 10 });
          });
        }}
        // onRow={(row) => {
        //   return {
        //     onDoubleClick: () => toDetail(row)
        //   };
        // }}
        {...tableProps}
      />
    </YRIndexPageLayout>
  );
};

export default Dict(['SCENE_TYPE'])(Decision);
