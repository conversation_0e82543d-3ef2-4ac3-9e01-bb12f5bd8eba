/**
 * @Author: 刘文强
 * @Description: 客户经理助理管理对话框
 * @Date: Created in
 * @Modifed By:
 */
import React, { useState, useEffect } from 'react';
import {
  YRForm,
  YRModal,
  YRTable,
  FormItemListProps,
  YRDict,
  YRMessage,
  YRLink,
  YRButton,
  YREasyUseModal,
  YRTableProps,
  YRBadge,
  YRSpace
} from 'yrantd';
import AddAssistantModal from '@/pages/User/components/AddAssistantModal';
import { deleteManagerAssisantRel, queryManagerAssisantPage } from '../../../services/user';
import { EnumGuardUserStatusColor } from '../../../constant/StyleConst';

type TData = {
  managerName: string;
  managerId: string;
  managerAccountNo: string;
  managerStatus: string;
  assisantUserList: CData[];
};

type CData = {
  assisantName: string;
  assisantId: string;
  assisantAccountNo: string;
  assisantStatus: string;
  identityId: string;
};

const AssistantModal = (props) => {
  const [form] = YRForm.useForm();
  const { assistantVisible, setAssistantVisible } = props;
  const [dataSource, setDataSource] = useState([]);
  const [pageLoading, setPageLoading] = useState(false);
  const [total, setTotal] = useState(0);
  let pagination = {
    pageSize: 10,
    pageNum: 1,
    defaultPageSize: 10,
    pageSizeOptions: ['10', '20', '40', '80'],
    showSizeChanger: true,
    showQuickJumper: true
  };

  const changePage = (pg) => {
    pg.pageNum = pg.current;
    pagination = { ...pg };
    handleSearch();
  };

  const handleCancel = () => {
    setAssistantVisible(false);
  };

  useEffect(() => {
    if (assistantVisible) {
      setPageLoading(true);
      queryManagerAssisantPage({ pageNum: 1, pageSize: 10 }).then((res) => {
        setPageLoading(false);
        if (res && res.success) {
          setDataSource(res.data?.list || []);
          setTotal(res.data?.total || 0);
        }
      });
    }
  }, [assistantVisible]);

  const handleOk = () => {
    handleCancel();
  };

  const handleSearch = () => {
    const values: object = (form.getFieldsValue() as object) || {};
    const param = {
      ...values,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    };
    setPageLoading(true);
    queryManagerAssisantPage(param).then((res) => {
      setPageLoading(false);
      if (res.success) {
        setDataSource(res.data?.list || []);
        setTotal(res.data?.total || 0);
      }
    });
  };

  const handleDelete = (row: CData, record: TData) => {
    const param = {
      managerId: record.managerId,
      assisantId: row.assisantId,
      identityId: row.identityId
    };
    YRModal.confirm({
      title: '确定删除吗？',
      content: '',
      okText: '确定',
      cancelText: '取消',
      onOk() {
        deleteManagerAssisantRel(param).then((res) => {
          if (res && res.success) {
            YRMessage.success('删除成功');
            handleSearch();
          }
        });
      }
    });
  };

  /**
   * @description 新增客户经理助理
   * @param row 客户经理信息
   * @param type
   * @return void
   */
  const handleAdd = () => {
    YREasyUseModal.show(AddAssistantModal, { handleSearch });
  };

  const getTableColumns = (): any[] => {
    return [
      { title: '客户经理姓名', dataIndex: 'managerName', key: 'managerName' },
      { title: '客户经理编号', dataIndex: 'managerId', key: 'managerId' },
      { title: '客户经理账号', dataIndex: 'managerAccountNo', key: 'managerAccountNo' },
      { title: '客户经理身份名称', dataIndex: 'identityName', key: 'identityName' },
      {
        title: '状态',
        dataIndex: 'managerStatus',
        key: 'managerStatus',
        width: 80,
        render: (val) => (
          <YRSpace>
            <YRBadge color={EnumGuardUserStatusColor[val]} />
            <YRDict.Select dictkey="EnumGuardUserStatus" type="text" defaultValue={val} />
          </YRSpace>
        )
      }
    ];
  };

  const formItemList: FormItemListProps[] = [
    {
      placeholder: '客户经理姓名',
      key: 'managerName',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '客户经理助理姓名',
      key: 'assisantName',
      type: 'input',
      position: 'show'
    }
  ];
  const expandedRowRender = (record) => {
    const columns: YRTableProps['columns'] = [
      { title: '客户经理助理姓名', dataIndex: 'assisantName', key: 'assisantName' },
      { title: '客户经理助理编号', dataIndex: 'assisantId', key: 'assisantId' },
      { title: '客户经理助理账号', dataIndex: 'assisantAccountNo', key: 'assisantAccountNo' },
      {
        title: '状态',
        dataIndex: 'assisantStatus',
        key: 'assisantStatus',
        render: (val) => (
          <YRSpace>
            <YRBadge color={EnumGuardUserStatusColor[val]} />
            <YRDict.Select dictkey="EnumGuardUserStatus" type="text" defaultValue={val} />
          </YRSpace>
        )
      },
      {
        title: '操作',
        dataIndex: 'operation',
        key: 'operation',
        render: (value, row) => (
          <YRLink type="primary" onClick={() => handleDelete(row, record)}>
            删除
          </YRLink>
        )
      }
    ];
    return (
      <YRTable
        columns={columns}
        dataSource={record?.assisantUserList || []}
        pagination={false}
        scroll={{ x: 'hidden' }}
      />
    );
  };
  return (
    <YRModal
      title="客户经理助理管理"
      open={assistantVisible}
      onCancel={handleCancel}
      onOk={handleOk}
      destroyOnClose
      size="large"
    >
      <YRTable
        form={form}
        formItemList={formItemList}
        rowKey={(row, idx) => idx!}
        handleSearch={handleSearch}
        dataSource={dataSource}
        loading={pageLoading}
        columns={getTableColumns()}
        expandable={{ expandedRowRender, defaultExpandedRowKeys: ['0'] }}
        pagination={{
          ...pagination,
          total,
          showTotal: () => `共${total}条`
        }}
        onChange={changePage}
        extAction={[
          <YRButton onClick={() => handleAdd()} type="primary">
            新增客户经理助理
          </YRButton>
        ]}
      />
    </YRModal>
  );
};
export default AssistantModal;
