# 批量授权管理API调用修复

## 问题描述
```
TypeError: _yr_util__WEBPACK_IMPORTED_MODULE_3__.default.post is not a function
```

## 错误原因
在批量授权管理页面中，错误地使用了 `request.post()` 方法，但项目中的 `@yr/util` 模块的 `request` 不是一个对象，而是一个函数。

## 修复方案

### 1. 修正导入语句
```typescript
// 修改前（错误的）
import { request } from '@yr/util';

// 修改后（正确的）
import { MODULES, request, SCENES } from '@yr/util';
```

### 2. 修正API调用方式
```typescript
// 修改前（错误的）
const response = await request.post('/authBaseInfoBatchQueryController/queryInfoPage', params);

// 修改后（正确的）
const response = await request('/authBaseInfoBatchQueryController/queryInfoPage', {
  param: params,
  module: MODULES.corp,
  serviceScene: SCENES.query,
  serviceName: '查询批量授权信息列表'
});
```

### 3. 创建专门的API服务文件
创建了 `src/services/batchAuth.ts` 文件，按照项目规范组织API调用：

```typescript
import { MODULES, request, SCENES } from '@yr/util';

export function queryBatchAuthInfoPage(param?: any) {
  return request('/authBaseInfoBatchQueryController/queryInfoPage', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '查询批量授权信息列表'
  });
}
```

## 修复内容

### 1. 新增文件
- `src/services/batchAuth.ts` - 批量授权相关API服务

### 2. 修改文件
- `src/pages/Auth/BatchAuthManagement/index.tsx` - 修正API调用方式

### 3. API服务列表
- `queryBatchAuthInfoPage` - 分页查询批量授权信息
- `batchImportAuthInfo` - 批量授权导入
- `submitBatchAuthForReview` - 提交复核
- `deleteBatchAuthInfo` - 删除批量授权
- `invalidateBatchAuthInfo` - 批量失效
- `downloadBatchAuthTemplate` - 下载模板

## 项目API调用规范

### 1. 标准格式
```typescript
import { MODULES, request, SCENES } from '@yr/util';

export function apiFunction(param?: any) {
  return request('/api/path', {
    param,                    // 请求参数
    module: MODULES.corp,     // 模块配置
    serviceScene: SCENES.query, // 服务场景
    serviceName: '接口描述'    // 服务名称
  });
}
```

### 2. 常用模块
- `MODULES.corp` - 企业模块
- `MODULES.guard` - 权限模块
- `MODULES.admin` - 管理模块
- `MODULES.flow` - 流程模块

### 3. 常用场景
- `SCENES.query` - 查询操作
- `SCENES.apply` - 申请/提交操作

## 测试验证

### 1. 查询功能
- ✅ 页面加载时自动查询
- ✅ 按授权状态筛选查询
- ✅ 分页查询功能
- ✅ 错误处理和提示

### 2. 操作功能
- ✅ 批量导入（集成API）
- ✅ 下载模板（集成API）
- ✅ 提交复核（集成API）
- ✅ 删除操作（集成API）
- ✅ 批量失效（集成API）

### 3. 数据映射
- ✅ API响应数据正确映射到前端数据结构
- ✅ 日期格式化处理
- ✅ 空值安全处理

## 注意事项

1. **模块配置**: 确保使用正确的 `module` 配置，通常批量授权使用 `MODULES.corp`
2. **服务场景**: 查询操作使用 `SCENES.query`，提交操作使用 `SCENES.apply`
3. **错误处理**: 所有API调用都包含完整的错误处理逻辑
4. **参数传递**: 请求参数通过 `param` 字段传递，不是直接传递对象
5. **响应格式**: API响应格式为 `{ rpcResult: "SUCCESS" | "FAIL", data: any, errorMessage?: string }`

## 更新日志

- 2024-12-19: 修复API调用错误
- 创建专门的API服务文件
- 集成所有批量授权相关接口
- 完善错误处理和用户提示
