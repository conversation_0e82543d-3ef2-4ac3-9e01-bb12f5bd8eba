/*
 * @Author: 伍晶晶
 * @Description: 字典项
 * @Date: 2023-03-20 11:14:48
 * @LastEditors: 伍晶晶
 * @LastEditTime: 2023-03-29 19:10:47
 */
import { clearCache, useAntdTable, useRequest } from 'ahooks';
import React, { useEffect } from 'react';
import {
  YRBadge,
  YRButton,
  YRConfirmBtn,
  YREasyUseModal,
  YRLink,
  YRMessage,
  YRSpace,
  YRTable,
  YRTableProps
} from 'yrantd';
import { useAntd } from '../Context';
import AddModal from './AddModal';

import { addDictItem, changeDictItemStatus, modifyDictItem, selectPage } from '@/services/setting';
import { KeyEnum } from '../Interface';

const DictItem = () => {
  const antd = useAntd();

  const [selectedState] = antd.selectedState;

  const antdProps = useAntdTable(
    (param) =>
      selectPage({
        ...param,
        pageNum: param.current || 1,
        pageSize: param.pageSize || 10,
        dictKey: selectedState[KeyEnum.KEY]!
      }).then((res) => {
        if (res.success) {
          return {
            list: res.data.list,
            total: res.data.total
          };
        }
        return {
          list: [],
          total: 0
        };
      }),
    {
      manual: true,
      debounceWait: 300
    }
  );

  useEffect(() => {
    if (selectedState[KeyEnum.KEY] !== 'all') {
      antdProps.run({ dictKey: selectedState[KeyEnum.KEY]!, current: 1, pageSize: 10, pageNum: 1 });
    }
  }, [selectedState]);

  const columns: YRTableProps['columns'] = [
    { dataIndex: 'itemName', title: '字典项名称' },
    { dataIndex: 'itemKey', title: '字典项值' }
  ];
  if (selectedState.isChildNode !== 'Y') {
    columns.push({
      dataIndex: 'parentItemKey',
      title: '父字典项',
      render: (v) => (v && v !== '0' ? v : CONST.null)
    });
  }
  columns.push(
    {
      dataIndex: 'enableStatus',
      title: '状态',
      render: (v) => <YRBadge status={v === 'Y' ? 'success' : 'error'} text={v === 'Y' ? '启用' : '禁用'} />
    },
    { dataIndex: 'createTime', title: '创建时间' },
    { dataIndex: 'updateTime', title: '更新时间' },
    {
      title: '操作',
      fixed: 'right',
      width: 100,
      render: (v, record) => (
        <YRButton.Space>
          <YRLink type="primary">
            <YRConfirmBtn
              msg={`确定${record.enableStatus === 'Y' ? '禁用' : '启用'}吗？`}
              type="pop"
              onConfirm={() =>
                runChangeDictItemStatus({
                  enableStatus: record.enableStatus === 'Y' ? 'N' : 'Y',
                  itemId: record.itemId
                })
              }
            >
              <YRLink type="primary">{record.enableStatus === 'Y' ? '禁用' : '启用'}</YRLink>
            </YRConfirmBtn>
          </YRLink>
          <YRLink
            type="primary"
            onClick={() =>
              YREasyUseModal.show(AddModal, {
                type: 'edit',
                row: { ...selectedState, ...record }
              }).then((values: any) => {
                updateDictItem(values);
              })
            }
          >
            编辑
          </YRLink>
        </YRButton.Space>
      )
    }
  );

  const { run: updateDictItem } = useRequest(modifyDictItem, {
    manual: true,
    onSuccess: (res) => {
      if (res.success) {
        YRMessage.success('修改成功');
        clearCache(selectedState[KeyEnum.KEY]);
        antdProps.refresh();
      }
    }
  });

  const { run: runChangeDictItemStatus, params } = useRequest(changeDictItemStatus, {
    manual: true,
    onSuccess: (res) => {
      if (res.success) {
        YRMessage.success(`${params[0]?.enableStatus === 'Y' ? '启用' : '禁用'}成功`);
        clearCache(selectedState[KeyEnum.KEY]);
        antdProps.refresh();
      }
    }
  });

  const { run: runAddTableItem } = useRequest(addDictItem, {
    manual: true,
    onSuccess: (res) => {
      if (res.success) {
        YRMessage.success('新增成功');
        clearCache(selectedState.dictKey!);
        antdProps.refresh();
      }
    }
  });

  return (
    <YRSpace direction="vertical" block>
      <YRButton
        key="add"
        type="primary"
        onClick={() =>
          YREasyUseModal.show(AddModal, {
            type: 'add',
            row: selectedState
          }).then((values: any) => {
            runAddTableItem(values);
          })
        }
      >
        新增字典项
      </YRButton>
      <YRTable
        {...antdProps}
        rowKey="itemKey"
        dataSource={antdProps.data?.list || []}
        columns={columns}
        scroll={{ y: 600, x: 1200 }}
      />
    </YRSpace>
  );
};

export default DictItem;
