/**
 * @Author: 刘文强
 * @Description: 变更机构对话框
 * @Date: Created in
 * @Modifed By:
 */
import React, { useState, useEffect } from 'react';
import { YRClassificationLayout, YRModal, YRForm, YRInput, YRTreeSelect, YRRadio, YRMessage, YRTable } from 'yrantd';
import {
  queryUserOrganTree
} from '@/services/setting';
import { countTableWidth } from '@yr/util';
import {changeUserOrgan, changeUserOrganApproval, queryUserCustomerAndBiz} from "../../../services/user";

const ChangeOrganModal = (props) => {
  const [form] = YRForm.useForm();
  const { organChangeVisible, setOrganChangeVisible, detail } = props;
  const [treeData, setTreeData] = useState<any>([]);
  const [dataVisible, setDataVisible] = useState(false);
  const [customerDtoList, setCustomerDtoList] = useState([]);
  const [bizDataDtoList, setBizDataDtoList] = useState([]);
  const [selectedCustomerRowKeys, setSelectedCustomerRowKeys] = useState([]);
  const [selectedBizRowKeys, setSelectedBizRowKeys] = useState([]);

  useEffect(() => {
    if (!detail || Object.keys(detail).length === 0) return;
    const { userId } = detail;
    queryUserCustomerAndBiz({ userId }).then((res) => {
      if (res && res.success) {
        setCustomerDtoList(res.data?.customerDtoList || []);
        setBizDataDtoList(res.data?.bizDataDtoList || []);
      }
    });
  }, [detail]);

  const handleOk = () => {
    form.validateFields().then((values) => {
      const isWithBiz = form.getFieldValue('isWithBiz') === 'Y';
      if (!isWithBiz) {
        const param = {
          ...values,
          userId: detail.userId,
          organId: detail.organId,
          organName: detail.organName
        };
        changeUserOrgan(param).then((res) => {
          if (res && res.success) {
            YRMessage.success('变更成功');
          }
        });
      } else {
        const param = {
          ...values,
          customerDtoList: selectedCustomerRowKeys,
          bizDataDtoList: selectedBizRowKeys
        };
        changeUserOrganApproval(param).then((res) => {
          if (res && res.success) {
            YRMessage.success('变更成功');
          }
        });
      }
      setOrganChangeVisible(false);
    });
  };

  useEffect(() => {
    queryUserOrganTree({}).then((res) => {
      if (res.success && res.data) {
        const organTreeDto = res.data.organTreeDto ? [res.data.organTreeDto] : [];
        setTreeData(organTreeDto);
      }
    });
  }, []);

  const handleRadio = (e) => {
    if (e.target.value === 'N') {
      setDataVisible(false);
    } else {
      setDataVisible(true);
    }
  };

  const customerDto = () => {
    const width = 200;
    return [
      {
        title: '客户名称',
        dataIndex: 'customerName',
        width
      },
      {
        title: '客户编号',
        dataIndex: 'customerId',
        width
      },
      {
        title: '证件类型',
        dataIndex: 'certificateKind',
        width
      },
      {
        title: '证件号码',
        dataIndex: 'certificateNo',
        width
      },
      {
        title: '管护人',
        dataIndex: 'operatorName',
        width
      },
      {
        title: '管护机构',
        dataIndex: 'ownOrganName',
        width
      }
    ];
  };

  const bizDataDto = () => {
    const width = 200;
    return [
      {
        title: '授信编号',
        dataIndex: 'creditApplyId',
        width
      },
      {
        title: '客户编号',
        dataIndex: 'customerId',
        width
      },
      {
        title: '客户名称',
        dataIndex: 'customerName',
        width
      },
      {
        title: '授信金额',
        dataIndex: 'creditAmount',
        width
      },
      {
        title: '授信期限',
        dataIndex: 'creditTerm',
        width,
        render: (value, row) => {
          return (
            <>
              {value}
              {row.termUnit}
            </>
          );
        }
      },
      {
        title: '经办人',
        dataIndex: 'operatorName',
        width
      },
      {
        title: '经办机构',
        dataIndex: 'ownOrganName',
        width
      },
      {
        title: '申请日期',
        dataIndex: 'bizDateTime',
        width
      }
      // {
      //   title: '操作',
      //   dataIndex: 'ops',
      //   render: (value, row, index) => {
      //     return <YRButton>删除</YRButton>;
      //   }
      // }
    ];
  };

  const customerRowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedCustomerRowKeys(selectedRowKeys);
    }
  };

  const bizRowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedBizRowKeys(selectedRowKeys);
    }
  };

  return (
    <YRModal
      title="用户机构变更"
      open={organChangeVisible}
      onCancel={() => setOrganChangeVisible(false)}
      onOk={handleOk}
      size="middle"
      destroyOnClose
    >
      <YRClassificationLayout.Space>
        <YRForm form={form} mode={'edit'}>
          <YRForm.Item
            name="organName"
            label="所属机构"
            rules={[{ required: true, message: '所属机构必填' }]}
            initialValue={detail?.organName}
          >
            <YRInput disabled />
          </YRForm.Item>
          <YRForm.Item name="afterOrganId" label="变更后所属机构" rules={[{ required: true, message: '所属机构必填' }]}>
            <YRTreeSelect
              treeData={treeData}
              fieldNames={{ label: 'organName', value: 'organId' }}
              placeholder="请选择机构"
              onSelect={(value, node) => {
                form.setFieldValue('afterOrganName', node.organName);
              }}
            />
          </YRForm.Item>
          <YRForm.Item name="afterOrganName" style={{ display: 'none' }}>
            <YRInput />
          </YRForm.Item>
          <YRForm.Item
            label="是否带数据"
            name="isWithBiz"
            initialValue="N"
            rules={[{ required: true, message: '是否带数据必选' }]}
          >
            <YRRadio.Group onChange={handleRadio}>
              <YRRadio value="Y">是</YRRadio>
              <YRRadio value="N">否</YRRadio>
            </YRRadio.Group>
          </YRForm.Item>
        </YRForm>
        {dataVisible && (
          <>
            <YRClassificationLayout title="管护客户">
              <YRTable
                rowKey="customerId"
                dataSource={customerDtoList}
                columns={customerDto()}
                rowSelection={{
                  ...customerRowSelection
                }}
                scroll={{ x: countTableWidth(customerDto()) }}
              />
            </YRClassificationLayout>
            <YRClassificationLayout title="业务数据">
              <YRTable
                rowKey="creditApplyId"
                dataSource={bizDataDtoList}
                columns={bizDataDto()}
                rowSelection={{
                  ...bizRowSelection
                }}
                scroll={{ x: countTableWidth(bizDataDto()) }}
              />
            </YRClassificationLayout>
          </>
        )}
      </YRClassificationLayout.Space>
    </YRModal>
  );
};

export default ChangeOrganModal;
