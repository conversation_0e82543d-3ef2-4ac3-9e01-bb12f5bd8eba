/*
 * @Author: liaokt
 * @Description:
 * @Date: 2024-11-25 11:01:17
 * @LastEditors: liaokt
 * @LastEditTime: 2024-11-25 14:48:23
 */
import React from 'react';
import { ValueTypeEnum } from '@yr/multi-view-table';
import { YRButton, YREmpty, YRForm, YRLink, YRSpin, YRTable, YRTableProps } from 'yrantd';
import { useDict } from '@yr/util';

interface IFormSettingPageProps {
  queryDataId: string;
}

const COLOR_DICT_STATUS = {
  '001': 'grey',
  '002': 'green',
  '003': 'red'
};

const FormSettingPage = (props: IFormSettingPageProps) => {
  useDict(['FORM_SETTING_STATUS']);

  const [form] = YRForm.useForm();

  const { queryDataId } = props;

  const operation = (formSettingStatus: string, formSettingId: string) => {
    const editLink =
      formSettingStatus !== '002' ? (
        <YRLink type="primary" onClick={() => {}}>
          修改
        </YRLink>
      ) : null;

    const enableLink = (
      <YRLink type="primary" onClick={() => {}}>
        {formSettingStatus === '002' ? '下架' : '上架'}
      </YRLink>
    );

    return (
      <YRButton.Overflow>
        <YRLink type="primary" onClick={() => {}}>
          详情
        </YRLink>
        {editLink}
        {enableLink}
        <YRLink type="primary" onClick={() => {}}>
          删除
        </YRLink>
      </YRButton.Overflow>
    );
  };

  const columns: YRTableProps['columns'] = [
    {
      dataIndex: 'formSettingId',
      key: 'formSettingId',
      title: '表单配置版本编号',
      valueType: ValueTypeEnum.id
    },
    {
      dataIndex: 'formSettingName',
      key: 'formSettingName',
      title: '表单配置版本名称'
    },
    {
      dataIndex: 'createTime',
      key: 'createTime',
      title: '创建时间',
      valueType: ValueTypeEnum.dateTime
    },
    {
      dataIndex: 'updateTime',
      key: 'updateTime',
      title: '更新时间',
      valueType: ValueTypeEnum.dateTime
    },
    {
      dataIndex: 'formSettingStatus',
      key: 'formSettingStatus',
      title: '版本状态',
      valueType: ValueTypeEnum.tag,
      dictkey: 'FORM_SETTING_STATUS',
      color: COLOR_DICT_STATUS,
      fixed: 'right'
    },
    {
      dataIndex: 'operations',
      key: 'operations',
      title: '操作',
      fixed: 'right',
      width: 120,
      render: (_value, row: any) => {
        const { formSettingStatus, formSettingId } = row;
        return operation(formSettingStatus, formSettingId);
      }
    }
  ];

  const searchList: YRTableProps['formItemList'] = [
    {
      placeholder: '表单配置版本名称',
      key: 'formSettingName',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '表单配置版本状态',
      key: 'formSettingStatus',
      type: 'dictSelect',
      dictkey: 'FORM_SETTING_STATUS',
      position: 'show'
    }
  ];

  const onSearch = () => {
    // const values: any = form.getFieldsValue();
    // run({ ...params[0], ...values });
  };

  return (
    <YRSpin spinning={false}>
      {queryDataId ? (
        <YRTable form={form} columns={columns} formItemList={searchList} handleSearch={onSearch} />
      ) : (
        <YREmpty className="empty" />
      )}
    </YRSpin>
  );
};

export default FormSettingPage;
