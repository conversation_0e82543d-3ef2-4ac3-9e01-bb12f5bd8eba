/**
 * 新增参数库弹窗
 */
import React, { useEffect } from 'react';
import { <PERSON><PERSON>utton, YRDict, YRForm, YRInput, YRMessage, YRModal } from 'yrantd';
import Dict from '@/pages/Organ/mock/getDict';
import { addFlowParamLibrary, modifyFlowParamLibrary } from '@/services/flow';
import { useRequest } from 'ahooks';
import { isObjectEmpty } from '@/utils/utils';

const ParamLibraryAddModal = (props: any) => {
  const [form] = YRForm.useForm();
  const { visible, changeVisible, onOkCallback, title = '新增参数库', currentLibrary, setCurrentLibrary } = props;

  /** 新增参数项接口 */
  const { loading: addLaoding, run: addRequest } = useRequest(addFlowParamLibrary, {
    manual: true,
    onSuccess: (result: any) => {
      const { errorMessage, data } = result;
      if (errorMessage === null) {
        YRMessage.success('新增成功', 0.5, () => {
          onOkCallback();
          onCancel();
        });
      }
    }
  });

  /** 修改参数库接口 */
  const { loading: modifyLaoding, run: modifyRequest } = useRequest(modifyFlowParamLibrary, {
    manual: true,
    onSuccess: (result: any) => {
      const { errorMessage, data } = result;
      if (errorMessage === null) {
        YRMessage.success('修改成功', 0.5, () => {
          onCancel();
          onOkCallback();
        });
      }
    }
  });

  useEffect(() => {
    if (!isObjectEmpty(currentLibrary)) {
      form.setFieldsValue({ ...currentLibrary });
    }
  }, [currentLibrary]);

  /** 取消事件 */
  const onCancel = () => {
    setCurrentLibrary && setCurrentLibrary({});
    changeVisible(false);
    form.resetFields();
  };

  /** 确认事件 */
  const onOk = () => {
    form
      .validateFields()
      .then((values: any) => {
        if (!isObjectEmpty(currentLibrary) && currentLibrary) {
          const { id } = currentLibrary;
          delete values.sceneType;
          modifyRequest({ ...values, id });
        } else {
          addRequest({ ...values });
        }
      })
      .catch((error) => {
        console.log(error);
      });
  };

  return (
    <YRModal
      width={'40%'}
      destroyOnClose
      onCancel={() => {
        onCancel();
      }}
      open={visible}
      title={title}
      footer={[
        <YRButton key="back" onClick={() => onCancel()}>
          取消
        </YRButton>,
        <YRButton key="submit" type="primary" onClick={() => onOk()} loading={addLaoding || modifyLaoding}>
          确定
        </YRButton>
      ]}
    >
      <YRForm form={form}>
        <YRForm.Row column={1}>
          <YRForm.Item
            label={'参数库名称'}
            name={'libraryName'}
            rules={[{ required: true, message: '参数库名称不能为空' }]}
          >
            <YRInput placeholder={'请输入参数库名称'} />
          </YRForm.Item>
          <YRForm.Item
            label={'参数库描述'}
            name={'libraryDesc'}
            rules={[{ required: true, message: '参数库描述不能为空' }]}
          >
            <YRInput placeholder={'请输入参数库描述'} />
          </YRForm.Item>
          <YRForm.Item
            label={'参数库分类'}
            name={'sceneType'}
            rules={[{ required: true, message: '参数库分类不能为空' }]}
          >
            <YRDict.Select
              dictkey={'flow_scene_type'}
              disabled={!isObjectEmpty(currentLibrary) && currentLibrary}
              placeholder={'请选择参数库分类'}
            />
          </YRForm.Item>
        </YRForm.Row>
      </YRForm>
    </YRModal>
  );
};

export default Dict(['flow_scene_type'])(ParamLibraryAddModal);
