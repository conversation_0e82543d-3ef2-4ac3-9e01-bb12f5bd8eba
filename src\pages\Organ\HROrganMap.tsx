/**
 * @Author: wangyw26123
 * @Description: HR机构映射
 * @Date: Created in 2022-12-09 16:46:35
 * @Modifed By:
 */

import React from 'react';
import { useQueryTableData } from '@yr/util';
import type { FormItemListProps } from 'yrantd';
import { ValueTypeEnum, YRTableProps } from 'yrantd/lib/yr-table/declare';

const HROrganMap = () => {
  const [form] = YRForm.useForm();
  // 查询表格数据
  const { tableProps, search } = useQueryTableData(queryListHttp);

  const formItemList: FormItemListProps[] = [
    {
      placeholder: '业务机构名称',
      key: 'couponName',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '业务机构编号',
      key: 'couponKind',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '账户机构名称',
      key: 'couponKind',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '账户机构编号',
      key: 'couponKind',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '账户机构类型',
      key: 'couponKind',
      type: 'dictSelect',
      dictkey: 'coupon_kind',
      position: 'show'
    }
  ];

  const toDetail = (row: any) => {};
  // 表格列
  const columns: YRTableProps['columns'] = [
    {
      title: '业务机构编号',
      dataIndex: 'id',
      valueType: ValueTypeEnum.id
    },
    {
      title: '业务机构名称',
      dataIndex: 'fatherName',
      valueType: ValueTypeEnum.department
    },
    {
      title: 'HR机构名称',
      dataIndex: 'fatherName',
      valueType: ValueTypeEnum.department
    },
    {
      title: 'HR机构编号',
      dataIndex: 'motherName',
      valueType: ValueTypeEnum.id
    },
    {
      title: '操作',
      dataIndex: 'operator',
      valueType: ValueTypeEnum.operation,
      fixed: 'right',
      render: () => {
        return (
          <YRButton.Overflow>
            <YRLink type="primary" onClick={() => {}}>
              修改
            </YRLink>
            <YRLink type="primary" onClick={() => {}}>
              删除
            </YRLink>
          </YRButton.Overflow>
        );
      }
    }
  ];

  const operationRender = (
    <YRButton.Space>
      <YRButton type="primary">新增</YRButton>
    </YRButton.Space>
  );

  return (
    <YRIndexPageLayout>
      <YRTable
        business="home"
        form={form}
        rowKey="id"
        columns={columns}
        handleSearch={search.submit}
        formItemList={formItemList}
        operationRender={operationRender}
        onRow={(row) => {
          return {
            onDoubleClick: () => toDetail(row)
          };
        }}
        {...tableProps}
      />
    </YRIndexPageLayout>
  );
};

import { YRButton, YRForm, YRIndexPageLayout, YRTable, YRSpace, YRDivider, YRTypography, YRLink } from 'yrantd';

const queryListHttp = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        data: {
          list: [
            {
              id: 'LN1231241243123123123',
              name: '张三儿子',
              sex: '男',
              fatherName: '张三',
              motherName: '张三老婆',
              status: '001'
            },
            {
              id: 'LN1231241243124123123',
              name: '李四女儿',
              sex: '女',
              fatherName: '李四',
              motherName: '李四老婆',
              status: '001'
            },
            {
              id: 'LN1231241241123123123',
              name: '王五女儿',
              sex: '女',
              fatherName: '王五',
              motherName: '王五老婆',
              status: '001'
            },
            {
              id: 'LN1231341243123123123',
              name: '赵六女儿',
              sex: '女',
              fatherName: '赵六',
              motherName: '赵六老婆',
              status: '001'
            },
            {
              id: 'LN1111241243123123123',
              name: '赵六女儿2',
              sex: '女',
              fatherName: '赵六',
              motherName: '赵六老婆',
              status: '002'
            },
            {
              id: 'LN3231241243123123123',
              name: '赵六女儿3',
              sex: '女',
              fatherName: '赵六',
              motherName: '赵六老婆',
              status: '002'
            },
            {
              id: 'LN1231241243123123122',
              name: '赵六女儿3',
              sex: '女',
              fatherName: '赵六',
              motherName: '赵六老婆',
              status: '002'
            },
            {
              id: 'LN1231241243123123ss3',
              name: '赵六女儿3',
              sex: '女',
              fatherName: '赵六',
              motherName: '赵六老婆',
              status: '002'
            },
            {
              id: 'LN1231241243123123333',
              name: '赵六女儿3',
              sex: '女',
              fatherName: '赵六',
              motherName: '赵六老婆',
              status: '002'
            },
            {
              id: 'LN1231241243123123qq3',
              name: '赵六女儿3',
              sex: '女',
              fatherName: '赵六',
              motherName: '赵六老婆',
              status: '002'
            },
            {
              id: 'LN1243123123123',
              name: '赵六女儿3',
              sex: '女',
              fatherName: '赵六',
              motherName: '赵六老婆',
              status: '002'
            },
            {
              id: 'LN1111111243123123123',
              name: '赵六女儿3',
              sex: '女',
              fatherName: '赵六',
              motherName: '赵六老婆',
              status: '002'
            }
          ]
        }
      });
    });
  });
};

export default HROrganMap;
