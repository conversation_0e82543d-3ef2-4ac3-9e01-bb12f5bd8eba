/**
 * @Author: 刘文强
 * @Description: 分配角色对话框
 * @Date: Created in
 * @Modifed By:
 */
import React, { useState, useEffect } from 'react';
import {
  YRClassificationLayout,
  YRModal,
  YRTabs,
  YRForm,
  YRTransfer,
  YRMessage,
  YRDict,
  YRTreeSelect,
  YRInput,
  YRTable,
  YRLink,
  YRButton,
  YREasyUseModal,
  YRTableProps,
  YRCard
} from 'yrantd';
import store from '@/store';
import {
  allocateUserRoles,
  deleteUserIdentity,
  queryRoleList,
  queryUserDetailWithIdentify
} from '../../../services/user';
import AddPartJobModal from './AddPartJobModal';

const AssignRoleModal = (props) => {
  const [form] = YRForm.useForm();
  const [mockData, setMockData] = useState([]);
  const [targetKeys, setTargetKeys] = useState([]);
  const [roleDetail, setRoleDetail] = useState<any>({});
  const { assignVisible, setAssignVisible, detail, queryList } = props;
  const [organState] = store.useModel('organ');
  const { organTreeList: treeData } = organState;
  // 监测tabkey 在切换的时候掉一次接口 因为所属条线表单值会同步
  const [tabKey, setTabKey] = useState<string>('本职角色');
  const [dataSource, setDataSource] = useState([]);
  useEffect(() => {
    if (!detail.userId || !assignVisible) return;
    queryDetail();
    queryUserDetailWithIdentify({ userId: detail.userId }).then((res) => {
      if (res.success) {
        setRoleDetail(res.data);
        form.resetFields();
        // 当用户当前所在机构所属条线为零售银行且用户岗位为客户经理时，默认全不选中，其他岗位默认全选中，选中零售银行时，三个二级条线全部选中
        if (res.data.bizLine === '002') {
          if (res.data.post === '客户经理') {
            form.setFieldValue('bizLines', []);
          } else {
            form.setFieldValue('bizLines', ['001', '003', '004', '002-1', '002-2', '002-3']);
          }
        } else if (res.data.bizLine === '005') {
          // 当前用户所在机构的所属条线为公司银行/数字银行/金融市场时，默认选中相应条线，且可编辑；当前用户所在机构的所属条线为综合条线时，公司银行/零售银行(消费/经营/按揭)数字银行/金融市场全部显示并默认全选中，支持手动维护
          form.setFieldValue('bizLines', ['001', '003', '004', '002-1', '002-2', '002-3']);
        } else {
          form.setFieldValue('bizLines', res.data.bizLine);
        }
      }
    });
  }, [detail.userId, assignVisible]);

  const queryDetail = () => {
    queryUserDetailWithIdentify({ userId: detail.userId }).then((res) => {
      if (res?.success) {
        const list = res.data.userIdentifyDtoList.filter((item) => item.identityRelationType === '002') || [];
        setDataSource(list);
      }
    });
  };

  const generateColumns = (): YRTableProps['columns'] => {
    const width = 100;
    return [
      {
        title: '兼职身份名称',
        dataIndex: 'identityName',
        key: 'identityName',
        width
      },
      {
        title: '关联机构名称',
        dataIndex: 'organId',
        key: 'organId',
        width,
        render: (val) => (
          <YRTreeSelect
            showSearch
            treeData={treeData}
            treeNodeFilterProp="organName"
            fieldNames={{ label: 'organName', value: 'organId' }}
            placeholder="请选择合同签约机构"
            value={val}
            disabled
            style={{ width: '100%' }}
          />
        )
      },
      {
        title: '所属条线',
        dataIndex: 'bizLines',
        key: 'bizLines',
        width: width + 100,
        render: (val) => <YRDict.Text dictkey="EnumUserBizLine" defaultValue={val} />
      },
      {
        title: '分配角色',
        dataIndex: 'roleDtoList',
        key: 'roleDtoList',
        width,
        render: (val) => (
          <>
            {val &&
              val?.map((item, idx) => {
                return idx !== val.length - 1 ? `${item.roleName}、` : item.roleName;
              })}
          </>
        )
      },
      {
        title: '操作',
        dataIndex: 'operator',
        key: 'operator',
        width: 80,
        fixed: 'right',
        render: (val, row) => (
          <YRButton.Space>
            <YRLink type="primary" onClick={() => handleAddPartJob(row)}>
              修改
            </YRLink>
            <YRLink type="primary" onClick={() => deletePartRole(row.identifyId)}>
              删除
            </YRLink>
          </YRButton.Space>
        )
      }
    ];
  };
  const handleAddPartJob = (partInfo?: { [key: string]: any }, type?: 'add') => {
    YREasyUseModal.show(AddPartJobModal, {
      partInfo: { organId: detail.organId, userId: detail.userId, ...partInfo },
      treeData,
      mockData,
      filterOption,
      targetKeys: type === 'add' ? [] : partInfo?.roleDtoList?.map((item) => item?.roleId),
      handleChange,
      queryDetail
    });
  };

  const deletePartRole = (identityId: string) => {
    YRModal.confirm({
      title: '是否确定删除该兼职角色？',
      okText: '确认',
      cancelText: '取消',
      onOk: () =>
        deleteUserIdentity({ identityId, userId: detail.userId }).then((res) => {
          if (res?.success) {
            YRMessage.success('删除成功');
            queryDetail();
          }
        })
    });
  };

  const hasRole = (data) => {
    for (let i = 0; i < roleList.length; i++) {
      if (roleList[i].roleId === data.key) {
        return true;
      }
    }
    return false;
  };

  const userIdentifyDtoList = roleDetail.userIdentifyDtoList || [];
  const mainRole = userIdentifyDtoList.find((item) => item.identityRelationType === '001') || {};
  const roleList = mainRole.roleDtoList || [];
  const defaultName = detail.userIdentifyDtoList.find((item) => item.identityRelationType === '001').identityName || {};

  useEffect(() => {
    const tempTargetKeys = [];
    const tempMockData = [];

    for (let i = 0; i < mockData.length; i++) {
      if (hasRole(mockData[i])) {
        // @ts-ignore
        tempTargetKeys.push(mockData[i].key);
      }
      tempMockData.push(mockData[i]);
    }
    setMockData(tempMockData);
    setTargetKeys(tempTargetKeys);
  }, [roleDetail]);

  useEffect(() => {
    queryRoleList({ roleStatus: '002' }).then((res) => {
      if (res && res.success) {
        const list = res?.data || [];
        const tempDatasource = [];
        for (let i = 0; i < list.length; i++) {
          const item = list[i];
          const data = {
            key: item.roleId,
            title: item.roleName
          };

          // @ts-ignore
          tempDatasource.push(data);
        }
        setMockData(tempDatasource);
      }
    });
  }, []);

  const filterOption = (inputValue, option) => option.title.indexOf(inputValue) > -1;
  const handleChange = (newTargetKeys) => {
    setTargetKeys(newTargetKeys);
  };
  const handleSearch = (dir, value) => {
    // console.log('search:', dir, value);
  };

  const handleOk = () => {
    form.validateFields().then((values: any) => {
      if (tabKey === '本职角色') {
        const param = {
          userId: roleDetail.userId,
          identityId: mainRole.identifyId,
          identityName: mainRole.identityName,
          organId: mainRole.organId,
          roleIdList: targetKeys,
          bizLines: values.bizLines.toString()
        };
        allocateUserRoles(param).then((res) => {
          if (res && res.success) {
            YRMessage.success('修改成功');
            setAssignVisible(false);
            queryList();
          }
        });
      } else {
        setAssignVisible(false);
        queryList();
      }
    });
  };

  return (
    <YREasyUseModal.Provider>
      <YRModal
        size="large"
        title="分配角色"
        open={assignVisible}
        onCancel={() => setAssignVisible(false)}
        onOk={handleOk}
      >
        <YRTabs
          onTabClick={(key: string) => {
            setTabKey(key);
          }}
        >
          <YRTabs.TabPane tab="本职角色" key="本职角色" tabKey="本职角色">
            <YRClassificationLayout.Space>
              <YRClassificationLayout title="归属条线">
                <YRForm form={form} mode={'edit'}>
                  <YRForm.Item name="bizLines" label="所属条线" rules={[{ required: true, message: '所属条线' }]}>
                    <YRDict.CheckboxGroup dictkey="EnumUserBizLine" />
                  </YRForm.Item>
                </YRForm>
              </YRClassificationLayout>
              <YRClassificationLayout title="分配角色">
                <YRTransfer
                  listStyle={{
                    width: '50%',
                    height: 400
                  }}
                  dataSource={mockData}
                  showSearch
                  filterOption={filterOption}
                  targetKeys={targetKeys}
                  onChange={handleChange}
                  onSearch={handleSearch}
                  render={(item) => (
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      {item.title}
                      {/*<YRButton*/}
                      {/*  type="primary"*/}
                      {/*  size="small"*/}
                      {/*  onClick={(e) => {*/}
                      {/*    e.preventDefault();*/}
                      {/*    e.stopPropagation();*/}
                      {/*  }}*/}
                      {/*>*/}
                      {/*  查看权限*/}
                      {/*</YRButton>*/}
                    </div>
                  )}
                />
              </YRClassificationLayout>
            </YRClassificationLayout.Space>
          </YRTabs.TabPane>
          <YRTabs.TabPane tab="兼职角色" key="兼职角色" tabKey="兼职角色">
            <YRClassificationLayout.Space>
              <YRForm form={form} mode={'edit'} wrapperCol={{ span: 18 }} labelCol={{ span: 6 }}>
                <YRForm.Item
                  column="block"
                  mode="readPretty"
                  label="默认身份"
                  name="defaultName"
                  rules={[{ required: true, message: '请输入' }]}
                  initialValue={defaultName}
                >
                  <YRInput />
                </YRForm.Item>
                <YRCard
                  title="兼职身份"
                  extra={
                    <YRButton type="primary" onClick={() => handleAddPartJob({}, 'add')}>
                      新增
                    </YRButton>
                  }
                  bordered
                >
                  <YRTable
                    columns={generateColumns()}
                    dataSource={dataSource || []}
                    onRow={() => {
                      return {
                        onClick: () => {}
                      };
                    }}
                  />
                </YRCard>
              </YRForm>
            </YRClassificationLayout.Space>
          </YRTabs.TabPane>
        </YRTabs>
      </YRModal>
    </YREasyUseModal.Provider>
  );
};

export default AssignRoleModal;
