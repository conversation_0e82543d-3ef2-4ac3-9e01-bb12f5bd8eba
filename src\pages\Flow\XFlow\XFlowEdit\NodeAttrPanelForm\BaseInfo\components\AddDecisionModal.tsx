import { selectPageForMaxVersion } from '@/services/flow';
import { useAntdTable, useDebounceEffect } from 'ahooks';
import React, { useState } from 'react';
import { YRModal, YRButton, YRTable, YRConfirmBtn, YRLink, YRTableProps, FormItemListProps } from 'yrantd';

const AddDecisionModal = (props: any) => {
  const { visible, changeVisible, onOK, currentTable, tableId } = props;
  const [selectUserList, setSelectUserList] = useState([]);
  const [selectUserKey, setSelectUserKey] = useState([]);

  useDebounceEffect(() => {
    if (tableId) {
      setSelectUserKey([tableId]);
    }
  }, [tableId]);

  const rowSelection: any = {
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectUserList(selectedRows);
      setSelectUserKey(selectedRowKeys);
    },
    type: 'radio',
    selectedRowKeys: selectUserKey
  };

  const getTableColumns: YRTableProps['columns'] = [
    {
      title: '决策表编号',
      dataIndex: 'modelNumber',
      width: 120
    },
    {
      title: '决策表名称',
      dataIndex: 'modelName',
      width: 220
    },
    {
      title: '场景类型',
      dataIndex: 'sceneType',
      width: 80
    },
    {
      title: '描述',
      dataIndex: 'modelDesc',
      width: 220
    }
  ];

  const formItemList: FormItemListProps[] = [
    {
      placeholder: '决策表编号',
      key: 'modelNumber',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '决策表名称',
      key: 'modelName',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '场景类型',
      key: 'sceneType',
      type: 'input',
      position: 'show'
    }
  ];

  const { tableProps, search, run } = useAntdTable(
    (p) => {
      return selectPageForMaxVersion({
        ...p,
        pageNum: p?.current,
        modelType: '2'
      })
        .then((res) => {
          return { list: res?.data?.list, total: res?.data?.total || 0 };
        })
        .catch(() => {});
    },
    {
      defaultParams: [
        {
          pageNum: 1,
          pageSize: 10,
          approveStatus: 0
        }
      ]
    }
  );

  const onCancel = () => {
    changeVisible(false);
  };

  const onOkCallback = () => {
    onOK(selectUserList);
    onCancel();
  };

  return (
    <YRModal
      width={'50%'}
      destroyOnClose
      onCancel={() => {
        onCancel();
      }}
      open={visible}
      title={'引入决策表'}
      footer={[
        <YRButton key="back" onClick={() => onCancel()}>
          取消
        </YRButton>,
        <YRButton key="submit" type="primary" onClick={() => onOkCallback()}>
          确认
        </YRButton>
      ]}
    >
      <YRTable
        rowKey="id"
        columns={getTableColumns}
        formItemList={formItemList}
        rowSelection={rowSelection}
        handleSearch={(reqParams) => {
          run({ ...reqParams, current: 1, pageSize: 10 });
        }}
        {...tableProps}
      />
    </YRModal>
  );
};

export default AddDecisionModal;
