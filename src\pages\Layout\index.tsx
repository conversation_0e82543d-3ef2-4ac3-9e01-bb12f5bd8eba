import React, { useEffect, useMemo, useState } from 'react';
import { pathToRegexp } from 'path-to-regexp';
import { Link } from 'ice';
import { isInIcestark } from '@ice/stark-app';
import { ProLayout } from '@yr/pro-layout';
import type { BasicLayoutProps } from '@yr/pro-layout';
import { formatCustomMenuTree, router } from '@/utils/utils';
import store from '@/store';
import logo from '@/assets/suzhoulogo.png';
import RenderContent from './RenderContent';

const YRLayout = (props) => {
  const { children, location } = props;
  const [pathname, setPathname] = useState('/');
  const [userState] = store.useModel('user');
  const [microState] = store.useModel('micro');
  const [layoutState] = store.useModel('layout');
  const { menuData } = userState;
  const { initialState } = layoutState;
  const { allFullPath } = microState;

  const menuDataList = useMemo(() => {
    if (menuData.length) {
      return formatCustomMenuTree(menuData);
    }

    return [];
  }, [menuData]);

  useEffect(() => {
    setPathname(location.pathname);
  }, [location.pathname]);

  // 是否全屏
  const isFullPath = useMemo(() => {
    return allFullPath.some((item) => item && pathToRegexp(item).test(pathname));
  }, [allFullPath, pathname]);

  const menuItemRender = (item, defaultDom) => {
    return <Link to={item.path}>{defaultDom}</Link>;
  };

  // 嵌入基座时不使用自身布局
  if (isInIcestark()) return children;

  const proLayoutConfig: BasicLayoutProps = {
    logo,
    ...initialState.settings,
    pure: isFullPath,
    route: {
      routes: menuDataList
    },
    location: { pathname },
    menuItemRender,
    disableContentMargin: false,
    collapsedButtonRender: false,
    onMenuHeaderClick: () => router.push('/'),
    rightContentRender: () => <RenderContent initialState={initialState} />
  };

  return <ProLayout {...proLayoutConfig}>{children}</ProLayout>;
};

export default YRLayout;
