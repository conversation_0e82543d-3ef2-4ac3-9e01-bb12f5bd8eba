/*
 * yunrong.cn Inc. Copyright (c) 2014-2020 All Rights Reserved
 */

/**
 * 审批流程信息配置
 * {
 *  action: 动作描述, 作为[功能中心]的功能名称,
 *  noFnCenter: 是否不加入功能菜单, 不配这个字段则会加入功能中心, 配了true则不加入功能中心列表
 *  taskType: 任务类型, 作为[工作台]筛选条件, 作为任务标题,
 *  processId: 审批流程Id,
 *  detailUrl: [待办、已办、我发起的]等详情页面地址,
 *  editUrl: [我发起的]编辑页面地址,
 *  addUrl: [功能中心]发起任务地址(可不配置, 默认取editUrl),
 * }
 */

// 客户
const customer = {
  template: {
    action: '个人客户信息变更',
    taskType: '个人客户信息变更',
    processId: 'SP10000066',
    detailUrl: '/workplat/task-detail/peosonal-change',
    editUrl: '/customer/personal-change'
  }
};

/**
 * 给每个模块的每个对象加上分组信息, 用于功能中心里的分组
 */
Object.keys(customer).forEach((k) => {
  customer[k].group = '客户';
});

/**
 * 组装成一个对象
 */
const processInfo = {
  ...customer
};

/**
 * 根据processId查找对象
 * @param { string } processId 流程id
 */
export function matchProcessId(processId) {
  const match = Object.keys(processInfo).filter((k) => processInfo[k].processId === processId);
  if (match && match.length > 0) {
    return processInfo[match[0]];
  }
  throw new Error(`processId[${processId}]未配置`);
}

/**
 * 导出配置信息
 */
export { processInfo };
