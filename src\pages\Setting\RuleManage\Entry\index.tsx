/**
 * 页面描述: 规则条目管理页
 * @文件名 index.tsx
 * @filePath \src\pages\Setting\RuleManager\Entry\index.tsx
 * @Date 2023-08-01 17:29:54
 * @Email <EMAIL>
 * <AUTHOR>
 */
import React, { useRef, useState } from 'react';
import { YRFlexPageLayout, YREmpty } from 'yrantd';
import EntryTree from './components/EntryTree';
import EntryInfo from './components/EntryInfo';

const Entry = () => {
  const [activeNode, setActiveNode] = useState({}) as any;

  return (
    <YRFlexPageLayout>
      <YRFlexPageLayout.Sider title="规则条目目录">
        <EntryTree setActiveNode={setActiveNode} />
      </YRFlexPageLayout.Sider>
      <YRFlexPageLayout.Main title="条目信息">
        {activeNode?.treeId ? (
          <EntryInfo activeNode={activeNode} />
        ) : (
          <YREmpty style={{ marginTop: '60px' }} />
        )}
      </YRFlexPageLayout.Main>
    </YRFlexPageLayout>
  );
};

export default Entry;
