/**
 * @Author: wangyw26123
 * @Description: hooks事件配置
 * @Date: Created in 2022-04-25 23:04:51
 * @Modifed By:
 */

import { EdgeNode } from '../NodeType';
// @ts-ignore
import { XFlowNodeCommands } from '@yr/xflow';
import { nodeTypeNames } from '@/pages/Flow/XFlow/constants';
import XflowInstance from '@/pages/Flow/XFlow/XflowInstance';
import { uuidv4 } from '@antv/xflow';
// import { appCycles } from 'create-app-shared/lib/appLifeCycles';

// 事件绑定
const hooksConfig = () => {
  return [
    {
      name: 'set-edge-attr',
      action: 'addEdge',
      handler: (args) => {
        const obj: any = {};
        const id = args.edgeConfig?.id;
        args.edgeConfig = {
          nodeFormData: { ...new EdgeNode(obj) },
          ...args.edgeConfig,
          name: nodeTypeN<PERSON>s['SequenceFlow'],
          edgePanel: ['edgeInfo'],
          edgeTabs: ['baseInfo'],
          id: id?.includes('node-') ? id : `node-${uuidv4()}`
        };
      }
    },
    {
      name: 'select-add-edge',
      action: 'addEdge',
      handler: (args) => {
        // 避免初始化时进行节点的选中
        if (XflowInstance.isAddNode) {
          // 新增边时选中，用于初始化表单字段
          const timer = setTimeout(() => {
            XflowInstance.instance.resetSelection(args.edgeConfig.id);
            clearTimeout(timer);
          }, 100);
        }
      }
    },
    {
      name: 'select-del-edge',
      action: 'delEdge',
      handler: async (args) => {
        const { edgeConfig } = args;
        deleteEdge(edgeConfig);
      }
    },
    {
      name: 'select-add-node',
      action: 'addNode',
      handler: (args) => {
        // 避免初始化时进行节点的选中
        if (XflowInstance.isAddNode) {
          // 新增节点时选中，用于初始化表单字段
          const timer = setTimeout(() => {
            XflowInstance.app.executeCommand(XFlowNodeCommands.SELECT_NODE.id, {
              nodeIds: [args.nodeConfig.id],
              resetSelection: true
            });
            clearTimeout(timer);
          }, 100);
        }
      }
    },
    {
      name: 'select-del-node',
      action: 'delNode',
      handler: async (args) => {
        // 为了修复删除节点无法删除当前节点前后节点的 outgoingEdges、incomingEdges 的 bug
        const { nodeConfig } = args;
        const currentNode = await XflowInstance.app.getNodeById(nodeConfig?.id);
        const currentNodeConfig = currentNode?.store.data.data || {};
        // 1. 获取当前节点的前后边的 config
        const outgoingEdges = currentNodeConfig.outgoingEdges ? [...currentNodeConfig.outgoingEdges] : [];
        const incomingEdges = currentNodeConfig.incomingEdges ? [...currentNodeConfig.incomingEdges] : [];
        // console.log(currentNodeConfig, 'nodeConfig ------------------', currentNode);
        // 2. 删除边
        outgoingEdges?.forEach((_item) => {
          deleteEdge(_item, nodeConfig);
        });

        incomingEdges?.forEach((_item) => {
          deleteEdge(_item, nodeConfig);
        });
      }
    },
    {
      name: 'after-graph-init',
      action: 'afterGraphInit',
      handler: () => {
        XflowInstance.initLoad = true;
      }
    }
  ];
};

// 删除边操作, 判断是否是在删除节点时删除边
const deleteEdge = async (edgeConfig, nodeConfig?) => {
  // 此删除边的hooks有bug，因为新增边时也会触发，所以暂且使用 source、target做判断。
  // 删除节点时，此方法也会触发所以需要对 edgeConfig 做判空处理
  if (!edgeConfig?.source && !edgeConfig?.target) return;

  const sourceNode = await XflowInstance.app.getNodeById(edgeConfig.source.cell);
  const targetNode = await XflowInstance.app.getNodeById(edgeConfig.target.cell);
  const sourceNodeConfig = sourceNode?.store.data.data || {};
  const targetNodeConfig = targetNode?.store.data.data || {};
  const sourceOutgoingEdges = sourceNodeConfig.outgoingEdges ? [...sourceNodeConfig.outgoingEdges] : [];
  const targetIncomingEdges = targetNodeConfig.incomingEdges ? [...targetNodeConfig.incomingEdges] : [];

  for (let i = 0; i < sourceOutgoingEdges.length; i++) {
    if (sourceOutgoingEdges[i].id === edgeConfig.id) {
      sourceOutgoingEdges.splice(i, 1);
      break;
    }
  }

  for (let i = 0; i < targetIncomingEdges.length; i++) {
    if (targetIncomingEdges[i].id === edgeConfig.id) {
      targetIncomingEdges.splice(i, 1);
      break;
    }
  }

  // 边的删除不会更新到节点上，所以需要在删除边时手动更新节点, 如果节点存在才更新，不然不更新
  if (Object.keys(sourceNodeConfig).length) {
    if (nodeConfig?.id !== sourceNodeConfig?.id) {
      XflowInstance.updateNode({ ...sourceNodeConfig, outgoingEdges: sourceOutgoingEdges });
    }
  }
  if (Object.keys(targetNodeConfig).length) {
    if (nodeConfig?.id !== targetNodeConfig?.id) {
      XflowInstance.updateNode({ ...targetNodeConfig, incomingEdges: targetIncomingEdges });
    }
  }

  // 清空选区 并更新节点的数据
  XflowInstance.instance.cleanSelection();
};

export default hooksConfig;
