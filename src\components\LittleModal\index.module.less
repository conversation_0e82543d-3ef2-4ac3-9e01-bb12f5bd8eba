// :global {
//   .ant-popover {
//     z-index: 999 !important;
//   }
// }

.little-modal {
  :global {
    .ant-popover-title {
      justify-content: space-between;
    }
  }
}
.little-modal {
  :global {
    .ant-popover-arrow {
      display: none;
    }
  }
}
.little-modal-title {
  display: flex;
  justify-content: space-between;
}
.little-modal-title .text {
  flex: 0 0 auto;
}
.little-modal-title .oper {
  flex: 0 0 auto;
}
.little-modal-buttons {
  margin-top: 10px;
  text-align: right;
}
.little-modal-buttons {
  :global {
    .ant-btn {
      height: 26px;
      padding: 0 12px;
    }
  }
}

.space {
  margin-left: 12px;
}

.little-modal-cont {
  :global {
    .ant-form-item-control .ant-form-explain {
      display: none;
    }
  }
}

.little-modal-cont {
  :global {
    .ant-form .ant-form-item {
      margin-bottom: 8px;
    }
  }
}
