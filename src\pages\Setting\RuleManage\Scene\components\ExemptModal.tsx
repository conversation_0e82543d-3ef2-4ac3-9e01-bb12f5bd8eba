/**
 * 页面描述: 豁免组编辑弹框
 * @文件名 ExemptModal.tsx
 * @filePath \src\pages\Setting\RuleManage\Scene\components\ExemptModal.tsx
 * @Date 2023-08-18 11:00:43
 * @Email <EMAIL>
 * <AUTHOR>
 */
import React, { useEffect, useMemo, useState } from 'react';
import {
  YRClassificationLayout,
  YREasyUseModal,
  YRForm,
  YRModal,
  YRInput,
  YRTextAreaPlus,
  YRButton,
  YRDict,
  YRTooltip,
  YRLink,
  YRTable,
  YRMessage,
  YRConfirmBtn
} from 'yrantd';
import store from '@/store';
import { businessStageNames, businessTacheItem, tacheNameAndStage } from '../config';
import { formatRuleList, splitRuleList } from '../util';
import RuleContext from '@/pages/Setting/RuleManage/Entry/components/RuleContext';
import { getRuleData, formatRuleData } from '@/pages/Setting/RuleManage/Entry/util/helper';
import ChooseRuleModal from './ChooseRuleModal';

const ExemptModal = (props) => {
  const { defaultData = {}, operateType, activeNode, stageList, tacheList, okCallback } = props;
  const disabled = operateType === 'detail';
  const modal = YREasyUseModal.useModal();
  const [form] = YRForm.useForm();
  const [ruleState, ruleDispatch] = store.useModel('rule');
  const { ruleData, ruleDataMap } = ruleState;
  const [exemptRuleList, setExemptRuleList] = useState([]) as any; // 豁免规则列表

  useEffect(() => {
    ruleDispatch.getDictList();
    ruleDispatch.getParameterList({ pageNum: 1, pageSize: 9999 });
  }, []);

  useEffect(() => {
    const exemptRuleDtoList = [...(defaultData?.exemptRuleDtoList || [])];
    const express = defaultData?.express ? JSON.parse(defaultData?.express) : '';
    const formatExemptRuleList = splitRuleList(exemptRuleDtoList);
    const { treeData, treeDataMap } = getRuleData(express);
    ruleDispatch.setType(operateType);
    ruleDispatch.setRuleData(treeData);
    ruleDispatch.setRuleDataMap(treeDataMap);
    setExemptRuleList(formatExemptRuleList);
    form.setFieldsValue(defaultData);
  }, [defaultData, operateType]);

  // 筛选当前目录选择的阶段
  const getFilterStage = (list) => {
    const { nodeType, key } = activeNode;
    switch (nodeType) {
      case 'all':
        return [...list];
      case 'stage':
        return list.filter((item) => businessStageNames[item] === key);
      case 'tache':
        return list.filter((item) => item === tacheNameAndStage[key].stage);
      default:
        return [];
    }
  };

  // 筛选当前目录选择的阶段
  const getFilterTache = (list) => {
    const { nodeType, key } = activeNode;
    switch (nodeType) {
      case 'all':
        return [...list];
      case 'stage':
        return [...list];
      case 'tache':
        return list.filter((item) => item === key);
      default:
        return [];
    }
  };

  // 阶段及环节列(用于拼接规则列表)
  const tacheColumns = useMemo(() => {
    const defaultColumns = [] as any;
    // 筛选当前目录选择的阶段及环节
    const newStageList = getFilterStage(stageList);
    const newTacheList = getFilterTache(tacheList);
    if (newStageList.length > 0 && newTacheList.length > 0) {
      newStageList.forEach((item) => {
        // 寻找该阶段下的环节
        const childList = newTacheList.filter((v) => {
          const tacheItem = businessTacheItem[item] as any;
          return tacheItem.find((i) => i?.value === v);
        });
        if (childList && childList.length > 0) {
          defaultColumns.push({
            title: businessStageNames[item],
            dataIndex: businessStageNames[item],
            width: 120 * childList.length,
            children: childList.map((key) => {
              return {
                title: tacheNameAndStage[key]?.name,
                dataIndex: key,
                width: 120,
                render: (value, row, index) => {
                  return (
                    <YRDict.Select
                      style={{ width: '100%' }}
                      disabled={disabled}
                      defaultValue={value}
                      dictkey="EnumBool"
                      onChange={(val) => {
                        const newList = [...exemptRuleList] as any;
                        const newItem = {
                          ...(newList[index] || {}),
                          [key]: val
                        };
                        newList.splice(index, 1, newItem);
                        setExemptRuleList(newList);
                      }}
                    />
                  );
                }
              };
            })
          });
        }
      });
    }
    return defaultColumns;
  }, [stageList, tacheList, exemptRuleList, disabled]);

  // 豁免规则列表
  const exemptRuleColumns = useMemo(() => {
    const defaultColumns = [
      {
        title: '分类',
        dataIndex: 'treeName',
        width: 120,
        render: (value) => {
          return (
            <YRTooltip onlyShow title={value}>
              <div className="ellipsis">{value}</div>
            </YRTooltip>
          );
        }
      },
      {
        title: '探测项',
        dataIndex: 'itemName',
        width: 150,
        render: (value) => {
          return (
            <YRTooltip onlyShow title={value}>
              <div className="ellipsis">{value}</div>
            </YRTooltip>
          );
        }
      },
      ...tacheColumns,
      ...(disabled
        ? []
        : [
          {
            title: '操作',
            dataIndex: 'operate',
            width: 80,
            fixed: 'right',
            render: (value, row, index) => {
              return (
                <YRConfirmBtn msg="确定删除吗？" type="pop" onConfirm={() => deleteItem(index)}>
                  删除
                </YRConfirmBtn>
              );
            }
          }
        ])
    ] as any;
    return defaultColumns;
  }, [tacheColumns, exemptRuleList, disabled]);

  // 新增规则
  const addRuleItem = (selectedItems) => {
    const newList = [...exemptRuleList];
    selectedItems.forEach((item) => {
      const { itemId, itemName, treeName, version } = item;
      newList.push({
        itemId,
        itemName,
        treeName,
        version
      });
    });
    setExemptRuleList(newList);
  };

  // 删除规则
  const deleteItem = (index) => {
    const newList = [...exemptRuleList];
    newList.splice(index, 1);
    setExemptRuleList(newList);
  };

  // 保存
  const submit = () => {
    const newData = formatRuleData(ruleData, ruleDataMap);
    const formatGenericList = formatRuleList(tacheList, exemptRuleList);
    if (!newData) {
      return;
    }
    if (!exemptRuleList || exemptRuleList?.length < 1) {
      YRMessage.error('规则条目列表不能为空');
      return;
    }
    if (formatGenericList.find((item) => !item?.flag)) {
      YRMessage.error('请选择豁免项');
      return;
    }
    form.validateFields().then((value) => {
      const params = {
        ...(value || {}),
        express: JSON.stringify(newData),
        exemptRuleDtoList: formatGenericList
      };
      okCallback(params);
      modal.remove();
    });
  };

  const operationRender = (
    <YRButton
      type="primary"
      onClick={() => {
        YREasyUseModal.show(ChooseRuleModal, {
          okCallback: addRuleItem,
          selectedList: exemptRuleList
        });
      }}
    >
      新增规则
    </YRButton>
  );

  return (
    <YRModal
      title="规则条目详情"
      onOk={submit}
      onCancel={modal.remove}
      footer={
        operateType === 'detail'
          ? null
          : [
            <YRButton key="cancel" onClick={() => modal.remove()}>
              取消
            </YRButton>,
            <YRButton key="confirm" type="primary" onClick={submit}>
              确定
            </YRButton>
          ]
      }
      open={modal.visible}
      destroyOnClose
      maskClosable={false}
      width={'80%'}
    >
      <YRForm form={form} mode={operateType === 'detail' ? 'readPretty' : 'edit'}>
        <YRClassificationLayout.Space>
          <YRClassificationLayout title="基本信息">
            <YRForm.Row>
              <YRForm.Item name="exemptId" label="豁免组编号">
                <YRInput disabled />
              </YRForm.Item>
              <YRForm.Item
                name="exemptName"
                label="豁免组名称"
                rules={[
                  { required: true },
                  {
                    max: 128,
                    message: '长度不能超过128字节!'
                  }
                ]}
              >
                <YRInput />
              </YRForm.Item>
              <YRForm.Item
                name="description"
                label="豁免组描述"
                rules={[
                  { required: true },
                  {
                    max: 1000,
                    message: '长度不能超过1000字节!'
                  }
                ]}
                column={'block'}
                labelCol={{ span: 4 }}
                wrapperCol={{ span: 20 }}
              >
                <YRTextAreaPlus maxLength={1000} />
              </YRForm.Item>
            </YRForm.Row>
          </YRClassificationLayout>
          <YRClassificationLayout title="豁免组条件">
            <RuleContext />
          </YRClassificationLayout>
          <YRClassificationLayout title="豁免组规则条目列表">
            <YRTable
              options={false}
              columns={exemptRuleColumns || []}
              dataSource={exemptRuleList || []}
              rowKey="itemId"
              operationRender={disabled ? null : operationRender}
            />
          </YRClassificationLayout>
        </YRClassificationLayout.Space>
      </YRForm>
    </YRModal>
  );
};

export default YREasyUseModal.create(ExemptModal);
