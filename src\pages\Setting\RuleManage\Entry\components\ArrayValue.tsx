import React, { useMemo, useState } from 'react';
import { YRLink, YRSelect } from 'yrantd';
import store from '@/store';

const ArrayValue = (props) => {
  const { setDict, dictCode, value, setDictItem, dictItem } = props;
  const [ruleState, ruleDispatch] = store.useModel('rule');
  const { type, dictList } = ruleState;
  const [arrayVisible, setArrayVisible] = useState(false);
  const [itemVisible, setItemVisible] = useState(false);
  const styleObj = { display: 'inline-block' };

  // 数组中文值
  const dictName = useMemo(() => {
    const selectedItem = dictList.find((item) => item?.dictKey === dictCode) || {};
    return selectedItem?.dictName || '请选择数组';
  }, [dictList, dictCode]);

  // 码值中文值
  const itemName = useMemo(() => {
    if (dictItem.length > 0 && value.length > 0) {
      const allName = (value || []).map((item) => dictItem.find((v) => v?.itemKey === item)?.itemName).join(',');
      return allName;
    } else {
      return '请选择码值';
    }
  }, [dictItem, value]);

  // 全选所有码值
  const selectAllItem = () => {
    const allItemList = dictItem.map((item) => item?.itemKey);
    setDictItem(allItemList);
  };

  return (
    <>
      {arrayVisible ? (
        <YRSelect
          showSearch
          placeholder="请选择数组"
          size="small"
          autoFocus
          style={{ width: 150, marginLeft: 10 }}
          options={dictList || []}
          filterOption={(input, option) => (option?.dictName || '').includes(input)}
          fieldNames={{ label: 'dictName', value: 'dictKey' }}
          defaultValue={dictCode}
          onSelect={(val) => setDict(val)}
          onBlur={() => setArrayVisible(false)}
        />
      ) : (
        <span
          className="parameter-label"
          style={styleObj}
          onClick={() => {
            if (type !== 'detail') {
              setArrayVisible(true);
            }
          }}
        >
          {dictName}
        </span>
      )}
      {dictCode &&
        (itemVisible ? (
          <>
            <YRSelect
              size="small"
              style={{ width: 500, margin: '0px 10px' }}
              mode="multiple"
              maxTagCount={3}
              showSearch
              autoFocus
              placeholder="选择码值后回车取消选择框"
              options={dictItem || []}
              filterOption={(input, option) => (option?.itemName || '').includes(input)}
              fieldNames={{ label: 'itemName', value: 'itemKey' }}
              value={value}
              onInputKeyDown={(e) => {
                e?.preventDefault();
                e?.stopPropagation();
                setItemVisible(false);
              }}
              onChange={(val) => setDictItem(val)}
            />
            <YRLink
              type="primary"
              onClick={() => {
                setItemVisible(false);
                selectAllItem();
              }}
            >
              全选
            </YRLink>
          </>
        ) : (
          <span
            style={{ marginLeft: 10 }}
            onClick={() => {
              if (type !== 'detail') {
                setItemVisible(true);
              }
            }}
          >
            ({itemName})
          </span>
        ))}
    </>
  );
};

export default ArrayValue;
