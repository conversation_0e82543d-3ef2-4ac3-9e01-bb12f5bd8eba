/**
 * @页面描述: 柜员管理列表表格
 * @文件名 index.tsx
 * @Path src\pages\User\CounterEmployee\TellerUserList\index.tsx
 * @Date 2023-03-29 14:42:38
 * <AUTHOR>
 */

import React from 'react';
import {
  YRBadge,
  YRButton,
  YRDict,
  YREasyUseModal,
  YRForm,
  YRLink,
  YRTable,
  YRTableProps,
  YRTableRowContent
} from 'yrantd';
import { useAntdTable } from 'ahooks';
import { queryTellerPage } from '../../../../services/user';
import { EnumGuardUserStatusColor } from '../../../../constant/StyleConst';
import { FormItemListProps } from 'yrantd/lib/yr-query-filter/declare';
import Dict from '../../mock/getDict';
import Detail from './Detail';
import { DefaultNode } from '../Interface';
import { useAntd } from '../Context';

function Index() {
  const [form] = YRForm.useForm();
  const antd = useAntd();
  const currentOrganId = antd.selectedState[0].organId ?? DefaultNode.organId;
  const { run, tableProps } = useAntdTable(
    (param) =>
      queryTellerPage({
        ...param,
        pageNum: param.current || 1,
        pageSize: param.pageSize || 10,
        current: param.current || 1,
        organId: currentOrganId
      }).then((res) => {
        if (res?.success) {
          return {
            list: res.data.list || [],
            total: res.data.total || 0
          };
        } else {
          return { list: [], total: 0 };
        }
      }),
    {
      defaultParams: [{ pageNum: 1, pageSize: 10, organId: DefaultNode.organId, current: 1 }],
      refreshDeps: [currentOrganId]
    }
  );
  const generateColumns = (): YRTableProps['columns'] => {
    const width = 150;
    return [
      {
        title: '用户姓名',
        dataIndex: 'userName',
        key: 'userName',
        width,
        render: (val, row) => (
          <YRLink type="primary" onClick={() => handleGoDetail(row?.userId)}>
            {val}
          </YRLink>
        )
      },
      {
        title: '登录账号',
        dataIndex: 'accountNo',
        key: 'accountNo',
        width
      },
      {
        title: '角色',
        dataIndex: 'roleName',
        key: 'roleName',
        width: width * 2,
        render: (val, row) => {
          const mainRole =
            Array.isArray(row.userIdentifyDtoList) &&
            row.userIdentifyDtoList.find((item) => item.identityRelationType === '001');
          const partRole = Array.isArray(row.userIdentifyDtoList)
            ? row.userIdentifyDtoList.filter((item) => item.identityRelationType === '002')
            : [];
          return (
            <>
              <YRTableRowContent key={mainRole.identifyId} title="本职角色">
                {mainRole?.identityName}
              </YRTableRowContent>
              <YRTableRowContent key="兼职角色" title="兼职角色">
                {partRole.length > 0 ? partRole.map((item) => item?.identityName).join('、') : CONST.null}
              </YRTableRowContent>
            </>
          );
        }
      },
      {
        title: '所属机构',
        dataIndex: 'organName',
        key: 'organName',
        width
      },
      {
        title: '手机号码',
        dataIndex: 'mobile',
        key: 'mobile',
        width
      },
      {
        title: '用户状态',
        dataIndex: 'userStatus',
        key: 'userStatus',
        width: width - 70,
        render: (value: string) => {
          if (!value) return CONST.null;
          return (
            <YRBadge
              color={EnumGuardUserStatusColor[value]}
              text={<YRDict.Text dictkey="EnumGuardUserStatus" defaultValue={value} />}
            />
          );
        }
      },
      {
        title: '岗位',
        dataIndex: 'post',
        key: 'post',
        width
      },
      {
        title: '所属HR机构',
        dataIndex: 'hrOrganName',
        key: 'hrOrganName',
        width
      },
      {
        title: '操作',
        dataIndex: 'operate',
        key: 'operate',
        fixed: 'right',
        width: 80,
        render: (val, row) => (
          <YRButton.Overflow>
            <YRLink type="primary" onClick={() => handleGoDetail(row?.userId)}>
              详情
            </YRLink>
            <YRLink type="primary" onClick={() => {}}>
              柜员信息同步
            </YRLink>
          </YRButton.Overflow>
        )
      }
    ];
  };
  const formItenList: FormItemListProps[] = [
    {
      label: '用户姓名',
      placeholder: '用户姓名',
      key: 'userName',
      type: 'input',
      position: 'show'
    },
    {
      label: '用户状态',
      placeholder: '用户状态',
      key: 'userStatus',
      type: 'dictSelect',
      dictkey: 'EnumGuardUserStatus',
      position: 'show'
    },
    {
      label: '登录账号',
      placeholder: '登录账号',
      key: 'accountNo',
      type: 'input',
      position: 'show'
    },
    {
      label: '手机号',
      placeholder: '手机号',
      key: 'mobile',
      type: 'input',
      position: 'show'
    }
  ];
  const handleSearch = (): void => {
    const values: any = form.getFieldsValue();
    run(values);
  };

  const handleGoDetail = (userId: string): void => {
    YREasyUseModal.show(Detail, { userId });
  };
  return (
    <YRTable
      columns={generateColumns()}
      formItemList={formItenList}
      extAction={<YRButton type="primary">导出</YRButton>}
      handleSearch={handleSearch}
      form={form}
      {...tableProps}
    />
  );
}

export default Dict(['EnumGuardUserStatus'])(Index);
