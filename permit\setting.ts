/**
 * 设置
 */
import { YRLoanPermit } from 'yr-loan-antd';
import packageInfo from '../package.json';
import { modifyHolidayInfo } from '@/services/setting';

const { buildPermit, EnumPermitType } = YRLoanPermit;

/**
 * 数据字典
 */
const M0301 = buildPermit('M0301', packageInfo, {
  interfaces: {}
});
/**
 * 业务参数
 */
const M0302 = buildPermit('M0302', packageInfo, {
  interfaces: {}
});
/**
 * 汇率
 */
const M0303 = buildPermit('M0303', packageInfo, {
  interfaces: {}
});
/**
 * 节假日
 */
const M0304 = buildPermit('M0304', packageInfo, {
  interfaces: {},
  E01: {
    name: '假期维护',
    type: EnumPermitType.Element,
    interfaces: { modifyHolidayInfo }
  }
});
/**
 * 基准利率（LPR）
 */
const M0305 = buildPermit('M0305', packageInfo, {
  interfaces: {}
});
/**
 * 风险探测分组
 */
const M0306 = buildPermit('M0306', packageInfo, {
  interfaces: {}
});
/**
 * 风险探测场景
 */
const M0307 = buildPermit('M0307', packageInfo, {
  interfaces: {}
});

/**
 * 规则管理
 */
const M030801 = buildPermit('M030801', packageInfo, {
  interfaces: {}
});

/**
 * 场景管理
 */
const M030802 = buildPermit('M030802', packageInfo, {
  interfaces: {}
});

/**
 * 变量管理管理
 */
const M0309 = buildPermit('M0309', packageInfo, {
  interfaces: {}
});

/**
 * 数据源管理
 */
const M0310 = buildPermit('M0310', packageInfo, {
  interfaces: {}
});

/**
 * 表单配置
 */
const M0311 = buildPermit('M0311', packageInfo, {
  interfaces: {}
});

export { M0301, M0302, M0303, M0304, M0305, M0306, M0307, M0309, M0310, M0311, M030801, M030802 };
