{"compileOnSave": false, "buildOnSave": false, "compilerOptions": {"experimentalDecorators": true, "baseUrl": ".", "outDir": "build", "module": "esnext", "target": "es6", "jsx": "react", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "lib": ["es6", "dom"], "sourceMap": true, "allowJs": true, "rootDir": "./", "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": false, "importHelpers": true, "strictNullChecks": true, "suppressImplicitAnyIndexErrors": true, "noUnusedLocals": true, "skipLibCheck": true, "resolveJsonModule": true, "paths": {"@/*": ["./src/*"], "ice": [".ice/index.ts"], "@permit/*": ["./permit/*"]}}, "include": ["src/*", ".ice"], "exclude": ["node_modules", "build", "public"]}