/**
 * @Author: wangyw26123
 * @Description: 流程管理
 * @Date: Created in 2022-05-11 00:47:57
 * @Modifed By:
 */
import { GlobalFormData } from '@/pages/Flow/XFlow/NodeType';

export default {
  state: {
    globalFormData: new GlobalFormData({}),
    originGlobalFormData: null,
    processBaseInfo: {},
    // 自定义规则列表
    customRules: [],
    // 参数库详情列表
    paramDetailInfoList: [],
    // 表达式规则列表
    expressRuleParam: {},
    // 是否开启实施保存
    startRealTimeSave: false
  },
  effects: (dispatch) => ({}),
  reducers: {
    setGlobalFormData(prevState, payload) {
      return {
        ...prevState,
        globalFormData: payload
      };
    },
    setOriginGlobalFormData(prevState, payload) {
      return {
        ...prevState,
        originGlobalFormData: payload
      };
    },
    setProcessBaseInfo(prevState, payload) {
      return {
        ...prevState,
        processBaseInfo: payload
      };
    },
    setCustomRules(prevState, payload) {
      return {
        ...prevState,
        customRules: payload
      };
    },
    setParamDetailInfoList(prevState, payload) {
      return {
        ...prevState,
        paramDetailInfoList: payload
      };
    },
    setExpressRuleParam(prevState, payload) {
      return {
        ...prevState,
        expressRuleParam: payload
      };
    },
    setStartRealTimeSave(prevState, payload) {
      return {
        ...prevState,
        startRealTimeSave: payload
      };
    }
  }
};
