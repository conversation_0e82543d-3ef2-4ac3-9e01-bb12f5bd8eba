/**
 * @Author: wangyw26123
 * @Description: 工单
 * @Date: Created in 2022-12-12 17:04:45
 * @Modifed By:
 */
import React, { useRef, useEffect } from 'react';
import store from '@/store';
import styles from './index.module.less';
import { YRButton, YRMessage, YRDict, YRDropdown, YRIcon, YRSelect } from 'yrantd';
import { useSetState, useRequest } from 'ahooks';
import { closeWin, jsonParse } from 'yr-loan-antd/lib/util';
import {
  YRXFlow,
  YRXFlowAttrPanel,
  YRXFlowNodeAttrPanel,
  YRXFlowCanvasAttrPanel,
  YRXFlowEdgeAttrPanel
} from '@yr/xflow';
import { router, downloadByUrl } from '@/utils/utils';
import nodeConfig from './XFlow/config/nodeConfig';
import eventConfig from './XFlow/config/eventConfig';
import hooksConfig from './XFlow/config/hooksConfig';
import XflowInstance, { XflowNodes } from './XFlow/XflowInstance';
import FullPageLayout from '@/components/FullPageLayout';
import NodeAttrPanel from './XFlow/XFlowEdit/NodeAttrPanel';
import CanvasAttrPanel from './XFlow/XFlowEdit/CanvasAttrPanel';
import EdgeAttrPanel from './XFlow/XFlowEdit/EdgeAttrPanel';
import { GlobalFormData } from './XFlow/NodeType';
import { BaseNode, State } from './XFlow/Interface';
import Dict from '@/pages/Organ/mock/getDict';
import { M02 } from '@permit/flow';
import { fileExport } from '@/services/flow';

const { queryFlowDiagramResourceDetail } = M02.interfaces;
const { modifyFlowDiagramResource, addFlowDiagramResource } = M02.E03.interfaces;
const { deployProcess } = M02.E04.interfaces;
const { queryFlowDiagramResourceVersion } = M02.E05.interfaces;

const position = {
  width: 300,
  top: 40,
  bottom: 0,
  right: 0
};

const XflowIndex = (props) => {
  const { history } = props;
  const [flowState, dispatchers] = store.useModel('flow');

  const { globalFormData, processBaseInfo } = flowState;
  const { setProcessBaseInfo } = dispatchers;
  const graphApp = useRef<any>();
  const cacheGraphData = useRef({});

  const [state, setState] = useSetState<State>({
    isDisabled: false,
    btnLoading: false,
    pageLoading: false,
    modelName: '',
    modelNumber: '',
    id: '',
    modelDesc: '',
    sceneType: '',
    status: '',
    updateTime: '',
    version: '',
    modelResource: '',
    graphData: {
      edges: [],
      nodes: []
    }
  });
  const { data: versions = [] } = useRequest(
    (params) => {
      return queryFlowDiagramResourceVersion({ modelType: '1', ...params }).then((res) => res.data || {});
    },
    {
      ready: !!state.modelNumber,
      defaultParams: [
        {
          modelNumber: state.modelNumber
        }
      ]
    }
  );
  const handleQueryFlowDiagramResourceDetail = (id) => {
    // setState({ pageLoading: true });
    queryFlowDiagramResourceDetail({ id, modelType: '1' }).then((res) => {
      setState({ pageLoading: false });
      if (res?.success) {
        const processInfo = {
          ...res?.data,
          graphData: res?.data?.modelResource
            ? jsonParse(res?.data?.modelResource)?.graphData
            : { nodes: [], edges: [] }
        };
        setState(processInfo);
        setProcessBaseInfo(processInfo);
      }
    });
  };

  const handleSave = () => {
    // 保存之前需要清空画布中的选中，避免数据更新不及时导致校验出错。(内部应该有什么更新机制，暂时没找到，待后面优化)
    XflowInstance?.instance?.cleanSelection();

    // setState({ btnLoading: true });
    const timer = setTimeout(() => {
      graphApp.current.getGraphData().then((canvasData) => {
        // nodeValidator({ ...canvasData, globalFormData, processBaseInfo }, (status) => {
        // 校验不通过
        // if (!status) {
        //   setState({ btnLoading: false });
        //   return;
        // }

        const currentGraphData = {
          nodes: canvasData.nodes,
          edges: canvasData.edges
        };

        const { modelName, modelNumber, type, copyVersion } = state;
        const globalData = new GlobalFormData(globalFormData);
        const params = {
          id: state.id,
          modelNumber,
          modelName: globalData?.flowName || processBaseInfo?.flowName,
          modelDesc: globalData?.flowDesc || processBaseInfo?.flowDesc,
          sceneType: globalData?.sceneType || processBaseInfo?.sceneType,
          modelType: '1',
          modelResource: JSON.stringify({
            graphData: currentGraphData,
            global: {
              properties: {
                ...globalData,
                name: globalData?.flowName || processBaseInfo?.flowName,
                process_id: modelNumber,
                documentation: globalData?.flowDesc || processBaseInfo?.flowDesc,
                process_author: '',
                process_version: '',
                process_namespace: 'http://www.flowable.org/processdef',
                process_historylevel: '',
                isexecutable: true,
                dataproperties: '',
                executionlisteners: '',
                eventlisteners: '',
                messagedefinitions: '',
                escalationdefinitions: '',
                process_potentialstarteruser: '',
                process_potentialstartergroup: '',
                iseagerexecutionfetch: 'false',
                exclusionGroups: globalData?.exclusionGroups ?? [],
                includeGroups: globalData?.includeGroups ?? [],
                signaldefinitions: globalData?.signaldefinitions ?? []
              }
            }
          })
        };

        if (state.status == '1') {
          setState({ btnLoading: true });
          addFlowDiagramResource(params).then((res) => {
            setState({ btnLoading: false });
            if (res.success) {
              YRMessage.success('流程图新增成功');
              handleQueryFlowDiagramResourceDetail(res?.data);
              // 修改页面id，每次更新id会变
              router.push({ pathname: '/setting/flow/edit', query: { id: res?.data } });
            }
          });
        } else {
          setState({ btnLoading: true });
          modifyFlowDiagramResource(params).then((res) => {
            setState({ btnLoading: false });
            if (res.success) {
              YRMessage.success('流程图更新成功');
              handleQueryFlowDiagramResourceDetail(state.id);
            }
          });
        }
        // });
        clearTimeout(timer);
      });
    }, 1000);
  };

  const handleDeploy = () => {
    deployProcess({ id: state.id }).then((res) => {
      if (res?.success) {
        YRMessage.success('部署成功', 0.5, () => {
          location.reload();
        });
      }
    });
  };

  const onLoad = async (app) => {
    graphApp.current = app;
    const instance = await app.getGraphInstance();
    XflowInstance.setApp(app, instance);
  };

  const handleChangeVersion = (value) => {
    handleQueryFlowDiagramResourceDetail(value);
    history.replace({
      pathname: '/setting/flow/edit',
      search: `?id=${value}`
    });
  };
  const extInfo = [
    {
      key: '流程编号',
      value: state?.modelNumber || CONST.null
    },
    {
      key: '流程名称',
      value: state?.modelName || CONST.null
    },
    {
      key: '流程状态',
      value: <YRDict.Select type="text" defaultValue={state?.status} dictkey="flow_diagram_status" /> || CONST.null
    },
    {
      key: '流程版本',
      value: (
        <YRSelect style={{ width: 100 }} value={state?.version || CONST.null} onChange={handleChangeVersion}>
          {versions.map((item) => {
            return <YRSelect.Option value={item?.first}>{item?.second}</YRSelect.Option>;
          })}
        </YRSelect>
      )
    },
    {
      key: '更新时间：',
      value: state?.updateTime || CONST.null
    }
  ];

  // 导出
  const exportFile = () => {
    const params = {
      processId: state?.modelNumber || '',
      version: state?.version || ''
    };
    fileExport(params).then((res) => {
      if (res?.data) {
        YRMessage.success('导出成功');
        downloadByUrl(res?.data, '流程图');
      }
    });
  };

  const getLeft = () => {
    return {
      title: '流程图编辑',
      extInfo,
      goBack: () => router.push('/setting/flow')
    };
  };

  const getRightOper = () => {
    const com = [
      // <YRButton disabled={isDisabled} check="/download/flowChart" onClick={this.onDownload}>
      //   下载
      // </YRButton>,
      <YRButton type="primary" onClick={exportFile}>
        导出
      </YRButton>,
      <YRButton
        type="primary"
        loading={state.btnLoading}
        // check="/IFlowConfigManage/saveProcess"
        onClick={handleDeploy}
        disabled={state.status !== '0'}
        check={M02.E04}
      >
        部署
      </YRButton>,
      <YRButton
        type="primary"
        loading={state.btnLoading}
        // check="/IFlowConfigManage/saveProcess"
        onClick={handleSave}
        check={M02.E03}
      >
        {state.status === '0' ? '保存' : '保存新版本'}
      </YRButton>
    ];
    return com;
  };

  useEffect(() => {
    setState({ id: props.location.query.id });
  }, []);

  useEffect(() => {
    if (state.id) {
      handleQueryFlowDiagramResourceDetail(state.id);
    }
  }, [state.id]);

  return (
    <FullPageLayout left={getLeft()} right={getRightOper()} loading={state.pageLoading}>
      <div className={styles.configEdit}>
        <YRXFlow
          key={state.version}
          graphLayout={null}
          graphData={state.graphData}
          customNodeConfig={nodeConfig}
          eventConfig={eventConfig(props)}
          hooksConfig={hooksConfig(props)}
          onLoad={onLoad}
          nodePanelProps={{
            defaultActiveKey: ['nodeList']
          }}
          displayConfig={{
            node_panel: true
          }}
        >
          <YRXFlowAttrPanel position={position}>
            <YRXFlowNodeAttrPanel>
              {/* 节点属性面板 */}
              <NodeAttrPanel />
            </YRXFlowNodeAttrPanel>
            <YRXFlowEdgeAttrPanel>
              {/* 边属性面板 */}
              <EdgeAttrPanel />
            </YRXFlowEdgeAttrPanel>
            <YRXFlowCanvasAttrPanel>
              {/* 画布属性面板 */}
              <CanvasAttrPanel />
            </YRXFlowCanvasAttrPanel>
          </YRXFlowAttrPanel>
        </YRXFlow>
      </div>
    </FullPageLayout>
  );
};

export default Dict(['flow_scene_type', 'flow_diagram_status'])(XflowIndex);
