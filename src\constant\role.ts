/**
 * @Author: wangyw26123
 * @Description: 角色管理模块
 * @Date: Created in 2022-12-20 09:54:26
 * @Modifed By:
 */
import { EnumStatus } from './common';

/**
 * 角色状态 字典：EnumGuardRoleStatus
 */

enum EnumRoleStatusColor {
  '001' = EnumStatus.default, // 未启用
  '002' = EnumStatus.success, // 启用
  '003' = EnumStatus.warning // 停用
}

/**
 * 枚举角色操作类型
 */
enum EnumOperType {
  NOT_ENABLE = '001', // 未启用
  ENABLE = '002', // 启用
  DISABLE = '003', // 停用
  MODIFY = '004', // 修改
  DELETE = '005', // 删除
  VIEW = '006', // 查看
  ADD = '007' // 新增
}

/**
 * 用户状态
 */
enum EnumUserStatus {
  '010' = EnumStatus.success, // 启用
  '020' = EnumStatus.warning, // 停用
  '030' = EnumStatus.error, // 注销
  '040' = EnumStatus.default // 未启用
}

export { EnumRoleStatusColor, EnumOperType, EnumUserStatus };
