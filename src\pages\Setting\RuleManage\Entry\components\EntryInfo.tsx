/**
 * 页面描述: 规则条目列表
 * @文件名 EntryInfo.tsx
 * @filePath \src\pages\Setting\RuleManager\Entry\components\EntryInfo.tsx
 * @Date 2023-08-01 17:03:27
 * @Email <EMAIL>
 * <AUTHOR>
 */
import React, { useEffect } from 'react';
import {
  YRTable,
  YRForm,
  YRButton,
  YREasyUseModal,
  YRSpace,
  YRBadge,
  YRDict,
  YRLink,
  YRTimeDescription,
  YRTooltip,
  YRMessage,
  YRConfirmBtn
} from 'yrantd';
import { useAntdTable, useRequest } from 'ahooks';
import type { FormItemListProps, YRTableProps } from 'yrantd';
import { EnumRuleItemStatusColor } from '@/constant/StyleConst';
import { ruleItemPage, ruleStatusChange } from '@/services/setting';
import { setDict } from '@/utils/utils';
import { getDictName } from '@yr/util';
import RuleModal from './RuleModal';
import HistoryModal from './HistoryModal';
import RelationModal from './RelationModal';

setDict('EnumRuleItemStatus', [
  { itemKey: '010', itemName: '启用' },
  { itemKey: '020', itemName: '停用' },
  { itemKey: '030', itemName: '删除' }
]);

const EntryInfo = (props) => {
  const { activeNode = {} } = props;
  const { treeId, treeName, treeDtoList = [] } = activeNode;
  const addIf = !(treeDtoList && treeDtoList?.length > 0);
  const [form] = YRForm.useForm();

  // 规则条目启用停用删除
  const { run: changeRuleRun } = useRequest(ruleStatusChange, {
    manual: true,
    onSuccess: (result, params: any) => {
      const { errorMessage } = result as any;
      const status = params[0]?.status;
      if (errorMessage === null) {
        YRMessage.success(`${getDictName('EnumRuleItemStatus', status)}成功`);
        refresh();
      }
    }
  });

  const {
    run: queryEntryList,
    refresh,
    tableProps,
    params
  } = useAntdTable(
    (param) =>
      ruleItemPage({
        ...param,
        pageNum: param.current
      }).then((res) => {
        if (res?.success) {
          return {
            list: res.data.list || [],
            total: res.data.total || 0
          };
        } else {
          return {
            list: [],
            total: 0
          };
        }
      }),
    { manual: true }
  );

  useEffect(() => {
    if (treeId) {
      queryEntryList({
        treeId,
        current: 1,
        pageSize: 10
      });
    }
  }, [treeId]);

  const formItemList: FormItemListProps[] = [
    {
      placeholder: '规则条目名称',
      key: 'itemName',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '规则条目状态',
      key: 'status',
      type: 'dictSelect',
      dictkey: 'EnumRuleItemStatus',
      position: 'show',
      filterkeys: ['010', '020']
    }
  ];

  const getTableColumns: YRTableProps['columns'] = [
    {
      title: '规则条目编号',
      dataIndex: 'itemId',
      width: 200,
      render: (value: string) => {
        return (
          <YRTooltip title={value}>
            <div className="ellipsis">{value || CONST.null}</div>
          </YRTooltip>
        );
      }
    },
    {
      title: '规则条目名称',
      dataIndex: 'itemName',
      width: 150,
      render: (value: string) => {
        return (
          <YRTooltip title={value}>
            <div className="ellipsis">{value || CONST.null}</div>
          </YRTooltip>
        );
      }
    },
    {
      title: '规则条目创建日期',
      dataIndex: 'createTime',
      width: 100,
      render: (value: string) => {
        return <YRTimeDescription needMillisecond={false} time={value} isSmartMode={false} />;
      }
    },
    {
      title: '登记人',
      dataIndex: 'operatorName',
      width: 100,
      render: (value: string) => {
        return (
          <YRTooltip title={value}>
            <div className="ellipsis">{value || CONST.null}</div>
          </YRTooltip>
        );
      }
    },
    {
      title: '登记机构',
      dataIndex: 'ownOrganName',
      width: 100,
      render: (value: string) => {
        return (
          <YRTooltip title={value}>
            <div className="ellipsis">{value || CONST.null}</div>
          </YRTooltip>
        );
      }
    },
    {
      title: '规则条目状态',
      dataIndex: 'status',
      width: 100,
      fixed: 'right',
      render: (value: string) => {
        return value ? (
          <YRBadge
            color={EnumRuleItemStatusColor[value]}
            text={<YRDict.Text dictkey="EnumRuleItemStatus" defaultValue={value} />}
          />
        ) : (
          CONST.null
        );
      }
    },
    {
      title: '操作',
      dataIndex: 'operate',
      width: 120,
      fixed: 'right',
      render: (value, row: any) => (
        <YRButton.Overflow>
          {row?.status === '020' && (
            <YRLink
              type={'primary'}
              onClick={() => {
                YREasyUseModal.show(RuleModal, {
                  defaultData: row,
                  refresh,
                  type: 'edit'
                });
              }}
            >
              编辑
            </YRLink>
          )}
          <YRLink
            type={'primary'}
            onClick={() => {
              YREasyUseModal.show(RuleModal, {
                defaultData: row,
                refresh,
                type: 'detail'
              });
            }}
          >
            详情
          </YRLink>
          {row?.status === '020' && (
            <YRConfirmBtn
              type={'pop'}
              msg="删除后无法恢复，确定进行删除吗？"
              confirmProps={{ zIndex: 9999 }}
              onConfirm={() => {
                changeRuleRun({ ruleItem: row?.itemId, status: '030' });
              }}
            >
              删除
            </YRConfirmBtn>
          )}
          <YRLink
            type={'primary'}
            onClick={() => {
              YREasyUseModal.show(RuleModal, {
                defaultData: {
                  treeId: row?.treeId,
                  treeName: row?.treeName,
                  express: row?.express,
                  hitReminder: row?.hitReminder,
                  version: 1
                },
                refresh,
                type: 'copy'
              });
            }}
          >
            复制
          </YRLink>
          {row?.status === '020' && (
            <YRConfirmBtn
              type={'pop'}
              msg="请确认是否启用"
              confirmProps={{ zIndex: 9999 }}
              onConfirm={() => {
                changeRuleRun({ ruleItem: row?.itemId, status: '010' });
              }}
            >
              启用
            </YRConfirmBtn>
          )}
          {row?.status === '010' && (
            <YRConfirmBtn
              type={'pop'}
              msg="请确认是否停用"
              confirmProps={{ zIndex: 9999 }}
              onConfirm={() => {
                changeRuleRun({ ruleItem: row?.itemId, status: '020' });
              }}
            >
              停用
            </YRConfirmBtn>
          )}
          <YRLink
            type={'primary'}
            onClick={() => {
              YREasyUseModal.show(HistoryModal, { itemId: row?.itemId });
            }}
          >
            历史版本查询
          </YRLink>
          <YRLink
            type={'primary'}
            onClick={() => {
              YREasyUseModal.show(RelationModal, { itemId: row?.itemId });
            }}
          >
            规则引用关系
          </YRLink>
        </YRButton.Overflow>
      )
    }
  ];

  const renderExtAction = (
    <YRSpace>
      <YRButton
        key="add"
        type="primary"
        onClick={() => {
          YREasyUseModal.show(RuleModal, {
            defaultData: { treeId, treeName, version: 1 },
            refresh,
            type: 'add'
          });
        }}
      >
        新增
      </YRButton>
    </YRSpace>
  );

  return (
    <YREasyUseModal.Provider>
      <YRTable
        {...tableProps}
        form={form}
        rowKey={(row) => row?.itemId}
        columns={getTableColumns}
        formItemList={formItemList}
        extAction={addIf ? renderExtAction : null}
        handleSearch={() => {
          const formValues = form.getFieldsValue() || {};
          queryEntryList({
            ...params[0],
            ...formValues,
            current: 1,
            pageSize: 10
          });
        }}
      />
    </YREasyUseModal.Provider>
  );
};
export default EntryInfo;
