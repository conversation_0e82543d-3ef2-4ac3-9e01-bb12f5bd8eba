/**
 * @Description: 机构管理-审批角色弹窗
 * @Date: Created in 2022-12-15 16:58:45
 * @Modifed By:
 */
import React, { useEffect } from 'react';
import {
  YRForm,
  YRModal,
  YRButton,
  YRTreeSelect,
  YRTable,
  YRTableProps,
  YRBadge,
  YRDict,
  YRTag,
  YRLink,
  FormItemListProps
} from 'yrantd';
import { useAntdTable, useSetState } from 'ahooks';
import { EnumOperType, EnumRoleStatusColor } from '@/constant/role';
import { M010301 } from '@permit/role';
import { checkAuth, openNewTab } from '@/utils/utils';
import { EnumGuardRoleTypeColor, EnumRoleLevelColor, EnumRoleBizLineColor } from '@/constant/StyleConst';
import { queryRolePage } from '@/services/setting';
import Dict from '@/pages/Organ/mock/getDict';

const RoleModal = (props: any) => {
  const { visible, onCancel, initialValue, onOk, data } = props;
  const [form] = YRForm.useForm();

  const [state, setState] = useSetState({
    pageLoading: false,
    dataSource: [],
    selectedIds: []
  });

  /** 查询角色接口 */
  const { tableProps, run, params, refresh } = useAntdTable(
    (p) => {
      delete p.extra;
      delete p.filters;
      return queryRolePage({
        ...p,
        pageNum: p.current
      }).then((res) => {
        return res.success ? { list: res.data.list, total: res.data.total } : { list: [], total: 0 };
      });
    },
    {
      manual: true
    }
  );

  useEffect(() => {
    visible && run({ ...(params[0] as any) });
  }, [visible]);

  useEffect(() => {
    setState({ selectedIds: initialValue });
  }, [initialValue]);

  /** 跳转到角色详情页 */
  const onAdd = (mode: string, row?: any) => {
    if (checkAuth(M010301.E01) || checkAuth(M010301.E02) || checkAuth(M010301.E09)) {
      openNewTab({
        pathname: '/setting/role/role-manage/edit',
        query: {
          roleId: row?.roleId,
          mode
        }
      });
    }
  };

  /** 确认 */
  const onSubmit = () => {
    if (state.selectedIds?.length > 0) {
      onOk(state.selectedIds);
      console.log(state?.selectedIds);
      onCancel();
    }
  };

  const onChange = (newValue: string[]) => {
    console.log('onChange ', newValue);
  };

  const formItemList: FormItemListProps[] = [
    {
      placeholder: '角色名称',
      key: 'roleName',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '角色编号',
      key: 'roleId',
      type: 'input',
      position: 'show'
    }
  ];

  const getTableColumns: YRTableProps['columns'] = [
    {
      title: '角色编号',
      dataIndex: 'roleId',
      width: 220,
      render: (value, row) => {
        if (value) {
          return (
            <YRLink type="primary" pseudoCheck={M010301.E09} onClick={() => onAdd('readPretty', row)}>
              {value}
            </YRLink>
          );
        }
        return CONST.null;
      }
    },
    {
      title: '角色名称',
      dataIndex: 'roleName',
      width: 220,
      render: (value) => value || CONST.null
    },
    {
      title: '角色级别',
      dataIndex: 'roleLevel',
      width: 120,
      render: (value: string) =>
        value ? (
          <YRTag color={EnumRoleLevelColor[value]}>
            <YRDict.Text dictkey="EnumRoleLevel" defaultValue={value} />
          </YRTag>
        ) : (
          CONST.null
        )
    },
    {
      title: '角色类型',
      dataIndex: 'roleType',
      width: 120,
      render: (value: string) =>
        value ? (
          <YRTag color={EnumGuardRoleTypeColor[value]}>
            <YRDict.Text dictkey="EnumGuardRoleType" defaultValue={value} />
          </YRTag>
        ) : (
          CONST.null
        )
    },
    {
      title: '角色所属条线',
      dataIndex: 'bizLine',
      width: 120,
      render: (value: string) =>
        value ? (
          <YRTag color={EnumRoleBizLineColor[value]}>
            <YRDict.Text dictkey="EnumRoleBizLine" defaultValue={value} />
          </YRTag>
        ) : (
          CONST.null
        )
    },
    {
      title: '角色描述',
      dataIndex: 'roleDesc',
      width: 220,
      render: (value) => (
        <div className="ellipsis" title={value}>
          {value || CONST.null}
        </div>
      )
    },
    {
      title: '角色状态',
      dataIndex: 'roleStatus',
      width: 80,
      fixed: 'right',
      render: (value: string) => {
        if (!value) return CONST.null;

        return (
          <YRBadge
            status={EnumRoleStatusColor[value]}
            text={<YRDict.Text dictkey="EnumGuardRoleStatus" defaultValue={value} />}
          />
        );
      }
    }
  ];

  const rowSelection = {
    onChange: (selectedRowKeys, rows) => {
      setState({
        selectedIds: selectedRowKeys
      });
    },
    selectedRowKeys: state.selectedIds
  };

  return (
    <YRModal
      title="选择审批角色"
      open={visible}
      onCancel={onCancel}
      width="60%"
      footer={[
        <YRButton key="cancel" onClick={onCancel}>
          取消
        </YRButton>,
        <YRButton key="submit" type="primary" onClick={onSubmit}>
          确定
        </YRButton>
      ]}
    >
      <YRTable
        form={form}
        formItemList={formItemList}
        rowKey={(row) => row?.roleId}
        handleSearch={() => {
          form.validateFields().then((values) => {
            run({ ...(params[0] as any), ...(values as any) });
          });
        }}
        columns={getTableColumns}
        rowSelection={rowSelection}
        showIndex={false}
        {...tableProps}
      />
    </YRModal>
  );
};

export default Dict(['EnumRoleLevel', 'EnumGuardRoleType', 'EnumGuardRoleStatus', 'EnumRoleBizLine'])(RoleModal);
