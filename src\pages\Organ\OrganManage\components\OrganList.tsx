/**
 * @Author: wangyw26123
 * @Description: 机构列表
 * @Date: Created in 2022-12-13 20:49:28
 * @Modifed By:
 */
import React, { useEffect, useState } from 'react';
import {
  YRTable,
  YRForm,
  YRTag,
  YRLink,
  YRButton,
  YRDropdown,
  YRMenu,
  YRDict,
  YRBadge,
  YRMessage,
  YRModal,
  YRHighlight,
  YREasyUseModal
} from 'yrantd';
import type { FormItemListProps, YRTableProps } from 'yrantd';
import { checkAuth, openNewTab } from '@/utils/utils';
import store from '@/store';
import { M010101 } from '@permit/organ';
import { uniq } from 'lodash-es';
import AdjustmentOrderModal from './AdjustmentOrderModal';
import OrganCopyModal from './OrganCopyModal';
import { EnumOperType } from '@/constant/organ';
import {
  EnumOrganStatusColor,
  EnumOrganTypeColor,
  EnumOrganNatureColor,
  EnumOrganLevelColor
} from '@/constant/StyleConst';

import { EnumMsg } from '@/constant/common';
import Dict from '../../mock/getDict';

const { changeOrganStatus } = M010101.interfaces;
const { delOrgan } = M010101.E03.interfaces;

interface OrganItem {
  organId: string;
  organLevel: string;
  organName: string;
  organNature: string;
  organStatus: string;
  organType: string;
  children?: OrganItem;
}

interface OrganListProps {
  organList: OrganItem[];
}

const flatTreeData: any = [];

const generateFlatOragnData = (sourceData) => {
  const loop = (data) => {
    data?.forEach((item) => {
      flatTreeData.push(item);
      loop(item.children);
    });
  };

  return loop(sourceData);
};

const OrganList = (props: OrganListProps) => {
  const { organList: dataSource } = props;
  const [form] = YRForm.useForm();
  const [searchValue, setSearchValue] = useState<string>('');
  const [expandedRowKeys, setExpandedRowKeys] = useState<any>([]);
  const [visibleAdjustmentOrder, setVisibleAdjustmentOrder] = useState<boolean>(false);
  const [organCopyVsisible, setOrganCopyVsisible] = useState<boolean>(false);
  const [currentOrganItem, setCurrentOrganItem] = useState<object>({});
  const [organState, organDispatch] = store.useModel('organ');
  const { pageLoading, organTreeList, activeOrganId } = organState;

  const formItemList: FormItemListProps[] = [
    {
      placeholder: '机构名称',
      key: 'organName',
      type: 'input',
      position: 'show'
    }
  ];

  useEffect(() => {
    generateFlatOragnData(dataSource);
    // 默认展开下一层级
    if (Array.isArray(dataSource[0]?.children) && dataSource[0].children.length) {
      setExpandedRowKeys([dataSource[0].organId]);
    }
  }, [dataSource]);

  const getParentKey = (tree, name) => {
    const targetKeys: string[] = [];
    const getAllPath = (getAllPathTree): string[] => {
      const paths: string[] = [];
      for (let i = 0; i < getAllPathTree.length; i++) {
        const { children, organName, organId } = getAllPathTree[i];
        organName.includes(name) && !targetKeys.includes(organId) && targetKeys.push(organId);
        if (children && children.length) {
          const res = getAllPath(children);
          for (let j = 0; j < res.length; j++) {
            paths.push([organId, ...res[j]] as any);
          }
        } else {
          paths.push([organId] as any);
        }
      }
      return paths;
    };
    const allPath = getAllPath(tree) as unknown as string[][];
    const filterPath = allPath.filter((path) => path.some((key) => targetKeys.includes(key)));
    return uniq(filterPath.flat(1));
  };

  const handleSearch = () => {
    form.validateFields().then(({ organName }) => {
      const expandedKeys = getParentKey(flatTreeData, organName);
      setSearchValue(organName);
      setExpandedRowKeys(!organName ? [] : expandedKeys);
    });
  };

  const onAddOrgan = (row?: any, mode?: string | unknown, subOrgan?: '001') => {
    if ([M010101.E01, M010101.E02, M010101.E09].some((permit) => checkAuth(permit))) {
      openNewTab({
        pathname: '/setting/organ/organ-manage/edit',
        query: {
          mode: mode ?? 'add',
          parentOrganId: row?.organId,
          subOrgan
        }
      });
    }
  };

  const onChangeOrganStatus = (val, status, msg) => {
    if (checkAuth(M010101.E04) || checkAuth(M010101.E05) || checkAuth(M010101.E06)) {
      YRModal.confirm({
        title: msg,
        onOk() {
          changeOrganStatus({
            organId: val.organId,
            organStatus: status
          }).then((res) => {
            if (res.success) {
              YRMessage.success(EnumMsg.operator);
              organDispatch.getOrganTreeList();
            }
          });
        }
      });
    }
  };

  const onDel = (row) => {
    if (!checkAuth(M010101.E03)) return;

    YRModal.confirm({
      title: '确认是否删除该机构，删除后不可恢复?',
      onOk() {
        delOrgan({ organId: row.organId }).then((res) => {
          if (res.success) {
            YRMessage.success(EnumMsg.delete);

            if (activeOrganId === row.organId) {
              organDispatch.setActiveOrganId('');
            }

            organDispatch.getOrganTreeList();
          }
        });
      }
    });
  };

  const onOper = (key, row) => {
    switch (key) {
      case EnumOperType.ADD: {
        onAddOrgan(row, 'add', '001');
        break;
      }
      case EnumOperType.COPY: {
        setCurrentOrganItem(row);
        setOrganCopyVsisible(true);
        break;
      }
      case EnumOperType.ORDER: {
        setCurrentOrganItem(row);
        setVisibleAdjustmentOrder(true);
        break;
      }
      case EnumOperType.MODIFY: {
        onAddOrgan(row, 'edit');
        break;
      }
      case EnumOperType.DELETE: {
        onDel(row);
        break;
      }
    }
  };

  const getTableColumns: YRTableProps['columns'] = [
    {
      title: '机构名称',
      dataIndex: 'organName',
      width: 220,
      render: (value: string) => <YRHighlight sourceText={value} highlightText={searchValue} />
    },
    {
      title: '排序号',
      dataIndex: 'sequence',
      width: 60,
      render: (value: string) => value || CONST.null
    },
    {
      title: '机构编号',
      dataIndex: 'organId',
      width: 220,
      render: (value: string) => value || CONST.null
    },
    {
      title: '级别',
      dataIndex: 'organLevel',
      width: 100,
      render: (value: string) => {
        return value ? (
          <YRTag color={EnumOrganLevelColor[value]}>
            <YRDict.Text dictkey="EnumOrganLevel" defaultValue={value} />
          </YRTag>
        ) : (
          CONST.null
        );
      }
    },
    {
      title: '上级机构',
      dataIndex: 'parentOrganName',
      width: 120,
      render: (value: string) => value || CONST.null
    },
    {
      title: '机构类型',
      dataIndex: 'organType',
      width: 80,
      render: (value: string) => {
        return value ? (
          <YRTag color={EnumOrganTypeColor[value]}>
            <YRDict.Text dictkey="EnumOrganType" defaultValue={value} />
          </YRTag>
        ) : (
          CONST.null
        );
      }
    },
    {
      title: '机构性质',
      dataIndex: 'organNature',
      width: 100,
      render: (value: string) => {
        return value ? (
          <YRTag color={EnumOrganNatureColor[value]}>
            <YRDict.Text dictkey="EnumOrganNature" defaultValue={value} />
          </YRTag>
        ) : (
          CONST.null
        );
      }
    },
    {
      title: '状态',
      dataIndex: 'organStatus',
      width: 100,
      fixed: 'right',
      render: (value: string) => {
        return value ? (
          <YRBadge
            color={EnumOrganStatusColor[value]}
            text={<YRDict.Text dictkey="EnumOrganStatus" defaultValue={value} />}
          />
        ) : (
          CONST.null
        );
      }
    },
    {
      title: '操作',
      dataIndex: 'operator',
      width: 100,
      fixed: 'right',
      render: (value, row) => {
        const getItems = (status): any[] => {
          const items = [
            // 停用/未启用状态下不允许新增下级机构
            status === EnumOperType.ENABLE && checkAuth(M010101.E09) && { key: EnumOperType.ADD, label: '新增子机构' },
            checkAuth(M010101.E08) && { key: EnumOperType.COPY, label: '复制现有机构' },
            status !== EnumOperType.DISABLE && checkAuth(M010101.E07) && { key: EnumOperType.ORDER, label: '调整层级' },
            status !== EnumOperType.DISABLE && checkAuth(M010101.E02) && { key: EnumOperType.MODIFY, label: '修改' },
            // 停用/未启用状态的机构可以删除
            status !== EnumOperType.ENABLE && checkAuth(M010101.E03) && { key: EnumOperType.DELETE, label: '删除' }
          ];

          return items.filter(Boolean);
        };

        const getMenus = getItems(row.organStatus);

        return (
          <YRButton.Space>
            {row.organStatus === EnumOperType.NOT_ENABLE && checkAuth(M010101.E04) && (
              <YRLink type="primary" onClick={() => onChangeOrganStatus(row, EnumOperType.ENABLE, '确认启用吗?')}>
                启用
              </YRLink>
            )}
            {row.organStatus === EnumOperType.ENABLE && checkAuth(M010101.E05) && (
              <YRLink type="primary" onClick={() => onChangeOrganStatus(row, EnumOperType.DISABLE, '确认停用吗?')}>
                停用
              </YRLink>
            )}
            {row.organStatus === EnumOperType.DISABLE && checkAuth(M010101.E06) && (
              <YRLink type="primary" onClick={() => onChangeOrganStatus(row, EnumOperType.ENABLE, '确认恢复吗?')}>
                恢复
              </YRLink>
            )}
            {getMenus.length > 0 && (
              <YRDropdown
                trigger={['click']}
                overlay={<YRMenu items={getMenus} onClick={({ key }) => onOper(key, row)} />}
              >
                <YRLink type="primary">更多</YRLink>
              </YRDropdown>
            )}
          </YRButton.Space>
        );
      }
    }
  ];

  // const handleExport = () => {};

  const renderExtAction = (
    <YRButton.Space>
      <YRButton check={M010101.E01} type="primary" onClick={() => onAddOrgan({ organId: activeOrganId })}>
        新增
      </YRButton>
      {/*<YRButton check={M010101.E01} onClick={handleExport}>*/}
      {/*  导出*/}
      {/*</YRButton>*/}
    </YRButton.Space>
  );

  return (
    <YREasyUseModal.Provider>
      <YRTable
        form={form}
        rowKey="organId"
        expandable={{
          expandedRowKeys,
          onExpandedRowsChange: (expandedRows) => {
            setExpandedRowKeys(expandedRows);
          }
        }}
        showIndex={false}
        loading={pageLoading}
        dataSource={dataSource}
        columns={getTableColumns}
        handleSearch={handleSearch}
        formItemList={formItemList}
        extAction={renderExtAction}
        scroll={{
          y: 500
        }}
      />
      {/* 机构层级调整 */}
      {visibleAdjustmentOrder && (
        <AdjustmentOrderModal
          treeData={organTreeList}
          organInfo={currentOrganItem}
          visible={visibleAdjustmentOrder}
          onCancel={() => setVisibleAdjustmentOrder(false)}
          callback={() => {
            setVisibleAdjustmentOrder(false);
            organDispatch.getOrganTreeList();
          }}
        />
      )}
      {/* 机构复制 */}
      {organCopyVsisible && (
        <OrganCopyModal
          organInfo={currentOrganItem}
          treeData={organTreeList}
          visible={organCopyVsisible}
          onCancel={() => setOrganCopyVsisible(false)}
        />
      )}
    </YREasyUseModal.Provider>
  );
};

export default Dict(['EnumOrganLevel', 'EnumOrganType', 'EnumOrganStatus', 'EnumAcctOrganType'])(OrganList);
