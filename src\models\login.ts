import { userLogin, userLogout } from '@/services/comm';
import { YRMessage } from 'yrantd';
import { history } from 'ice';
import { parse } from 'qs';
import { encryptPassword } from '@/utils/utils';

export default {
  state: {},
  effects: (dispatch) => ({
    async login(payload) {
      // 登录前先清空用户所有缓存
      sessionStorage.clear();
      sessionStorage.tenantId = payload.tenantId;
      const response = await userLogin({
        loginAccount: payload.loginAccount,
        password: encryptPassword(payload.password),
        picCode: payload.picCode
      });
      if (response.success) {
        sessionStorage.removeItem('loginErr');
        sessionStorage.removeItem('loginErrCode');
        sessionStorage.removeItem('loginErrAccount');
        sessionStorage.removeItem('resetPwd');
        const info = response.data;
        if (info.remark) {
          YRMessage.info(info.remark);
        }
        if (info.resetPwd === '1') {
          sessionStorage.setItem('resetPwd', '1');
          dispatch.global.changeUpdatePwdModal({
            payload: {
              show: true,
              showCancelBtn: false
            }
          });
        } else {
          sessionStorage.removeItem('resetPwd');
        }
        // 缓存用户信息
        sessionStorage.setItem(
          'userInfo',
          JSON.stringify({
            token: info.token,
            operatorId: info.operatorId,
            operatorName: info.name,
            loginAccount: info.loginAccount,
            organId: info.organId,
            organName: info.organName,
            tenantId: payload.tenantId,
            name: info.name
          })
        );
        const urlParams = new URL(window.location.href);
        const params = parse(window.location.href.split('?')[1]);
        let { redirect } = params;
        if (redirect) {
          const redirectUrlParams = new URL(redirect);
          if (redirectUrlParams.origin === urlParams.origin) {
            redirect = redirect.substr(urlParams.origin.length);
            if (redirect.startsWith('/#')) {
              redirect = redirect.substr(2);
            }
          } else {
            window.location.href = redirect;
            return;
          }
        }
        history?.replace({
          pathname: redirect || '/'
        });
      } else {
        sessionStorage.setItem('loginErr', response.errorMessage || '登录失败');
        sessionStorage.setItem('loginErrCode', response.errorCode);
        sessionStorage.setItem('loginErrAccount', payload.loginAccount);
        window.location.href = '/login/login.html';
      }
    },
    async logout() {
      userLogout().then(() => {
        sessionStorage.removeItem('userInfo');
        window.location.href = '/login/login.html';
      });
    }
  })
};
