@import "~@/global.less";

.content {
  display: inline-block;
  margin: 0 16px 0px 12px;
  vertical-align: top;
  background-color: #fff;
  border-radius: 4px;
  height: 100vh;
}
.content-top {
  display: flex;
  justify-content: space-between;
  padding: 0 12px;
  line-height: 36px;
  border-bottom: 1px solid fade(#1f2e4a, 10%);
  .title {
    font-weight: bolder;
    font-size: 16px;
  }
  :global {
    a {
      margin-left: 8px;
      color: #1890ff;
    }
    a:hover {
      color: @blue-7;
    }
    .ant-input {
      height: 28px;
      padding-top: 2px;
      padding-bottom: 2px;
    }
    .ant-select-selection--single {
      height: 28px;
      margin-top: 4px;
    }
    .ant-select-selection__rendered {
      line-height: 26px;
    }
  }
}
