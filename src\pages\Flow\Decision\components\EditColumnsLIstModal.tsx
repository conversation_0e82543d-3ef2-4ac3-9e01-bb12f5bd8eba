/*
 * @Author: ducf
 * @E-mail: <EMAIL>
 * @Date: 2023-05-06 10:11:03
 * @Description: 决策配置添加列
 */
import React, { useEffect, useState } from 'react';
import { YRForm, YRInput, YRModal, YRSelect } from 'yrantd';

interface Props {
  visible?: boolean;
  onOk?: (data: object) => void;
  onCancel?: () => void;
  typeDict?: [];
  editRow?: {};
}

const EditColumnsLIstModal: React.FC<Props> = (props) => {
  const { visible, onOk, onCancel, typeDict, editRow, paramItemList } = props;
  const [form] = YRForm.useForm();
  const submit = async () => {
    try {
      const values = await form.validateFields();
      form.resetFields();
      onOk(values);
    } catch (error) {}
  };

  useEffect(() => {
    if (visible && editRow?.variableId) {
      form.setFieldsValue(editRow);
    }
  }, [visible]);

  const selectParams = (values: any) => {
    const name = paramItemList!.filter((_item) => {
      return _item?.paramItemName === values;
    });
    form.setFieldsValue({ variableId: name[0]?.paramItemKey, type: name[0]?.paramValueType });
  };

  return (
    <YRModal onCancel={onCancel} title="编辑列" visible={visible} onOk={submit}>
      <YRForm form={form}>
        <YRForm.Row>
          <YRForm.Item label="列标签" name="label">
            <YRSelect placeholder="选择列标签" onChange={selectParams}>
              {!!paramItemList &&
                paramItemList?.map((_item) => {
                  return (
                    <YRSelect.Option value={_item?.paramItemName} key={_item?.paramItemName}>
                      {_item?.paramItemName}
                    </YRSelect.Option>
                  );
                })}
            </YRSelect>
          </YRForm.Item>
          <YRForm.Item label="变量名称" rules={[{ required: true }]} name="variableId">
            <YRInput disabled />
          </YRForm.Item>
          <YRForm.Item label="变量类型" rules={[{ required: true }]} name="type">
            <YRInput disabled />
          </YRForm.Item>
          <YRForm.Item label="允许值（可选）" name="entries">
            <YRInput />
          </YRForm.Item>
        </YRForm.Row>
      </YRForm>
    </YRModal>
  );
};

export default EditColumnsLIstModal;
