import React, { useState, useRef } from 'react';
import { YRIcon, YRInput, YRDropdown, YRTooltip } from 'yrantd';
import classnames from 'classnames';

export default (props) => {
  const { menu, title, search, create, topExtra, hideCreate, hideSearch } = props;

  const [searchShow, setSearchShow] = useState(false);
  const [inputValue, setInputValue] = useState('');

  const inputRef = useRef();

  const handleSearch = (e) => {
    const { value } = e.target;
    if (value === undefined) {
      return;
    }
    search(value);
  };

  const clearSearch = () => {
    if (inputValue) {
      setInputValue('');
      search('');
    }
  };

  const changeValue = (e) => {
    setInputValue(e.target.value);
  };

  // 收起输入框
  const closeInput = () => {
    setSearchShow(false);
    clearSearch();
  };

  const searchDisplay = () => {
    if (!searchShow) {
      inputRef.current.focus();
      setSearchShow(true);
    } else {
      closeInput();
    }
  };

  // 样式控制显示
  const style = searchShow ? { width: '88%', padding: '0 0 0 8px !important' } : { width: 0, display: 'none' };
  const spanStyle = classnames({
    'change-show': searchShow,
    'edit-search': true
  });
  return (
    <div className="top-bar-box">
      {!searchShow && title && <div className="left-content-title">{title}</div>}
      {!hideSearch && (
        <span className={spanStyle}>
          <YRTooltip title="搜索">
            <YRIcon icon="yunrongXXmonitor" fontSize="14px" onClick={searchDisplay} />
          </YRTooltip>
          <YRInput
            size="small"
            suffix={<YRIcon icon="yunrongXXclose" fontSize="14px" onClick={clearSearch} />}
            onPressEnter={handleSearch}
            placeholder="按Enter搜索"
            style={style}
            ref={inputRef}
            value={inputValue}
            onChange={changeValue}
          />
        </span>
      )}
      {topExtra}
      {!hideCreate && (
        <span onClick={create}>
          <YRTooltip title="添加">
            <YRIcon icon="yunrongXXplus" fontSize="14px" />
          </YRTooltip>
        </span>
      )}

      {menu && (
        <span>
          <YRTooltip title="更多">
            <YRDropdown overlay={menu} trigger={['click']}>
              <YRIcon icon="yunrongXXellipsis" fontSize="14px" />
            </YRDropdown>
          </YRTooltip>
        </span>
      )}
    </div>
  );
};
