@import "~@/global.less";
.layout-column {
  display: flex;
  flex-direction: row;
  box-sizing: 0 -10px 20px 0 rgba(31, 46, 74, 0.08);
  height: calc(100vh - 82px);
  margin: 16px;
  &.no-breadcrumb {
    height: calc(100vh - 98px);
  }
}
.oper-icon {
  margin-top: 4px;
  font-size: 14px;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.3);
  &:hover {
    color: #1890ff;
  }
}
.layout-column-left {
  flex: 0 0 auto;
  width: 280px;
  background-color: #fff;
  border-right: 1px solid fade(#1f2e4a, 10%);
  box-shadow: 10px 0 20px 0 rgba(31, 46, 74, 0.08);
  margin: 0;
  .column-left-top {
    padding: 10px;
    .title {
      color: fade(#1f2e4a, 100%);
      font-weight: bolder;
      line-height: 22px;
    }
    .oper {
      float: right;
      color: fade(#1f2e4a, 70%);
    }
  }
  .column-left-search {
    display: flex;
    margin: 0 10px;
    .row-icon {
      margin-top: 8px;
      margin-left: 8px;
      vertical-align: middle;
      &:hover {
        color: #1890ff;
      }
    }
  }

  .column-left-list {
    height: calc(100vh - 213px);
    margin-top: 8px;
    overflow-y: auto;
    .ant-list-item {
      margin: 2px 0;
      padding-top: 4px;
      padding-bottom: 4px;
      border-bottom: none;
      border-left: 2px solid #fff;
      cursor: pointer;
      &:hover,
      &.active {
        background-color: #e6f7ff;
        border-left-color: #1890ff;
        .row {
          color: #1890ff;
        }
      }
      &:hover {
        .ant-list-item-action {
          visibility: visible;
        }
      }
    }
    .ant-list-item-action {
      margin-left: 12px;
      visibility: hidden;
    }
    .aciton-icon:hover {
      color: #1890ff;
    }
    .row {
      padding-left: 12px;
      color: fade(#1f2e4a, 70%);
      line-height: 22px;
      .row-icon {
        display: inline;
        margin-right: 8px;
        vertical-align: middle;
        &:hover {
          color: #1890ff;
        }
      }
      .status_on {
        width: 6px;
        height: 6px;
        margin-top: 8px;
        margin-right: 8px;
        vertical-align: middle;
        background-color: rgba(82, 196, 26, 1);
        border-radius: 3px;
      }
      .status_off {
        width: 6px;
        height: 6px;
        margin-top: 8px;
        margin-right: 8px;
        vertical-align: middle;
        background-color: rgba(191, 191, 191, 1);
        border-radius: 3px;
      }
      .title {
        display: inline-block;
        width: 125px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        vertical-align: middle;
      }
    }
  }
}

.layout-column-right {
  flex: 1 1 auto;
  overflow: hidden;
  margin: 16px;
  .ant-tabs-bar {
    margin: 0 0 16px 0;
  }
  .column-right-top {
    position: relative;
    padding: 0 16px;
    background-color: #fff;
    border-bottom: 1px solid fade(#1f2e4a, 10%);
    // margin-bottom: 16px;
    margin: 0 10px 12px 0px;
    .title {
      color: fade(#1f2e4a, 100%);
      line-height: 40px;
    }
    .time {
      padding-left: 12px;
      color: fade(#1f2e4a, 40%);
      font-size: 12px;
      line-height: 40px;
    }
    .but {
      position: absolute;
      top: 0;
      right: 24px;
      line-height: 40px;
    }
  }
  .column-right-content {
    max-width: 100%;
    height: 100%;
    //   margin: 16px;
    padding: 16px 24px;
    overflow: scroll;
    background-color: #fff;
  }
  .column-right-tab {
    .ant-tabs-bar {
      padding: 0 24px;
      background-color: #fff;
      .ant-tabs-tab {
        padding: 8px 0;
      }
    }
    .tab-content {
      max-width: 100%;
      height: calc(100vh - 0px);
      margin: 16px 16px;
      overflow-y: auto;
      background-color: #fff;
    }
    .tab-space {
      padding: 16px 24px;
    }
  }
}
