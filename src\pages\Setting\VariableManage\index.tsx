/**
 * 页面描述: 变量管理页
 * @文件名 index.tsx
 * @filePath \src\pages\Setting\VariableManage\index.tsx
 * @Date 2023-08-11 10:20:50
 * @Email <EMAIL>
 * <AUTHOR>
 */
import React, { useRef, useState } from 'react';
import { YRFlexPageLayout, YREmpty } from 'yrantd';
import VariableTree from './components/VariableTree';
import VariableInfo from './components/VariableInfo';

const VariableManage = () => {
  const [activeNode, setActiveNode] = useState({}) as any;

  return (
    <YRFlexPageLayout>
      <YRFlexPageLayout.Sider title="变量集合目录">
        <VariableTree setActiveNode={setActiveNode} />
      </YRFlexPageLayout.Sider>
      <YRFlexPageLayout.Main title="变量信息">
        {activeNode?.treeId ? (
          <VariableInfo activeNode={activeNode} />
        ) : (
          <YREmpty style={{ marginTop: '60px' }} />
        )}
      </YRFlexPageLayout.Main>
    </YRFlexPageLayout>
  );
};

export default VariableManage;
