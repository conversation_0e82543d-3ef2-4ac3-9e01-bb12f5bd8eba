/* eslint-disable @typescript-eslint/indent */
/* eslint-disable no-console */
/*
 * @Author: DJscript
 * @Date: 2023-02-09 10:14:19
 * @LastEditTime: 2023-02-13 09:51:38
 * @FilePath: /kylin-guarantee/src/components/EditPlusTable/demo.tsx
 * @Description: ...
 */
import React from 'react';
import EditPlusTable from '.';
import type { EditPlusTableProps } from '.';
import { YRButton, YRForm, YRInput, YRSpace } from 'yrantd';
import { YRFormInternalPros } from 'yrantd/lib/yr-form/Form';
import { setDict } from '@/utils/utils';
import { uuid } from '@yr/util';

type TableType = EditPlusTableProps<{
  name: string;
  age: number;
  id: string;
}>;

setDict('cardType', [
  { itemName: '身份1', itemKey: '2141241' },
  { itemName: '身份2', itemKey: '51251252' }
]);
const Demo = () => {
  const [form] = YRForm.useForm();
  const editRef = React.useRef<TableType['EditRefType']>(null);
  const [mode, setMode] = React.useState<YRFormInternalPros['mode']>('edit');

  const columns: TableType['IEditPlusTableProps']['columns'] = [
    {
      dataIndex: 'name',
      title: '姓名',
      valueType: 'input',
      formItemProps: {
        style: { padding: 0, margin: 0 }
      },
      componentProps: { style: { width: 100 } }
    },
    {
      dataIndex: 'cardType',
      title: '身份',
      valueType: 'select',
      formItemProps: {
        initialValue: '2141241'
      },
      componentProps: {},
      options: [
        { label: '身份1', value: '2141241' },
        { label: '身份2', value: '51251252' }
      ]
    },
    {
      dataIndex: 'cardType2',
      title: '身份2',
      valueType: 'dictSelect',
      dictkey: 'cardType'
    },
    {
      dataIndex: 'date',
      title: '日期',
      valueType: 'datePicker'
    },
    {
      dataIndex: 'is',
      title: '是否成年',
      valueType: 'radio',
      formItemProps: {
        valuePropName: 'checked'
      }
    },
    {
      dataIndex: 'age',
      title: '年龄',
      valueType: 'input',
      componentProps: ({ rowIndex }) => ({
        style: { width: 100 }
      }),
      formItemProps: ({ rowIndex }) => ({
        rules: [{ required: true, message: `请输入年龄${rowIndex}` }],
        style: {
          padding: 0,
          margin: 0
        }
      })
    },
    {
      title: '操作',
      render: (t, r, i, { del, add }) => (
        <YRSpace>
          <YRButton onClick={del} type="link" disabled={mode === 'readPretty'}>
            删除
          </YRButton>
          <YRButton
            onClick={() =>
              add({
                name: 'dj',
                age: i,
                id: `隐藏域数据${uuid()}`
              })
            }
            type="link"
            disabled={mode === 'readPretty'}
          >
            新增
          </YRButton>
        </YRSpace>
      )
    }
  ];
  return (
    <YRForm form={form} mode={mode}>
      <YRSpace>
        <YRButton
          type="primary"
          onClick={() =>
            editRef.current?.add({
              name: 'dj',
              age: 18,
              id: `隐藏域数据${uuid()}`
            })
          }
        >
          新增
        </YRButton>
        <YRButton
          type="primary"
          onClick={async () => {
            try {
              const values = await form.validateFields();
              console.log('values', values);
            } catch (error) {
              console.log('error', error);
            }
          }}
        >
          保存
        </YRButton>
        <YRButton
          type="primary"
          onClick={() => {
            editRef.current?.del(1);
          }}
        >
          删除第二项
        </YRButton>
        <YRButton
          type="primary"
          onClick={() => {
            setMode(mode === 'readPretty' ? 'edit' : 'readPretty');
          }}
        >
          切换模式
        </YRButton>
      </YRSpace>
      <YRForm.Item label="姓名" name="ddd">
        <YRInput />
      </YRForm.Item>
      <YRForm.Item
        noStyle
        label=""
        name={['dj', 'list']}
        initialValue={[
          {
            name: 'dj',
            age: 18,
            date: '2021-02-09',
            cardType2: '2141241',
            is: true
          },
          {
            name: '周杰伦',
            age: 36
          }
        ]}
      >
        <EditPlusTable<{
          name: string;
          age: number;
          id: string;
        }>
          rowKey="id"
          mode={mode}
          editRef={editRef}
          columns={columns}
          style={{ width: '100%' }}
        />
      </YRForm.Item>
    </YRForm>
  );
};

export default Demo;
