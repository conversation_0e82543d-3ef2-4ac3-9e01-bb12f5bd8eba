/**
 * @Author: 刘文强
 * @Description: 新增/修改助理对话框
 * @Date: Created in
 * @Modifed By:
 */

import React, { useRef, useState } from 'react';
import { YRForm, YRModal, YRInput, YREasyUseModal, YRSelect, YRMessage } from 'yrantd';
import { addManagerAssisantRel, queryUserPageInfo } from '../../../services/user';
import { antdModal } from '../../../utils/utils';
import { useAntdTable, useRequest } from 'ahooks';
import { queryUserList } from '../../../services/setting';

const AddAssistantModal = YREasyUseModal.create(({ handleSearch }) => {
  const modal = YREasyUseModal.useModal();
  const [form] = YRForm.useForm();
  const [confirmLoading, setConfirmLoading] = useState(false);
  // 监测是否有身份名称
  const managerIdentityName = YRForm.useWatch('identityId', form) || undefined;
  const [userSelectList, setUserSelectList] = useState([]);
  const selectRef = useRef([]);
  const [identifyList, setIdentifyList] = useState([]);
  const { tableProps } = useAntdTable(() =>
    queryUserPageInfo({ pageSize: 500, pageNum: 1 }).then((res) => {
      if (res?.success) {
        return {
          list: res.data.list.filter((item) => item.post === '客户经理' && item.userStatus !== '030') || [],
          total: res.data.list.length
        };
      } else {
        return {
          list: [],
          total: 0
        };
      }
    })
  );
  // @ts-ignore
  const { data } = useRequest(queryUserList, {
    onSuccess: (userList) => {
      setUserSelectList(userList.data || []);
    }
  });
  const handleOk = () => {
    form.validateFields().then((values: object) => {
      const param: any = {
        ...values
      };
      delete param.managerAccountNo;
      delete param.assisantAccountNo;
      setConfirmLoading(true);
      addManagerAssisantRel(param)
        .then((res) => {
          if (res && res.success) {
            YRMessage.success('添加成功');
            modal.hide().then(() => handleSearch());
          }
        })
        .finally(() => setConfirmLoading(false));
    });
  };

  // 选中客户经理后反显账号
  const handleSelect = (value) => {
    const info = tableProps.dataSource.filter((item) => item.userId === value);
    form.setFieldValue('managerAccountNo', info[0].accountNo);
    selectRef.current = info[0]?.userIdentifyDtoList;
    setIdentifyList(info[0]?.userIdentifyDtoList || []);
  };

  const selectAssistant = (value) => {
    const association: any = userSelectList.find(
      (item: { userId: string; userName: string }) => item?.userId === value
    );
    form.setFieldValue('assisantAccountNo', association?.accountNo);
  };

  return (
    <YRModal
      {...antdModal(modal)}
      confirmLoading={confirmLoading}
      title={'新增助理'}
      destroyOnClose
      bodyStyle={{ maxHeight: 600 }}
      okText="保存"
      maskClosable={false}
      onOk={handleOk}
    >
      <YRForm form={form} mode={'add'}>
        <YRForm.Row>
          <YRForm.Item name="managerId" label="客户经理名称" rules={[{ required: true, message: '请选择客户经理' }]}>
            <YRSelect showSearch onSelect={(val) => handleSelect(val)}>
              {tableProps?.dataSource?.map((item) => (
                <YRSelect.Option value={item.userId} key={item.userId}>
                  {item.userName}
                </YRSelect.Option>
              ))}
            </YRSelect>
          </YRForm.Item>
          <YRForm.Item name="managerAccountNo" label="登录账号">
            <YRInput disabled />
          </YRForm.Item>
          <YRForm.Item name="identityId" label="客户经理身份名称">
            <YRSelect>
              {identifyList &&
                identifyList?.map((item: any) => (
                  <YRSelect.Option value={item.identifyId} key={item.identifyId}>
                    {item.identityName}
                  </YRSelect.Option>
                ))}
            </YRSelect>
          </YRForm.Item>
          {managerIdentityName && [
            <YRForm.Item
              name="assisantId"
              label="客户经理助理名称"
              rules={[{ required: true, message: '请选择客户经理' }]}
            >
              <YRSelect showSearch onSelect={(val) => selectAssistant(val)}>
                {userSelectList.map((item: any) => (
                  <YRSelect.Option key={item.userId} value={item.userId}>
                    {item.userName}
                  </YRSelect.Option>
                ))}
              </YRSelect>
            </YRForm.Item>,
            <YRForm.Item name="assisantAccountNo" label="客户经理助理账号">
              <YRInput disabled />
            </YRForm.Item>
          ]}
        </YRForm.Row>
      </YRForm>
    </YRModal>
  );
});

export default AddAssistantModal;
