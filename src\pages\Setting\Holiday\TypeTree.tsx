/*
 * @Author: 伍晶晶
 * @Description:
 * @Date: 2023-03-22 09:54:02
 * @LastEditors: 伍晶晶
 * @LastEditTime: 2023-03-30 14:36:50
 */
/* eslint-disable function-paren-newline */
/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
import React from 'react';
import { YREmpty, YRItem, YRSpin, YRTree } from 'yrantd';

import { useAntd } from './Context';
import { TreeNode, DefaultNode, KeyEnum } from './Interface';

import { TreeDataNode } from 'yrantd/lib/yr-tree';

const TypeTree = () => {
  const antd = useAntd();
  const [selectedKey, setSelectedKey] = antd.selectedState;

  const { data: list } = antd.antdTreeProps;

  return (
    <YRSpin spinning={antd.antdTreeProps.loading}>
      {list?.length ? (
        <YRTree
          blockNode
          fieldNames={{ title: KeyEnum.LABEL, key: KeyEnum.KEY, children: 'children' }}
          titleRender={(nodeData: TreeNode & TreeDataNode) => {
            return <YRItem title={nodeData[KeyEnum.LABEL]} />;
          }}
          onSelect={(selectedKeys, info) => {
            setSelectedKey(info.node);
          }}
          treeData={[{ ...DefaultNode, children: list }] as any}
          defaultExpandedKeys={[DefaultNode[KeyEnum['KEY']]]}
          selectedKeys={[selectedKey[KeyEnum.KEY]!]}
        />
      ) : (
        <YREmpty />
      )}
    </YRSpin>
  );
};

export default TypeTree;
