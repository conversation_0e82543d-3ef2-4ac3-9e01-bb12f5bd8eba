/**
 * 信号定义添加弹窗
 * @param props
 */

import React from 'react';
import { YRModal, YRButton, YRForm, YRInput, YRSelect } from 'yrantd';
const { Option } = YRSelect;

const scope = [
  { value: '流程实例', key: 'processInstance' },
  { value: '全局', key: 'global' }
];

const regExp = new RegExp('^[a-zA-Z]');

const SignalAddModal = (props: any) => {
  const { visible, changeVisible, onOK } = props;
  const [form] = YRForm.useForm();

  const onCancel = () => {
    changeVisible(false);
    form.resetFields();
  };

  const onOkCallback = () => {
    form
      .validateFields()
      .then((values: any) => {
        changeVisible(false);
        onOK({ ...values });
        form.resetFields();
      })
      .catch((errorInfo) => {});
  };

  /** 担保金额校验 */
  const totalValid = (rule, value, callback) => {
    if (regExp.test(value)) {
      callback();
    } else {
      callback('Id 首个字符应该为字母');
    }
  };

  return (
    <YRModal
      width={'50%'}
      destroyOnClose
      onCancel={() => {
        onCancel();
      }}
      open={visible}
      title={'信号定义添加'}
      footer={[
        <YRButton key="back" onClick={() => onCancel()}>
          取消
        </YRButton>,
        <YRButton key="submit" type="primary" onClick={() => onOkCallback()}>
          保存
        </YRButton>
      ]}
    >
      <YRForm form={form}>
        <YRForm.Item
          label={'code'}
          name={'id'}
          rules={[{ required: true, message: 'code 不能为空' }, { validator: totalValid }]}
        >
          <YRInput placeholder={'请输入 code'} />
        </YRForm.Item>
        <YRForm.Item label={'名称'} name={'name'} rules={[{ required: true, message: '名称不能为空' }]}>
          <YRInput placeholder={'请输入名称'} />
        </YRForm.Item>
        <YRForm.Item label={'类型'} name={'scope'} rules={[{ required: true, message: '类型不能为空' }]}>
          <YRSelect placeholder={'请输入类型'}>
            {scope &&
              scope.map((item) => {
                return (
                  <Option value={item.key} key={item.key}>
                    {item.value}
                  </Option>
                );
              })}
          </YRSelect>
        </YRForm.Item>
      </YRForm>
    </YRModal>
  );
};

export default SignalAddModal;
