/**
 * @Author: wangyw26123
 * @Description: 账务机构映射
 * @Date: Created in 2022-12-09 16:46:35
 * @Modifed By:
 */

import React, { useState } from 'react';
import type { FormItemListProps } from 'yrantd';
import { useQueryTableData } from '@yr/util';
import { YRButton, YRForm, YRIndexPageLayout, YRTable, YRLink, YRMessage, YRConfirmBtn } from 'yrantd';
import OrganMapModal from './OrganMapModal';
import { EnumMsg } from '@/constant/common';
import { M010102 } from '@permit/organ';
import { checkAuth } from '@/utils/utils';
import Dict from '../mock/getDict';
import { EnumAcctOrganTypelColor } from '@/constant/StyleConst';
import { ValueTypeEnum, YRTableProps } from 'yrantd/lib/yr-table/declare';

const { queryOrganRelationPage } = M010102.interfaces;
const { delOrgRelate } = M010102.E03.interfaces;

const FinancialOrganMap = () => {
  const [form] = YRForm.useForm();
  // 查询表格数据
  const { tableProps, search, refresh } = useQueryTableData(queryOrganRelationPage);

  const [mode, setMode] = useState<'add' | 'edit'>('add');
  const [surrentOrganInfo, setCurrentOrganInfo] = useState({});
  const [organModalVisible, setOrganModalVisible] = useState(false);

  const formItemList: FormItemListProps[] = [
    {
      placeholder: '业务机构名称',
      key: 'organName',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '业务机构编号',
      key: 'organId',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '账务机构名称',
      key: 'acctOrganName',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '账务机构编号',
      key: 'acctOrganId',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '账务机构类型',
      key: 'acctOrganType',
      type: 'dictSelect',
      dictkey: 'EnumAcctOrganType',
      position: 'show'
    }
  ];

  const modifyOrganMap = (row) => {
    setCurrentOrganInfo(row);
    setOrganModalVisible(true);
    setMode('edit');
  };

  const onDel = (row: any) => {
    if (!checkAuth(M010102.E03)) return;

    delOrgRelate({
      organId: row.organId,
      acctOrganId: row.acctOrganId,
      acctOrganType: row.acctOrganType
    }).then((res) => {
      if (res.success) {
        YRMessage.success(EnumMsg.delete);
        refresh();
      }
    });
  };

  // 表格列
  const columns: YRTableProps['columns'] = [
    {
      title: '业务机构编号',
      dataIndex: 'organId',
      valueType: ValueTypeEnum.id
    },
    {
      title: '业务机构名称',
      dataIndex: 'organName',
      valueType: ValueTypeEnum.department
    },
    {
      title: '账务机构编号',
      dataIndex: 'acctOrganId',
      valueType: ValueTypeEnum.id
    },
    {
      title: '账务机构名称',
      dataIndex: 'acctOrganName',
      valueType: ValueTypeEnum.department
    },
    {
      title: '账务机构类型',
      dataIndex: 'acctOrganType',
      valueType: ValueTypeEnum.tag,
      color: EnumAcctOrganTypelColor,
      dictkey: 'EnumAcctOrganType'
    },
    {
      title: '操作',
      dataIndex: 'operator',
      valueType: ValueTypeEnum.operation,
      fixed: 'right',
      render: (_, row) => {
        return (
          <YRButton.Overflow>
            <YRLink type="primary" check={M010102.E02} onClick={() => modifyOrganMap(row)}>
              修改
            </YRLink>
            <YRConfirmBtn msg="确定要删除该记录吗？" type="modal" check={M010102.E03} onConfirm={() => onDel(row)}>
              删除
            </YRConfirmBtn>
          </YRButton.Overflow>
        );
      }
    }
  ];

  const operationRender = (
    <YRButton
      check={M010102.E01}
      type="primary"
      onClick={() => {
        setCurrentOrganInfo({});
        setOrganModalVisible(true);
        setMode('add');
      }}
    >
      新增
    </YRButton>
  );

  return (
    <YRIndexPageLayout>
      <YRTable
        business="home"
        form={form}
        rowKey={(record) => `${record.organId}_${record.acctOrganId}`}
        columns={columns}
        handleSearch={search.submit}
        formItemList={formItemList}
        operationRender={operationRender}
        {...tableProps}
      />

      <OrganMapModal
        visible={organModalVisible}
        organInfo={surrentOrganInfo}
        mode={mode}
        onCancel={() => {
          setOrganModalVisible(false);
        }}
        callback={() => {
          setOrganModalVisible(false);
          refresh();
        }}
      />
    </YRIndexPageLayout>
  );
};

export default Dict(['EnumAcctOrganType'])(FinancialOrganMap);
