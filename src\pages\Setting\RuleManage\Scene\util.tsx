import { tacheNameAndStage } from './config';

// 规则表格数据格式转换
export const formatRuleList = (tacheList, list) => {
  const targetList = [] as any;
  list.forEach((item) => {
    const { ruleType, itemId, itemName, treeName, version, bizTypeList, circleFlag, circleType } = item;
    tacheList.forEach((v) => {
      targetList.push({
        ruleType,
        itemId,
        itemName,
        treeName,
        version,
        bizTypeList,
        ...(ruleType ? { circleFlag } : {}), // 通用和产品规则时传入字段circleFlag: 是否循环
        ...(ruleType ? { circleType } : {}), // 通用和产品规则时传入字段circleType: 循环类别
        ...(ruleType ? { operateType: item[v] || null } : { flag: item[v] || null }), // 豁免规则传入字段不同
        businessStage: tacheNameAndStage[v].stage,
        businessTache: v
      });
    });
  });
  return targetList;
};

// 将详情中规则信息组合为页面所需字段
export const splitRuleList = (list) => {
  const targetItemIdList = [...new Set(list.map((item) => item?.itemId))] as any; // 规则条目编号列表
  // 组合规则数据
  const targetList = targetItemIdList.map((item) => {
    const filterItemList = list.filter((i) => i?.itemId === item);
    const defaultItem = filterItemList?.length > 0 ? { ...filterItemList[0] } : {};
    filterItemList.forEach((i) => {
      defaultItem[i?.businessTache] = i?.operateType || i?.flag; // 豁免规则传入字段不同
    });
    if (defaultItem?.businessStage) {
      delete defaultItem.businessStage;
    }
    if (defaultItem?.businessTache) {
      delete defaultItem.businessTache;
    }
    return defaultItem;
  });
  return targetList;
};
