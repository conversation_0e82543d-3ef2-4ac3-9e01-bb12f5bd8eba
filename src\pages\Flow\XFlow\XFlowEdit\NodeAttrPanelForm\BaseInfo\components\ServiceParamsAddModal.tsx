/*
 * @Author: liaokt
 * @E-mail: <EMAIL>
 * @Date: 2023-04-20 18:28:22
 * @Description: 服务节点参数添加弹窗
 */

import React from 'react';
import { YRModal, YRButton, YRForm, YRInput } from 'yrantd';

const ServiceParamsAddModal = (props: any) => {
  const { visible, changeVisible, onOK } = props;
  const [form] = YRForm.useForm();

  const onCancel = () => {
    changeVisible(false);
    form.resetFields();
  };

  const onOkCallback = () => {
    form
      .validateFields()
      .then((values: any) => {
        changeVisible(false);
        onOK({ ...values });
        form.resetFields();
      })
      .catch((errorInfo) => {});
  };

  return (
    <YRModal
      width={'50%'}
      destroyOnClose
      onCancel={() => {
        onCancel();
      }}
      open={visible}
      title={'参数添加'}
      footer={[
        <YRButton key="back" onClick={() => onCancel()}>
          取消
        </YRButton>,
        <YRButton key="submit" type="primary" onClick={() => onOkCallback()}>
          保存
        </YRButton>
      ]}
    >
      <YRForm form={form}>
        <YRForm.Item label={'字段'} name={'field'} rules={[{ required: true, message: '字段不能为空' }]}>
          <YRInput placeholder={'请输入字段'} />
        </YRForm.Item>
        <YRForm.Item label={'参数'} name={'stringValue'} rules={[{ required: true, message: '参数不能为空' }]}>
          <YRInput placeholder={'请输入参数'} />
        </YRForm.Item>
      </YRForm>
    </YRModal>
  );
};

export default ServiceParamsAddModal;
