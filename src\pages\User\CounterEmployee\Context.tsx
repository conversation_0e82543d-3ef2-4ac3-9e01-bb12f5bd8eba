/*
 * @Author: DJscript
 * @Date: 2023-03-07 10:12:54
 * @LastEditTime: 2023-03-28 15:19:30
 * @FilePath: /kylin-admin/src/pages/Setting/DataDictionary/Context.tsx
 * @Description: ...
 */
import React from 'react';
import { Result } from 'ahooks/lib/useRequest/src/types';

import { TreeNode } from './Interface';

type ProviderValueType = {
  antdTreeProps: Result<TreeNode[], any>;
} & {
  selectedState: [Partial<TreeNode>, React.Dispatch<React.SetStateAction<TreeNode>>];
};
const AntdContext = React.createContext<ProviderValueType>(null as unknown as ProviderValueType);

export const AntdContextProvider: React.FC<React.PropsWithChildren<{ value: ProviderValueType }>> = (props) => {
  return <AntdContext.Provider {...props} />;
};

export const useAntd = () => React.useContext(AntdContext);
