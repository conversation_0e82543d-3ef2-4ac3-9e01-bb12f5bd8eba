/**
 * @Author: wangyw26123
 * @Description: 基本信息-审批人配置
 * @Date: Created in 2022-04-25 16:22:47
 * @Modifed By:
 */

import React, { useEffect, useRef, useState } from 'react';
import { YRSelect, YRTag, YRForm, YRInput, YRTreeSelect } from 'yrantd';
import { get, flatMap } from 'lodash-es';
import store from '@/store';
import { useSetState } from 'ahooks';
import UserModal from '@/pages/Flow/XFlow/components/UserModal';
import GroupModal from '@/pages/Flow/XFlow/components/GroupModal';
import RoleModal from '@/pages/Flow/XFlow/components/RoleModal';

import { M02 } from '@permit/flow';
import { queryRolePage } from '@/services/setting';

const { queryFlowApprovalStationList, queryUserList } = M02.interfaces;

const FormItem = YRForm.Item;
const { Option } = YRSelect;
const { withModel } = store;
const { SHOW_PARENT } = YRTreeSelect;

const radioStyle = {
  display: 'block',
  height: '30px',
  lineHeight: '30px'
};

const candidateGroups = ['properties', 'usertaskassignment', 'assignment', 'candidateGroups'];
const candidateUsers = ['properties', 'usertaskassignment', 'assignment', 'candidateUsers'];

const ApprovalChannel = (props) => {
  const {
    globalDisabled,
    targetData,
    targetData: { nodeFormData },
    form: { getFieldValue, setFieldValue, getFieldsValue, setFieldsValue },
    form,
    updateNode
  } = props;
  const [state, setState] = useSetState({
    groupVisible: false,
    userVisible: false,
    groupList: [] as any,
    userList: [] as any
  });
  const assignModel = YRForm.useWatch(['properties', 'assignModel'], form);

  const onCloseTag = (type, index) => {
    const namePath = type == '0' ? candidateGroups : candidateUsers;
    const users = getFieldValue(namePath);
    setFieldValue(
      namePath,
      users.filter((item, i) => i !== index)
    );

    updateNode({
      ...targetData,
      nodeFormData: getFieldsValue()
    });
  };

  useEffect(() => {
    if (assignModel) {
      if (assignModel === '1' || assignModel === '3') {
        setFieldsValue({ properties: { usertaskassignment: { assignment: { assignee: '${user}' } } } });
      } else {
        setFieldsValue({ properties: { usertaskassignment: { assignment: { assignee: null } } } });
      }
    }
  }, [assignModel]);

  useEffect(() => {
    queryRolePage({}).then((res) => {
      setState({ groupList: res?.data?.list });
    });
    queryUserList().then((res) => {
      setState({ userList: res?.data });
    });
  }, []);

  return (
    <>
      <FormItem name={['properties', 'multiinstance_type']} initialValue={'Parallel'} hidden>
        <YRInput />
      </FormItem>
      <FormItem name={['properties', 'multiinstance_cardinality']} initialValue={''} hidden>
        <YRInput />
      </FormItem>
      <FormItem name={['properties', 'multiinstance_collection']} initialValue={'users'} hidden>
        <YRInput />
      </FormItem>
      <FormItem name={['properties', 'multiinstance_variable']} initialValue={'user'} hidden>
        <YRInput />
      </FormItem>
      <FormItem name={['properties', 'isforcompensation']} initialValue={'false'} hidden>
        <YRInput />
      </FormItem>
      <FormItem name={['properties', 'usertaskassignment', 'assignment', 'type']} initialValue={'static'} hidden>
        <YRInput />
      </FormItem>
      <FormItem name={['properties', 'usertaskassignment', 'assignment', 'assignee']} initialValue={'${user}'} hidden>
        <YRInput />
      </FormItem>

      {/* <YRDivider orientation="left">基础配置-基础规则</YRDivider> */}

      <FormItem
        name={candidateGroups}
        label="审批角色"
        initialValue={get(nodeFormData, candidateGroups)}
        rules={[
          {
            required: true,
            message: '请选择审批角色'
          }
        ]}
      >
        {getFieldValue(candidateGroups)?.map((id, index) => (
          <YRTag key={id} closable={!globalDisabled} onClose={() => onCloseTag('0', index)}>
            {state.groupList?.find((item: any) => item.roleId == id)?.roleName}
          </YRTag>
        ))}
        {globalDisabled || (
          <YRTag
            key="candidateGroups"
            onClick={() => {
              setState({ groupVisible: true });
            }}
            style={{ background: '#fff', borderStyle: 'dashed', cursor: 'pointer' }}
          >
            +添加
          </YRTag>
        )}
      </FormItem>

      <FormItem
        name={candidateUsers}
        label="审批人员"
        initialValue={get(nodeFormData, candidateUsers)}
        rules={[
          {
            required: true,
            message: '请选择审批人员'
          }
        ]}
      >
        {getFieldValue(candidateUsers)?.map((id, index) => (
          <YRTag key={id} closable={!globalDisabled} onClose={() => onCloseTag('1', index)}>
            {state.userList?.find((item) => item.userId == id)?.userName}
          </YRTag>
        ))}
        {globalDisabled || (
          <YRTag
            key="candidateUsers"
            onClick={() => {
              setState({ userVisible: true });
            }}
            style={{ background: '#fff', borderStyle: 'dashed', cursor: 'pointer' }}
          >
            +添加
          </YRTag>
        )}
      </FormItem>

      {/* <GroupModal
        data={state.groupList}
        visible={state.groupVisible}
        onOk={(values) => {
          setFieldValue(candidateGroups, values);

          updateNode({
            ...targetData,
            nodeFormData: getFieldsValue()
          });
        }}
        onCancel={() => {
          setState({ groupVisible: false });
        }}
        initialValue={getFieldValue(candidateGroups)}
      /> */}

      <RoleModal
        data={state.groupList}
        visible={state.groupVisible}
        onOk={(values) => {
          setFieldValue(candidateGroups, values);
          updateNode({
            ...targetData,
            nodeFormData: getFieldsValue()
          });
        }}
        onCancel={() => {
          setState({ groupVisible: false });
        }}
        initialValue={getFieldValue(candidateGroups)}
      />

      <UserModal
        data={state.userList}
        visible={state.userVisible}
        onOk={(values) => {
          setFieldValue(candidateUsers, values);

          updateNode({
            ...targetData,
            nodeFormData: getFieldsValue()
          });
        }}
        onCancel={() => {
          setState({ userVisible: false });
        }}
        initialValue={getFieldValue(candidateUsers)}
      />
    </>
  );
};

export default ApprovalChannel;
