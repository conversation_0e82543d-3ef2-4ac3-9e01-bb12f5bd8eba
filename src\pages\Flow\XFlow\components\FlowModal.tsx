/**
 * 流程管理列表
 */

import { flowSceneTypeColor } from '@/constant/StyleConst';
import store from '@/store';
import { openNewTab } from '@/utils/utils';
import { useAntdTable, useSetState } from 'ahooks';

import React, { useEffect, useState } from 'react';

import { YRButton, YRDict, YRForm, YRLink, YRModal, YRTable, YRTableProps, YRTag } from 'yrantd';

const FlowModal = (props: any) => {
  const { visible, changeVisible, onOk, tableProps, form, run, params, currentSonId } = props;
  const [flowState] = store.useModel('flow');
  const { processBaseInfo } = flowState;
  const referenceType = YRForm.useWatch(['properties', 'callactivitycalledelementtype'], form);

  const [state, setState] = useSetState<any>({
    selectedIds: '',
    selectedRows: {}
  });

  /** 调用查询接口 */
  useEffect(() => {
    if (visible && processBaseInfo) {
      const { flowNumber } = processBaseInfo as any;
      run({ ...params[0], current: 1, currentFlowNumber: flowNumber } as any);
    } else {
      setState({
        selectedIds: '',
        selectedRows: {}
      });
    }
  }, [visible]);

  /** 如果 currentSonId 有值, 那么就是就修改，设置 selectId 为 currentSonId */
  useEffect(() => {
    if (currentSonId) {
      setState({
        selectedIds: currentSonId
      });
    }
  }, [currentSonId]);

  /** 表格 rowSelection */
  const rowSelection: any = {
    type: 'radio',
    onChange: (selectedRowKeys, rows) => {
      setState({
        selectedIds: selectedRowKeys,
        selectedRows: rows
      });
    },
    selectedRowKeys: state.selectedIds
  };

  const toDetail = (row: any) => {
    openNewTab({
      pathname: '/setting/flow/detail',
      query: { id: row?.id }
    });
  };

  const onCancel = () => {
    changeVisible(false);
  };

  const onSubmit = () => {
    onOk({ ...state.selectedRows });
    changeVisible(false);
  };

  const getTableColumns: YRTableProps['columns'] = [
    {
      title: '流程编号',
      dataIndex: 'modelNumber',
      render: (value, row) => {
        if (value) {
          return (
            <YRLink type="primary" onClick={() => toDetail(row)}>
              {value}
            </YRLink>
          );
        }
        return CONST.null;
      }
    },
    {
      title: '流程名称',
      dataIndex: 'modelName'
    },
    {
      title: '流程类型',
      dataIndex: 'sceneType',
      render: (value: string) =>
        value ? (
          <YRTag color={flowSceneTypeColor[value]}>
            <YRDict.Text dictkey="flow_scene_type" defaultValue={value} />
          </YRTag>
        ) : (
          CONST.null
        )
    }
  ];

  // 版本 key | 编号 id
  const expandableRenderComponent = (record) => {
    const { versions } = record;

    const columns: YRTableProps['columns'] = [
      {
        title: '版本',
        dataIndex: 'version',
        width: 80
      },
      {
        title: '版本编号',
        dataIndex: 'versionId'
      }
    ];

    return (
      <YRTable
        rowKey={(row: any) => row?.versionId}
        rowSelection={rowSelection}
        dataSource={versions}
        columns={columns}
        showIndex={false}
        scroll={{ y: '310px' }}
      />
    );
  };

  return (
    <YRModal
      title="选择引用元素"
      open={visible}
      width={'50%'}
      onCancel={onCancel}
      footer={[
        <YRButton key="cancel" onClick={onCancel}>
          取消
        </YRButton>,
        <YRButton key="submit" type="primary" onClick={onSubmit}>
          确定
        </YRButton>
      ]}
    >
      {referenceType === 'id' ? (
        <YRTable
          rowKey={(row: any) => row?.modelNumber}
          columns={getTableColumns}
          showIndex={false}
          expandable={{
            expandedRowRender: (record: any) => expandableRenderComponent(record)
          }}
          {...tableProps}
        />
      ) : (
        <YRTable
          rowKey={(row: any) => row?.modelNumber}
          rowSelection={rowSelection}
          columns={getTableColumns}
          showIndex={false}
          {...tableProps}
        />
      )}
    </YRModal>
  );
};

export default FlowModal;
