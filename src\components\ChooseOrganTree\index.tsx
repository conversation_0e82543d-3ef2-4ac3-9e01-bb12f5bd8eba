import React, { useState } from 'react';
import { useRequest } from 'ahooks';
import { M0105 } from '@permit/organ';
import { YRTreeSelect } from 'yrantd';

const ChooseOrganTree = (props: { selectBack: (values: any) => void }) => {
  const [organData, setOrganData] = useState([] as any[]);
  const { selectBack } = props;

  const { queryFirstLvlOrgList } = M0105.interfaces;

  const { loading: queryOrganTreeLoading } = useRequest(queryFirstLvlOrgList, {
    onSuccess: (res: any) => {
      if (res?.errorMessage === null) {
        const { data } = res;
        setOrganData([data]);
      }
    },
    defaultParams: [
      {
        queryAuthFlag: '0'
      }
    ]
  });

  return (
    <YRTreeSelect
      loading={queryOrganTreeLoading}
      style={{ width: '100%' }}
      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
      treeData={organData}
      fieldNames={{ label: 'orgName', value: 'orgId' }}
      onSelect={(_, node) => {
        const { orgLvl, orgId } = node as { orgId: string; orgLvl: never };
        selectBack({ orgId, orgLvlList: [orgLvl] });
      }}
      placeholder="请选择机构"
      treeDefaultExpandAll
      allowClear
    />
  );
};

export default ChooseOrganTree;
