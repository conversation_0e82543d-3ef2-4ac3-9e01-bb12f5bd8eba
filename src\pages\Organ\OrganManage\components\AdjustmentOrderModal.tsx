/**
 * @Author: wangyw26123
 * @Description: 机构管理-机构层级调整
 * @Date: Created in 2022-12-15 16:58:45
 * @Modifed By:
 */
import React, { useEffect, useState } from 'react';
import { YRForm, YRInput, YRModal, YRButton, YRDict, YRTreeSelect, YRMessage, YRTypography } from 'yrantd';
import { EnumMsg } from '@/constant/common';
import { M010101 } from '@permit/organ';
import { checkAuth } from '@/utils/utils';

const { Paragraph } = YRTypography;
const { changeOrganLevelApprove } = M010101.E07.interfaces;

/**
 * 根据机构Id获取机构项及父级机构
 * @param treeData 机构树
 * @param organId 机构Id
 * @param parent 父级机构
 * @param fn 回调函数，找到时触发
 */
const getOrganItemById = (treeData, organId, fn) => {
  const loop = (data, id, _parent, _callback) => {
    for (let i = 0; i < data.length; i++) {
      if (data[i].organId === id) {
        return _callback(data[i], _parent);
      }

      if (data[i].children) {
        loop(data[i].children!, id, data[i], _callback);
      }
    }
  };

  loop(treeData, organId, null, (item, parentOrgan) => {
    fn(item, parentOrgan);
  });
};

interface AdjustmentOrderProps {
  visible: boolean;
  organInfo: any;
  treeData: any[];
  onCancel: () => void;
  callback: () => void;
}

/**
 * 机构层级调整请求入参
 */
interface Params {
  /**
   * 机构名称
   */
  organName: string;
  /**
   * 机构编号
   */
  organId: string;
  /**
   * 调整前级别
   */
  beforeLevel: string;
  /**
   * 调整后级别
   */
  afterLevel: string;
  /**
   * 调整前上级机构
   */
  beforeParentOrganId: string;
  /**
   * 调整后上级机构
   */
  afterParentOrganId: string;
  /**
   * 调整后上级机构级别
   */
  afterParentOrganLevel: string;
  /**
   * 调整前上级机构名称
   */
  beforeParentOrganName: string;
  /**
   * 调整后上级机构名称
   */
  afterParentOrganName: string;
}

const AdjustmentOrderModal = (props: AdjustmentOrderProps) => {
  const { visible, organInfo, treeData, onCancel: cancelProps, callback } = props;
  const [form] = YRForm.useForm();
  const [beforeParentOrganName, setBeforeParentOrganName] = useState<string>('');
  const [afterParentOrganName, setAfterParentOrganName] = useState<string>('');
  const [btnLoading, setBtnLoading] = useState<boolean>(false);

  useEffect(() => {
    if (visible) {
      form.resetFields();
      getOrganItemById(treeData, organInfo.organId, (_, parentOrgan) => {
        if (!parentOrgan) return;
        setBeforeParentOrganName(parentOrgan.organName);
        form.setFieldValue('beforeParentOrganId', parentOrgan.organId);
      });
    }
  }, [organInfo, visible]);

  const onSelectOrgan = (value) => {
    getOrganItemById(treeData, value, (item) => {
      setAfterParentOrganName(item.organName);
      form.setFieldValue('afterParentOrganLevel', item.organLevel);
    });
  };

  const onSubmit = () => {
    if (!checkAuth(M010101.E07)) return;

    form.validateFields().then((values) => {
      const params: Params = {
        ...values,
        beforeParentOrganName,
        afterParentOrganName
      };
      setBtnLoading(true);
      changeOrganLevelApprove(params).then((res) => {
        setBtnLoading(false);
        if (res.success) {
          YRMessage.success(EnumMsg.operator);
          callback();
        }
      });
    });
  };

  const onCancel = () => {
    cancelProps();
    form.resetFields();
  };

  return (
    <YRModal
      size="middle"
      title="机构层级调整"
      open={visible}
      onCancel={onCancel}
      footer={[
        <YRButton key="cancel" onClick={onCancel}>
          取消
        </YRButton>,
        <YRButton
          key="submit"
          type="primary"
          check={M010101.E07}
          disabled={btnLoading}
          loading={btnLoading}
          onClick={onSubmit}
        >
          确定
        </YRButton>
      ]}
    >
      <YRForm form={form} bordered>
        <YRForm.Row>
          <YRForm.Item
            name="organName"
            label="机构名称"
            column="block"
            initialValue={organInfo.organName}
            rules={[{ required: true, message: '请输入机构名称' }]}
          >
            <YRInput disabled />
          </YRForm.Item>
          <YRForm.Item
            name="organId"
            label="机构编号"
            column="block"
            initialValue={organInfo.organId}
            rules={[{ required: true, message: '请输入机构编号' }]}
          >
            <YRInput disabled />
          </YRForm.Item>
          <YRForm.Item
            name="beforeLevel"
            label="调整前级别"
            column="block"
            initialValue={organInfo.organLevel}
            rules={[{ required: true, message: '请选择当前级别' }]}
          >
            <YRDict.Select disabled dictkey="EnumOrganLevel" placeholder="请选择当前级别" />
          </YRForm.Item>
          <YRForm.Item
            name="afterLevel"
            label="调整后级别"
            column="block"
            rules={[{ required: true, message: '请选择调整后级别' }]}
          >
            <YRDict.Select dictkey="EnumOrganLevel" placeholder="请选择调整后级别" />
          </YRForm.Item>
          <YRForm.Item
            name="beforeParentOrganId"
            label="调整前上级机构"
            column="block"
            rules={[{ required: true, message: '请选择调整前上级机构' }]}
          >
            <YRTreeSelect
              disabled
              treeData={treeData}
              fieldNames={{ label: 'organName', value: 'organId' }}
              placeholder="请选择调整前上级机构"
            />
          </YRForm.Item>
          <YRForm.Item
            name="afterParentOrganId"
            label="调整后上级机构"
            column="block"
            rules={[{ required: true, message: '请选择调整后上级机构' }]}
          >
            <YRTreeSelect
              onSelect={onSelectOrgan}
              treeData={treeData}
              fieldNames={{ label: 'organName', value: 'organId' }}
              placeholder="请选择调整后上级机构"
            />
          </YRForm.Item>
          <YRForm.Item
            name="afterParentOrganLevel"
            label="调整后上级机构级别"
            column="block"
            rules={[{ required: true, message: '请选择调整后上级机构级别' }]}
          >
            <YRDict.Select disabled dictkey="EnumOrganLevel" placeholder="请选择调整后上级机构级别" />
          </YRForm.Item>
        </YRForm.Row>
      </YRForm>
      <Paragraph ellipsis={{ expandable: true, symbol: '更多' }} style={{ color: '#8C8C8C' }}>
        1、机构层级调整审批通过前或审批拒绝，由该机构发起的业务，均按照原机构/流程处理；非该机构发起的业务，流转到该机构均按照原流程处理。
        <br />
        2、机构层级调整审批通过后，由该机构发起的业务，未提交的业务按照新机构/流程处理；已提交的业务按照原机构/流程处理。
        <br />
        3、机构层级调整审批通过后，非该机构发起的业务，未流转到该机构的按照新机构/流程处理；审批完成前，已流转到该机构的(已完成派单)按照老机构/流程处理；审批完成后流转到该机构的按照新机构/流程处理。
        <br />
        4、机构层级调整审批通过后，由该机构发起的业务，退回到上一岗或任意岗(非发起人)，按照原机构/流程处理；退回到发起人的业务，按照新机构/流程处理。
        <br />
        5、机构层级调整审批通过后，非该机构发起的业务，按照新机构/流程处理。
      </Paragraph>
    </YRModal>
  );
};

export default AdjustmentOrderModal;
