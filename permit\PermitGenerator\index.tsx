import { request } from '@yr/util';
import React, { useState, useMemo } from 'react';
import { YRLoanPermitGenerator } from 'yr-loan-antd';
import { COMMON } from '../common';
import store from '@/store';
import { joinRoutePath } from '@/utils/utils';
import partRoutes from '../../src/routes/part';
import packageInfo from '../../package.json';
import './styles.less';

const { withModel } = store;

function queryAllPermit(param) {
  return request('/v1/permitQuery/queryAllPermit', {
    param,
    module: { cname: '管理后台', prefix: '/hsjry/admin/manager' },
    serviceScene: '002',
    serviceName: '权限配置详情'
  });
}

export default withModel('login', ([state, dispatchers]) => ({
  login: dispatchers.login
}))(PermitGenerator);

function PermitGenerator(props) {
  const [oldAllPermit = [], setOldAllPermit] = useState([]);

  async function getAllPermits() {
    const { login } = props;
    sessionStorage.tenantId = '000';
    if (!sessionStorage.userInfo) {
      await login({
        loginAccount: 'hundsun',
        password: 'hundsun',
        picCode: null,
        tenantId: '000'
      });
    }
    const res = await queryAllPermit({ permitTypeList: ['1', '2', '4'] });
    setOldAllPermit(res?.data?.permitTreeDtoList || []);
  }

  useMemo(getAllPermits, []);

  return (
    <YRLoanPermitGenerator
      packageInfo={packageInfo}
      partRoutes={joinRoutePath(partRoutes)}
      DEPLOY={DEPLOY}
      COMMON={COMMON}
      oldAllPermit={oldAllPermit}
    />
  );
}
