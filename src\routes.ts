import { isInIcestark } from '@ice/stark-app';
import fullRoutes from '@/routes/full';
import partRoutes from '@/routes/part';
import NotFound from '@/pages/NotFound';
import Login from '@/pages/login';
import ProLayout from '@/pages/Layout';
import SecurityLayout from '@/layouts/SecurityLayout';
import PermitGenerator from '../permit/PermitGenerator';

const routerConfig = [
  ...(isInIcestark() ? [] : [{ path: '/login', component: Login }]),
  {
    path: '/',
    component: startMode === 'PERMIT_SQL' ? PermitGenerator : SecurityLayout,
    appId: packageInfo.name,
    appIdList: [packageInfo.name],
    children: [
      ...fullRoutes,
      {
        path: '/',
        component: ProLayout,
        appId: packageInfo.name,
        appIdList: [packageInfo.name],
        children: [...partRoutes]
      },
      {
        component: NotFound
      }
    ]
  }
];

export default routerConfig;
