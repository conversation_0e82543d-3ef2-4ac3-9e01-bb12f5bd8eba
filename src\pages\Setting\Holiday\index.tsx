/*
 * @Author: 伍晶晶
 * @Description: 节假日
 * @Date: 2023-03-20 11:14:48
 * @LastEditors: 伍晶晶
 * @LastEditTime: 2023-03-29 17:07:55
 */
import { queryLastModifyRecordList } from '@/services/setting';
import { useEventListener, useRequest } from 'ahooks';
import React, { useEffect, useMemo } from 'react';
import { YRFlexPageLayout } from 'yrantd';
import Calendar from './Calendar';
import { AntdContextProvider } from './Context';
import { DefaultNode, IData, KeyEnum, TreeNode } from './Interface';
import RecordList from './RecordList';
import TypeTree from './TypeTree';

type AntdContextProviderValue = React.ComponentProps<typeof AntdContextProvider>['value'];

const HolidayIndex = () => {
  const [selectedKey, setSelectedKey] = React.useState<Partial<TreeNode>>(DefaultNode);

  const antdTreeState = useRequest<IData[], any>(
    (p) => queryLastModifyRecordList(p).then((res) => (res.success ? res.data : [])),
    {
      throttleWait: 5000,
      manual: true
    }
  );

  const memoProviderValue = useMemo<AntdContextProviderValue>(
    () => ({
      antdTreeProps: antdTreeState,
      selectedState: [selectedKey, setSelectedKey]
    }),
    [antdTreeState, selectedKey, setSelectedKey]
  );

  useEventListener('keydown', (ev) => {
    if (ev.code === 'Escape' && selectedKey[KeyEnum.KEY] !== 'all') {
      setSelectedKey(DefaultNode);
    }
  });

  useEffect(() => {
    if (selectedKey[KeyEnum.KEY] === 'all') {
      antdTreeState.run();
    }
  }, [selectedKey]);

  return (
    <YRFlexPageLayout>
      <AntdContextProvider value={memoProviderValue}>
        <YRFlexPageLayout.Sider title="假期种类">
          <TypeTree />
        </YRFlexPageLayout.Sider>
        <YRFlexPageLayout.Main
          breadcrumb={{
            items: [DefaultNode, ...(selectedKey[KeyEnum.KEY] !== 'all' ? [selectedKey] : [])],
            fieldNames: { label: KeyEnum.LABEL, key: KeyEnum.KEY },
            disabled: (item) => item === selectedKey,
            onClick: (item) => {
              setSelectedKey(item);
            }
          }}
        >
          {selectedKey[KeyEnum.KEY] === 'all' ? <RecordList /> : <Calendar />}
        </YRFlexPageLayout.Main>
      </AntdContextProvider>
    </YRFlexPageLayout>
  );
};

export default HolidayIndex;
