/**
 * @Author: wangyw26123
 * @Description: 节点属性面板
 * @Date: Created in 2022-04-24 15:23:15
 * @Modifed By:
 */

import React, { useCallback, useEffect, forwardRef, useImperativeHandle, useState } from 'react';
import { YRCard, YRForm } from 'yrantd';
import { isEmpty } from 'lodash-es';
import { cardBodyStyle } from '@/pages/Flow/XFlow/constants';
import { useDebounceEffect, useDebounceFn, useUpdateEffect } from 'ahooks';
import FormIndex from './NodeAttrPanelForm/FormIndex';
import XflowInstance from '@/pages/Flow/XFlow/XflowInstance';

export default forwardRef((props: any, ref) => {
  const [form] = YRForm.useForm();
  const { targetData = {}, updateNode, globalDisabled } = props;
  // 引入元素值
  const formData = YRForm.useWatch([], form);
  // 为了防止点击过怪造成无法赋值，设置一个数据存放老的数据
  const [baseData, setBaseData] = useState<any>();

  useImperativeHandle(ref, () => {
    return {
      targetData: { ...targetData }
    };
  });

  useUpdateEffect(() => {
    if (!globalDisabled) {
      runChange();
    }
  }, [formData]);

  useDebounceEffect(() => {
    targetData && setBaseData(targetData);
  }, [targetData]);

  const { run: runChange } = useDebounceFn(
    () => {
      onChange();
      XflowInstance.setRealTimeSave();
    },
    {
      wait: 500
    }
  );

  const onChange = useCallback(() => {
    // console.log('触发 onChange', targetData);
    // 当字段的值是list，在onFieldsChange中无法获取更新后的最新值，所以暂时使用定时器处理
    const timer = setTimeout(() => {
      const values: any = form.getFieldsValue();
      const dueDateObj = form.getFieldValue(['properties', 'dueDate']);
      const dueDateValue = form.getFieldValue(['properties', 'dueDate', 'value']);
      // 更新画布节点数据
      const properties = form.getFieldValue('properties');
      properties?.multiinstance_condition === '${orSign.completionCondition(execution)}' &&
        delete properties.signModelConfig;
      // 防止字符串闪烁
      targetData.label = values.name;
      updateNode({
        ...(targetData || baseData),
        label: values.name,
        // 这里需要更新所有表单值，非表单注册的字段不进行更新
        nodeFormData: {
          ...values,
          properties: {
            ...properties,
            dueDate: isEmpty(dueDateValue) ? undefined : dueDateObj // 后端要求value没值，属性不传
          }
        }
      });
      clearTimeout(timer);
    }, 100);
  }, []);

  useEffect(() => {
    // 表单初始化完成后，进行一次初始值保存，因为表单注册时值是不会同步到节点的。
    if (!globalDisabled) {
      onChange();
    }
  }, []);

  return (
    <YRCard size="small" title={targetData?.label} bodyStyle={cardBodyStyle}>
      {/* id变化时会重新渲染表单 */}
      <YRForm
        // onFieldsChange={() => {
        //   onChange();
        //   XflowInstance.setRealTimeSave();
        // }}
        form={form}
      >
        <FormIndex {...props} key={targetData?.id} form={form} />
      </YRForm>
    </YRCard>
  );
});
