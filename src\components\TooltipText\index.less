@import "~@/global.less";
.tooltip-cont-text {
  display: block;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.tip-content {
  :global {
    .ant-tooltip-inner {
      color: fade(#1f2e4a, 70%);
      background-color: #fff;
    }
    .ant-tooltip-arrow::before {
      background-color: #fff;
    }
  }
}

.tip-content-base {
  position: relative;
  .real-cont {
    padding: 8px 16px;
    border-bottom: 1px solid fade(#1f2e4a, 10%);
    .text {
      color: fade(#1f2e4a, 70%);
    }
    .title {
      color: fade(#1f2e4a, 40%);
      font-size: 12px;
    }
  }
  .actions {
    position: absolute;
    top: 8px;
    right: 16px;
    font-size: 12px;
    line-height: 21px;
    visibility: hidden;
    :global {
      a {
        margin-left: 4px;
        color: #1890ff;
      }
      a:hover {
        color: #1890ff;
      }
    }
  }
}
.tip-content-base:hover {
  .actions {
    visibility: visible;
  }
}
