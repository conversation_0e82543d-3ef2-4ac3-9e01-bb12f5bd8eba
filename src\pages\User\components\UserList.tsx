/**
 * @Author: 刘文强
 * @Description: 用户列表
 * @Date: Created in 2022-12-13 20:49:28
 * @Modifed By:
 */

import React, { useState } from 'react';
import {
  YRTable,
  YRForm,
  YRLink,
  YRButton,
  YRFlexPageLayout,
  YRDict,
  YRTableRowContent,
  YRModal,
  YRMessage,
  YRBadge,
  YREasyUseModal,
  YRSpace,
  YRText
} from 'yrantd';
import type { FormItemListProps, YRTableProps } from 'yrantd';
import AddUserModal from './AddUserModal';
import Detail from './Detail';
import AssignRoleModal from './AssignRoleModal';
import { EnumMsg } from '@/constant/common';
import ChangeOrganModal from '@/pages/User/components/ChangeOrganModal';
import Dict from '../mock/getDict';
import AssistantModal from '@/pages/User/components/AssistantModal';
import { EnumGuardUserStatusColor } from '@/constant/StyleConst';
import ChooseUserModal from '@/components/ChooseUserModal';
import { checkAuth } from '@/utils/utils';
import RegisterModal from './RegisterModal';
import {
  changeUserStatus,
  deleteUser,
  handleOverAdminPermission,
  queryUserDetailWithIdentify,
  resetPassword
} from '../../../services/user';
import { M010401 } from '@permit/user';

const UserList = (props) => {
  const [form] = YRForm.useForm();
  const [addVisible, setAddVisible] = useState(false);
  const [detailVisible, setDetailVisible] = useState(false);
  const [content, setContent] = useState({});
  const [assignVisible, setAssignVisible] = useState(false);
  const [changeVisible, setChangeVisible] = useState(false);
  const [assistantVisible, setAssistantVisible] = useState(false);
  const [userDetail, setUserDetail] = useState({});
  const [organChangeVisible, setOrganChangeVisible] = useState(false);
  const [type, setType] = useState('readPretty');
  const { currentOrganId, dictMap = {}, run, tableProps } = props;

  const formItemList: FormItemListProps[] = [
    {
      placeholder: '用户名称',
      key: 'userName',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '用户状态',
      key: 'userStatus',
      type: 'dictSelect',
      dictkey: 'EnumGuardUserStatus',
      position: 'show'
    },
    {
      placeholder: '登录账号',
      key: 'accountNo',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '手机号',
      key: 'mobile',
      type: 'input',
      position: 'show'
    }
  ];

  const handleSearch = () => {
    const values: any = form.getFieldsValue() || {};
    run(values);
  };

  const lookUp = (row, mode) => {
    const param = {
      userId: row.userId
    };
    setUserDetail(row);
    queryUserDetailWithIdentify(param).then((res) => {
      if (res && res.success) {
        mode === 'edit' && setVisible(true);
        setContent(res.data || {});
        mode === 'readPretty' && setDetailVisible(true);
        setType(mode);
      }
    });
  };

  const setVisible = (visible) => {
    setAddVisible(visible);
  };

  const handleStop = (userId, userStatus) => {
    // 用户停用
    YRModal.confirm({
      title: '正在使用中，是否停用？',
      content: '',
      onOk() {
        changeUserStatus({ userId, userStatus }).then((res) => {
          if (res && res.success) {
            YRMessage.success('停用成功');
            handleSearch();
          }
        });
      }
    });
  };
  const handleLogof = (userId, userStatus) => {
    // 用户注销
    YRModal.confirm({
      title: '确定注销吗？',
      content: '',
      onOk() {
        changeUserStatus({ userId, userStatus }).then((res) => {
          if (res && res.success) {
            YRMessage.success('注销成功');
            handleSearch();
          }
        });
      }
    });
  };
  const handleStart = (userId, userStatus) => {
    // 用户启用
    YRModal.confirm({
      title: '是否确实启用该用户？',
      content: '',
      onOk() {
        changeUserStatus({ userId, userStatus }).then((res) => {
          if (res && res.success) {
            YRMessage.success('启用成功');
            handleSearch();
          }
        });
      }
    });
  };
  const handleDelete = (userId) => {
    // 用户删除
    YRModal.confirm({
      title: '确定删除吗？',
      content: '',
      onOk() {
        deleteUser({ userId }).then((res) => {
          if (res && res.success) {
            YRMessage.success('删除成功');
            handleSearch();
          }
        });
      }
    });
  };
  const handleAssignRoles = (row) => {
    setAssignVisible(true);
    setUserDetail(row);
  };
  const hadleChangeOrgan = (row) => {
    setOrganChangeVisible(true);
    setUserDetail(row);
  };
  const handleResetPwd = (userId) => {
    YRModal.confirm({
      title: '重置密码',
      content: '确定重置密码吗，重置后密码将为系统初始化密码?',
      onOk() {
        resetPassword({ userId }).then((res) => {
          if (res.success) {
            YRMessage.success(EnumMsg.reset);
          }
        });
      }
    });
  };
  const handleOver = (userId) => {
    YRModal.confirm({
      title: '超管权限移交',
      content: (
        <>
          <YRText type="danger">移交后您将失去超级管理员权限</YRText>，你还要继续吗？
        </>
      ),
      onOk() {
        handleOverAdminPermission({ userId }).then((res) => {
          if (res.success) {
            YRMessage.success(EnumMsg.handover);
            handleSearch();
          }
        });
      }
    });
  };

  const getTableColumns: YRTableProps['columns'] = [
    // {
    //   title: '用户编号',
    //   dataIndex: 'userId',
    //   width: 220,
    //   render: (value: string) => value || CONST.null
    // },
    {
      title: '用户名称',
      dataIndex: 'userName',
      width: 120,
      render: (value: string) => value || CONST.null
    },
    {
      title: '登录账号',
      dataIndex: 'accountNo',
      width: 150,
      render: (value: string) => value || CONST.null
    },
    {
      title: '角色',
      dataIndex: 'role',
      width: 200,
      render: (value: string, row) => {
        const userIdentifyDtoList = row?.userIdentifyDtoList || [];
        const roleDtoList = [];
        userIdentifyDtoList.forEach((item) => {
          // @ts-ignore
          item.roleDtoList?.forEach((x: any) => roleDtoList.push(x));
        });
        return (
          <>
            {
              // eslint-disable-next-line array-callback-return
              userIdentifyDtoList.map((y) => {
                if (y.identityRelationType === '001') {
                  const mainRoles = y?.roleDtoList?.map((item: any) => item.roleName);
                  return (
                    <YRTableRowContent key={y.identifyId} title="本职角色">
                      {mainRoles?.join('，')}
                    </YRTableRowContent>
                  );
                }
                if (y.identityRelationType === '002') {
                  const sideRoles = y?.roleDtoList?.map((item: any) => item.roleName);
                  return (
                    <YRTableRowContent key={y.identifyId} title="兼职角色">
                      {sideRoles?.join('，')}
                    </YRTableRowContent>
                  );
                }
                return '';
              })
            }
          </>
        );
      }
    },
    {
      title: '所属机构',
      dataIndex: 'organName',
      width: 200,
      render: (value: string, row) => {
        return value || CONST.null;
        // const { userIdentifyDtoList } = row;
        // const organs = userIdentifyDtoList?.map((item) => item.organName);
        // return <>{organs?.join('，')}</>;
      }
    },
    {
      title: '手机号码',
      dataIndex: 'mobile',
      width: 120,
      render: (value: string) => value || CONST.null
    },
    {
      title: '用户状态',
      dataIndex: 'userStatus',
      width: 100,
      fixed: 'right',
      render: (value: string) => {
        if (!value) return CONST.null;
        return (
          <YRBadge
            color={EnumGuardUserStatusColor[value]}
            text={<YRDict.Text dictkey="EnumGuardUserStatus" defaultValue={value} />}
          />
        );
      }
    },
    {
      title: '操作',
      dataIndex: 'ops',
      width: 80,
      fixed: 'right',
      render: (value, row, index) => {
        return (
          /*
           * 未启用：启用/修改/删除/分配角色/变更机构
           * 启用：停用/修改/分配角色/变更机构/权限移交
           * 停用：启用/修改/注销/分配角色/变更机构
           * 注销：删除
           */
          <YRButton.Overflow>
            <YRLink type="primary" onClick={() => lookUp(row, 'readPretty')}>
              详情
            </YRLink>
            {row?.userStatus === '010' && (
              <YRLink type="primary" onClick={() => handleStop(row.userId, '020')}>
                停用
              </YRLink>
            )}
            {row?.userStatus === '020' && (
              <YRLink type="primary" onClick={() => handleLogof(row.userId, '030')}>
                注销
              </YRLink>
            )}
            {(row?.userStatus === '020' || row?.userStatus === '040') && (
              <YRLink type="primary" onClick={() => handleStart(row.userId, '010')}>
                启用
              </YRLink>
            )}
            {row?.userStatus !== '030' && (
              <YRLink type="primary" onClick={() => lookUp(row, 'edit')}>
                修改
              </YRLink>
            )}
            {(row?.userStatus === '030' || row?.userStatus === '040') && (
              <YRLink type="primary" onClick={() => handleDelete(row.userId)}>
                删除
              </YRLink>
            )}
            {row?.userStatus === '010' && checkAuth(M010401.E01) && (
              <YRLink type="primary" onClick={() => handleOver(row.userId)}>
                权限移交
              </YRLink>
            )}
            {(row?.userStatus === '010' || row?.userStatus === '040' || row.userStatus === '020') && (
              <YRLink type="primary" onClick={() => handleAssignRoles(row)}>
                分配角色
              </YRLink>
            )}
            {(row?.userStatus === '010' || row?.userStatus === '040') && (
              <YRLink type="primary" onClick={() => hadleChangeOrgan(row)}>
                变更机构
              </YRLink>
            )}
            {checkAuth(M010401.E10) && (
              <YRLink type="primary" onClick={() => handleResetPwd(row.userId)}>
                重置密码
              </YRLink>
            )}
          </YRButton.Overflow>
        );
      }
    }
  ];

  const renderExtAction = (
    <YRSpace>
      <YRButton
        key="add"
        type="primary"
        onClick={() => {
          setContent({});
          setVisible(true);
          setType('add');
        }}
      >
        用户新增
      </YRButton>
      {/*<YRButton
        type="primary"
        onClick={() => {
          setAssistantVisible(true);
        }}
      >
        客户经理助理管理
      </YRButton>*/}
      <YRButton type="primary" onClick={() => YREasyUseModal.show(ChooseUserModal, { currentOrganId })}>
        跨租户权限
      </YRButton>
      {/*<YRButton onClick={() => YREasyUseModal.show(RegisterModal)}>用户注册</YRButton>*/}
    </YRSpace>
  );

  return (
    <YREasyUseModal.Provider>
      {detailVisible ? (
        <Detail
          setAssignVisible={setAssignVisible}
          type={type}
          content={content}
          dictMap={dictMap}
          setDetailVisible={setDetailVisible}
          userDetail={userDetail}
          handleStop={handleStop}
          handleLogof={handleLogof}
          handleStart={handleStart}
          lookUp={lookUp}
          handleDelete={handleDelete}
          handleOver={handleOver}
          handleAssignRoles={handleAssignRoles}
          hadleChangeOrgan={hadleChangeOrgan}
          handleResetPwd={handleResetPwd}
        />
      ) : (
        <YRFlexPageLayout.Main title="用户信息">
          <YRTable
            form={form}
            rowKey={(row) => row.userId!}
            columns={getTableColumns}
            formItemList={formItemList}
            extAction={renderExtAction}
            handleSearch={handleSearch}
            {...tableProps}
          />
        </YRFlexPageLayout.Main>
      )}
      <AddUserModal
        handleSearch={handleSearch}
        type={type}
        setVisible={setVisible}
        addVisible={addVisible}
        detail={content}
      />
      {assignVisible && (
        <AssignRoleModal
          title="分配角色"
          queryList={handleSearch}
          setAssignVisible={setAssignVisible}
          assignVisible={assignVisible}
          detail={userDetail}
        />
      )}
      <ChangeOrganModal setChangeVisible={setChangeVisible} changeVisible={changeVisible} />
      <AssistantModal
        title="客户经理助理改造"
        setAssistantVisible={setAssistantVisible}
        assistantVisible={assistantVisible}
      />
      <ChangeOrganModal
        setOrganChangeVisible={setOrganChangeVisible}
        organChangeVisible={organChangeVisible}
        detail={userDetail}
      />
    </YREasyUseModal.Provider>
  );
};
export default Dict([
  'EnumGuardUserStatus',
  'EnumGuardApprovalStatus',
  'EnumUserBizLine',
  'EnumOrganLevel',
  'EnumGuardUserType'
])(UserList);
