/*
 * @Description: file content
 * @Author: your name
 * @Date: 2020-10-27 17:58:52
 * @LastEditors: your name
 * @LastEditTime: 2020-11-12 10:43:39
 */
import React from 'react';
import {
  threeComPathName,
  threeClientType,
  returnType,
  isDependentType,
  isScreatType,
  isAsyncType
} from '@/utils/data-dictionary';
import './index.less';

export default function BaseInformation(props) {
  const { currentInterface, allInterfaceList } = props;
  function getBaseList() {
    if (!currentInterface.comPathCode) {
      return [];
    }
    let baseList = [
      { title: '访问路径', value: currentInterface.accessPath },
      { title: '缓存时间', value: `${currentInterface.expireTime}毫秒` }
    ];
    if (currentInterface.connectTimeout) {
      baseList.push({
        title: '连接超时时间',
        value: `${currentInterface.connectTimeout}毫秒`
      });
    }
    if (currentInterface.readTimeout) {
      baseList.push({
        title: '读超时时间',
        value: `${currentInterface.readTimeout}毫秒`
      });
    }
    if (currentInterface.charset) {
      baseList.push({ title: '编码格式', value: currentInterface.charset });
    }
    if (currentInterface.asyncQueryComPathCode) {
      baseList.push({
        title: '异步查证接口Code',
        value: currentInterface.asyncQueryComPathCode
      });
    }
    // baseList.push({
    //   title: '同步/异步',
    //   value: isAsyncType[currentInterface.asyncSwitch]
    // });
    let depend = isDependentType[currentInterface.isDependent];
    if (currentInterface.isDependent === 1 && currentInterface.comPathCodeR) {
      const found = allInterfaceList.find((item) => {
        return item.comPathCode === currentInterface.comPathCodeR;
      });
      if (found) {
        depend = `${depend}:${found.comPathName}`;
      }
    }
    // baseList.push({ title: '依赖接口', value: depend });
    baseList.push({
      title: '接入方式',
      value: threeClientType[currentInterface.clientType]
    });
    if (currentInterface.requestMethod) {
      baseList.push({
        title: '请求方式',
        value: threeComPathName[currentInterface.requestMethod]
      });
    }
    baseList = [
      ...baseList,
      ...[
        { title: '返回类型', value: returnType[currentInterface.returnType] }
        // {
        //   title: '是否加密',
        //   value: isScreatType[currentInterface.encryptSwitch]
        // }
      ]
    ];
    if (currentInterface.requestBody) {
      baseList.push({
        title: '请求报文体',
        value: currentInterface.requestBody,
        needColor: true
      });
    }
    return baseList;
  }

  const baseList = getBaseList();
  function isJSON(str) {
    if (typeof str === 'string') {
      try {
        JSON.parse(str);
        return true;
      } catch (e) {
        return false;
      }
    }
  }

  return (
    <div className={'inter-cont-base'}>
      {baseList.map((item) => {
        const { needColor = false } = item;
        return (
          <div className={'inter-cont-base-row'} key={item.title}>
            <div className={'title'}>{item.title}:</div>
            <div className={`text ${needColor && item.value ? 'bg' : ''}`}>
              {item.title === '请求报文体' ? (
                isJSON(item.value) ? (
                  <pre>{JSON.stringify(JSON.parse(item.value), null, 2)}</pre>
                ) : (
                  item.value
                )
              ) : (
                item.value
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
}
