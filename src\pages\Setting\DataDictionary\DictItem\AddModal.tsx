/*
 * @Author: 伍晶晶
 * @Description: 字典项弹窗
 * @Date: 2023-03-20 15:06:05
 * @LastEditors: 伍晶晶
 * @LastEditTime: 2023-03-29 18:50:55
 */
import { antdModal } from '@/utils/utils';
import React, { useEffect, useState } from 'react';
import { YREasyUseModal, YRForm, YRInput, YRModal, YRTreeSelect } from 'yrantd';

import { batchQueryDictItem } from '@/services/setting';
import { useRequest } from 'ahooks';

type YRFormProps = React.ComponentProps<typeof YRForm>;
const AddModal: React.FC<{ type: YRFormProps['mode']; row: any }> = ({ type, row }) => {
  const modal = YREasyUseModal.useModal();
  const [form] = YRForm.useForm();
  const [currentRow, setCurrentRow] = useState<any>({});
  const { data, run } = useRequest(
    (p) => batchQueryDictItem(p).then((res) => (res.success && res.data ? res.data[row.dictKey] : [])),
    {
      manual: true
    }
  );

  useEffect(() => {
    setCurrentRow({
      itemKey: row.itemKey,
      itemName: row.itemName,
    });
    if (row?.isChildNode === 'N') {
      run({ dictKeys: [row.dictKey] });
    }
  }, [row]);

  return (
    <YRModal
      {...antdModal(modal)}
      title={`字典 ${row.dictName} ${type === 'add' ? '新增' : '编辑'}项`}
      onOk={() => {
        form
          .validateFields()
          .then((values: any) => {
            modal.resolve({
              ...values,
              dictKey: row.dictKey,
              itemId: row.itemId
            });
            modal.hide();
            modal.remove();
          })
          .catch((err) => {
            // eslint-disable-next-line no-console
            console.log(err);
          });
      }}
    >
      <YRForm form={form} initialValues={currentRow}>
        <YRForm.Row>
          {row?.isChildNode === 'N' && (
            <YRForm.Item label="父字典项" name="parentItemKey">
              <YRTreeSelect
                showSearch
                treeData={data || []}
                fieldNames={{ label: 'itemName', value: 'itemKey', children: 'itemTreeDtoList' }}
                placeholder="请选择父字典项"
              />
            </YRForm.Item>
          )}
          <YRForm.Item label="字典项值" name="itemKey" rules={[{ required: true }]}>
            <YRInput disabled={type === 'edit'} maxLength={50} />
          </YRForm.Item>
          <YRForm.Item label="字典项描述" name="itemName" rules={[{ required: true }]}>
            <YRInput maxLength={50} />
          </YRForm.Item>
        </YRForm.Row>
      </YRForm>
    </YRModal>
  );
};

export default YREasyUseModal.create(AddModal);
