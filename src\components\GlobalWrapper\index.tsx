/**
 * @Author: wangyw26123
 * @Description:
 * @Date: Created in 2023-04-10 14:12:55
 * @Modifed By:
 */

import React, { useEffect, useState } from 'react';
import { YRConfigProvider, YREasyUseModal } from 'yrantd';
import { YRConfigProviderProps } from 'yrantd/lib/yr-config-provider';
import { store } from '@ice/stark-data';
import zhCN from 'antd/es/locale/zh_CN';
import { defaultYRConfig } from '../../defaultSettings';
import merge from 'lodash/merge';

const GlobalWrapper = ({ children }) => {
  const [config, setConfig] = useState<YRConfigProviderProps>({ ...defaultYRConfig });

  useEffect(() => {
    const getSetting = (yrantdConfig) => {
      setConfig((values) => merge({ ...values }, yrantdConfig) );
    };
    store.on('yrantdConfig', getSetting, true);

    return () => store.off('yrantdConfig', getSetting);
  }, []);

  return (
    <YRConfigProvider locale={zhCN as any} {...config}>
      <YREasyUseModal.Provider>{children}</YREasyUseModal.Provider>
    </YRConfigProvider>
  );
};

export default GlobalWrapper;
