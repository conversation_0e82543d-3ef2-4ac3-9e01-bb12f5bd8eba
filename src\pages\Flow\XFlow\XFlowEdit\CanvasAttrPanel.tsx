/*
 * @Author: liaokt
 * @E-mail: <EMAIL>
 * @Date: 2023-03-10 10:20:38
 * @Description: 全局配置页面
 */
import React, { useEffect, useState, forwardRef, useImperativeHandle, useRef, useMemo } from 'react';
import { YRCard, YRForm } from 'yrantd';
import store from '@/store';
import debounce from 'lodash-es/debounce';
import { uuid } from 'yr-loan-antd/lib/util';
import { cardBodyStyle } from '@/pages/Flow/XFlow/constants';
import { useDebounceEffect, useUpdateEffect } from 'ahooks';
import FormIndex from './CanvasAttrPanelForm/FormIndex';
import XflowInstance from '@/pages/Flow/XFlow/XflowInstance';

const WrappedPanelForm = forwardRef((props: any, ref) => {
  const {
    globalFormData,
    originGlobalFormData,
    globalDisabled,
    setGlobalFormData,
    onChange,
    processBaseInfo,
    setProcessBaseInfo,
    startRealTimeSave,
    setOriginGlobalFormData
  } = props;
  const [form] = YRForm.useForm();
  const [renderKey, setRenderKey] = useState(uuid());
  const formData = YRForm.useWatch([], form);

  useDebounceEffect(() => {
    XflowInstance.initLoad = true;
    setRenderKey(uuid());
  }, [startRealTimeSave]);

  // 1. 设置详情反显
  useDebounceEffect(() => {
    // 重置流程基本信息字段
    const { modelNumber, modelName, modelDesc, sceneType, sort, remark, processType, modelResource } = processBaseInfo;
    const { signaldefinitions, exclusionGroups, includeGroups, paramLibraryId, paramLibraryName } = modelResource
      ? JSON.parse(modelResource)?.global?.properties
      : [];

    !originGlobalFormData &&
      form.setFieldsValue({
        flowNumber: modelNumber,
        flowName: modelName,
        flowDesc: modelDesc,
        sceneType,
        type: processType,
        // 避免下拉框出现空标签
        paramLibraryId: paramLibraryId || [],
        paramLibraryName,
        sort,
        remark,
        signaldefinition: signaldefinitions,
        exclusionGroups,
        includeGroups
      });
  }, [processBaseInfo]);

  // 2. 监听 form 值变化，再进行 onChange
  useDebounceEffect(() => {
    !globalDisabled && formData && onChange(form, { formData: { ...globalFormData, ...formData } });
  }, [formData]);

  useImperativeHandle(ref, () => {
    return {
      form
    };
  });

  return (
    <YRCard
      size="small"
      title={globalFormData.name}
      bodyStyle={cardBodyStyle}
      style={{
        height: document.body.clientHeight - 100,
        overflowY: 'auto'
      }}
    >
      <YRForm form={form}>
        <FormIndex {...props} globalFormData={globalFormData} key={renderKey} />
      </YRForm>
    </YRCard>
  );
});

export default forwardRef((props) => {
  const formRef = useRef();
  const [state, dispatchers] = store.useModel('flow');
  const { processBaseInfo, globalFormData, originGlobalFormData, startRealTimeSave } = state;
  const { setGlobalFormData, setOriginGlobalFormData, setProcessBaseInfo } = dispatchers;

  // 不需要重复更新，只需要更新最新的数据即可
  const setGlobalFormDataFn = useMemo(() => {
    return debounce((form, formData) => {
      const formValues = formData?.formData;
      // const values = form.getFieldsValue();
      const formFieldsValue = {
        ...formValues
      };

      setGlobalFormData({
        ...globalFormData,
        ...formFieldsValue
      });
      setOriginGlobalFormData(true);
    }, 100);
  }, []);

  return (
    <WrappedPanelForm
      onChange={setGlobalFormDataFn}
      wrappedComponentRef={formRef}
      processBaseInfo={processBaseInfo}
      globalFormData={globalFormData}
      setGlobalFormData={setGlobalFormData}
      originGlobalFormData={originGlobalFormData}
      setOriginGlobalFormData={setOriginGlobalFormData}
      startRealTimeSave={startRealTimeSave}
      setProcessBaseInfo={setProcessBaseInfo}
      {...props}
    />
  );
});
