/**
 * @Author: wangyw26123
 * @Description: 高级-审批处理配置
 * @Date: Created in 2022-04-25 16:22:47
 * @Modifed By:
 */

import React, { useEffect, useState } from 'react';
import { YRForm, YRRadio, YRSelect, YRSwitch, YRTooltip, YRInput, YRInputNumber } from 'yrantd';
import store from '@/store';
import { InputNumber } from 'antd';

const FormItem = YRForm.Item;
const { Option } = YRSelect;

const radioStyle = {
  display: 'block',
  height: '30px',
  lineHeight: '30px'
};

const ApproverProcessConfig = (props) => {
  const {
    globalDisabled,
    targetData: { nodeFormData },
    form
  } = props;

  const [flowState, dispatchers] = store.useModel('flow');

  const { globalFormData, processBaseInfo } = flowState;
  const { setGlobalFormData } = dispatchers;
  const passRate = YRForm.useWatch(['properties', 'signModelConfig', 'passRate'], form);
  const passNumber = YRForm.useWatch(['properties', 'signModelConfig', 'passNumber'], form);
  const multiinstance_condition = YRForm.useWatch(['properties', 'multiinstance_condition'], form);

  const handleCondition = (e) => {};

  /** 通过率正则 */
  const checkNum = (rule, value, callback) => {
    console.log(passRate, 'passRate', value);
    if (passRate && value !== null) {
      callback('通过率、通过数只能选填一个');
      return;
    }
    callback();
  };

  /** 通过数正则 */
  const checkRate = (rule, value, callback) => {
    const reg = /^(0.\d+|0|1)$/;
    console.log(passNumber, 'passNumber', value);
    if (passNumber && value !== '') {
      callback('通过率、通过数只能选填一个');
      return;
    }
    if (!reg.test(value) && value) {
      callback('请输入正确的通过率（大于0，小于1的小数）');
      return;
    }
    callback();
  };

  return (
    <>
      <FormItem
        name={['properties', 'multiinstance_condition']}
        label="会签或签"
        initialValue={nodeFormData?.properties?.multiinstance_condition || '${orSign.completionCondition(execution)}'}
        rules={[
          {
            required: true,
            whitespace: true,
            message: '请选择会签或签'
          }
        ]}
      >
        <YRRadio.Group onChange={handleCondition} disabled={globalDisabled}>
          <YRRadio style={radioStyle} value="${andSign.completionCondition(execution)}">
            会签
          </YRRadio>
          <YRRadio style={radioStyle} value="${orSign.completionCondition(execution)}">
            或签
          </YRRadio>
        </YRRadio.Group>
      </FormItem>
      {multiinstance_condition === '${andSign.completionCondition(execution)}' && [
        <FormItem
          name={['properties', 'signModelConfig', 'instanceNumber']}
          label="会签人数"
          initialValue={nodeFormData?.properties?.signModelConfig?.instanceNumber || 20}
          tooltip={{
            title: '当实际人数小于该人数时按实际人数分配，当实际人数大于该人数时随机选出该数量进行分配'
          }}
        >
          <YRInputNumber style={{ width: '100%' }} addonAfter={<>人</>} min={2} max={20} />
        </FormItem>,
        <FormItem
          name={['properties', 'signModelConfig', 'passNumber']}
          label="通过数"
          initialValue={nodeFormData?.properties?.signModelConfig?.passNumber}
          rules={[{ validator: checkNum }]}
          tooltip={{
            title: '当同意的数量大于等于该值时，节点完成'
          }}
        >
          <YRInputNumber placeholder={'请输入通过数'} style={{ width: '100%' }} min={0} />
        </FormItem>,
        <FormItem
          name={['properties', 'signModelConfig', 'passRate']}
          label="通过率"
          initialValue={nodeFormData?.properties?.signModelConfig?.passRate}
          rules={[
            { validator: checkRate },
            {
              type: 'number'
            }
          ]}
          tooltip={{
            title: '当同意的数量除以总的会签人数大于等于该值时，节点完成'
          }}
        >
          <YRInputNumber style={{ width: '100%' }} placeholder={'请输入通过率'} max={1} min={0} />
        </FormItem>
      ]}
      <FormItem
        name={['properties', 'dueDate', 'value']}
        label="岗位时效"
        initialValue={nodeFormData?.properties?.dueDate?.value}
      >
        <YRInput
          disabled={globalDisabled}
          addonAfter={
            <FormItem
              name={['properties', 'dueDate', 'type']}
              initialValue={nodeFormData?.properties?.dueDate?.type || '1'}
              rules={[
                {
                  required: true,
                  message: '请选择单位',
                  pattern: new RegExp(/^[1-9]\d*$/, 'g')
                }
              ]}
              noStyle
            >
              <YRSelect disabled={globalDisabled} style={{ width: 70 }} placeholder="请选择">
                <Option value="1">分</Option>
                <Option value="2">时</Option>
                <Option value="3">天</Option>
              </YRSelect>
            </FormItem>
          }
        />
      </FormItem>
    </>
  );
};

export default ApproverProcessConfig;
