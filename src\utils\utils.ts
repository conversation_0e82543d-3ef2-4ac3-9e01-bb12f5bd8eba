/*
 * yunrong.cn Inc. Copyright (c) 2014-2021 All Rights Reserved
 */
import { isInIcestark } from '@ice/stark-app';
import { YRConfigProvider } from 'yrantd';
import { useState, useEffect, useCallback } from 'react';
import { openNewTab as openNewWin, SYSTEM, History, checkAuth as checkAuthUtil } from '@yr/util';
import crypto from 'crypto';
import { sm2 } from 'sm-crypto';
import { MenuDataItem } from '@yr/pro-layout/lib/typings';
import defaultConfig from '@/defaultSettings';

const { encryptWay, publicKey } = SYSTEM;
const openWin = openNewWin(packageInfo.name);

/**
 * MD5加密
 * @param password
 * @returns {string}
 */
export function md5Crypto(password) {
  const hash = crypto.createHash('md5');
  hash.update(password);
  return hash.digest('hex');
}
/**
 * 登陆密码加密,默认MD5加密
 * @param password
 * @returns {*}
 */
export function encryptPassword(password) {
  switch (encryptWay) {
    case 'sm2':
      // 国密加密
      return sm2.doEncrypt(md5Crypto(password), publicKey, 1); // 1 - C1C3C2，0 - C1C2C3，默认为1
    default:
      // MD5加密
      return md5Crypto(password);
  }
}

/**
 * 新打开一个浏览器窗口
 * @param param 跳转的url
 * @param needBasename 是否需要拼接基座标识
 *
 * @example
 *
 * // 基于自身应用新开页面，并携带查询参数
 * openNewTab({
 *   pathname: '/product/list',
 *   query: {
 *     name: '小明'
 *   }
 * });
 *
 * // 跨应用新开页面
 * openNewTab({
 *   pathname: '/kylin-operation/product/fee',
 *   query: {
 *     name: '小明'
 *   }
 * }, false);
 *
 */
export function openNewTab(param, needBasename = true) {
  return openWin(param, needBasename);
}

/**
 * 路由跳转
 * @type {History}
 */
export const router = new History();

/**
 * 拼接路由的路径
 * @param routers 路由集合
 * @returns {*[]} 拼接后的路由集合
 */
export const joinRoutePath = (routers) => {
  const loop = (data, parent) => {
    const routes = [];
    data.forEach((item) => {
      const routeItem = { ...item };
      routeItem.component = null;
      routeItem.path = parent ? `${parent.path}${item.path}` : item.path;

      if (item.children) {
        const children = loop(item.children, routeItem);
        if (children.length) {
          routeItem.children = children;
        }
      }

      // @ts-ignore
      routes.push(routeItem);
    });

    return routes;
  };

  return loop(routers, null);
};

export const formatCustomMenuTree = (menuData) => {
  const loop = (data) => {
    const arr: MenuDataItem[] = [];
    data.forEach((item) => {
      const menuItem: MenuDataItem = {
        path: item.path,
        name: item.name,
        icon: null,
        routes: null
      };

      if (item.children) {
        const getSubMenus = loop(item.children);
        if (getSubMenus.length) {
          menuItem.routes = getSubMenus;
        }
      }

      arr.push(menuItem);
    });

    return arr;
  };

  return loop(menuData);
};

/**
 * 改变全局主题
 * @param color
 */
export const changeTheme = (color) => {
  // 嵌入基座时不使用自身主题
  if (!YRConfigProvider.config || isInIcestark()) return;
  YRConfigProvider.config({
    theme: {
      primaryColor: color || defaultConfig.primaryColor
    }
  });
};

/**
 * 校验是否有权限
 * @param uri 要校验的权限
 * 暂时写死返回true
 */
export function checkAuth(uri) {
  return checkAuthUtil(uri);
}

export const antdModal = (
  modal: any
  // eslint-disable-next-line function-paren-newline
): { open: boolean; onCancel: () => void; onOk: () => void; afterClose: () => void } => {
  return {
    open: modal.visible,
    onOk: () => modal.hide(),
    onCancel: () => modal.hide(),
    afterClose: () => {
      modal.resolveHide();
      if (!modal.keepMounted) modal.remove();
    }
  };
};

export const setDict = (key: string, value: Array<{ itemKey: string; itemName: string }>) => {
  sessionStorage.setItem(`dict_${key}`, JSON.stringify(value));
};

export const tryCatch: <Result = any, Params extends { [key: string]: any } = { [key: string]: any }>(
  arg: (arg: Params) => Promise<Result>
) => (arg: Params) => Promise<[null, Result] | [unknown]> = (http) => async (params) => {
  try {
    const res = await http(params);
    return [null, res];
  } catch (error) {
    return [error];
  }
};

/**
 * 对象是否为空
 * @param obj
 * @returns
 */
export const isObjectEmpty = (obj: any) => {
  return JSON.stringify(obj) === '{}';
};

/**
 * 从键值对中获取值
 * @param list 键值对数组 key | vlaue
 * @param dictNum key
 * @returns
 */
export const getOptions = (list: any[], dictNum: string) => {
  const value = list.filter((item) => {
    return item.key === dictNum;
  });
  return value[0]?.value;
};

/**
 * 下载文件
 */
export function downloadByUrl(href, title) {
  try {
    const domA = document.createElement('a');
    domA.setAttribute('href', href);
    domA.setAttribute('download', title);
    domA.style.display = 'none';
    // 触发点击
    document.body.appendChild(domA);
    domA.click();
    // 然后移除
    document.body.removeChild(domA);
  } catch (e) {}
}

/**
 * 弹窗控制
 */
export function useModalChange() {
  const [modalMessage, setModalMessage] = useState({
    editType: '',
    editObj: null,
    isShow: false
  });
  function handleShow(editType = '', editObj = null) {
    const message = {
      editType,
      editObj,
      isShow: true
    };
    setModalMessage(message);
  }
  const handleCancel = useCallback((editType = '') => {
    setModalMessage({
      editType,
      editObj: null,
      isShow: false
    });
  }, []);
  return { handleShow, handleCancel, modalMessage };
}
