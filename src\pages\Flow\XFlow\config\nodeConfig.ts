/**
 * @Author: wangyw26123
 * @Description: 自定义节点列表配置
 * @Date: Created in 2022-04-25 22:50:35
 * @Modifed By:
 */

import { nodeTypeNames } from '@/pages/Flow/XFlow/constants';
import CustomeNode from '../components/CustomeNode';
import {
  StartNode,
  EndNode,
  TaskNode,
  OrganNode,
  ParallelGatewayNode,
  ExclusiveGatewayNode,
  InclusionGatewayNode,
  EventBasedGatewayNode,
  SubprocessNode,
  TimerNode,
  SignalCatchNode,
  SignalThrowNode,
  ServiceNode,
  DecisionTablesNode
} from '../NodeType';

const tabKey = ['baseInfo', 'highLevel'];
const initNodeData: any = {};
const size = {
  width: 200,
  height: 60
};

const nodeConfig = [
  {
    title: '节点列表',
    key: 'nodeList',
    nodes: [
      {
        component: CustomeNode,
        popover: '审批流程的开始节点',
        name: nodeTypeNames['StartNoneEvent'],
        label: '开始节点',
        // 以下为自定义属性
        nodeFormData: { ...new StartNode(initNodeData) },
        nodePanel: ['nodeInfo'],
        nodeTabs: ['baseInfo'],
        ...size
      },
      {
        component: CustomeNode,
        popover: '审批流程的结束节点',
        name: nodeTypeNames['EndNoneEvent'],
        label: '结束节点',
        nodeFormData: { ...new EndNode(initNodeData) },
        nodePanel: ['nodeInfo'],
        nodeTabs: ['baseInfo'],
        ...size
      },
      {
        component: CustomeNode,
        popover: '发起人',
        name: nodeTypeNames['Organiser'],
        label: '发起人',
        nodeFormData: { ...new OrganNode(initNodeData) },
        nodePanel: ['nodeInfo', 'formConfig', 'ruleConfig'],
        nodeTabs: tabKey,
        ...size
      },
      {
        component: CustomeNode,
        popover: '任务节点',
        name: nodeTypeNames['UserTask'],
        label: '任务节点',
        nodeFormData: { ...new TaskNode(initNodeData) },
        nodeTabs: tabKey,
        nodePanel: [
          'nodeInfo',
          'approvalChannel',
          'formConfig',
          'timeConfig',
          'dispatchConfig',
          'approverConfig',
          'approverProcessConfig',
          'infoNotice',
          'userNodeConfig',
          'ruleConfig'
        ],
        ...size
      },
      {
        component: CustomeNode,
        popover: '排他网关',
        name: nodeTypeNames['ExclusiveGateway'],
        label: '排他网关',
        nodeFormData: { ...new ExclusiveGatewayNode(initNodeData) },
        nodePanel: ['nodeInfo', 'decisionRule', 'ruleConfig'],
        nodeTabs: tabKey,
        ...size
      },
      {
        component: CustomeNode,
        popover: '并行流程的开始、聚合时的必选节点',
        name: nodeTypeNames['ParallelGateway'],
        label: '并行节点',
        nodeFormData: { ...new ParallelGatewayNode(initNodeData) },
        nodePanel: ['nodeInfo', 'ruleConfig'],
        nodeTabs: tabKey,
        ...size
      },
      {
        component: CustomeNode,
        popover: '决策表',
        name: nodeTypeNames['DecisionTask'],
        label: '决策表',
        nodeFormData: { ...new DecisionTablesNode(initNodeData) },
        nodePanel: ['nodeInfo', 'ruleConfig', 'nodeConfig'],
        nodeTabs: tabKey,
        ...size
      },
      // {
      //   component: CustomeNode,
      //   popover: '并行流程的开始、聚合时的必选节点',
      //   name: nodeTypeNames['DecisionGateway'],
      //   label: '决策网关',
      //   nodeFormData: { ...new DecisionGatewayNode(initNodeData) },
      //   nodePanel: ['nodeInfo'],
      //   nodeTabs: tabKey,
      //   ...size
      // },
      {
        component: CustomeNode,
        popover: '包容网关',
        name: nodeTypeNames['InclusiveGateway'],
        label: '包容网关',
        nodeFormData: { ...new InclusionGatewayNode(initNodeData) },
        nodePanel: ['nodeInfo', 'ruleConfig'],
        nodeTabs: tabKey,
        ...size
      },
      {
        component: CustomeNode,
        popover: '事件网关',
        name: nodeTypeNames['EventGateway'],
        label: '事件网关',
        nodeFormData: { ...new EventBasedGatewayNode(initNodeData) },
        nodePanel: ['nodeInfo', 'ruleConfig'],
        nodeTabs: tabKey,
        ...size
      },
      {
        component: CustomeNode,
        popover: '信号捕获',
        name: nodeTypeNames['CatchSignalEvent'],
        label: '信号捕获',
        nodeFormData: { ...new SignalCatchNode(initNodeData) },
        nodePanel: ['nodeInfo', 'nodeConfig', 'ruleConfig'],
        nodeTabs: tabKey,
        ...size
      },
      {
        component: CustomeNode,
        popover: '信号抛出',
        name: nodeTypeNames['ThrowSignalEvent'],
        label: '信号抛出',
        nodeFormData: { ...new SignalThrowNode(initNodeData) },
        nodePanel: ['nodeInfo', 'nodeConfig', 'ruleConfig'],
        nodeTabs: tabKey,
        ...size
      },
      {
        component: CustomeNode,
        popover: '服务节点',
        name: nodeTypeNames['ServiceTask'],
        label: '服务节点',
        nodeFormData: { ...new ServiceNode(initNodeData) },
        nodePanel: ['nodeInfo', 'nodeConfig', 'ruleConfig'],
        nodeTabs: tabKey,
        ...size
      },
      {
        component: CustomeNode,
        popover: '子流程',
        name: nodeTypeNames['CallActivity'],
        label: '子流程',
        nodeFormData: { ...new SubprocessNode(initNodeData) },
        nodePanel: ['nodeInfo', 'nodeConfig', 'ruleConfig'],
        nodeTabs: tabKey,
        ...size
      },
      {
        component: CustomeNode,
        popover: '定时器',
        name: nodeTypeNames['CatchTimerEvent'],
        label: '定时器',
        nodeFormData: { ...new TimerNode(initNodeData) },
        nodePanel: ['nodeInfo', 'nodeConfig', 'ruleConfig'],
        nodeTabs: tabKey,
        ...size
      }
      // {
      //   component: CustomeNode,
      //   popover: '中断节点',
      //   name: nodeTypeNames['INTERRUPT'],
      //   label: '中断节点',
      //   nodeFormData: { ...new IntermediateThrowNode(initNodeData) },
      //   nodePanel: ['nodeInfo'],
      //   nodeTabs: tabKey,
      //   ...size
      // }
    ]
  }
];

export default nodeConfig;
