import React from 'react';
import { ValueTypeEnum } from '@yr/multi-view-table';
import { FormItemListProps, YRTableProps, YRButton, YRLink, YRModal, YRMessage } from 'yrantd';
import type { PendingBatchAuthInfo, BatchReviewParams } from './types';

// 处理复核操作的通用函数
const handleBatchReviewAction = (
  actionName: string,
  reviewResult: 'PASS' | 'REJECT',
  batchAuthId: string,
  reviewCallback: (params: BatchReviewParams) => void
) => {
  YRModal.confirm({
    title: actionName,
    okText: '确定',
    cancelText: '取消',
    content: `确定要${actionName}此批量授权吗？`,
    onOk: () => {
      reviewCallback({
        batchAuthId,
        reviewResult,
        reviewRemark: actionName === '复核通过' ? '批量授权复核通过' : '批量授权复核退回'
      });
    }
  });
};

// 表单项配置
const formItemList: FormItemListProps[] = [
  {
    placeholder: '批量授权编号',
    key: 'batchAuthNo',
    type: 'input',
    position: 'show'
  },
  {
    placeholder: '授权状态',
    key: 'authStatus',
    type: 'dictSelect',
    position: 'show',
    dictkey: 'AUTH_STATUS'
  }
];

// 表格列定义
const columns: (
  detailCallback: (record: PendingBatchAuthInfo) => void,
  reviewCallback: (params: BatchReviewParams) => void,
  reviewLoading: boolean
) => YRTableProps['columns'] = (
  detailCallback,
  reviewCallback,
  reviewLoading
) => {
  return [
    {
      title: '批量授权编号',
      dataIndex: 'batchAuthNo',
      key: 'batchAuthNo',
      width: 200,
      fixed: 'left',
      ellipsis: true
    },
    {
      title: '导入文件名称',
      dataIndex: 'fileName',
      key: 'fileName',
      width: 200,
      ellipsis: true
    },
    {
      title: '授权状态',
      dataIndex: 'authStatus',
      key: 'authStatus',
      width: 120,
      valueType: ValueTypeEnum.dict,
      dictkey: 'AUTH_STATUS'
    },
    {
      title: '登记人',
      dataIndex: 'operatorName',
      key: 'operatorName',
      width: 120
    },
    {
      title: '登记机构',
      dataIndex: 'ownOrganName',
      key: 'ownOrganName',
      width: 180,
      ellipsis: true
    },
    {
      title: '登记日期',
      dataIndex: 'inputTime',
      key: 'inputTime',
      width: 120,
      valueType: ValueTypeEnum.date
    },
    {
      title: '操作',
      dataIndex: 'operator',
      valueType: ValueTypeEnum.operation,
      fixed: 'right',
      width: 200,
      render: (value, record) => {
        const { batchAuthId } = record as PendingBatchAuthInfo;
        
        return (
          <YRButton.Space size="small">
            <YRLink
              type="primary"
              onClick={() => detailCallback(record as PendingBatchAuthInfo)}
            >
              详情
            </YRLink>
            <YRLink
              type="primary"
              onClick={() => 
                handleBatchReviewAction('复核退回', 'REJECT', batchAuthId, reviewCallback)
              }
            >
              复核退回
            </YRLink>
            <YRLink
              type="primary"
              onClick={() => 
                handleBatchReviewAction('复核通过', 'PASS', batchAuthId, reviewCallback)
              }
            >
              复核通过
            </YRLink>
          </YRButton.Space>
        );
      }
    }
  ];
};

export { columns, formItemList };
