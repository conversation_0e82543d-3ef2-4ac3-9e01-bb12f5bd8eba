/*
 * @Author: 伍晶晶
 * @Description: 日历
 * @Date: 2023-03-20 11:14:48
 * @LastEditors: 伍晶晶
 * @LastEditTime: 2023-03-29 17:07:34
 */
import { useRequest } from 'ahooks';
import moment, { Moment } from 'moment';
import React, { useEffect, useState } from 'react';
import { YRCalendar, YRMessage, YRPopover, YRRadio, YRRow, YRSpace, YRSpin, YRText } from 'yrantd';

import { modifyHolidayInfo, queryHolidayByYearMonth } from '@/services/setting';

import { useAntd } from '../Context';
import { KeyEnum } from '../Interface';

const Calendar = () => {
  const antd = useAntd();

  const [selectedKey] = antd.selectedState;
  const [defaultValue, setDefaultValue] = useState(() => moment(new Date()));

  const { runAsync: modify } = useRequest((p) => modifyHolidayInfo(p).then(), {
    manual: true,
    onSuccess: (res) => {
      if (res.success) {
        YRMessage.success('节假日变更成功');
      }
    }
  });

  const {
    data: holidayList,
    run,
    loading
  } = useRequest((p) => queryHolidayByYearMonth(p).then((res) => (res.success ? res.data : [])), {
    manual: true
  });

  const onPanelChange = (newValue: Moment) => {
    setDefaultValue(newValue);
    const params = {
      year: newValue.year(),
      month: newValue.month() + 1,
      holidayType: selectedKey[KeyEnum.KEY]
    };
    run(params);
  };

  useEffect(() => {
    if (defaultValue && selectedKey[KeyEnum.KEY]) {
      const params = {
        year: defaultValue.year(),
        month: defaultValue.month() + 1,
        holidayType: selectedKey[KeyEnum.KEY]
      };
      run(params);
    }
  }, [selectedKey]);

  const changeHoliday = (e, current: Moment) => {
    const params = {
      year: current.year(),
      month: current.month() + 1,
      day: current.date(),
      holidaySign: e.target.value,
      holidayType: selectedKey[KeyEnum.KEY]
    };
    modify(params).then(() => {
      run(params);
    });
  };

  const dateCellRender = (current: Moment) => {
    const currentDate = moment(current).format('YYYY-MM-DD');
    let filterList = [];
    if (holidayList && holidayList.length > 0) {
      filterList = holidayList.filter(
        (item) =>
          currentDate ===
          `${item.year}-${item.month > 9 ? item.month : `0${item.month}`}-${item.day > 9 ? item.day : `0${item.day}`}`
      );
    }
    const flag = filterList.length > 0;
    const content = (
      <YRSpace>
        <YRText>假期标志</YRText>
        <YRRadio.Group
          name="radiogroup"
          defaultValue={flag ? '001' : '002'}
          onChange={(e) => changeHoliday(e, current)}
        >
          <YRRadio value="001">休假</YRRadio>
          <YRRadio value="002">上班</YRRadio>
        </YRRadio.Group>
      </YRSpace>
    );
    return (
      defaultValue.month() === current.month() && (
        <YRPopover content={content} title={currentDate}>
          <YRRow style={{ height: '100% ' }}>
            <YRText type="success">{flag ? '休' : ''}</YRText>
          </YRRow>
        </YRPopover>
      )
    );
  };

  return (
    <YRSpin spinning={loading}>
      <YRCalendar
        defaultValue={defaultValue}
        fullscreen
        dateCellRender={dateCellRender}
        onPanelChange={onPanelChange}
      />
    </YRSpin>
  );
};

export default Calendar;
