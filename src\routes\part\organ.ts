/**
 * @页面描述: 组织机构 一级菜单
 * @文件名 organ.ts
 * @Path src\routes\part\organ.ts
 * @Date 2023-03-30 14:47:30
 * <AUTHOR>
 */
import { M0105, M010101, M010102, M010103, M010501, M010502, M010503, M010504, M010505, M010506 } from '@permit/organ';
import { lazy } from 'ice';
import { M0102 } from '@permit/permit';
import { M010301, M010302 } from '@permit/role';
import { M0104, M010401, M010402 } from '@permit/user';

const partRoutes = [
  {
    path: '/organ-group',
    name: '组织管理',
    permit: 'M01',
    children: [
      {
        path: '/',
        exact: true,
        redirect: '/organ-group/organ'
      },
      {
        path: '/organ',
        name: '机构管理',
        permit: 'M0101',
        children: [
          {
            path: '/',
            exact: true,
            redirect: '/organ-group/organ/organ-manage'
          },
          {
            path: '/organ-manage',
            name: '机构管理',
            permit: M010101,
            component: lazy(() => import('@/pages/Organ/OrganManage'))
          },
          {
            path: '/financial-organ',
            name: '账务机构映射',
            permit: M010102,
            component: lazy(() => import('@/pages/Organ/FinancialOrganMap'))
          },
          {
            path: '/hr-organ',
            name: 'HR机构映射',
            permit: M010103,
            component: lazy(() => import('@/pages/Organ/HROrganMap'))
          }
        ]
      },
      {
        path: '/permit',
        name: '功能权限',
        permit: M0102,
        component: lazy(() => import('@/pages/Permit'))
      },
      {
        path: '/role',
        name: '角色管理',
        permit: 'M0103',
        children: [
          {
            path: '/',
            exact: true,
            redirect: '/organ-group/role/role-manage'
          },
          {
            path: '/role-manage',
            name: '角色管理',
            permit: M010301,
            component: lazy(() => import('@/pages/Role/RoleManage'))
          },
          {
            path: '/access-role',
            name: '准入角色名单',
            permit: M010302,
            component: lazy(() => import('@/pages/Role/AccessRoleList'))
          }
        ]
      },
      {
        path: '/user',
        name: '用户管理',
        permit: M0104,
        children: [
          {
            path: '/',
            exact: true,
            redirect: '/organ-group/user/personal'
          },
          {
            path: '/personal',
            name: '用户列表',
            component: lazy(() => import('@/pages/User/Personal')),
            permit: M010401
          },
          {
            path: '/counter-employee',
            name: '核心柜员用户',
            component: lazy(() => import('@/pages/User/CounterEmployee')),
            permit: M010402
          }
        ]
      },

      {
        path: '/auth',
        name: '授权管理',
        permit: M0105,
        children: [
          {
            path: '/',
            exact: true,
            redirect: '/organ-group/auth/management'
          },
          {
            path: '/management',
            name: '单笔授权管理',
            component: lazy(() => import('@/pages/Auth/AuthManagement')),
            permit: M010501
          },
          {
            path: '/batch-auth',
            name: '批量授权管理',
            component: lazy(() => import('@/pages/Auth/BatchAuthManagement')),
            permit: M010502
          },
          {
            path: '/pending-single',
            name: '待复核-单笔',
            component: lazy(() => import('@/pages/Auth/PendingSingleAuth')),
            permit: M010503
          },
          {
            path: '/pending-batch',
            name: '待复核-批量',
            component: lazy(() => import('@/pages/Auth/PendingBatchAuth')),
            permit: M010504
          },
          {
            path: '/reviewed-single',
            name: '已复核-单笔',
            component: lazy(() => import('@/pages/Auth/ReviewedSingleAuth')),
            permit: M010505
          },
          {
            path: '/reviewed-batch',
            name: '已复核-批量',
            permit: M010506
          }
        ]
      }
    ]
  }
];
export default partRoutes;
