/**
 * @Author: wangyw26123
 * @Description: 数据权限
 * @Date: Created in 2022-12-20 15:14:58
 * @Modifed By:
 */
import React from 'react';
import { YRForm, YRCard, YRDict } from 'yrantd';

const PermitData = (props) => {
  const { form, roleDetail, mode } = props;
  return (
    <YRCard title="数据权限" id="permitData">
      <YRForm form={form} mode={mode}>
        <YRForm.Row>
          <YRForm.Item
            label="机构数据权限"
            name="filterGroupId"
            column="block"
            initialValue={roleDetail.filterGroupId}
            rules={[{ required: true, message: '请选择机构数据权限' }]}
          >
            <YRDict.RadioGroup dictkey="EnumFilterGroup" placeholder="请选择机构数据权限" />
          </YRForm.Item>
        </YRForm.Row>
      </YRForm>
    </YRCard>
  );
};

export default PermitData;
