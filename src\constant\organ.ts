/**
 * @Author: wangyw26123
 * @Description: 组织管理
 * @Date: Created in 2022-12-20 09:54:26
 * @Modifed By:
 */
import { EnumStatus } from './common';
/**
 * 机构状态
 */
enum EnumOrganStatusColor {
  '001' = EnumStatus.default, // 未启用
  '002' = EnumStatus.success, // 启用
  '003' = EnumStatus.error // 停用
}

/**
 * 枚举机构管理操作类型
 */
enum EnumOperType {
  NOT_ENABLE = '001', // 未启用
  ENABLE = '002', // 启用
  DISABLE = '003', // 停用
  MODIFY = '004', // 修改
  DELETE = '005', // 删除
  VIEW = '006', // 查看
  ADD = '007', // 新增
  COPY = '008', // 复制
  ORDER = '009' // 层级调整
}

export { EnumOrganStatusColor, EnumOperType };
