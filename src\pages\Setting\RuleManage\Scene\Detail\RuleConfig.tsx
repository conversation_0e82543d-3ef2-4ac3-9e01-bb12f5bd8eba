/**
 * 页面描述: 规则配置列表
 * @文件名 RuleConfig.tsx
 * @filePath \src\pages\Setting\RuleManage\Scene\Detail\RuleConfig.tsx
 * @Date 2023-08-17 17:44:28
 * @Email <EMAIL>
 * <AUTHOR>
 */
import React, { useState, useEffect, useMemo, forwardRef, useImperativeHandle } from 'react';
import {
  YRButton,
  YRTable,
  YRTabs,
  YRDict,
  YRSpace,
  YRTooltip,
  YREasyUseModal,
  YRLink,
  YRTreeSelect,
  YRModal,
  YRConfirmBtn,
  YRMessage
} from 'yrantd';
import { Switch, useLocation, useParams } from 'ice';
import { useRequest } from 'ahooks';
import { setDict } from '@/utils/utils';
import { businessStageNames, businessTacheItem, tacheNameAndStage, productOptions } from '../config';
import { splitRuleList } from '../util';
import ChooseRuleModal from '../components/ChooseRuleModal';
import ExemptModal from '../components/ExemptModal';
import SceneModal from '../components/SceneModal';
import { sceneDetail } from '@/services/setting';

const { TabPane } = YRTabs;

setDict('EnumOperateType', [
  { itemKey: '001', itemName: '不检查' },
  { itemKey: '002', itemName: '禁止类' },
  { itemKey: '003', itemName: '提示类' },
  { itemKey: '004', itemName: '确认类' }
]);

setDict('EnumBool', [
  { itemKey: 'Y', itemName: '豁免' },
  { itemKey: 'N', itemName: '非豁免' }
]);

const RuleConfig = forwardRef((props: any, ref) => {
  const { form, activeNode, stageList, setStageList, tacheList, setTacheList } = props;
  const { query } = useLocation() as any;
  const { mode } = useParams() as any;
  const { businessStage, businessTache, sceneId } = query;
  const disabled = mode === 'readPretty';
  const [ruleType, setRuleType] = useState('001'); // 当前规则页
  const [selectedIds, setSelectedIds] = useState([]) as any; // 所选规则(用于批量删除)
  const [genericList, setGenericList] = useState([]); // 通用规则列表
  const [productList, setProductList] = useState([]); // 产品规则列表
  const [exemptList, setExemptList] = useState([]); // 豁免组列表列表

  /** 查询详情接口 */
  const { loading: detailLoading, run: detailRequest } = useRequest(sceneDetail, {
    manual: true,
    onSuccess: (result, params) => {
      const { data } = result as any;
      if (data) {
        const sceneRuleDtoList = [...(data?.sceneRuleDtoList || [])];
        const exemptGroupDtoList = [...(data?.exemptGroupDtoList || [])].map((item) => {
          return {
            ...item,
            exemptId: mode === 'copy' ? '' : item?.exemptId // 复制场景时需清空豁免组编号
          };
        });
        const filterGeneric = sceneRuleDtoList.filter((item) => item?.ruleType === '001') || []; // 筛选通用规则
        const filterProduct = sceneRuleDtoList.filter((item) => item?.ruleType === '002') || []; // 筛选产品规则
        const formatStageList = [...new Set(filterGeneric.map((item) => item?.businessStage))] as any; // 阶段列表
        const formatTacheList = [...new Set(filterGeneric.map((item) => item?.businessTache))] as any; // 环节列表
        const formatGenericList = splitRuleList(filterGeneric);
        const formatProductList = splitRuleList(filterProduct);
        setStageList(formatStageList);
        setTacheList(formatTacheList);
        setGenericList(formatGenericList);
        setProductList(formatProductList);
        setExemptList(exemptGroupDtoList);
        mode !== 'copy' && form.setFieldsValue(data);
      }
    }
  });

  // 将子组件状态传递给父级
  useImperativeHandle(ref, () => {
    return {
      detailLoading,
      genericList,
      productList,
      exemptList
    };
  });

  // 详情查询
  useEffect(() => {
    if (sceneId && mode) {
      detailRequest({ sceneId });
    }
  }, [sceneId, mode]);

  // 新增时根据向导页选择的阶段及环节展示配置列表
  useEffect(() => {
    if (mode === 'add' && businessStage && businessTache) {
      setStageList(businessStage.split(','));
      setTacheList(businessTache.split(','));
    }
  }, [businessStage, businessTache]);

  const rowSelection = {
    type: 'checkBox',
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedIds(selectedRowKeys);
    },
    selectedRowKeys: selectedIds
  };

  // 筛选当前目录选择的阶段
  const getFilterStage = (list) => {
    const { nodeType, key } = activeNode;
    switch (nodeType) {
      case 'all':
        return [...list];
      case 'stage':
        return list.filter((item) => businessStageNames[item] === key);
      case 'tache':
        return list.filter((item) => item === tacheNameAndStage[key].stage);
      default:
        return [];
    }
  };

  // 筛选当前目录选择的阶段
  const getFilterTache = (list) => {
    const { nodeType, key } = activeNode;
    switch (nodeType) {
      case 'all':
        return [...list];
      case 'stage':
        return [...list];
      case 'tache':
        return list.filter((item) => item === key);
      default:
        return [];
    }
  };

  // 阶段及环节列(用于拼接规则列表)
  const tacheColumns = useMemo(() => {
    const defaultColumns = [] as any;
    // 筛选当前目录选择的阶段及环节
    const newStageList = getFilterStage(stageList);
    const newTacheList = getFilterTache(tacheList);
    if (newStageList.length > 0 && newTacheList.length > 0) {
      newStageList.forEach((item) => {
        // 寻找该阶段下的环节
        const childList = newTacheList.filter((v) => {
          const tacheItem = businessTacheItem[item] as any;
          return tacheItem.find((i) => i?.value === v);
        });
        if (childList && childList.length > 0) {
          defaultColumns.push({
            title: businessStageNames[item],
            dataIndex: businessStageNames[item],
            width: 120 * childList.length,
            children: childList.map((key) => {
              return {
                title: tacheNameAndStage[key]?.name,
                dataIndex: key,
                width: 120,
                render: (value, row, index) => {
                  return (
                    <YRDict.Select
                      style={{ width: '100%' }}
                      disabled={disabled}
                      defaultValue={value}
                      dictkey="EnumOperateType"
                      onChange={(val) => {
                        const newList = [...tableDataGroup[ruleType]] as any;
                        const newItem = {
                          ...(newList[index] || {}),
                          [key]: val
                        };
                        newList.splice(index, 1, newItem);
                        setTableDataGroup[ruleType](newList);
                      }}
                    />
                  );
                }
              };
            })
          });
        }
      });
    }
    return defaultColumns;
  }, [ruleType, activeNode, stageList, tacheList, genericList, productList, exemptList, disabled]);

  // 通用规则列表
  const genericColumns = useMemo(() => {
    const defaultColumns = [
      {
        title: '分类',
        dataIndex: 'treeName',
        width: 120,
        render: (value) => {
          return (
            <YRTooltip onlyShow title={value}>
              <div className="ellipsis">{value}</div>
            </YRTooltip>
          );
        }
      },
      {
        title: '探测项',
        dataIndex: 'itemName',
        width: 150,
        render: (value) => {
          return (
            <YRTooltip onlyShow title={value}>
              <div className="ellipsis">{value}</div>
            </YRTooltip>
          );
        }
      },
      ...tacheColumns,
      ...(disabled
        ? []
        : [
          {
            title: '操作',
            dataIndex: 'operate',
            width: 80,
            fixed: 'right',
            render: (value, row, index) => {
              return (
                <YRConfirmBtn msg="确定删除吗？" type="pop" onConfirm={() => deleteItem(index)}>
                  删除
                </YRConfirmBtn>
              );
            }
          }
        ])
    ] as any;
    return defaultColumns;
  }, [tacheColumns, genericList, disabled]);

  // 产品规则列表
  const productColumns = useMemo(() => {
    const defaultColumns = [
      {
        title: '分类',
        dataIndex: 'treeName',
        width: 120,
        render: (value) => {
          return (
            <YRTooltip onlyShow title={value}>
              <div className="ellipsis">{value}</div>
            </YRTooltip>
          );
        }
      },
      {
        title: '适用业务品种',
        dataIndex: 'bizTypeList',
        width: 300,
        render: (value, row, index) => {
          return (
            <YRTreeSelect
              disabled={disabled}
              showSearch
              multiple
              allowClear
              treeCheckable
              maxTagCount={1}
              style={{ width: '100%' }}
              dropdownStyle={{ maxHeight: 800, overflow: 'auto' }}
              placeholder="请选择"
              defaultValue={value ? value.split(',') : []}
              treeData={productOptions}
              filterTreeNode={(input, option) => (option?.label || '').includes(input)}
              onChange={(valueList) => {
                const newList = [...productList] as any;
                const newItem = {
                  ...(newList[index] || {}),
                  bizTypeList: valueList && valueList.length > 0 ? valueList.join(',') : null
                };
                newList.splice(index, 1, newItem);
                setProductList(newList);
              }}
            />
          );
        }
      },
      {
        title: '探测项',
        dataIndex: 'itemName',
        width: 150,
        render: (value) => {
          return (
            <YRTooltip onlyShow title={value}>
              <div className="ellipsis">{value}</div>
            </YRTooltip>
          );
        }
      },
      ...tacheColumns,
      ...(disabled
        ? []
        : [
          {
            title: '操作',
            dataIndex: 'operate',
            width: 80,
            fixed: 'right',
            render: (value, row, index) => {
              return (
                <YRConfirmBtn msg="确定删除吗？" type="pop" onConfirm={() => deleteItem(index)}>
                  删除
                </YRConfirmBtn>
              );
            }
          }
        ])
    ] as any;
    return defaultColumns;
  }, [tacheColumns, productList, disabled]);

  // 豁免组列表
  const exemptColumns = useMemo(() => {
    const defaultColumns = [
      {
        title: '豁免组编号',
        dataIndex: 'exemptId',
        width: 120,
        render: (value) => {
          return (
            <YRTooltip onlyShow title={value}>
              <div className="ellipsis">{value}</div>
            </YRTooltip>
          );
        }
      },
      {
        title: '豁免组名称',
        dataIndex: 'exemptName',
        width: 150,
        render: (value) => {
          return (
            <YRTooltip onlyShow title={value}>
              <div className="ellipsis">{value}</div>
            </YRTooltip>
          );
        }
      },
      {
        title: '操作',
        dataIndex: 'operate',
        width: 80,
        fixed: 'right',
        render: (value, row, index) => {
          return (
            <YRSpace>
              {!disabled && (
                <YRLink
                  type="primary"
                  onClick={() => {
                    YREasyUseModal.show(ExemptModal, {
                      mode,
                      operateType: 'edit',
                      activeNode,
                      defaultData: row,
                      stageList,
                      tacheList,
                      okCallback: (params) => editExemptItem(params, index)
                    });
                  }}
                >
                  编辑
                </YRLink>
              )}
              <YRLink
                type="primary"
                onClick={() => {
                  YREasyUseModal.show(ExemptModal, {
                    mode,
                    operateType: 'detail',
                    activeNode,
                    defaultData: row,
                    stageList,
                    tacheList,
                    okCallback: () => {}
                  });
                }}
              >
                查看
              </YRLink>
              {!disabled && (
                <YRConfirmBtn msg="确定删除吗？" type="pop" onConfirm={() => deleteItem(index)}>
                  删除
                </YRConfirmBtn>
              )}
            </YRSpace>
          );
        }
      }
    ];
    return defaultColumns;
  }, [ruleType, exemptList, stageList, tacheList, disabled]);

  // 表格根据当前规则类型展示的列
  const tableColumnsGroup = {
    '001': genericColumns,
    '002': productColumns,
    '003': exemptColumns
  };

  // 表格根据当前规则类型展示的数据
  const tableDataGroup = {
    '001': genericList,
    '002': productList,
    '003': exemptList
  };

  // 根据当前规则类型执行对应的数据变更
  const setTableDataGroup = {
    '001': setGenericList,
    '002': setProductList,
    '003': setExemptList
  };

  // 新增规则
  const addRuleItem = (selectedItems) => {
    const newList = [...tableDataGroup[ruleType]];
    selectedItems.forEach((item) => {
      const { itemId, itemName, treeName, version, circleFlag, circleType } = item;
      newList.push({
        itemId,
        itemName,
        treeName,
        ruleType,
        version,
        circleFlag: circleFlag || '',
        circleType: circleType || ''
      });
    });
    setTableDataGroup[ruleType](newList);
  };

  // 删除规则
  const deleteItem = (index) => {
    const newList = [...tableDataGroup[ruleType]];
    newList.splice(index, 1);
    setTableDataGroup[ruleType](newList);
  };

  // 批量删除规则
  const batchDeleteItem = () => {
    if (selectedIds.length < 1) {
      YRMessage.warning('请选择删除项');
      return;
    }
    YRModal.confirm({
      title: '请确认是否删除',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        const newList = [...tableDataGroup[ruleType]];
        const filterData = newList.filter((item, index) => {
          return !selectedIds.includes(`${item?.itemId}${index}${ruleType}`);
        });
        setTableDataGroup[ruleType](filterData);
        setSelectedIds([]);
      }
    });
  };

  // 新增豁免组
  const addExemptItem = (params) => {
    const newList = [...tableDataGroup[ruleType]];
    newList.push({ ...params });
    setTableDataGroup[ruleType](newList);
  };

  // 修改豁免组
  const editExemptItem = (params, index) => {
    const newList = [...tableDataGroup[ruleType]];
    newList.splice(index, 1, { ...params });
    setTableDataGroup[ruleType](newList);
  };

  // 更改业务阶段及环节
  const editStageTache = (values) => {
    setStageList(values?.businessStage);
    setTacheList(values?.businessTache);
  };

  const operationRender = (
    <YRSpace>
      {ruleType !== '003' && (
        <>
          <YRButton
            type="primary"
            onClick={() => {
              YREasyUseModal.show(ChooseRuleModal, {
                okCallback: addRuleItem,
                selectedList: ruleType === '002' ? [] : [...tableDataGroup[ruleType]] // 产品规则可选重复规则
              });
            }}
          >
            新增规则
          </YRButton>
          <YRButton type="primary" onClick={batchDeleteItem}>
            批量删除
          </YRButton>
        </>
      )}
      {ruleType === '003' && (
        <YRButton
          type="primary"
          onClick={() => {
            YREasyUseModal.show(ExemptModal, {
              mode,
              operateType: 'add',
              activeNode,
              stageList,
              tacheList,
              okCallback: addExemptItem,
              defaultData: {}
            });
          }}
        >
          新增豁免组
        </YRButton>
      )}
      <YRButton
        type="primary"
        onClick={() => {
          YREasyUseModal.show(SceneModal, {
            okCallback: editStageTache,
            defaultValue: {
              businessStage: stageList,
              businessTache: tacheList
            }
          });
        }}
      >
        编辑阶段
      </YRButton>
    </YRSpace>
  );

  return (
    <>
      <YRTabs
        defaultActiveKey={'001'}
        onChange={(value) => {
          setSelectedIds([]);
          setRuleType(value);
        }}
      >
        <TabPane tab="通用规则" key="001" />
        <TabPane tab="产品规则" key="002" />
        <TabPane tab="豁免规则" key="003" />
      </YRTabs>
      <YRTable
        options={false}
        columns={tableColumnsGroup[ruleType] || []}
        dataSource={tableDataGroup[ruleType] || []}
        rowKey={(row, index) => {
          const onlyKey = row?.itemId + index + ruleType;
          return ruleType === '003' ? index : onlyKey;
        }}
        rowSelection={mode === 'readPretty' || ruleType === '003' ? undefined : rowSelection}
        operationRender={mode === 'readPretty' ? null : operationRender}
        onRow={() => {
          return {
            onClick: () => {}
          };
        }}
      />
    </>
  );
});

export default RuleConfig;
