# 授权列表功能修改说明

## 修改内容

### 1. API接口调整
- **接口地址**: 修改为 `/hsjry/corp/authBaseInfoQuery/queryPage`
- **请求方法**: 改为 POST 方式
- **请求参数**: 调整为新的QueryParams结构

### 2. 请求参数结构更新
```typescript
interface QueryParams {
  pageNum: number;                    // 页码（必填）
  pageSize: number;                   // 每页大小（必填）
  authCatalogRoleId?: string;         // 授权类别角色关联ID
  authStatus?: string;                // 授权状态
  pauseDeadlineDate?: string;         // 暂停止日（支持范围查询）
  transferAuthFlag?: string;          // 转授权标识
  bizLine?: string;                   // 业务条线
  roleId?: string;                    // 角色ID
  authCategory?: string;              // 授权类别
  orgId?: string;                     // 机构ID
}
```

### 3. 响应数据结构处理
- 处理新的API响应格式：`{ rpcResult: string, data: PageResponse }`
- 支持分页数据：`{ pageNum, pageSize, total, pages, list }`

### 4. 分页功能实现
- 添加完整的分页控制
- 支持页码切换和每页条数设置
- 显示总数和当前页范围
- 分页变化时自动重新查询

### 5. 查询条件增强
- **授权状态**: 支持状态筛选
- **业务条线**: 支持业务条线筛选
- **暂停止日**: 支持日期范围查询
- **转授权标识**: 支持转授权/非转授权筛选

### 6. 表格列优化
- 修正登记人字段显示
- 添加转授权标识列
- 添加业务条线列
- 优化数据展示格式

### 7. 交互体验改进
- 切换机构/角色/业务条线时自动重置分页
- 搜索时重置到第一页
- 增加错误提示和加载状态
- 保持与现有页面风格一致

## 文件修改列表

1. **src/services/auth.ts**: 修改API调用方式
2. **src/pages/Auth/AuthManagement/index.tsx**: 主页面逻辑更新
3. **src/pages/Auth/AuthManagement/useIndex.tsx**: 表格列和表单配置更新
4. **src/pages/Auth/AuthManagement/types.ts**: 新增类型定义文件

## 技术要点

- 使用TypeScript严格类型检查
- 遵循项目编码规范
- 使用yrantd组件库
- 支持权限控制
- 错误处理和用户提示

## 注意事项

1. 确保后端API `/hsjry/corp/authBaseInfoQuery/queryPage` 已实现
2. 分页参数 `pageNum` 从1开始计数
3. 日期格式统一使用 `yyyy-MM-dd`
4. 转授权标识：1-转授权，0-非转授权
5. 业务条线使用字典值进行显示 