/**
 * 组织机构
 */
import { YRLoanPermit } from 'yr-loan-antd';
import packageInfo from '../package.json';
import {
  addOrgan,
  addOrgRelateList,
  changeOrganLevelApprove,
  changeOrganStatus,
  delOrgan,
  delOrgRelate,
  modifyOrgan,
  queryOrganInfoByOrganId,
  queryOrganRelationPage,
  queryUserOrganTree,
  updateOrgRelateList,
  queryHrOrganList
} from '@/services/setting';
import {
  addAuthCategoryRoleInfo,
  addOrgAuthCategoryRoleInfo,
  delAuthCategoryRoleInfo,
  queryAuthRoleList,
  queryFirstLvlOrgList,
  queryRole,
  queryAuthInfoRuleList,
  addBaseAuthInfo,
  queryAuthInfoByAuthNo,
  queryDimensionLoadByAuthCode,
  delAuthInfo,
  loseEffectAuthInfo,
  submitAuthRuleInfo,
  pauseAuthInfo,
  restartAuthInfo,
  copyAuthInfo,
  queryApproveAuthInfo,
  approveRejectAuthRuleInfo,
  approvePassAuthRuleInfo,
  delOrgAuthCategoryRoleInfo,
  updateBaseAuthInfo,
  addTranferAuthInfo,
  queryTransferAuthInfoRuleList,
  queryTransferAuthRoleList
} from '@/services/auth';

const { buildPermit, EnumPermitType } = YRLoanPermit;

/**
 * 机构管理
 */
const M010101 = buildPermit('M010101', packageInfo, {
  interfaces: {
    queryUserOrganTree,
    queryHrOrganList,
    changeOrganStatus,
    addOrgan
  },
  E01: {
    name: '机构新增',
    path: '/hsjry/guard/base/IOrgan/addOrgan',
    type: EnumPermitType.Element
  },
  E02: {
    name: '机构修改',
    path: '/hsjry/guard/base/IOrgan/modifyOrgan',
    type: EnumPermitType.Element,
    interfaces: {
      modifyOrgan
    }
  },
  E03: {
    name: '机构删除',
    path: '/hsjry/guard/base/IOrgan/delOrgan',
    type: EnumPermitType.Element,
    interfaces: {
      delOrgan
    }
  },
  E04: {
    name: '机构启用',
    path: '/hsjry/guard/base/IOrgan/changeOrganStatus/start',
    type: EnumPermitType.Element
  },
  E05: {
    name: '机构停用',
    path: '/hsjry/guard/base/IOrgan/changeOrganStatus/disabled',
    type: EnumPermitType.Element
  },
  E06: {
    name: '机构恢复',
    path: '/hsjry/guard/base/IOrgan/changeOrganStatus/restore',
    type: EnumPermitType.Element
  },
  E07: {
    name: '调整层级',
    path: '/hsjry/guard/base/changeOrganLevelApprove',
    type: EnumPermitType.Element,
    interfaces: {
      changeOrganLevelApprove
    }
  },
  E08: {
    name: '复制现有机构',
    path: '/hsjry/guard/base/IOrgan/copyOrgan',
    type: EnumPermitType.Element
  },
  E09: {
    name: '新增子机构',
    path: '/hsjry/guard/base/IOrgan/addSubOrgan',
    type: EnumPermitType.Element
  },
  E10: {
    name: '机构详情',
    path: '/hsjry/guard/base/IOrganQuery/queryOrganInfoByOrganId',
    type: EnumPermitType.Element,
    interfaces: {
      queryOrganInfoByOrganId
    }
  },
  E11: {
    name: '机构导出',
    path: '/hsjry/guard/base/IOrganQuery/queryOrganInfoByOrganId',
    type: EnumPermitType.Element,
    interfaces: {
      queryOrganInfoByOrganId
    }
  }
});

/**
 * 账务机构映射
 */
const M010102 = buildPermit('M010102', packageInfo, {
  interfaces: { queryOrganRelationPage },
  E01: {
    name: '新增账务机构映射',
    path: '/hsjry/guard/base/IOrganAcctRelate/addOrgRelateList',
    type: EnumPermitType.Element,
    interfaces: {
      addOrgRelateList
    }
  },
  E02: {
    name: '修改账务机构映射',
    path: '/hsjry/guard/base/IOrganAcctRelate/updateOrgRelateList',
    type: EnumPermitType.Element,
    interfaces: {
      updateOrgRelateList
    }
  },
  E03: {
    name: '删除账务机构映射',
    path: '/hsjry/guard/base/IOrganAcctRelate/delOrgRelate',
    type: EnumPermitType.Element,
    interfaces: {
      delOrgRelate
    }
  }
});

/**
 * HR机构映射
 */
const M010103 = buildPermit('M010103', packageInfo, {
  interfaces: {}
});

/**
 * 授权管理
 */
const M0105 = buildPermit('M0105', packageInfo, {
  interfaces: {
    queryFirstLvlOrgList,
    queryRole,
    addOrgAuthCategoryRoleInfo,
    queryAuthRoleList,
    addAuthCategoryRoleInfo,
    delAuthCategoryRoleInfo,
    queryAuthInfoRuleList,
    addBaseAuthInfo,
    queryAuthInfoByAuthNo,
    queryDimensionLoadByAuthCode,
    delAuthInfo,
    loseEffectAuthInfo,
    submitAuthRuleInfo,
    pauseAuthInfo,
    restartAuthInfo,
    copyAuthInfo,
    queryApproveAuthInfo,
    approveRejectAuthRuleInfo,
    approvePassAuthRuleInfo,
    delOrgAuthCategoryRoleInfo,
    updateBaseAuthInfo,
    addTranferAuthInfo,

    queryTransferAuthRoleList,
    queryTransferAuthInfoRuleList
  }
});

/** 单笔授权管理 */
const M010501 = buildPermit('M010501', packageInfo, {
  interfaces: {}
});

/** 批量授权管理 */
const M010502 = buildPermit('M010502', packageInfo, {
  interfaces: {}
});

/** 待复核-单笔 */
const M010503 = buildPermit('M010503', packageInfo, {
  interfaces: {}
});

/** 待复核-批量 */
const M010504 = buildPermit('M010504', packageInfo, {
  interfaces: {}
});

/** 已复核-单笔 */
const M010505 = buildPermit('M010505', packageInfo, {
  interfaces: {}
});

/** 已复核-批量 */
const M010506 = buildPermit('M010506', packageInfo, {
  interfaces: {}
});

export { M010101, M010102, M010103, M0105, M010501, M010502, M010503, M010504, M010505, M010506 };
