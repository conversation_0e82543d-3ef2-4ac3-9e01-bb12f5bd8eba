/**
 * @Author: wangyw26123
 * @Description: 画布属性面板tabs集合
 * @Date: Created in 2022-04-25 16:18:51
 * @Modifed By:
 */

import React, { useEffect, useState } from 'react';
import { YRTabs, YRSpin } from 'yrantd';

import XflowInstance from '@/pages/Flow/XFlow/XflowInstance';
import GlobalConfigIndex from './GlobalConfig/GlobalConfigIndex';
import OverviewIndex from './Overview/OverviewIndex';

const { TabPane } = YRTabs;

const style = {
  height: 'calc(100vh - 200px)',
  overflow: 'auto'
};

const FormIndex = (props) => {
  const [loading, setLoading] = useState(false);
  const [initLoad, setInitLoad] = useState(() => XflowInstance.initLoad);

  useEffect(() => {
    // 使用开关属性，解决页面首次加载无法获取到节点数据问题
    if (initLoad) {
      setLoading(true);
      const timer = setTimeout(() => {
        XflowInstance.initLoad = false;
        setInitLoad(false);
        setLoading(false);
        clearTimeout(timer);
      }, 600);
    }
  }, [initLoad]);

  return (
    <YRSpin spinning={loading}>
      <YRTabs size="small" defaultActiveKey="overviewIndex">
        {/* 设置 forceRender 隐藏时依然渲染DOM确保表单被初始化 */}
        <TabPane forceRender tab="概览" key="overviewIndex">
          <div style={style}>
            <OverviewIndex {...props} />
          </div>
        </TabPane>
        <TabPane forceRender tab="全局" key="globalConfigIndex" style={{ margin: '-16px 0 0 0' }}>
          {/* <div style={style}>
            <GlobalConfigIndex {...props} />
          </div> */}
          <div>
            <GlobalConfigIndex {...props} />
          </div>
        </TabPane>
      </YRTabs>
    </YRSpin>
  );
};

export default FormIndex;
