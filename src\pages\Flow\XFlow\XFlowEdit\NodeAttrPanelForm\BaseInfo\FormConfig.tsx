/*
 * @Author: 陈昭阳
 * @Description:
 * @Date: 2023-01-04 16:19:44
 * @LastEditors: liaokt
 * @LastEditTime: 2025-02-28 09:36:36
 */
/**
 * @Author: wangyw26123
 * @Description: 高级-表单配置
 * @Date: Created in 2022-04-27 14:08:32
 * @Modifed By:
 */

import { useDict } from '@yr/util';
import React, { useEffect, useState } from 'react';
import { YRSelect, YRForm, YRAutoComplete, YRDict, YRRadio, YRCheckbox } from 'yrantd';

const FormItem = YRForm.Item;
const { Option } = YRSelect;

const radioStyle = {
  display: 'block',
  height: '30px',
  lineHeight: '30px'
};

const FormConfig = (props) => {
  const {
    globalDisabled,
    targetData: { nodeFormData }
  } = props;

  useDict(['APPROVE_CONCLUSION']);

  return (
    <>
      <FormItem
        label="表单信息"
        name={['properties', 'formUrl']}
        initialValue={nodeFormData?.properties?.formUrl}
        // rules={[
        //   {
        //     required: true,
        //     whitespace: true,
        //     message: '请选择表单信息'
        //   }
        // ]}
      >
        <YRAutoComplete disabled={globalDisabled} style={{ width: '100%' }} placeholder="请选择">
          <Option value={1}>测试1</Option>
          <Option value={2}>测试2</Option>
          <Option value={3}>测试3</Option>
        </YRAutoComplete>
      </FormItem>
      <FormItem
        label="岗位意见"
        name={['properties', 'opinion']}
        initialValue={nodeFormData?.properties?.opinion}
        getValueFromEvent={(value) => value.join(',')}
        // eslint-disable-next-line no-nested-ternary
        getValueProps={(value) => ({ value: Array.isArray(value) ? value : value ? value.split(',') : [] })}
      >
        {/* <YRSelect mode="multiple" disabled={globalDisabled} style={{ width: '100%' }} placeholder="请选择">
          <Option value={'001'}>同意</Option>
          <Option value={'002'}>否决</Option>
          <Option value={'003'}>续议</Option>
          <Option value={'004'}>有条件同意</Option>
          <Option value={'005'}>弃权</Option>
        </YRSelect> */}
        <YRCheckbox.Group disabled={globalDisabled}>
          <YRCheckbox value="001" style={{ width: '100%', marginLeft: '8px' }}>
            同意
          </YRCheckbox>
          <YRCheckbox value="002" style={{ width: '100%' }}>
            否决
          </YRCheckbox>
          <YRCheckbox value="003" style={{ width: '100%' }}>
            续议
          </YRCheckbox>
          <YRCheckbox value="004" style={{ width: '100%' }}>
            有条件同意
          </YRCheckbox>
          <YRCheckbox value="005" style={{ width: '100%' }}>
            弃权
          </YRCheckbox>
        </YRCheckbox.Group>
      </FormItem>
    </>
  );
};

export default FormConfig;
