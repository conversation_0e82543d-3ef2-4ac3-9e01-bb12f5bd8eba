/**
 * @Author: wangyw26123
 * @Description: 节点数据
 * @Date: Created in 2022-04-29 16:25:30
 * @Modifed By:
 */

import { NsGraph } from '@yr/xflow';

export enum AppointResourceType {
  INSIDE = '002', // 内部
  EXTERNAL = '001' // 外部
}

export enum listenersEventType {
  START = 'start',
  END = 'end'
}

// 节点类型
export enum NodeType {
  // 画布
  PROCESS = 'process',
  // 线条节点
  SEQUENCE_FLOW = 'SequenceFlow',
  // 中断节点
  INTERMEDIATE_THROW_EVENT = 'intermediateThrowEvent',

  // 开始节点
  START_EVENT = 'StartNoneEvent',
  // 结束节点
  END_EVENT = 'EndNoneEvent',
  // 任务节点
  USER_TASK = 'UserTask',
  // 发起人
  ORGAN_ISER = 'Organiser',
  // 排他网关
  EXCLUSIVE_GATEWAY = 'ExclusiveGateway',
  // 并行网关节点
  PARALLEL_GATEWAY = 'ParallelGateway',
  // 包含网关
  INCLUSIVE_GATEWAY = 'InclusiveGateway',
  // 事件网关
  EVENT_GATEWAY = 'EventGateway',
  // 信号发出节点
  THROWSINGAL_EVENT = 'ThrowSignalEvent',
  // 信号捕获节点
  CATCHSIGNAL_EVENT = 'CatchSignalEvent',
  // 子流程
  CAll_ACTIVITY = 'CallActivity',
  // 定时器
  CATCHTIMER_EVENT = 'CatchTimerEvent',
  // 服务节点
  SERVICE_TASK = 'ServiceTask',
  // 决策表
  DECISION_TABLES = 'DecisionTask'
}

/**
 * 节点扩展属性
 */
export interface PropertyItem {
  /**
   * 扩展节点
   */
  name: string;

  /**
   * 扩展节点值
   */
  value: string;

  /**
   * extensionProperty
   */
  nodeType?: string;
}

/**
 * 线条输入
 */
export interface InsItem {
  /**
   * 输入线条值
   */
  value: string;
  id?: string;
  name?: string;
  displayName?: string;
  nodeType?: string;
}

/**
 * 线条输出
 */
export interface OutsItem {
  /**
   * 输出线条编号
   */
  value: string;
  id?: string;
  name?: string;
  displayName?: string;
  nodeType?: string;
}

export interface NodeListener {
  /**
   * 事件类型
   */
  event: listenersEventType;

  /**
   * 监听器配置值
   */
  clazz: string;

  /**
   * 监听器类型
   */
  type: string;
}

/**
 * 基础节点大类，包含所有属性
 */
export interface BaseNode {
  /**
   * type
   */
  type: string;

  /**
   * instanceUrl
   */
  instanceUrl: string;

  /**
   * 子节点
   */
  children: [];

  /**
   * 节点属性
   */
  properties: PropertyItem[];

  /**
   * 流程版本
   */
  version: string;

  /**
   * 优先级
   */
  sort: string;

  /**
   * 流程参数库Id
   */
  paramLibraryId: string[];

  /**
   * 流程参数库名称
   */
  paramLibraryName: string;

  /**
   * 流程说明
   */
  remark: string;

  /**
   * 流程摘要
   */
  digest: string;

  /**
   * 流转配置开关
   */
  transferSwitch: 'Y' | 'N';

  /**
   * json字符串
   */
  transferList: string;

  /**
   * 场景名称
   */
  sceneName: string;

  /**
   * 触发操作
   */
  triggerOperate: string;

  /**
   * 流转节点
   */
  transferNode: string;

  /**
   * 流转条件
   */
  transferCondition: string;

  /**
   * 流转类型
   */
  transferType: string;

  /**
   * 转向节点
   */
  transferToNode: string;

  /**
   * 互斥组配置开关
   */
  differentSwitch: 'Y' | 'N';

  /**
   * 互斥组配置
   */
  differentConfig: { differentId: string; differentNode: string }[];

  /**
   * 相同组配置开关
   */
  sameTeamSwitch: 'Y' | 'N';

  /**
   * 相同组配置
   */
  sameTeamConfig: { sameTeamId: string; sameTeamNode: string }[];

  /**
   * 审批结束通知拦截器
   */
  endApproveInterceptor: string;

  /**
   * 业务所属变更通知拦截器
   */
  businessManagerInterceptor: 'Y' | 'N';

  /**
   * 业务变更通知开关
   */
  businessChangeSwitch: 'Y' | 'N';

  /**
   * 业务变更通知拦截器
   */
  businessChangeInterceptor: string;

  /**
   * 流程编号/节点编号/线编号
   */
  flowNumber?: string;

  id: string;

  /**
   * 流程名称/节点名称/线名称
   */
  flowName?: string;

  name: string;

  /**
   * 流程展示名称/线展示名称
   */
  displayName: string;

  /**
   * process
   */
  nodeType: NodeType;

  /**
   * 节点监听器
   */
  listeners: NodeListener[];

  /**
   * 用户列表
   */
  users: { name: string; id: string }[];

  /**
   * 角色列表
   */
  roles: { name: string; id: string }[];

  /**
   * 委派类型 字典AssignType: 1-用户 2-角色
   */
  assignType: string;

  /**
   * 审批类型 2 或签  1 会签
   */
  performType: string;

  /**
   * 派件模式 auto 自动派单 human 人工领单  assignTake 审批指派
   */
  assignMode: string;

  /**
   * 领取时效
   */
  takeAging: string;

  /**
   * 审批页面
   */
  approvalPage: string;

  /**
   * 审批功能
   */
  approvalButton: string[];

  /**
   * 退回模式 oldMode 原操作员 newMode 重新派件
   */
  sendMode: string;

  /**
   * 规则自定义编号
   */
  appointInfoId: string;

  /**
   * 规则自定义编号
   */
  appointValue: string;

  /**
   * 派件类别
   */
  appointType: string;

  /**
   * 审批渠道配置
   */
  approveChannel: string;

  /**
   * 批复信息
   */
  approveInfo: string;

  /**
   * 分配超时时效
   */
  allotTimeOut: string;

  /**
   * 分配超时处理
   */
  allotOverTimeDeal: string;

  /**
   * 标准处理工时
   */
  normDealTime: string;

  /**
   * 处理超时时效
   */
  normDealOverTime: string;

  /**
   * 处理超时处理
   */
  normDealWay: string;

  /**
   * 滞留预警时间
   */
  retentionTime: string;

  /**
   * 滞留超时时效
   */
  retentionDealOverTime: string;

  /**
   * 委派源值
   */
  appointResourceResult: string;

  /**
   * 委派源类型 非角色/用户 时为外部
   */
  appointResource: AppointResourceType;

  /**
   * 匹配规则
   */
  matchingRule: string;

  /**
   * 业务所属人开关
   */
  businessSwitch: 'Y' | 'N';

  /**
   * 会签人数下限
   */
  counterSignMin: string;

  /**
   * 会签人数上限
   */
  counterSignMax: string;

  /**
   * 会签结果
   */
  counterSignResult: 'Y' | 'N';

  /**
   * 决策标准
   */
  design: string;

  /**
   * 决策规则
   */
  designRule: string;

  /**
   * 决策时间
   */
  designTime: string;

  /**
   * 比例
   */
  rate: string;

  /**
   * 批复权限配置
   */
  approveAuthorityConfig: string;

  /**
   * 任务池消息提醒
   */
  poolMessageNotice: string;

  /**
   * 派件消息提醒
   */
  assignMessageNotice: string;

  /**
   * 审批权限配置开关
   */
  approveAuthoritySwitch: 'Y' | 'N';

  /**
   * 审批节点
   */
  approveNodeList: string;

  /**
   * 审批权限类型
   */
  approveAuthType: string[];

  /**
   * 自定义执行器编号
   */
  customizeActuatorId: string;

  /**
   * 终审节点集合
   */
  endApproveNodeList: string;

  /**
   * 决策表达式
   */
  expr: string;

  /**
   * def
   */
  def: string;

  /**
   * 互斥组编号
   */
  differentId: string;

  /**
   * 互斥节点编号
   */
  differentNode: string;

  /**
   * 相同组编号
   */
  sameTeamId: string;

  /**
   * 相同节点编号
   */
  sameTeamNode: string;

  /**
   * 详情页面
   */
  approveDetail: string;

  /**
   * 输入节点编号
   */
  sourceRef: string;

  /**
   * 输出节点编号
   */
  targetRef: string;

  /**
   * 线条表达式
   */
  conditionExpress: string;
  assignee: string;

  /**
   * 筛选方
   */
  screeningParty: string;

  /**
   * 标准处理工时 单位
   */
  normDealTimeUnit: string;

  /**
   * 流程线类型
   */
  lineType: string;
  /**
   * 决策规则
   */
  decisionRule: [];

  /**
   * 流程基本信息
   */
  processBaseInfo: any;

  /**
   * 审批人分配
   */
  sendBackMode: 'oldMode' | 'newMode';

  /**
   * 信号节点信息
   */
  signaldefinitions: [];

  /**
   * 互斥组信息
   */
  exclusionGroups: [];

  /**
   * 相同组信息
   */
  includeGroups: [];

  /**
   * 服务节点执行程序
   */
  servicetaskclass: string | string[];

  /**
   * 服务节点参数
   */
  params: [];

  /**
   *
   */
  defaultFlow: string;
}

/**
 * 画布初始数据
 */
export interface State {
  isDisabled: boolean;
  btnLoading: boolean;
  pageLoading: boolean;

  modelName: string;
  modelNumber: string;
  id: string;
  modelDesc: string;
  sceneType: string;
  status: string;
  updateTime: string;
  version: string;
  modelResource: string;
  graphData: NsGraph.IGraphData;
}
