/*
 * @Author: liaokt
 * @Description:
 * @Date: 2024-10-16 13:54:00
 * @LastEditors: liaokt
 * @LastEditTime: 2024-10-22 16:25:45
 */
/*
 * @Author: liaokt
 * @Description: 新增机构树
 * @Date: 2024-02-27 16:10:32
 * @LastEditors: liaokt
 * @LastEditTime: 2024-10-22 14:03:42
 */
import { useRequest } from 'ahooks';
import { YRDict, YREasyUseModal, YRForm, YRMessage, YRModal, YRTreeSelect } from 'yrantd';
import { M0105 } from '@permit/organ';
import React, { useState, useEffect } from 'react';
// import RoleAddModal from '../../RoleAddModal/index';

const AddOrganTree = (props: { okCallback: () => void }) => {
  const { okCallback } = props;
  const { queryFirstLvlOrgList, queryRole, addOrgAuthCategoryRoleInfo } = M0105.interfaces;

  const [form] = YRForm.useForm();
  const [organData, setOrganData] = useState([] as any[]);
  const [roleData, setRoleData] = useState([] as any[]);
  const [currentOrgName, setCurrentOrgName] = useState('');

  const orgAuthFlag = YRForm.useWatch('orgAuthFlag', form) || false;

  const modal = YREasyUseModal.useModal();

  // 处理树结构，确保没有循环引用
  const processTreeData = (data) => {
    if (!data || !Array.isArray(data)) return [];
    
    // 使用新对象来避免引用问题
    return data.map(item => {
      const newItem = { ...item };
      if (newItem.children && Array.isArray(newItem.children)) {
        newItem.children = processTreeData(newItem.children);
      }
      return newItem;
    });
  };

  const { loading: queryOrganTreeLoading } = useRequest(queryFirstLvlOrgList, {
    onSuccess: (res: any) => {
      if (res?.errorMessage === null) {
        const { data } = res;
        // 处理树数据以避免循环引用
        const processedData = data ? [processTreeData([data])[0]] : [];
        setOrganData(processedData);
      }
    },
    defaultParams: [
      {
        queryAuthFlag: '0'
      }
    ]
  });

  const { loading: queryRoleLoading, run: queryRoleRequest } = useRequest(queryRole, {
    manual: true,
    onSuccess: (res: any) => {
      if (res?.errorMessage === null) {
        const { data } = res;
        // 处理角色树数据以避免循环引用
        const processedRoleData = processTreeData(data || []);
        setRoleData(processedRoleData);
      }
    }
  });

  const { loading: submitLoading, run: submitRequest } = useRequest(addOrgAuthCategoryRoleInfo, {
    manual: true,
    onSuccess: (res: any) => {
      if (res?.errorMessage === null) {
        YRMessage.success('新增成功', 0.5, () => {
          modal.remove();
          okCallback();
        });
      }
    }
  });

  //   const getRole = ({ rows }) => {
  //     const { roleId, roleName } = rows;
  //     form.setFieldsValue({ roleName });
  //     setAddParams({
  //       roleId
  //     });
  //   };

  // 提交事件
  const onSubmit = () => {
    form.validateFields().then((values: any) => {
      if (orgAuthFlag === '1') {
        delete values.roleNo;
      }
      const params = { ...values, orgName: currentOrgName };
      submitRequest({ ...params });
    });
  };

  // 组件卸载时清理状态，避免潜在的内存泄漏
  useEffect(() => {
    return () => {
      // 清理函数
    };
  }, []);

  return (
    <YRModal
      title={'新增授权类型'}
      open={modal.visible}
      onCancel={modal.hide}
      afterClose={modal.remove}
      confirmLoading={submitLoading}
      okText="确定"
      onOk={onSubmit}
      destroyOnClose
    >
      <YRForm mode={'add'} form={form}>
        <YRForm.Row column={2}>
          <YRForm.Item label="机构" name="orgId" required>
            <YRTreeSelect
              loading={queryOrganTreeLoading}
              style={{ width: '100%' }}
              dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
              treeData={organData}
              fieldNames={{ label: 'orgName', value: 'orgId' }}
              onSelect={(_, node) => {
                const { orgLvl, orgId, orgName } = node as { orgId: string; orgLvl: never; orgName: string };
                setCurrentOrgName(orgName);
                queryRoleRequest({ orgId, orgLvlList: [orgLvl] });
              }}
              placeholder="请选择机构"
              treeDefaultExpandAll
              allowClear
            />
          </YRForm.Item>
          <YRForm.Item label="授权方式" name="orgAuthFlag" required>
            <YRDict.RadioGroup dictkey="AUTH_METHOD" />
          </YRForm.Item>
          {orgAuthFlag === '0' && (
            <YRForm.Item label="角色" name="roleIdList" required>
              {/* <YRInput
              placeholder="请选择角色"
              onClick={() => {
                YREasyUseModal.show(RoleAddModal, { handleSubmit: getRole });
              }}
            />
             */}
              <YRTreeSelect
                multiple
                loading={queryRoleLoading}
                style={{ width: '100%' }}
                dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                treeData={roleData}
                fieldNames={{ label: 'roleName', value: 'roleNo' }}
                placeholder="请选择角色"
                treeDefaultExpandAll
                allowClear
              />
            </YRForm.Item>
          )}
          <YRForm.Item label="授权类型" name="authCategory" required>
            <YRDict.Select dictkey="AUTH_CATEGORY" placeholder="请选择授权类型" />
          </YRForm.Item>
        </YRForm.Row>
      </YRForm>
    </YRModal>
  );
};

export default YREasyUseModal.create(AddOrganTree);
