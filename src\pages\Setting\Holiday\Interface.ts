/*
 * @Author: 伍晶晶
 * @Description: 假节日类型定义
 * @Date: 2023-03-27 19:00:01
 * @LastEditors: 伍晶晶
 * @LastEditTime: 2023-03-27 20:16:05
 */

export interface IData {
  createTime: string;
  day: string;
  holidayType: string;
  month: string;
  remark: string;
  tenantId: string;
  updateTime: string;
  year: string;
}

export interface TreeNode {
  holidayType: string;
  holidayName?: string;
  operatorId?: string;
  operatorName?: string;
  updateTime?: string;
}

export enum KeyEnum {
  KEY = 'holidayType',
  LABEL = 'holidayName'
}

export const DefaultNode = { holidayType: 'all', holidayName: '全部' };
