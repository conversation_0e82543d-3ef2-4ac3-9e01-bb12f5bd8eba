import React, { useCallback } from 'react';
import { YRAvatar, YRMenu, YRSpin } from 'yrantd';
import { LogoutOutlined } from '@ant-design/icons';
import store from '@/store';
import HeaderDropdown from '../HeaderDropdown';
import styles from './index.module.less';

const AvatarDropdown = () => {
  const [, loginDispatchers] = store.useModel('login');
  const currentUser = window.g_userInfo ? window.g_userInfo : {};
  const { logout } = loginDispatchers;
  const { operatorName } = currentUser;

  const onMenuClick = useCallback((event) => {
    const { key } = event;
    if (key === 'logout') {
      logout();
    }
  }, []);

  const loading = (
    <span className={`${styles.action} ${styles.account}`}>
      <YRSpin
        size="small"
        style={{
          marginLeft: 8,
          marginRight: 8
        }}
      />
    </span>
  );

  if (!operatorName) {
    return loading;
  }

  const items = [{ label: '退出登录', key: 'logout', icon: <LogoutOutlined /> }];

  return (
    <HeaderDropdown overlay={<YRMenu className={styles.menu} selectedKeys={[]} items={items} onClick={onMenuClick} />}>
      <span className={`${styles.action} ${styles.account}`}>
        <YRAvatar size="small" className={styles.avatar} src="/avatar.png" alt="avatar" />
        <span className={`${styles.name} anticon`}>{operatorName}</span>
      </span>
    </HeaderDropdown>
  );
};

export default AvatarDropdown;
