/**
 * @页面描述: 柜员用户机构
 * @文件名 Index.tsx
 * @Path src\pages\User\CounterEmployee\index.tsx
 * @Date 2023-03-29 15:16:32
 * <AUTHOR>
 */

import React from 'react';
import { YREmpty, YRItem, YRSpace, YRSpin, YRTree } from 'yrantd';

import { useAntd } from '../Context';

import { TreeDataNode } from 'yrantd/lib/yr-tree';
import { DefaultNode, KeyEnum, TreeNode } from '../Interface';

const Index = () => {
  const antd = useAntd();
  const [selectedKey, setSelectedKey] = antd.selectedState;
  const { data: list } = antd.antdTreeProps;

  return (
    <YRSpace direction="vertical" block>
      <YRSpin spinning={antd.antdTreeProps.loading}>
        {list?.length ? (
          <YRTree
            blockNode
            fieldNames={{ title: KeyEnum.LABEL, key: KeyEnum.KEY, children: 'children' }}
            titleRender={(nodeData: TreeNode & TreeDataNode) => {
              return (
                <YRItem
                  key={nodeData[KeyEnum.KEY]}
                  title={`${nodeData[KeyEnum.LABEL]}`}
                  count={nodeData.children?.length}
                  countTitle="子机构数量"
                />
              );
            }}
            onSelect={(_, info) => {
              setSelectedKey(info.node);
            }}
            height={700}
            treeData={list as any}
            defaultExpandedKeys={[DefaultNode[KeyEnum['KEY']]]}
            selectedKeys={[selectedKey[KeyEnum.KEY]!]}
          />
        ) : (
          <YREmpty />
        )}
      </YRSpin>
    </YRSpace>
  );
};

export default Index;
