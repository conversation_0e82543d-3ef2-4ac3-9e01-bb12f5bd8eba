/**
 * @Author: 刘文强
 * @Description: 用户详情
 * @Date: Created in
 * @Modifed By:
 */

import React, { useEffect, useState } from 'react';
import {
  YRButton,
  YRClassificationLayout,
  YRDatePicker,
  YRDict,
  YRFlexPageLayout,
  YRForm,
  YRInput,
  YRSelect,
  YRSpace,
  YRTable,
  YRTag,
  YRTreeSelect
} from 'yrantd';
import moment from 'moment';
import { countTableWidth } from '@yr/util';
import { queryUserOrganTree } from '@/services/setting';
import queryAcctOrganTreeJson from '@/pages/Organ/mock/queryAcctOrganTree.json';
import { EnumCertificateKind } from '../mock/enum.js';
import { checkAuth } from '../../../utils/utils';
import { M010401 } from '../../../../permit/user';
import { M010301 } from '../../../../permit/role';

const queryAcctOrganTree = () => Promise.resolve(queryAcctOrganTreeJson);

const Detail = (props) => {
  const {
    setDetailVisible,
    content,
    setAssignVisible,
    dictMap,
    userDetail,
    handleStop,
    handleLogof,
    handleStart,
    lookUp,
    handleDelete,
    handleOver,
    handleAssignRoles,
    hadleChangeOrgan,
    handleResetPwd
  } = props;
  const [form] = YRForm.useForm();
  const [treeData, setTreeData] = useState<any>([]);
  const [acctOrganTreeData, setAcctOrganTreeData] = useState<any[]>([]);
  useEffect(() => {
    queryUserOrganTree({}).then((res) => {
      if (res.success && res.data) {
        const organTreeDto = res.data.organTreeDto ? [res.data.organTreeDto] : [];
        setTreeData(organTreeDto);
      }
    });
    queryAcctOrganTree().then((res: any) => {
      if (res.success && res.data) {
        const organTreeDto = res.data.organTreeDto ? [res.data.organTreeDto] : [];
        setAcctOrganTreeData(organTreeDto);
      }
    });
  }, []);
  const getTableColumns = () => {
    return [
      {
        title: '身份',
        dataIndex: 'identityRelationType',
        width: 100,
        render: (value: string) => {
          return value === '001' ? '本职' : '兼职';
        }
      },
      {
        title: '关联机构',
        dataIndex: 'organName',
        width: 200
      },
      {
        title: '条线归属',
        dataIndex: 'bizLines',
        width: 200,
        render: (value) => {
          const text =
            value?.map((key) => dictMap['EnumUserBizLine']?.find((item) => item.itemKey === key)?.itemName) || [];
          return text.filter(Boolean).join('、');
        }
      },
      {
        title: '角色',
        dataIndex: 'role',
        width: 300,
        render: (value, userDetail) => {
          const roleDtoList = userDetail.roleDtoList || [];
          const roles = roleDtoList?.map((item: any) => item.roleName);
          return roles.join('、');
        }
      }
    ];
  };

  const getOrganTableColumns = () => {
    return [
      {
        title: '变更前所属机构',
        dataIndex: 'beforeOrganName',
        width: 150,
        render: (value: string) => value || CONST.null
      },
      {
        title: '变更后所属机构',
        dataIndex: 'afterOrganName',
        width: 150,
        render: (value: string) => value || CONST.null
      },
      {
        title: '审批状态',
        dataIndex: 'applyStatus',
        width: 150,
        render: (value: string) => value || CONST.null
      },
      {
        title: '变更时间',
        dataIndex: 'updateDate',
        width: 150,
        render: (value: string) => value || CONST.null
      },
      {
        title: '操作人',
        dataIndex: 'operatorName',
        width: 150,
        render: (value: string) => value || CONST.null
      },
      {
        title: '操作机构',
        dataIndex: 'ownOrganName',
        width: 150,
        render: (value: string) => value || CONST.null
      }
    ];
  };

  const renderMainOperation = () => {
    return (
      <>
        {/* <YRButton>停用</YRButton> */}
        {/* <YRButton type="primary">修改</YRButton> */}
      </>
    );
  };

  const renderTitle = () => {
    const status = <YRDict.Select dictkey="EnumGuardUserStatus" type="text" defaultValue={content.userStatus} />;
    return (
      <div>
        <a style={{ fontSize: 18 }} onClick={back}>
          &lt;
        </a>
        <strong> {content.userName}</strong>
        <YRTag color={content.userStatus === '010' ? 'green' : 'red'}>{status}</YRTag>
      </div>
    );
  };

  const back = () => {
    setDetailVisible(false);
  };

  const handleAssign = () => {
    setAssignVisible(true);
  };

  return (
    <YRFlexPageLayout.Main title={renderTitle()} extra={renderMainOperation()}>
      <YRSpace direction="vertical">
        <YRButton.Space>
          {userDetail?.userStatus === '010' && (
            <YRButton type="primary" onClick={() => handleStop(userDetail.userId, "020")}>
              停用
            </YRButton>
          )}
          {userDetail?.userStatus === '020' && (
            <YRButton type="primary" onClick={() => handleLogof(userDetail.userId, "030")}>
              注销
            </YRButton>
          )}
          {(userDetail?.userStatus === '020' || userDetail?.userStatus === '040') && (
            <YRButton type="primary" onClick={() => handleStart(userDetail.userId, "010")}>
              启用
            </YRButton>
          )}
          {userDetail?.userStatus !== '030' && (
            <YRButton type="primary" onClick={() => lookUp(userDetail, 'edit')}>
              修改
            </YRButton>
          )}
          {(userDetail?.userStatus === '030' || userDetail?.userStatus === '040') && (
            <YRButton type="primary" onClick={() => handleDelete(userDetail.userId)}>
              删除
            </YRButton>
          )}
          {userDetail?.userStatus === '010' && checkAuth(M010401.E01) && (
            <YRButton type="primary" onClick={() => handleOver(userDetail.userId)}>
              权限移交
            </YRButton>
          )}
          {(userDetail?.userStatus === '010' ||
            userDetail?.userStatus === '040' ||
            userDetail.userStatus === '020') && (
            <YRButton type="primary" onClick={() => handleAssignRoles(userDetail)}>
              分配角色
            </YRButton>
          )}
          {(userDetail?.userStatus === '010' || userDetail?.userStatus === '040') && (
            <YRButton type="primary" onClick={() => hadleChangeOrgan(userDetail)}>
              变更机构
            </YRButton>
          )}
          {checkAuth(M010301.E10) && (
            <YRButton onClick={() => handleResetPwd(userDetail.userId)} type="primary">
              重置密码
            </YRButton>
          )}
        </YRButton.Space>
        <YRForm form={form} mode="readPretty">
          <YRClassificationLayout.Space>
            <YRClassificationLayout title="用户信息">
              <YRForm.Row>
                <YRForm.Item name="userName" label="用户姓名" initialValue={content.userName}>
                  <YRInput maxLength={30} />
                </YRForm.Item>
                <YRForm.Item name="accountNo" label="登录账号" initialValue={content.accountNo}>
                  <YRInput maxLength={60} />
                </YRForm.Item>
                <YRForm.Item name="mobile" label="手机号码" initialValue={content.mobile}>
                  <YRInput maxLength={60} />
                </YRForm.Item>
                <YRForm.Item name="organId" label="所属机构" initialValue={content.organId}>
                  <YRTreeSelect treeData={treeData} fieldNames={{ label: 'organName', value: 'organId' }} />
                </YRForm.Item>
                <YRForm.Item
                  name="certificateKind"
                  label="证件类型"
                  initialValue={EnumCertificateKind[content.certificateKind]}
                >
                  <YRInput maxLength={60} />
                </YRForm.Item>
                <YRForm.Item name="certificateNo" label="证件号码" initialValue={content.certificateNo}>
                  <YRInput maxLength={60} />
                </YRForm.Item>
                <YRForm.Item name="email" label="电子邮箱" initialValue={content.email}>
                  <YRInput maxLength={60} />
                </YRForm.Item>
                <YRForm.Item name="post" label="岗位" initialValue={content.post}>
                  <YRInput maxLength={60} />
                </YRForm.Item>
                <YRForm.Item name="userType" label="用户类型" initialValue={content.userType}>
                  <YRDict.Select dictkey="EnumGuardUserType" />
                </YRForm.Item>
                <YRForm.Item name="level" label="客户经理级别" initialValue={content.level}>
                  <YRInput maxLength={60} />
                </YRForm.Item>
                <YRForm.Item name="ownBankFlag" label="是否本行员工" initialValue={content.ownBankFlag}>
                  <YRSelect>
                    <YRSelect.Option value="Y">是</YRSelect.Option>
                    <YRSelect.Option value="N">否</YRSelect.Option>
                  </YRSelect>
                </YRForm.Item>
                <YRForm.Item name="tellerFlag" label="是否柜员" initialValue={content.tellerFlag}>
                  <YRSelect>
                    <YRSelect.Option value="Y">是</YRSelect.Option>
                    <YRSelect.Option value="N">否</YRSelect.Option>
                  </YRSelect>
                </YRForm.Item>
                <YRForm.Item name="acctOrganId" label="账务机构" initialValue={content.acctOrganId}>
                  <YRTreeSelect
                    treeData={acctOrganTreeData}
                    fieldNames={{ label: 'acctOrganName', value: 'acctOrganId' }}
                  />
                </YRForm.Item>
              </YRForm.Row>
            </YRClassificationLayout>
            <YRClassificationLayout title="用户角色信息">
              {/* {type === 'edit' && <YRButton onClick={handleAssign}>分配角色</YRButton>} */}
              <YRTable dataSource={content.userIdentifyDtoList} rowKey="userId" columns={getTableColumns()} />
            </YRClassificationLayout>
            {/* <YRClassificationLayout title="客户经理助理信息">暂无助理</YRClassificationLayout> */}
            <YRClassificationLayout title="机构变更记录">
              <YRTable
                dataSource={content.userOrganChangeApprovalInfoList || []}
                rowKey="applyId"
                columns={getOrganTableColumns()}
                scroll={{ x: countTableWidth(getOrganTableColumns()) }}
              />
            </YRClassificationLayout>
            <YRClassificationLayout title="管理信息">
              <YRForm.Row>
                <YRForm.Item name="registerUserName" label="登记人" initialValue={content.registerUserName}>
                  <YRInput maxLength={60} />
                </YRForm.Item>
                <YRForm.Item name="registerOrganId" label="登记机构" initialValue={content.registerOrganId}>
                  <YRTreeSelect treeData={treeData} fieldNames={{ label: 'organName', value: 'organId' }} />
                </YRForm.Item>
                <YRForm.Item name="registerDate" label="登记日期" initialValue={moment(content.registerDate)}>
                  <YRDatePicker />
                </YRForm.Item>
                <YRForm.Item name="updateUserName" label="更新人" initialValue={content.updateUserName}>
                  <YRInput maxLength={60} />
                </YRForm.Item>
                <YRForm.Item name="updateDate" label="更新日期" initialValue={moment(content.updateDate)}>
                  <YRDatePicker />
                </YRForm.Item>
              </YRForm.Row>
            </YRClassificationLayout>
          </YRClassificationLayout.Space>
        </YRForm>
      </YRSpace>
    </YRFlexPageLayout.Main>
  );
};

export default Detail;
