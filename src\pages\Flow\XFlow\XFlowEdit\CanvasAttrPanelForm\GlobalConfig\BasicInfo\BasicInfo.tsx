/**
 * 基本信息组
 */
import React from 'react';
import { YRForm, YRInput } from 'yrantd';

const FormItem = YRForm.Item;

const BasicInfo = (props: any) => {
  const { globalDisabled, globalFormData } = props;
  return (
    <>
      <FormItem name="nodeType" initialValue={globalFormData.nodeType} hidden>
        <YRInput />
      </FormItem>
      <FormItem
        name="flowNumber"
        label="流程编号"
        rules={[
          {
            required: true,
            whitespace: true,
            message: '请输入流程编号'
          }
        ]}
        initialValue={globalFormData.flowNumber}
      >
        <YRInput placeholder="请输入" disabled />
      </FormItem>
      <FormItem
        name="flowName"
        label="流程名称"
        rules={[
          {
            required: true,
            whitespace: true,
            message: '请输入流程名称'
          }
        ]}
        initialValue={globalFormData.flowName}
      >
        <YRInput placeholder="请输入" disabled={globalDisabled} maxLength={64} />
      </FormItem>
      <FormItem name="flowDesc" label="流程描述" initialValue={globalFormData.flowDesc}>
        <YRInput placeholder="请输入" disabled={globalDisabled} maxLength={64} />
      </FormItem>
      <FormItem
        name="sceneType"
        label="场景类型"
        rules={[
          {
            required: true,
            whitespace: true,
            message: '请输入场景类型'
          }
        ]}
        initialValue={globalFormData.sceneType}
      >
        <YRInput placeholder="请输入" disabled={globalDisabled} maxLength={64} />
      </FormItem>
    </>
  );
};

export default BasicInfo;
