/**
 * @Author: wangyw26123
 * @Description: 高级-节点配置
 * @Date: Created in 2022-04-27 14:08:32
 * @Modifed By:
 */

import store from '@/store';
import { useDebounceEffect, useUpdateEffect } from 'ahooks';
import React, { useEffect, useState } from 'react';
import { YRSelect, YRForm, YRInput, YREditableTable, YRButton, YRTable, YRSpace } from 'yrantd';
import { EditableColumnsType } from 'yrantd/lib/yr-editable-table/declare';
import SignalAddModel from './SignalAddModal';

const scope = [
  { value: '流程实例', key: 'processInstance' },
  { value: '全局', key: 'global' }
];

const SignalConfig = (props) => {
  const {
    globalDisabled,
    targetData: { nodeFormData },
    globalFormData
  } = props;
  const [dataSource, setDataSource] = useState<any>([]);
  const [visible, setVisible] = useState<any>(false);
  const form = YRForm.useFormInstance();
  const signaldefinition = YRForm.useWatch(['signaldefinition'], form);
  const signaldefinitions = YRForm.useWatch(['signaldefinitions'], form);

  const getOptions = (list: any[], dictNum: string) => {
    const value = list.filter((item) => {
      return item.key === dictNum;
    });
    return value[0]?.value;
  };

  const defaultColumns: EditableColumnsType<any> = [
    {
      title: 'code',
      dataIndex: 'id',
      key: 'id',
      required: true,
      editable: false,
      width: 80
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      required: true,
      editable: false,
      width: 80
    },
    {
      title: '类型',
      dataIndex: 'scope',
      key: 'scope',
      required: true,
      editable: false,
      width: 80,
      render: (text) => {
        return getOptions(scope, text) ?? '-';
      }
    }
  ];

  const onOk = (vlaues: any) => {
    const list = [...dataSource];
    list.push({ ...vlaues });
    setDataSource([...list]);
  };

  const handleDelete = (index: any) => {
    const list = [...dataSource];
    list.splice(index, 1);
    setDataSource([...list]);
  };

  //   useDebounceEffect(() => {
  //     console.log(signaldefinitions);
  //     if (signaldefinitions?.length > 0) {
  //       setDataSource([...signaldefinitions]);
  //     }
  //     form.setFieldValue('signaldefinitions', signaldefinitions || []);
  //   }, [signaldefinitions]);

  // 当获取详情之后，在进行修改，保存值
  useDebounceEffect(() => {
    const _signaldefinitions = globalFormData?.signaldefinitions || [];
    if (_signaldefinitions?.length > 0) {
      setDataSource([..._signaldefinitions]);
    }
  }, [globalFormData]);

  useUpdateEffect(() => {
    form.setFieldValue('signaldefinition', signaldefinitions);
  }, [signaldefinitions]);

  useDebounceEffect(() => {
    signaldefinition && setDataSource(signaldefinition);
  }, [signaldefinition]);

  return (
    <YRSpace block direction={'vertical'}>
      <YRForm.Item name="signaldefinition" hidden />
      <YREditableTable
        form={form}
        name={'signaldefinitions'}
        showIndex={false}
        mode={!globalDisabled ? 'editable' : 'readPretty'}
        dataSource={dataSource}
        columns={defaultColumns}
        rowKey={(row, index) => index}
        onBeforeAdd={() => {
          setVisible(true);
        }}
        onAfterDelete={(index) => {
          handleDelete(index);
        }}
        toolbar={{
          buttonsProps: {
            add: {
              text: '新增',
              type: 'primary'
            }
          }
        }}
      />
      <SignalAddModel visible={visible} changeVisible={setVisible} onOK={onOk} />
    </YRSpace>
  );
};

export default SignalConfig;
