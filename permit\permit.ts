/**
 * 功能权限
 */
import { YRLoanPermit } from 'yr-loan-antd';
import packageInfo from '../package.json';
import {
  addPermitGroup,
  deletePermitGroup,
  modifyPermitGroup,
  queryPermitGroupList,
  queryPermitGroupWithPermit
} from '@/services/setting';

const { buildPermit, EnumPermitType } = YRLoanPermit;

/**
 * 功能权限
 */
const M0102 = buildPermit('M0102', packageInfo, {
  interfaces: { queryPermitGroupList, queryPermitGroupWithPermit },
  E01: {
    name: '新增权限组',
    path: '/hsjry/guard/base/IPermit/addPermitGroup',
    type: EnumPermitType.Element,
    interfaces: {
      addPermitGroup
    }
  },
  E02: {
    name: '删除权限组',
    path: '/hsjry/guard/base/IPermit/deletePermitGroup',
    type: EnumPermitType.Element,
    interfaces: {
      deletePermitGroup
    }
  },
  E03: {
    name: '修改权限组',
    path: '/hsjry/guard/base/IPermit/modifyPermitGroup',
    type: EnumPermitType.Element,
    interfaces: {
      modifyPermitGroup
    }
  }
});

export { M0102 };
