export default {
  // 手机号码：11位，以1开头，第二位为3,4,5,7,8中的一个，后9位数字。
  phone: /^[1][3,4,5,7,8][0-9]{9}$/,

  // 固定电话：区号+号码，区号以0开头，3位或4位，号码由7位或8位数字组成，区号与号码之间可以无连接符，也可以“-”连接。
  tele: /^0\d{2,3}-?\d{7,8}$/,

  // 邮箱格式：第一部分@第二部分。第一部分：由字母、数字、下划线、短线“-”、点号“.”组成。第二部分：为一个域名，域名由字母、数字、短线“-”、域名后缀组成，域名后缀一般为.xxx或.xxx.xx。
  email: /^(\w-*\.*)+@(\w-?)+(\.\w{2,})+$/,

  // 身份证:15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
  idCard: /(^\d{15}$)|(^\d{17}(\d|X|x)$)/,

  // 密码：至少10位，密码至少包含大写字母、小写字母、数字、特殊符号各1个
  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[$@$!%*?&.])[A-Za-z\d$@$!%*?&.]{8,20}/,

  // 银行卡号：第一位不是0，16位或者19位
  bankCard: /^([1-9]{1})(\d{15}|\d{18})$/,
  money: /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)/,
  // 支持小于1的数字
  money1: /(^([1-9]([0-9]+)?|0)(\.[0-9]{1,2})?$)/,

  // 禁止输入中文
  unchinese: /^[^\u4e00-\u9fa5]{0,}$/,
  unSpace: /^[^\s]*$/,
  // 禁止输入特殊字符
  uncode: /^[\u4e00-\u9fa5_a-zA-Z0-9]+$/,
  // 禁止输入特殊字符
  uncode2: /^(\w|-)+$/,
  // 禁止输入特殊字符 ，也不能输入-
  uncode3: /^\w+$/,
  // 大小写字母、中划线(-)、下划线(_)
  code: /^[-_a-zA-Z]+$/,
  // 大小写字母、数字、中划线(-)、下划线(_)
  code1: /^[-_a-zA-Z0-9]+$/,
  code2: /^[A-Z0-9]+$/,
  code3: /^[_a-zA-Z0-9]+$/,
  number: /^[0-9]+$/,
  // 数值
  number1: /^[0-9]+(\.[0-9]{1,})?$/
};
