import React from 'react';
import { Draggable } from 'react-beautiful-dnd';

const DraggableItem = (props) => {
  const { comp, index } = props;

  return (
    <Draggable key={index} draggableId={index} index={index}>
      {(provided) => (
        <div ref={provided.innerRef} {...provided.draggableProps} {...provided.dragHandleProps}>
          {comp}
          {provided.placeholder}
        </div>
      )}
    </Draggable>
  );
};

export default DraggableItem;
