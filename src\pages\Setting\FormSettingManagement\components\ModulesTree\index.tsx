/*
 * @Author: liaokt
 * @Description: 设置-表单配置管理-模块目录树
 * @Date: 2024-11-25 10:29:30
 * @LastEditors: liaokt
 * @LastEditTime: 2024-11-25 14:49:37
 */

import React, { useState } from 'react';
import { YREmpty, YRSpin, YRTree } from 'yrantd';

const { DirectoryTree } = YRTree;

interface IModulesTreeProps {
  selectedCallBack: (selectedNode: Record<string, any>) => void;
}

const ModulesTree = (props: IModulesTreeProps) => {
  const { selectedCallBack } = props;

  const [treeData, setTreeData] = useState([
    {
      title: '客户管理',
      key: '0-0',
      selectable: false,
      children: [
        { title: '个人客户', key: '0-0-0', isLeaf: true },
        { title: '公司客户', key: '0-0-1', isLeaf: true },
        { title: '集团客户', key: '0-0-2', isLeaf: true },
        { title: '同业客户', key: '0-0-3', isLeaf: true }
      ]
    }
  ]);

  const onSelectedNode = (selectedNodeKeys: string[], { selectedNodes }) => {
    selectedCallBack({ selectedNodeKey: selectedNodeKeys[0], selectedNode: selectedNodes[0] });
  };

  return (
    <YRSpin spinning={false}>
      {treeData && treeData.length ? (
        <DirectoryTree multiple defaultExpandAll onSelect={onSelectedNode} treeData={treeData} />
      ) : (
        <YREmpty className="empty" />
      )}
    </YRSpin>
  );
};

export default ModulesTree;
