/**
 * 页面描述: 参数选择组件
 * @文件名 Parameter.tsx
 * @filePath \src\pages\Setting\RuleManager\Entry\components\Parameter.tsx
 * @Date 2023-08-08 18:24:15
 * @Email <EMAIL>
 * <AUTHOR>
 */
import React, { useState, useMemo } from 'react';
import { YRDropdown, YRInput, YRMenu, YRTooltip } from 'yrantd';
import store from '@/store';

const Parameter = (props) => {
  const { selectedId, selectedLabel, setValue } = props;
  const [ruleState, ruleDispatch] = store.useModel('rule');
  const { type, parameterList } = ruleState;
  const [visible, setVisible] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const styleObj = { display: 'inline-block' };

  // 根据搜索框筛选可选参数
  const filterParameterList = useMemo(() => {
    return searchValue !== ''
      ? parameterList.filter((item: any) => item.variableName.includes(searchValue)).slice(0, 150)
      : parameterList;
  }, [parameterList, searchValue]);

  // 设置值
  const selectMenu = ({ item }) => {
    if (!item || !item.props) return;
    // 选中项
    const active = item.props?.data;
    setVisible(false);
    setValue(active);
  };

  const parameterMenu = (
    <>
      <YRInput.Search
        placeholder="最多显示150条,输入值查询"
        style={{
          width: '100%',
          padding: '5px 7px',
          position: 'sticky',
          top: 0,
          background: '#fff',
          boxShadow:
            '0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)'
        }}
        onSearch={(value) => setSearchValue(value)}
      />
      <YRMenu
        onClick={selectMenu}
        className="parameter-value parameter-value-max"
        style={{ maxHeight: 300, overflowY: 'scroll' }}
      >
        {filterParameterList.map((item: any) => {
          const valueIndex = item.variableName.indexOf(searchValue);
          const beforeStr = item.variableName.substr(0, valueIndex);
          const afterStr = item.variableName.substr(valueIndex + searchValue.length);
          const title =
            searchValue && valueIndex > -1 ? (
              <span>
                {beforeStr}
                <span style={{ color: '#f50' }}>{searchValue}</span>
                {afterStr}
              </span>
            ) : (
              item.variableName
            );
          return (
            <YRMenu.Item
              key={item.variableId}
              data={{ variableCode: item?.variableCode || '', variableName: item?.variableName || '' }}
            >
              {title}
            </YRMenu.Item>
          );
        })}
      </YRMenu>
    </>
  );

  return (
    <>
      <YRDropdown
        disabled={type === 'detail'}
        overlay={parameterMenu}
        trigger={['click']}
        placement="bottomLeft"
        visible={visible}
        onVisibleChange={(value) => setVisible(value)}
      >
        {selectedId ? (
          <YRTooltip title={selectedId}>
            <span className="parameter-label" style={styleObj}>
              {selectedLabel}
            </span>
          </YRTooltip>
        ) : (
          <span className="parameter-label" style={styleObj}>
            {selectedLabel}
          </span>
        )}
      </YRDropdown>
    </>
  );
};

export default Parameter;
