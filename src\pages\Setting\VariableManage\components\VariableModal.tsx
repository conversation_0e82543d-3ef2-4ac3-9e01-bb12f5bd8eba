import React, { useEffect } from 'react';
import { YREasyUseModal, YRForm, YRModal, YRInput, YRMessage } from 'yrantd';
import { saveVariable } from '@/services/setting';

const VariableModal = (props) => {
  const { defaultData, refresh, type } = props;
  const modal = YREasyUseModal.useModal();
  const [form] = YRForm.useForm();
  const typeName = type === 'add' ? '新增' : '修改';

  useEffect(() => {
    const treeId = defaultData?.treeId || '';
    if (treeId) {
      form.setFieldsValue(defaultData);
    }
  }, [defaultData]);

  const submit = () => {
    form.validateFields().then((value) => {
      const params = {
        ...defaultData,
        ...(value || {})
      };
      saveVariable(params).then((res) => {
        if (res?.data) {
          YRMessage.success(`${typeName}成功`);
          modal.remove();
          refresh();
        }
      });
    });
  };

  return (
    <YRModal
      title={`${typeName}变量`}
      onOk={submit}
      onCancel={modal.remove}
      open={modal.visible}
      destroyOnClose
      width={'40%'}
    >
      <YRForm form={form}>
        <YRForm.Item name="variableId" label="变量id" hidden>
          <YRInput />
        </YRForm.Item>
        <YRForm.Item name="treeId" label="变量id" hidden>
          <YRInput />
        </YRForm.Item>
        <YRForm.Row column={1}>
          <YRForm.Item name="treeName" label="目录名称" rules={[{ required: true }]}>
            <YRInput disabled />
          </YRForm.Item>
          <YRForm.Item name="variableCode" label="变量code" rules={[{ required: true }]}>
            <YRInput />
          </YRForm.Item>
          <YRForm.Item name="variableName" label="变量名称" rules={[{ required: true }]}>
            <YRInput />
          </YRForm.Item>
        </YRForm.Row>
      </YRForm>
    </YRModal>
  );
};

export default YREasyUseModal.create(VariableModal);
