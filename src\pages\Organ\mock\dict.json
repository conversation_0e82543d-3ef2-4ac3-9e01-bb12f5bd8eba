{"rpcResult": "000000", "httpStatus": 200, "success": true, "responseType": "HSJRY_SUCCESS", "errorCode": null, "errorMessage": null, "data": {"dictItemMap": {"EnumOrganLevel": [{"itemKey": "001", "itemName": "总行"}, {"itemKey": "002", "itemName": "总行部室"}, {"itemKey": "003", "itemName": "省级区域"}, {"itemKey": "004", "itemName": "市级区域"}, {"itemKey": "005", "itemName": "中心区域"}, {"itemKey": "006", "itemName": "营业机构"}], "EnumOrganStatus": [{"itemKey": "001", "itemName": "未启用"}, {"itemKey": "002", "itemName": "启用"}, {"itemKey": "003", "itemName": "停用"}], "EnumOrganType": [{"itemKey": "001", "itemName": "经营机构"}, {"itemKey": "002", "itemName": "管理部门"}], "EnumOrganNature": [{"itemKey": "001", "itemName": "一般机构"}, {"itemKey": "002", "itemName": "放款中心"}, {"itemKey": "003", "itemName": "集中作业中心"}, {"itemKey": "004", "itemName": "审批中心"}, {"itemKey": "005", "itemName": "保全中心"}], "EnumRoleLevel": [{"itemKey": "001", "itemName": "总行"}, {"itemKey": "002", "itemName": "总行部室"}, {"itemKey": "003", "itemName": "省级区域"}, {"itemKey": "004", "itemName": "市级区域"}, {"itemKey": "005", "itemName": "中心区域"}, {"itemKey": "006", "itemName": "营业机构"}, {"itemKey": "099", "itemName": "通用"}], "EnumGuardRoleType": [{"itemKey": "000", "itemName": "审批角色"}, {"itemKey": "001", "itemName": "催收角色"}, {"itemKey": "002", "itemName": "业务角色"}, {"itemKey": "003", "itemName": "核算角色"}, {"itemKey": "004", "itemName": "营销角色"}, {"itemKey": "005", "itemName": "风控角色"}, {"itemKey": "006", "itemName": "报表角色"}, {"itemKey": "007", "itemName": "管理后台角色"}, {"itemKey": "008", "itemName": "权限组"}, {"itemKey": "009", "itemName": "机构管理员角色"}], "EnumGuardRoleStatus": [{"itemKey": "001", "itemName": "未启用"}, {"itemKey": "002", "itemName": "启用"}, {"itemKey": "003", "itemName": "停用"}], "EnumRoleBizLine": [{"itemKey": "001", "itemName": "公司银行"}, {"itemKey": "002", "itemName": "零售银行"}, {"itemKey": "003", "itemName": "数字银行"}, {"itemKey": "004", "itemName": "金融市场"}, {"itemKey": "005", "itemName": "通用"}], "EnumOrganBizLine": [{"itemKey": "001", "itemName": "公司银行"}, {"itemKey": "002", "itemName": "零售银行"}, {"itemKey": "003", "itemName": "数字银行"}, {"itemKey": "004", "itemName": "金融市场"}, {"itemKey": "005", "itemName": "综合条线"}], "EnumGuardAccessRoleType": [{"itemKey": "001", "itemName": "关键角色"}, {"itemKey": "002", "itemName": "特定资质角色"}], "EnumFilterGroup": [{"itemKey": "001", "itemName": "本人"}, {"itemKey": "002", "itemName": "本机构"}, {"itemKey": "003", "itemName": "本机构及下级机构"}, {"itemKey": "004", "itemName": "所有"}], "EnumGuardUserStatus": [{"itemKey": "010", "itemName": "启用"}, {"itemKey": "020", "itemName": "停用"}, {"itemKey": "030", "itemName": "注销"}, {"itemKey": "040", "itemName": "未启用"}], "EnumAcctOrganType": [{"itemKey": "001", "itemName": "一般记账机构"}, {"itemKey": "002", "itemName": "国际业务记账机构"}, {"itemKey": "003", "itemName": "贴现业务记账机构"}], "flow_scene_type": [{"itemKey": "1", "itemName": "组织管理"}, {"itemKey": "2", "itemName": "产品管理"}, {"itemKey": "3", "itemName": "额度管理"}, {"itemKey": "4", "itemName": "担保管理"}, {"itemKey": "5", "itemName": "客户管理"}, {"itemKey": "6", "itemName": "征信管理"}, {"itemKey": "7", "itemName": "评级管理"}, {"itemKey": "8", "itemName": "授信管理"}, {"itemKey": "9", "itemName": "合同管理"}, {"itemKey": "10", "itemName": "出账管理"}, {"itemKey": "11", "itemName": "核算管理"}, {"itemKey": "12", "itemName": "贷后管理"}, {"itemKey": "13", "itemName": "档案管理"}, {"itemKey": "14", "itemName": "其它管理"}, {"itemKey": "15", "itemName": "授权管理"}], "flow_diagram_status": [{"itemKey": "0", "itemName": "编辑中"}, {"itemKey": "1", "itemName": "已发布"}], "对应HR机构": [{"itemKey": "0", "itemName": "编辑中"}, {"itemKey": "1", "itemName": "已发布"}], "映射机构类型": [{"itemKey": "001", "itemName": "一般记账机构"}, {"itemKey": "002", "itemName": "国际业务记账机构"}, {"itemKey": "003", "itemName": "贴现业务记账机构"}, {"itemKey": "004", "itemName": "票据签发记账机构"}, {"itemKey": "005", "itemName": "扫描机构"}, {"itemKey": "006", "itemName": "财富支行"}], "EnumBoolean": [{"itemKey": "1", "itemName": "是"}, {"itemKey": "0", "itemName": "否"}], "param_value_type": [{"itemKey": "string", "itemName": "string"}, {"itemKey": "number", "itemName": "number"}, {"itemKey": "boolean", "itemName": "boolean"}, {"itemKey": "date", "itemName": "date"}, {"itemKey": "collection", "itemName": "collection"}], "EnumVersoinStatus": [{"itemKey": "1", "itemName": "激活"}, {"itemKey": "0", "itemName": "挂起"}]}}, "parentIdemSerialId": null}