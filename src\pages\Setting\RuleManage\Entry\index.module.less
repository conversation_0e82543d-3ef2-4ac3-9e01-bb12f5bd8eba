.yr-rule {
  position: relative;
}
:global(.center-cont-rule .description_term) {
  color: rgba(0, 0, 0, 0.45);
}
.yr-rule-list {
  display: flex;
  position: relative;
  padding: 10px 12px;
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  align-items: center;
  transition: all 0.3s;
  position: relative;
}
.yr-rule-list:hover {
  cursor: pointer;
  box-shadow: 0px 2px 10px 0px rgba(24, 144, 255, 0.15);
  border: 1px solid var(--primary-3);
}
.yr-rule-list-par {
  /* padding: 16px; */
  background-color: #fff;
}
.yr-rule-list-par:not(:last-child) {
  margin-bottom: 4px;
}
.yr-rule-list .icon {
  margin-right: 8px;
  color: #ededed;
  height: 32px;
}
.yr-rule-list .icon i {
  position: relative;
  font-size: 72px;
}
.yr-rule-list .main {
  flex: 1;
  width: 100%;
  font-size: 12px;
}
.yr-rule-context {
  height: calc(100vh - 160px);
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;
  padding: 0 4px 4px;
}
.yr-rule-context .yr-rule-empty {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -100px;
  margin-left: -50px;
  width: 100px;
  text-align: center;
  color: rgba(0, 0, 0, 0.45);
}
.yr-rule-context .main > div:first-child {
  margin-bottom: 4px;
}
.yr-rule-context .main .head {
  color: rgba(0, 0, 0, 0.2);
  padding-right: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.yr-rule-context .main .title {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  white-space: nowrap;
}
.yr-rule-context .main .leve {
  min-width: 130px;
  max-width: 100%;
  height: 18px;
  background: rgba(254, 244, 233, 1);
  border-radius: 12px;
  display: inline-block;
  padding: 0 12px;
  text-align: center;
  color: #fe942e;
  line-height: 18px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 12px;
}
.yr-rule-context .main .number {
  padding-left: 8px;
  color: rgba(0, 0, 0, 0.2);
  white-space: nowrap;
}
.yr-rule-context .main .prop-value {
  padding-right: 8px;
  font-size: 12px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.45);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.yr-rule-context .main .prop-title {
  color: rgba(0, 0, 0, 0.45);
}
.yr-rule-context .edit > i {
  display: block;
  width: 32px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  color: rgba(0, 0, 0, 0.2);
  border-radius: 50%;
  transition: all 0.3s;
}
.yr-rule-context .edit > i:hover {
  cursor: pointer;
  background: rgba(254, 244, 233, 1);
  color: rgba(245, 34, 45, 1);
}
.yr-rule-context .edit > span {
  display: inline-block;
  position: absolute;
  top: 8px;
  right: 10px;
}
.yr-rule-context .edit > span:hover > i {
  background: #1890ff;
  color: #fff;
}
.yr-rule-context .edit > span > i {
  display: block;
  width: 22px;
  height: 22px;
  font-size: 12px;
  line-height: 22px;
  text-align: center;
  color: rgba(0, 0, 0, 0.15);
  border-radius: 50%;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}
.yr-rule-header {
  overflow: hidden;
  padding: 4px 16px;
  /* margin: 0 0 4px 0; */
  /* background-color: rgb(229, 231, 235); */
  background-color: #fff;
  user-select: text;
}
.yr-rule-header > div > span {
  color: rgba(0, 0, 0, 0.3);
  margin-right: 12px;
}
.yr-rule-header-left {
  position: relative;
}
.yr-rule-header-left > span {
  position: relative;
  top: -1px;
}
.yr-rule-header-left > span.title {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  margin: 0 0 0 8px;
}
.yr-rule-header > div > i {
  margin-right: 4px;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.15);
  transition: all 0.3s;
  cursor: pointer;
}
.yr-rule-header > div > i:first-child:hover {
  color: #1890ff;
}
.yr-rule-header-right > span {
  font-size: 12px;
}
.yr-rule-header-right > span:last-child {
  margin-right: 0;
}
.edit-rule-form {
  background: rgba(255, 255, 255, 1) !important;
  box-shadow: 0px 2px 20px 0px rgba(0, 111, 255, 0.14) !important;
  border-radius: 12px !important;
  padding: 8px 24px 2px 24px;
  margin-bottom: 15px;
  border: 1px solid transparent;
  position: relative;
}
.edit-rule-form :global(.ant-form-explain) {
  /* display: none; */
}
.edit-rule-form i.icon {
  font-size: 4em;
  color: #dddddd;
  margin-top: 10px;
}
:global(.edit-rule-modal .ant-form-item) {
  margin-bottom: 8px !important;
}
.comm-context {
  background: rgba(255, 255, 255, 1);
  /* box-shadow: 0px 2px 20px 0px rgba(0, 111, 255, 0.14); */
  /* border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 1); */
  position: relative;
  -moz-user-select: none;
  -o-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  overflow: hidden;
}
.comm-context .resizeR,
.comm-context .resizeL {
  position: absolute;
  width: 10px;
  opacity: 0;
  height: 100%;
  cursor: w-resize;
  z-index: 99999;
  top: 0;
}
.comm-context .resizeR {
  right: 0;
}
.comm-context .resizeL {
  left: 0;
}
.comm-context:hover {
  /* border: 1px solid #1890ff; */
  cursor: pointer;
}
.special-if-item {
  z-index: 999;
  position: relative;
  white-space: nowrap;
  display: flex;
}
.if-item {
  height: 32px;
  z-index: 999;
  position: relative;
  white-space: nowrap;
}
.special-if-item:hover .move-wrap {
  display: flex;
}
.if-item:hover .move-wrap {
  display: flex;
}
.if-item .ant-btn-sm-span {
  width: 65px;
  height: 24px;
  background: #e6f7ff;
  color: #1890ff;
  /* background: #e4eaed; */
  font-weight: 600;
  /* border-radius: 12px; */
  border: none;
  display: inline-block;
  text-align: center;
  line-height: 24px;
  box-shadow: 0px 0px 10px 0px rgba(3, 115, 255, 0.26);
}
.if-item .ant-btn-sm-span:hover {
  background: #fff;
}
.move-wrap {
  display: none;
  position: absolute;
  left: 20px;
  top: 3px;
  border: 1px;
  width: 20px;
  justify-content: space-between;
  flex-direction: column;
  align-items: center;
}
.if-item-rownum {
  display: inline-block;
  flex-shrink: 0;
  height: 32px;
  width: 32px;
  font-size: 13px;
  font-weight: 500;
  color: var(--neutral-5);
  /* background: rgba(229, 249, 255, 1); */
  text-align: center;
  line-height: 32px;
}
.special-if-item:last-child .if-item-rownum {
  border-radius: 0px 0px 0px 12px;
}
.if-item:last-child .if-item-rownum {
  border-radius: 0px 0px 0px 12px;
}
.comm-context .context {
  position: relative;
  /* background: url("@/images/back2.png") repeat left top; */
  font-size: 12px;
  overflow-x: auto;
  overflow-y: hidden;
}
.comm-context .context::-webkit-scrollbar-track {
  border-radius: 10px !important;
}
.comm-context .context::-webkit-scrollbar {
  width: 8px !important;
  height: 8px !important;
}
.comm-context .context::-webkit-scrollbar-thumb {
  border-radius: 12px;
  background-color: rgba(0, 0, 0, 0.1) !important;
}
.comm-context .if-context-svg {
  position: absolute;
  top: 0;
  left: 0;
}
.operator,
.parameter-value,
.special-parameter-value,
.special-input-value,
.input-value,
.action-type,
.method-action {
  height: 24px;
  /* border-radius: 6px; */
  display: inline-block;
  padding: 0 15px;
  line-height: 24px;
  margin: 0 4px;
}
.special-parameter-value {
  height: auto;
  white-space: normal;
  width: 25%;
  flex-shrink: 0;
  text-align: center;
}
.special-input-value {
  margin-right: 0;
  height: auto;
  white-space: normal;
}
.special-parameter-value,
.parameter-value {
  background: #e4eaed;
}
.special-parameter-value:hover {
  color: #1890ff;
}
.parameter-value:hover {
  color: #1890ff;
}
.action-type {
  background: #e4eaed;
}
.action-type:hover {
  color: #1890ff;
}
.operator {
  background: #e4eaed;
}
.operator-no-color {
  /* color: rgba(255, 158, 238, 1); */
  padding-right: 8px;
}
.operator:hover {
  /* background: rgba(255, 235, 252, 0.75);
  color: rgba(255, 158, 238, 0.95); */
  color: #1890ff;
}
.special-input-value,
.input-value {
  background: #e4eaed;
}
.special-input-value:hover,
.input-value:hover {
  /* background: rgba(255, 158, 238, 0.75);
  color: rgba(255, 255, 255, 0.95); */
  color: #1890ff;
}
.special-parameter-value:hover {
  cursor: pointer;
}
.parameter-value:hover {
  cursor: pointer;
}
.if-item-remove {
  position: absolute;
  left: 40px;
  top: 8px;
  font-size: 14px;
  display: none;
  opacity: 0.5;
}
.special-if-item:hover .if-item-remove {
  display: block;
}
.if-item:hover .if-item-remove {
  display: block;
}
.special-if-item .edit-icon,
.edit-icon-sim i {
  padding: 0 2px 0 5px;
}
.if-item .edit-icon,
.edit-icon-sim i {
  padding: 0 2px 0 5px;
}
.special-if-item .edit-icon i {
  display: none;
  opacity: 0.8;
  font-size: 0.5em;
}
.if-item .edit-icon i {
  display: none;
  opacity: 0.8;
  font-size: 0.5em;
}
.icon-down {
  padding-right: 5px;
}
.special-parameter-value:hover .edit-icon i {
  display: inline-block;
}
.parameter-value:hover .edit-icon i {
  display: inline-block;
}
.special-input-value .hover-icon {
  opacity: 0.2;
  padding: 0 5px 0 0px !important;
}
.input-value .hover-icon {
  opacity: 0.2;
  padding: 0 5px 0 1px !important;
}
.special-input-value:hover .hover-icon {
  opacity: 1;
}
.input-value:hover .hover-icon {
  opacity: 1;
}
.special-if-then-item {
  padding-left: 25px;
  padding-top: 5px;
  width: 100%;
  display: flex;
  align-items: flex-start;
}
.if-then-item {
  padding-left: 25px;
}
.method-action {
  background: #e4eaed;
}
.method-action:hover {
  /* background: rgba(255, 237, 201, 0.75); */
  color: #1890ff;
}
.method-action-red {
  background: #e4eaed;
}
.method-action-red:hover {
  /* background: rgba(255, 107, 80, 0.75); */
  color: #1890ff;
}
.edit-context {
  background: rgba(245, 247, 250, 1);
  /* border-radius: 12px; */
  padding: 4px;
}
.edit-rule-form-delete {
  position: absolute;
  top: 5px;
  right: 5px;
  color: #d7d7d7;
  font-size: 12px;
}
