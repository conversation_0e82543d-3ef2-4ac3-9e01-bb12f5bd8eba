/*
 * yunrong.cn Inc. Copyright (c) 2014-2021 All Rights Reserved
 */

/**
 * 关系代码
 */
const EnumClientRelation = {
  ONESELF: { code: '0', desc: '本人' },
  SPOUSE: { code: '1', desc: '配偶' },
  SON: { code: '2', desc: '子' },
  DAUGHTER: { code: '3', desc: '女' },
  GRANDCHILDREN: { code: '4', desc: '孙子女或外孙子女' },
  PARENT: { code: '5', desc: '父母' },
  GRANDPARENTS: { code: '6', desc: '祖父母或外祖父母' },
  BROTHERS_AND_SISTERS: { code: '7', desc: '兄弟姐妹' },
  OTHER: { code: '8', desc: '其他' },
  LEGAL_PERSON: { code: '013', desc: '法人' },
  LEGAL_REPRESENTATIVE: { code: '014', desc: '法人代表人' },
  IMMEDIATE: { code: '015', desc: '直系亲属关系' },
  Collateral: { code: '016', desc: '旁系亲属关系' },
  BE_SECURED: { code: '017', desc: '被担保关系' },
  GUARANTEE: { code: '018', desc: '担保关系' },
  INVESTMENT: { code: '019', desc: '投资关系' },
  BE_INVESTED: { code: '020', desc: '被投资关系' },
  COLLEAGUE: { code: '021', desc: '同事' },
  EMERGENCY_CONTACT: { code: '022', desc: '紧急联系人关系' },
  SHAREHOLDER: { code: '023', desc: '股东' },
  SENIOR_EXECUTIVE: { code: '024', desc: '高管' },
  STAFF: { code: '025', desc: '员工' },
  FRIEND: { code: '026', desc: '朋友' },
  CLASSMATE: { code: '027', desc: '同学' },
  ENTERPRISE_CONTACTS: { code: '028', desc: '企业联系人' },
  AFFILIATED_ENTERPRISE: { code: '029', desc: '关联企业' },
  LEGAL_REPRESENTATIVE_SPOUSE: { code: '030', desc: '法人与法定代表人配偶' },
  IMMEDIATE_OF_LEGAL_REPRESENTATIVES: { code: '032', desc: '法人与法定代表人直系亲属' },
  ACTUAL_CONTROLLER: { code: '033', desc: '法人与实际控制人' },
  SINGLE_PROJECT_SHAREHOLDER: { code: '034', desc: '单一项目股东' },
  MAJOR_SHAREHOLDER: { code: '035', desc: '出资占其净资产30%（含）以上股东' },
  SAME_SHAREHOLDER_LEGAL_PERSON: { code: '036', desc: '相同股东法人' },
  SAME_ACTUAL_CONTROLLER: { code: '037', desc: '相同实际控制人法人' },
  SAME_FAMILY_MEMBER_LEGAL_PERSON: { code: '038', desc: '同一家族成员控制法人' },
  MAJOR_PARTNER: { code: '039', desc: '投资额占其净资产30%（含）以上合伙人' },
  SITUATION_COMPLEX_PARTNER: { code: '040', desc: '情况复杂合伙人' },
  PROJECT_LEADER: { code: '041', desc: '无主业经营合伙人与项目牵头人' },
  SINGLE_PROJECT_PARTNER: { code: '042', desc: '单一项目合伙人' },
  BORROWING_LOAN: { code: '043', desc: '借名贷款' },
  MUTUAL_INSURANCE: { code: '044', desc: '互保（含他行）' },
  EMPLOYEE_PARTNERSHIP: { code: '045', desc: '员工合伙投向企业项目' },
  EMPLOYEES_RAISE_FUNDS: { code: '046', desc: '向员工集资' },
  BENEFICIARY: { code: '047', desc: '受益人' },
  MICRO_INTRODUCER: { code: '048', desc: '介绍人' }
};

export { EnumClientRelation };
