/*
 * @Author: liaokt
 * @Description: 选择角色弹窗
 * @Date: 2024-02-27 16:10:32
 * @LastEditors: liaokt
 * @LastEditTime: 2024-10-16 18:45:57
 */
import { useAntdTable } from 'ahooks';
import { FormItemListProps, YREasyUseModal, YRForm, YRModal, YRTable } from 'yrantd';
import { ValueTypeEnum, YRTableProps } from 'yrantd/lib/yr-table';
import { M0105 } from '@permit/organ';
import { EnumGuardRoleTypeColor, EnumRoleStatusColor, EnumRoleBizLineColor } from '../../../../constant/StyleConst';
import React, { useState } from 'react';

const AddRoleModal = (props) => {
  const { queryRole } = M0105.interfaces;

  const [form] = YRForm.useForm();
  const modal = YREasyUseModal.useModal();

  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [selectedRows, setSelectedRows] = useState<string[]>([]);

  const { tableProps, run } = useAntdTable(
    (p) =>
      queryRole({ ...props }).then((res) => {
        return { list: res?.data?.list, total: res?.data?.total };
      }),
    {
      defaultParams: [
        {
          pageSize: 10,
          current: 1
        }
      ]
    }
  );

  const rowSelection = {
    type: 'checkout',
    onChange: (selectedRowKeys, rows) => {
      setSelectedKeys(selectedRowKeys);
      setSelectedRows(rows);
    },
    selectedRowKeys: selectedKeys
  };

  // 表格列
  const columns: YRTableProps['columns'] = [
    {
      title: '角色编号',
      dataIndex: 'roleId',
      valueType: ValueTypeEnum.id
    },
    {
      title: '角色名称',
      dataIndex: 'roleName',
      width: 220
    },
    {
      title: '角色级别',
      dataIndex: 'roleLevel',
      valueType: ValueTypeEnum.tag,
      color: EnumGuardRoleTypeColor,
      dictkey: sessionStorage.getItem('tenantId') === '000' ? 'EnumRoleLevel' : 'EnumSearchRoleLevel'
    },
    {
      title: '角色类型',
      dataIndex: 'roleType',
      valueType: ValueTypeEnum.tag,
      color: EnumGuardRoleTypeColor,
      dictkey: 'EnumGuardRoleType'
    },
    {
      title: '角色所属条线',
      dataIndex: 'bizLine',
      valueType: ValueTypeEnum.tag,
      color: EnumRoleBizLineColor,
      dictkey: 'EnumRoleBizLine'
    },
    {
      title: '角色描述',
      dataIndex: 'roleDesc',
      width: 220
    },
    {
      title: '角色状态',
      dataIndex: 'roleStatus',
      valueType: ValueTypeEnum.status,
      dictkey: 'EnumGuardRoleStatus',
      color: EnumRoleStatusColor,
      fixed: 'right'
    }
  ];

  const formItemList: FormItemListProps[] = [
    {
      placeholder: '角色名称',
      key: 'roleName',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '角色编号',
      key: 'roleId',
      type: 'input',
      position: 'show'
    }
  ];

  // 提交事件
  const onSubmit = () => {
    const { handleSubmit } = props;
    handleSubmit && handleSubmit({ rows: selectedRows[0] });
    modal.hide();
  };

  return (
    <YRModal
      title={'选择角色'}
      open={modal.visible}
      onCancel={modal.hide}
      afterClose={modal.remove}
      okText="确定"
      width={'60%'}
      bodyStyle={{ height: '80%' }}
      onOk={onSubmit}
      destroyOnClose
    >
      <YRTable
        business="home"
        form={form}
        rowKey="roleId"
        columns={columns}
        rowSelection={rowSelection}
        handleSearch={() => {
          const vlaues = form.getFieldsValue() as object;
          run({ ...vlaues, current: 1, pageSize: 30 });
        }}
        formItemList={formItemList}
        {...tableProps}
      />
    </YRModal>
  );
};

export default YREasyUseModal.create(AddRoleModal);
