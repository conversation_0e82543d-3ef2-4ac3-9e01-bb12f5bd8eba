/*
 * @Description: file content
 * @Author: your name
 * @Date: 2020-10-24 14:46:20
 * @LastEditors: your name
 * @LastEditTime: 2020-11-06 13:42:23
 */
import React, { useEffect } from 'react';
import { YRForm, YRInput, YRSelect, YRRow, YRCol } from 'yrantd';
import LittleModal from '@/components/LittleModal';
import RegexRules from '@/utils/regex';

const FormItem = YRForm.Item;
const { Option } = YRSelect;

const formItemLayout = {
  labelCol: {
    span: 6
  },
  wrapperCol: {
    span: 18
  }
};

const formItemLayout1 = {
  labelCol: {
    span: 3
  },
  wrapperCol: {
    span: 20
  }
};

const formItemLayoutWithoutLabel = {
  labelCol: {
    span: 0
  },
  wrapperCol: {
    span: 22
  }
};
const ModalCommon = (props) => {
  const [form] = YRForm.useForm();
  const { type, initValues = {}, title, children } = props;
  const cancel = () => {
    form.resetFields();
  };

  useEffect(() => {
    if (initValues && Object.keys(initValues).length > 0) {
      form.setFieldsValue(initValues);
    } else {
      form.resetFields();
    }
  }, [initValues]);

  // 提交方法
  const submit = ({ callback }) => {
    const { handleOk } = props;
    form.validateFields().then((values) => {
      const params = values;
      callback(false);
      handleOk(type, params);
    });
  };

  const generateFormFields = () => {
    const { getFieldDecorator } = form;
    return (
      <YRForm form={form}>
        <YRRow gutter={16}>
          <YRCol span={12}>
            <FormItem
              label="参数名"
              {...formItemLayout}
              name="fieldCode"
              initValues={initValues.fieldCode || ''}
              rules={[
                { required: true, message: '请输入参数名!' },
                { max: 20, message: '长度不能超过20！' },
                { pattern: RegexRules.unchinese, message: '禁止输入中文！' },
                { message: '不能有空格！', pattern: RegexRules.unSpace }
              ]}
            >
              <YRInput placeholder="参数名" disabled={type !== 'add'} />
            </FormItem>
          </YRCol>
          <YRCol span={12}>
            <FormItem
              {...formItemLayoutWithoutLabel}
              name="fieldName"
              initValues={initValues.fieldName || ''}
              rules={[
                { required: true, message: '请输入参数说明!' },
                { max: 20, message: '长度不能超过20!' },
                { message: '不能有空格！', pattern: RegexRules.unSpace }
              ]}
            >
              <YRInput placeholder="参数说明" />
            </FormItem>
          </YRCol>
          <YRCol span={24}>
            <FormItem
              label="值"
              {...formItemLayout1}
              name="fieldVal"
              initValues={initValues.fieldVal || ''}
              rules={[
                { required: true, message: '请输入参数值' },
                { message: '不能有空格！', pattern: RegexRules.unSpace }
              ]}
            >
              <YRInput placeholder="参数值" />
            </FormItem>
          </YRCol>
        </YRRow>
      </YRForm>
    );
  };

  return (
    <LittleModal title={title} onOk={submit} onCancel={() => {}} width={700} content={generateFormFields()}>
      {children}
    </LittleModal>
  );
};

export default ModalCommon;
