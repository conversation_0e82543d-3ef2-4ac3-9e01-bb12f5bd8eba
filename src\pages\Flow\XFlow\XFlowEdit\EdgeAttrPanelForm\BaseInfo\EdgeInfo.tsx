/**
 * @Author: wangyw26123
 * @Description: 基本信息-节点信息
 * @Date: Created in 2022-04-25 16:22:47
 * @Modifed By:
 */

import React, { useState } from 'react';
import { YRForm, YRInput, YRInputNumber, YRSelect } from 'yrantd';
import { uuid } from 'yr-loan-antd/lib/util';
import { customNodeIdPrefix } from '@/pages/Flow/XFlow/constants';
import { useDebounceEffect, useRequest, useSetState } from 'ahooks';
import { selectList } from '@/services/flow';
import { isObjectEmpty } from '@/utils/utils';

const FormItem = YRForm.Item;

const EdgeInfo = (props) => {
  const {
    globalDisabled,
    globalFormData,
    targetData: { nodeFormData = {}, name },
    form
  } = props;
  const [paramsItem, setParamsItem] = useSetState({
    fieldsList: [],
    operatorsList: [],
    variableList: []
  });
  // 当前选中的字段的坐标
  const [selectedIndex, setSelectedIndex] = useState<string>('0');
  // 当前变量表单的表单类型
  const [vType, setVType] = useState<string>('isString');
  // 字段的值
  const _field = YRForm.useWatch(['conditionsequenceflow', 'expression', 'field'], form) || '';
  // 符号的值
  const _operators = YRForm.useWatch(['conditionsequenceflow', 'expression', 'operators'], form) || '';
  // 变量的值
  const _variable = YRForm.useWatch(['conditionsequenceflow', 'expression', 'variable'], form) || '';

  // id的默认值必须是静态的，不能是动态的(edgeFormData.id || `SequenceFlow_${uuid().substr(0, 7).toLowerCase()}`),因为id会频繁更新
  const [defaultId] = useState(() => `${customNodeIdPrefix[name]}_${uuid().substr(0, 7).toLowerCase()}`);

  /** 查询参数项接口 */
  const { loading: selectListLaoding, run: selectListRequest } = useRequest(selectList, {
    manual: true,
    onSuccess: (result: any) => {
      const { errorMessage, data } = result;
      if (errorMessage === null) {
        const { fieldsList, operatorsList, variableList } = data?.reduce(
          (acc, item) => {
            acc.fieldsList.push(item?.field);
            acc.operatorsList.push(item?.operators);
            acc.variableList.push(item?.variable);
            return acc;
          },
          { fieldsList: [], operatorsList: [], variableList: [] }
        );
        setParamsItem({ fieldsList, operatorsList, variableList });
      }
    }
  });

  /** 从全局中获取到参数库 id，调用获取参数项接口 */
  useDebounceEffect(() => {
    const { paramLibraryId } = globalFormData;
    if (paramLibraryId !== []) {
      selectListRequest({ paramLibraryId });
    }
  }, [globalFormData]);

  // 选择完字段之后，获取到当前字段的表单类型
  useDebounceEffect(() => {
    const variable = paramsItem?.variableList[selectedIndex];
    const { values, isSelect, isNumber, isDecimal } = variable || {};
    let flag;
    switch (true) {
      case isSelect:
        flag = 'isSelect';
        break;
      case isNumber:
        flag = 'isNumber';
        break;
      case isDecimal:
        flag = 'isDecimal';
        break;
      default:
        flag = 'isString';
    }
    setVType(flag);
  }, [selectedIndex]);

  useDebounceEffect(() => {
    // 获取到当前字段的 display
    const _fieldDisplay = (paramsItem?.fieldsList?.find((item: any) => item?.value === _field) as any)?.display || '';
    const _operatorDisplay =
      paramsItem?.operatorsList[selectedIndex]?.find((item: any) => item?.value === _operators)?.display || '';
    const _name: string = _fieldDisplay + _operatorDisplay + _variable || '';
    const _static = _field ? '${ ' + _field + ' ' + _operators + ' ' + _variable + ' }' : '';
    form.setFieldsValue({ name: _name || '', conditionsequenceflow: { expression: { staticValue: _static } } });
  }, [_field, _operators, _variable, selectedIndex]);

  return (
    <>
      {/* 声明表单 */}
      <FormItem name="displayName" initialValue={nodeFormData.displayName} hidden>
        <YRInput />
      </FormItem>
      <FormItem name="nodeType" initialValue={nodeFormData.nodeType} hidden>
        <YRInput />
      </FormItem>
      <FormItem name="conditionExpress" initialValue={nodeFormData.conditionExpress} hidden>
        <YRInput />
      </FormItem>
      <FormItem name="sourceRef" initialValue={nodeFormData.sourceRef} hidden>
        <YRInput />
      </FormItem>
      <FormItem name="targetRef" initialValue={nodeFormData.targetRef} hidden>
        <YRInput />
      </FormItem>
      <FormItem name="lineType" initialValue={nodeFormData.lineType} hidden>
        <YRInput />
      </FormItem>
      <FormItem name="defaultFlow" initialValue={'false'} hidden>
        <YRInput />
      </FormItem>
      <FormItem name={['conditionsequenceflow', 'expression', 'type']} initialValue={'static'} hidden>
        <YRInput />
      </FormItem>
      <FormItem
        name="id"
        label="编号"
        rules={[
          {
            required: true,
            whitespace: true,
            message: '请输入节点编号'
          }
        ]}
        initialValue={nodeFormData.id || defaultId}
      >
        <YRInput disabled={globalDisabled} placeholder="请输入" maxLength={32} />
      </FormItem>
      <FormItem name="name" label="名称" initialValue={nodeFormData.name}>
        <YRInput disabled={globalDisabled} placeholder="请输入" maxLength={64} />
      </FormItem>
      <FormItem
        name={['conditionsequenceflow', 'expression', 'staticValue']}
        initialValue={nodeFormData?.conditionsequenceflow?.expression?.staticValue}
        hidden
      >
        <YRInput />
      </FormItem>
      <FormItem
        label="字段"
        tooltip="如果没有选项，请先配置参数库"
        name={['conditionsequenceflow', 'expression', 'field']}
        initialValue={nodeFormData?.conditionsequenceflow?.expression?.field}
      >
        <YRSelect
          placeholder="请选择字段"
          onChange={(e) => {
            const { fieldsList } = paramsItem;
            let index = '';
            fieldsList?.forEach((_, _index) => {
              if (e === _?.value) {
                index = _index;
              }
            });
            setSelectedIndex(index);
          }}
        >
          {paramsItem?.fieldsList?.map((item, index) => (
            <YRSelect.Option value={item?.value} key={index}>
              {item?.display}
            </YRSelect.Option>
          ))}
        </YRSelect>
      </FormItem>
      <FormItem
        label="算数符"
        name={['conditionsequenceflow', 'expression', 'operators']}
        initialValue={nodeFormData?.conditionsequenceflow?.expression?.operators}
      >
        <YRSelect placeholder="请输入变量">
          {!isObjectEmpty(paramsItem) &&
            paramsItem?.operatorsList[selectedIndex]?.map((item, index) => (
              <YRSelect.Option value={item?.value} key={item?.index}>
                {item?.display}
              </YRSelect.Option>
            ))}
        </YRSelect>
      </FormItem>
      <FormItem
        label="值"
        name={['conditionsequenceflow', 'expression', 'variable']}
        initialValue={nodeFormData?.conditionsequenceflow?.expression?.variable}
      >
        {(() => {
          switch (vType) {
            case 'isSelect':
              return <YRSelect />;
            case 'isNumber':
            case 'isDecimal':
              return <YRInputNumber style={{ width: '100%' }} />;
            default:
              return <YRInput />;
          }
        })()}
      </FormItem>
    </>
  );
};

export default EdgeInfo;
