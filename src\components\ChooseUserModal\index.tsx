/**
 * @页面描述: 选择用户
 * @文件名 index.tsx
 * @Path src\components\ChooseUserModal\index.tsx
 * @Date 2023-03-10 14:45:04
 * <AUTHOR>
 */

import React, { useState } from 'react';
import {
  FormItemListProps,
  YRBadge,
  YRDict,
  YREasyUseModal,
  YRForm,
  YRMessage,
  YRModal,
  YRTable,
  YRTableProps,
  YRTableRowContent
} from 'yrantd';
import { EnumGuardUserStatusColor } from '../../constant/StyleConst';
import { useAntdTable } from 'ahooks';
import { antdModal } from '@/utils/utils';
import { TableRowSelection } from 'antd/lib/table/interface';
import { queryUserPageInfo } from '../../services/user';

const ChooseUserModal = YREasyUseModal.create(({ currentOrganId }) => {
  // currentOrganId 从列表页传入进来的orgnId
  const modal = YREasyUseModal.useModal();
  const [selectUserList, setSelectUserList] = useState<any[]>([]);
  const [form] = YRForm.useForm();
  const { run, tableProps } = useAntdTable((param) => {
    return queryUserPageInfo({ ...param, pageNum: param.current, organId: currentOrganId }).then((res) => {
      if (res?.success) {
        return {
          list: res?.data.list || [],
          total: res?.data.total || 0
        };
      } else {
        return {
          list: [],
          total: 0
        };
      }
    });
  });
  const columns: YRTableProps['columns'] = [
    // {
    //   title: '用户编号',
    //   dataIndex: 'userId',
    //   width: 220,
    //   render: (value: string) => value || CONST.null
    // },
    {
      title: '用户名称',
      dataIndex: 'userName',
      width: 120,
      render: (value: string) => value || CONST.null
    },
    {
      title: '登录账号',
      dataIndex: 'accountNo',
      width: 120,
      render: (value: string) => value || CONST.null
    },
    {
      title: '角色',
      dataIndex: 'role',
      width: 200,
      render: (value: string, row) => {
        const userIdentifyDtoList = row?.userIdentifyDtoList || [];
        const roleDtoList = [];
        userIdentifyDtoList.forEach((item) => {
          // @ts-ignore
          item.roleDtoList?.forEach((x: any) => roleDtoList.push(x));
        });
        return (
          <>
            {
              // eslint-disable-next-line array-callback-return
              userIdentifyDtoList.map((y) => {
                if (y.identityRelationType === '001') {
                  const mainRoles = y?.roleDtoList?.map((item: any) => item.roleName);
                  return (
                    <YRTableRowContent key={y.identifyId} title="本职角色">
                      {mainRoles?.join('，')}
                    </YRTableRowContent>
                  );
                }
                if (y.identityRelationType === '002') {
                  const sideRoles = y?.roleDtoList?.map((item: any) => item.roleName);
                  return (
                    <YRTableRowContent key={y.identifyId} title="兼职角色">
                      {sideRoles?.join('，')}
                    </YRTableRowContent>
                  );
                }
                return '';
              })
            }
          </>
        );
      }
    },
    {
      title: '所属机构',
      dataIndex: 'organName',
      width: 200,
      render: (value: string, row) => {
        return value || CONST.null;
      }
    },
    {
      title: '手机号码',
      dataIndex: 'mobile',
      width: 200,
      render: (value: string) => value || CONST.null
    },
    {
      title: '用户状态',
      dataIndex: 'userStatus',
      width: 100,
      fixed: 'right',
      render: (value: string) => {
        if (!value) return CONST.null;
        return (
          <YRBadge
            color={EnumGuardUserStatusColor[value]}
            text={<YRDict.Text dictkey="EnumGuardUserStatus" defaultValue={value} />}
          />
        );
      }
    }
  ];

  const formItemList: FormItemListProps[] = [
    {
      placeholder: '用户名称',
      key: 'userName',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '用户状态',
      key: 'userStatus',
      type: 'dictSelect',
      dictkey: 'EnumGuardUserStatus',
      position: 'show'
    },
    {
      placeholder: '登录账号',
      key: 'accountNo',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '手机号',
      key: 'mobile',
      type: 'input',
      position: 'show'
    }
  ];

  const rowSelection: TableRowSelection<any> = {
    onChange: (rowkeys, rowSelect) => {
      setSelectUserList(rowSelect);
    },
    type: 'checkbox'
  };

  const submit = () => {
    if (selectUserList.length) {
      YRModal.confirm({
        title: '确定要进行该操作吗？',
        content: '',
        onOk: () => {}
      });
    } else {
      YRMessage.warn('请先选择用户');
    }
  };

  const handleSearch = (): void => {
    const values: any = form.getFieldsValue();
    run(values);
  };

  return (
    <YRModal {...antdModal(modal)} title="选择用户" size="large" onOk={submit}>
      <YRTable
        form={form}
        columns={columns}
        {...tableProps}
        scroll={{ x: 200, y: 500 }}
        rowSelection={rowSelection}
        rowKey={(row) => row.userId}
        formItemList={formItemList}
        handleSearch={handleSearch}
      />
    </YRModal>
  );
});
export default ChooseUserModal;
