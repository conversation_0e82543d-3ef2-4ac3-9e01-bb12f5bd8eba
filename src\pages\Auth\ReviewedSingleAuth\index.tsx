/*
 * @Author: AI Assistant
 * @Description: 已复核-单笔授权管理页面
 * @Date: 2024-12-19
 */
import React, { useState, useEffect } from 'react';
import { YRButton, YRTable, YRForm, YRMessage, YRIndexPageLayout, YRModal, YRTreeSelect, YRSelect } from 'yrantd';
import { useRequest } from 'ahooks';
import { useDict } from '@yr/util';
import { M0105 } from '@permit/organ';
import { columns, formItemList } from './useIndex';
import type { 
  ReviewedSingleQueryParams, 
  ApiResponse, 
  PageResponse, 
  ReviewedSingleAuthInfo,
  OrganTreeNode,
  DeptInfo,
  RoleInfo,
  AuthDetailInfo
} from './types';
import {
  queryReviewedSingleAuthList,
  querySingleAuthDetail,
  queryAuthCatalogOrgTree
} from '@/services/batchAuth';

const ReviewedSingleAuth: React.FC = () => {
  // 初始化数据字典
  useDict(['AUTH_STATUS', 'AUTH_CATEGORY']);

  // 机构树数据映射函数：将API返回的数据映射为TreeSelect组件需要的格式
  const mapOrgTreeData = (orgData: any): OrganTreeNode[] => {
    if (!orgData) return [];

    const mapNode = (node: any): OrganTreeNode => {
      return {
        orgId: node.orgId || node.id || '',
        orgName: node.orgName || node.name || node.title || '',
        children: node.children && node.children.length > 0
          ? node.children.map(mapNode)
          : undefined
      };
    };

    // 处理API响应中的机构树数据
    if (Array.isArray(orgData)) {
      return orgData.map(mapNode);
    } else if (orgData.categoryList && Array.isArray(orgData.categoryList)) {
      return orgData.categoryList.map(mapNode);
    } else if (orgData.children && Array.isArray(orgData.children)) {
      return orgData.children.map(mapNode);
    }

    return [];
  };

  const [form] = YRForm.useForm();
  const [dataSource, setDataSource] = useState<ReviewedSingleAuthInfo[]>([]);
  
  // 下拉框数据
  const [organTreeData, setOrganTreeData] = useState<OrganTreeNode[]>([]);
  const [deptOptions, setDeptOptions] = useState<DeptInfo[]>([]);
  const [roleOptions, setRoleOptions] = useState<RoleInfo[]>([]);

  // 分页数据
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) =>
      `共 ${total} 条记录，当前显示第 ${range[0]}-${range[1]} 条`
  });

  // 查询已复核授权列表
  const {
    loading: queryLoading,
    run: queryReviewedAuthList,
    refresh: refreshList
  } = useRequest(
    async (params: ReviewedSingleQueryParams) => {
      // 添加状态过滤条件，查询已生效或其他已完成复核状态的记录
      const queryParams = {
        ...params,
        authStatus: params.authStatus || '040' // 默认查询已生效状态
      };
      return await queryReviewedSingleAuthList(queryParams);
    },
    {
      onSuccess: (res: ApiResponse<PageResponse<ReviewedSingleAuthInfo>>) => {
        if (res?.rpcResult === 'SUCCESS' && !res?.errorMessage) {
          const { data } = res;
          setDataSource(data.list || []);
          setPagination((prev) => ({
            ...prev,
            current: data.pageNum,
            total: data.total
          }));
        } else {
          YRMessage.error(res?.errorMessage || '查询失败');
        }
      },
      onError: (error) => {
        console.error('查询已复核授权列表失败:', error);
        YRMessage.error('查询失败，请稍后重试');
        setDataSource([]);
        setPagination((prev) => ({
          ...prev,
          current: 1,
          total: 0
        }));
      },
      manual: true
    }
  );

  // 初始化查询
  useEffect(() => {
    handleSearch();
    // 初始化下拉框数据
    initSelectData();
  }, []);

  // 初始化下拉框数据
  const initSelectData = async () => {
    try {
      // 调用机构树查询接口
      const orgTreeRes = await queryAuthCatalogOrgTree({
        queryAuthFlag: "1",
        logPermitId: "kylin-admin-M0105I1",
        logOperateName: "查询所有一级机构"
      });
      if (orgTreeRes?.rpcResult === 'SUCCESS' && orgTreeRes?.data) {
        const mappedOrgTree = mapOrgTreeData(orgTreeRes.data);
        setOrganTreeData(mappedOrgTree);
        console.log('机构树数据加载成功:', mappedOrgTree);
      } else {
        console.warn('机构树数据加载失败:', orgTreeRes?.errorMessage);
        YRMessage.warning('机构树数据加载失败，请刷新页面重试');
        // 设置默认的机构树数据
        setOrganTreeData([
          {
            orgId: '1',
            orgName: '总行',
            children: [
              { orgId: '101', orgName: '风险管理部' },
              { orgId: '102', orgName: '信贷管理部' }
            ]
          }
        ]);
      }

      // TODO: 调用实际的接口获取部门、角色数据
      setDeptOptions([
        { deptId: '1001', deptName: '风险合规部', orgId: '101' },
        { deptId: '1002', deptName: '授信审批部', orgId: '102' }
      ]);

      setRoleOptions([
        { roleId: 'R001', roleName: '风险经理', roleCode: 'RISK_MANAGER' },
        { roleId: 'R002', roleName: '授信经理', roleCode: 'CREDIT_MANAGER' }
      ]);
    } catch (error) {
      console.error('初始化下拉框数据失败:', error);
      YRMessage.error('初始化数据失败，请刷新页面重试');
      // 设置默认的机构树数据
      setOrganTreeData([
        {
          orgId: '1',
          orgName: '总行',
          children: [
            { orgId: '101', orgName: '风险管理部' },
            { orgId: '102', orgName: '信贷管理部' }
          ]
        }
      ]);
    }
  };

  // 查询处理
  const handleSearch = () => {
    form.validateFields().then((formValues: any) => {
      const queryParams: ReviewedSingleQueryParams = {
        pageNum: 1,
        pageSize: pagination.pageSize,
        authNo: formValues.authNo,
        authStatus: formValues.authStatus,
        orgId: formValues.orgId,
        deptId: formValues.deptId,
        roleId: formValues.roleId,
        creditCategory: formValues.creditCategory
      };

      console.log('查询参数:', queryParams);
      setPagination((prev) => ({ ...prev, current: 1 }));
      queryReviewedAuthList(queryParams);
    }).catch((error) => {
      console.error('表单验证失败:', error);
      YRMessage.error('查询条件验证失败');
    });
  };

  // 分页变化处理
  const handlePaginationChange = (page: number, pageSize?: number) => {
    const newPagination = {
      ...pagination,
      current: page,
      pageSize: pageSize || pagination.pageSize
    };
    setPagination(newPagination);

    form.validateFields().then((formValues: any) => {
      const queryParams: ReviewedSingleQueryParams = {
        pageNum: page,
        pageSize: pageSize || pagination.pageSize,
        authNo: formValues.authNo,
        authStatus: formValues.authStatus,
        orgId: formValues.orgId,
        deptId: formValues.deptId,
        roleId: formValues.roleId,
        creditCategory: formValues.creditCategory
      };
      console.log('分页查询参数:', queryParams);
      queryReviewedAuthList(queryParams);
    }).catch((error) => {
      console.error('分页查询表单验证失败:', error);
      YRMessage.error('查询条件验证失败');
    });
  };

  // 详情处理
  const handleDetail = (record: ReviewedSingleAuthInfo) => {
    // 查询详情数据
    querySingleAuthDetail({ authBaseId: record.authBaseId })
      .then((res: ApiResponse<AuthDetailInfo>) => {
        if (res?.rpcResult === 'SUCCESS') {
          const detailData = res.data;
          YRModal.info({
            title: '授权详情',
            width: 800,
            content: (
              <div>
                <p><strong>授权编号：</strong>{detailData.authNo}</p>
                <p><strong>机构：</strong>{detailData.orgName}</p>
                <p><strong>部门：</strong>{detailData.deptName}</p>
                <p><strong>角色：</strong>{detailData.roleName}</p>
                <p><strong>授权类别：</strong>{detailData.authCategory}</p>
                <p><strong>授信类别：</strong>{detailData.creditCategory}</p>
                <p><strong>授权状态：</strong>{detailData.authStatus}</p>
                <p><strong>生效日期：</strong>{detailData.effectBeginDate}</p>
                <p><strong>失效日期：</strong>{detailData.effectEndDate}</p>
                <p><strong>复核通过日期：</strong>{detailData.approveDate}</p>
                <p><strong>登记人：</strong>{detailData.operatorName}</p>
                <p><strong>登记机构：</strong>{detailData.ownOrganName}</p>
                <p><strong>复核人：</strong>{detailData.approverName || '系统'}</p>
                <p><strong>复核意见：</strong>{detailData.approveRemark || '无'}</p>
              </div>
            )
          });
        } else {
          YRMessage.error(res?.errorMessage || '查询详情失败');
        }
      })
      .catch((error) => {
        console.error('查询授权详情失败:', error);
        // 如果详情接口失败，显示基本信息
        YRModal.info({
          title: '授权详情',
          width: 800,
          content: (
            <div>
              <p><strong>授权编号：</strong>{record.authNo}</p>
              <p><strong>机构：</strong>{record.orgName}</p>
              <p><strong>部门：</strong>{record.deptName}</p>
              <p><strong>角色：</strong>{record.roleName}</p>
              <p><strong>授权类别：</strong>{record.authCategory}</p>
              <p><strong>授信类别：</strong>{record.creditCategory}</p>
              <p><strong>授权状态：</strong>{record.authStatus}</p>
              <p><strong>复核通过日期：</strong>{record.approveDate}</p>
              <p><strong>失效日期：</strong>{record.effectEndDate}</p>
              <p><strong>登记人：</strong>{record.operatorName}</p>
              <p><strong>登记机构：</strong>{record.ownOrganName}</p>
            </div>
          )
        });
      });
  };

  // 动态更新表单项的组件属性
  const dynamicFormItemList = formItemList.map(item => {
    if (item.key === 'orgId') {
      return {
        ...item,
        component: (
          <YRTreeSelect
            style={{ width: '100%' }}
            dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
            placeholder="请选择机构"
            allowClear
            treeDefaultExpandAll
            treeData={organTreeData}
            fieldNames={{ label: 'orgName', value: 'orgId' }}
          />
        )
      };
    }
    if (item.key === 'deptId') {
      return {
        ...item,
        component: (
          <YRSelect
            style={{ width: '100%' }}
            placeholder="请选择部门"
            allowClear
            options={deptOptions.map(dept => ({ label: dept.deptName, value: dept.deptId }))}
          />
        )
      };
    }
    if (item.key === 'roleId') {
      return {
        ...item,
        component: (
          <YRSelect
            style={{ width: '100%' }}
            placeholder="请选择角色"
            allowClear
            options={roleOptions.map(role => ({ label: role.roleName, value: role.roleId }))}
          />
        )
      };
    }
    return item;
  });

  return (
    <YRIndexPageLayout>
      <YRTable
        rowKey="authBaseId"
        form={form}
        dataSource={dataSource}
        handleSearch={handleSearch}
        loading={queryLoading}
        columns={columns(handleDetail)}
        formItemList={dynamicFormItemList}
        pagination={{
          ...pagination,
          onChange: handlePaginationChange,
          onShowSizeChange: handlePaginationChange
        }}
        scroll={{
          x: 1400
        }}
      />
    </YRIndexPageLayout>
  );
};

export default ReviewedSingleAuth;
