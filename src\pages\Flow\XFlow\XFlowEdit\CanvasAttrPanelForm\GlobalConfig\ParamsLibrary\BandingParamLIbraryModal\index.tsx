/*
 * @Author: liaokt
 * @Description: 流程定义-全局-参数库绑定弹窗
 * @Date: 2023-05-09 16:04:11
 * @LastEditors: liaokt
 * @LastEditTime: 2023-05-09 17:26:24
 */
import { selectPageParamLibrary } from '@/services/flow';
import { isObjectEmpty } from '@/utils/utils';
import { useAntdTable, useDebounceEffect } from 'ahooks';
import React, { useState } from 'react';
import { YREasyUseModal, YRModal, YRTable, YRTableProps } from 'yrantd';

const BandingParamLIbraryModal = YREasyUseModal.create((props: any) => {
  const { onOk, globalFormData, currentId } = props;
  const [state, setState] = useState<any>({
    selectedIds: [],
    selectedRows: {}
  });
  const modal = YREasyUseModal.useModal();

  useDebounceEffect(() => {
    if (!isObjectEmpty(currentId)) {
      setState({
        selectedIds: [currentId]
      });
    }
  }, [currentId]);

  /** 选择项 */
  const rowSelection: any = {
    type: 'radio',
    onChange: (selectedRowKeys, rows) => {
      setState({
        selectedIds: selectedRowKeys,
        selectedRows: rows
      });
    },
    selectedRowKeys: state.selectedIds
  };

  /** 查询引用元素接口 */
  const { tableProps, run, params } = useAntdTable((p) => {
    return selectPageParamLibrary({ ...p, pageNum: p.current, sceneType: globalFormData?.sceneType }).then((res) => {
      return res.success ? { list: res.data.list, total: res.data.total } : { list: [], total: 0 };
    });
  });

  /** 表格项 */
  const columns: YRTableProps['columns'] = [
    {
      title: '参数库名',
      dataIndex: 'libraryName',
      width: 120,
      render: (value: string) => value || CONST.null
    },
    {
      title: '参数库描述',
      dataIndex: 'libraryDesc',
      width: 120,
      render: (value: string) => value || CONST.null
    }
  ];

  /** 确认事件 */
  const onOkCall = () => {
    onOk(state.selectedRows);
    modal.hide();
  };

  return (
    <YRModal
      title={'绑定参数库'}
      open={modal.visible}
      onOk={() => {
        onOkCall();
      }}
      onCancel={modal.hide}
      afterClose={modal.remove}
    >
      <YRTable rowKey={'id'} showIndex={false} {...tableProps} columns={columns} rowSelection={rowSelection} />
    </YRModal>
  );
});

export default BandingParamLIbraryModal;
