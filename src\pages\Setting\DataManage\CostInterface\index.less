@import "~@/global.less";
.mock-gray {
  display: inline-block;
  margin-right: 8px;
  padding: 1px;
  color: fade(#1f2e4a, 20%);
  font-size: 12px;
  line-height: 1;
  vertical-align: text-top;
  background-color: fade(color(~`colorPalette("#1f2e4a", 1) `), 5%);
  border: 1px solid fade(#1f2e4a, 20%);
}
.mock-blue {
  display: inline-block;
  margin-right: 9px;
  padding: 1px;
  color: #1890ff;
  font-size: 12px;
  line-height: 1;
  vertical-align: text-top;
  background-color: fade(@blue-1, 40%);
  border: 1px solid #1890ff;
}
.interface-cont {
  display: flex;
  .column-list {
    flex: 0 0 auto;
    width: 250px;
    height: calc(100vh - 215px);
    overflow-y: auto;
    border-right: 1px solid fade(#1f2e4a, 10%);
    .row {
      padding: 8px 16px;
      background-color: #fff;
      border-bottom: 1px solid fade(#1f2e4a, 10%);
      cursor: pointer;
      &.active,
      &:hover {
        background-color: fade(color(~`colorPalette("#1f2e4a", 1) `), 10%);
      }
      .row-right {
        display: inline-block;
        width: 75%;
        color: fade(#1f2e4a, 40%);
        font-size: 12px;
        vertical-align: top;
        .title {
          overflow: hidden;
          color: fade(#1f2e4a, 70%);
          font-size: 14px;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .code {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
  .column-detail {
    flex: 1 1 auto;
    height: calc(100vh - 215px);
    padding: 8px 16px;
    overflow-y: auto;
    background-color: #fff;
  }
  .column-detail-top {
    display: flex;
    justify-content: space-between;
    .left {
      .text {
        margin-top: 4px;
        color: fade(#1f2e4a, 40%);
        font-size: 12px;
      }
    }
    .right {
      margin-top: 5px;
    }
  }
}

.params-list {
  height: calc(100vh - 215px);
  overflow-y: auto;
}
.params-cont {
  width: 200px;
  padding: 8px;
  font-size: 12px;
  .title {
    margin: 4px 0;
    font-weight: bolder;
  }
  .row {
    display: flex;
    margin: 4px 0;
    .left {
      flex: 0 0 auto;
      max-width: 30%;
      word-break: break-all;
    }
    .right {
      flex: 1 1 auto;
      word-break: break-all;
    }
  }
}
.space {
  margin-left: 8px;
}

.inter-cont-base {
  padding: 0 16px 16px;
  .inter-cont-base-row {
    display: flex;
    margin: 8px 0;
    .title {
      flex: 0 0 auto;
      max-width: 30%;
      color: fade(#1f2e4a, 70%);
      word-break: break-all;
    }
    .text {
      flex: 1 1 auto;
      margin-left: 8px;
      color: fade(#1f2e4a, 40%);
      word-break: break-all;
      &.bg {
        max-height: 300px;
        padding: 4px 8px;
        overflow-y: auto;
        color: fade(#1f2e4a, 70%);
        font-size: 12px;
        background-color: fade(color(~`colorPalette("#1f2e4a", 1) `), 10%);
        border-radius: 4px;
      }
    }
  }
}

.request-cont {
  margin: 0 16px 16px;
}
.request-cont-block {
  position: relative;
  margin-top: 16px;
  padding: 8px 16px;
  border: 1px solid fade(#1f2e4a, 10%);
  .row {
    display: flex;
    margin-top: 8px;
    color: fade(#1f2e4a, 70%);
    &:first-child {
      margin-top: 0;
    }
    .text1 {
      flex: 0 0 auto;
      width: 50px;
      text-align: right;
    }
    .text2 {
      flex: 0 0 auto;
      margin-left: 16px;
      .label {
        display: block;
        padding: 4px;
        color: fade(#1f2e4a, 10%);
        font-size: 12px;
        line-height: 1;
        background-color: fade(fade(#1f2e4a, 10%), 10%);
        border: 1px solid fade(#1f2e4a, 10%);
        border-radius: 4px;
        &.color-text1 {
          color: @purple-4;
          background-color: fade(@purple-6, 10%);
          border-color: @purple-4;
        }
        &.color-text2 {
          color: @blue-2;
          background-color: fade(@blue-2, 10%);
          border-color: @blue-2;
        }
        &.color-text3 {
          color: @orange-6;
          background-color: fade(@orange-6, 10%);
          border-color: @orange-6;
        }
        &.color-text4 {
          color: @blue-6;
          background-color: fade(@blue-6, 10%);
          border-color: @blue-6;
        }
        &.color-text5 {
          color: @geekblue-6;
          background-color: fade(@geekblue-6, 10%);
          border-color: @geekblue-6;
        }
      }
    }
    .text3 {
      flex: 1 1 auto;
      margin-left: 16px;
      font-weight: bolder;
      word-break: break-all;
      margin-right: 60px;
      .text3-space {
        margin-left: 6px;
      }
    }
    .text4 {
      flex: 1 1 auto;
      margin-left: 16px;
      word-break: break-all;
    }
  }
  .actions {
    position: absolute;
    top: 8px;
    right: 8px;
    visibility: hidden;
    :global {
      a {
        margin-left: 4px;
        color: #1890ff;
      }
    }
  }
}
.request-cont-block:hover {
  .actions {
    visibility: visible;
  }
}

.tip-content-base .actions {
  position: absolute;
  top: 3px;
  right: 3px;
  font-size: 14px;
  line-height: 21px;
  visibility: hidden;
}

.mock-params {
  padding: 0 16px 16px;
  .mock-params-oper {
    margin-bottom: 8px;
    .title {
      display: inline-block;
      vertical-align: middle;
    }
    .switch {
      display: inline-block;
      margin-left: 8px;
      vertical-align: middle;
    }
  }
}
