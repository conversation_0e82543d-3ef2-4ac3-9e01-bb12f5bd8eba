# 已复核-单笔授权管理页面

## 页面概述

已复核-单笔授权管理页面用于查看和管理已完成复核的单笔授权记录，主要提供查询、详情查看等功能。该页面是只读性质的，不支持状态变更操作。

## 功能特性

### 1. 查询功能
- **授权编号**: 支持按授权编号精确查询
- **授权状态**: 支持按授权状态下拉选择查询（从数据字典获取）
- **机构**: 支持按机构树形结构选择查询
- **部门**: 支持按部门下拉选择查询
- **角色**: 支持按角色下拉选择查询
- **授信类别**: 支持按授信类别文本输入查询
- **分页查询**: 支持分页显示，提高查询性能
- **多条件组合**: 支持多个查询条件的组合查询

### 2. 列表展示
按照要求显示以下字段：
- **授权编号**: 唯一标识，固定在左侧
- **机构**: 授权所属机构
- **部门**: 授权所属部门
- **角色**: 授权关联角色
- **授权分类**: 授权类别（显示中文名称）
- **授信类别**: 授信业务类别
- **授权状态**: 当前授权状态（显示中文名称）
- **复核通过日期**: 授权复核通过的日期
- **失效日期**: 授权失效日期

### 3. 操作功能
每条记录提供以下操作：
- **详情**: 查看授权详细信息（弹窗展示）

## 数据结构

### 已复核单笔授权信息 (ReviewedSingleAuthInfo)
```typescript
interface ReviewedSingleAuthInfo {
  authBaseId: string;       // 授权ID
  authNo: string;           // 授权编号
  orgId: string;            // 机构ID
  orgName: string;          // 机构名称
  deptId: string;           // 部门ID
  deptName: string;         // 部门名称
  roleId: string;           // 角色ID
  roleName: string;         // 角色名称
  authCategory: string;     // 授权类别
  creditCategory: string;   // 授信类别
  authStatus: string;       // 授权状态
  effectBeginDate: string;  // 生效日期
  effectEndDate: string;    // 失效日期
  approveDate: string;      // 复核通过日期
  operatorName: string;     // 操作人
  ownOrganName: string;     // 登记机构
  approverName?: string;    // 复核人
  approveRemark?: string;   // 复核意见
  // ... 其他字段
}
```

## 技术实现

### 组件结构
```
ReviewedSingleAuth/
├── index.tsx           # 主组件
├── types.ts           # 类型定义
├── useIndex.tsx       # 表格配置和工具函数
└── README.md          # 文档说明
```

### 主要依赖
- **yrantd**: UI组件库
- **ahooks**: React Hooks工具库
- **@yr/util**: 工具函数库和数据字典

### 权限控制
- 页面权限: `M010505` (已复核-单笔)
- 接口权限: 通过授权相关接口获取

## API接口

### 1. 查询接口
- **接口**: `queryReviewedSingleAuthList`
- **路径**: `/authBaseInfoQueryController/queryReviewedPage`
- **功能**: 分页查询已复核单笔授权列表

### 2. 详情查询接口
- **接口**: `querySingleAuthDetail`
- **路径**: `/authBaseInfoQueryController/queryDetail`
- **功能**: 查询单笔授权详细信息

## 使用说明

### 1. 查询流程
1. 在查询条件区域输入或选择查询条件
2. 点击"查询"按钮执行查询
3. 点击"重置"按钮清空查询条件

### 2. 详情查看
1. 在列表中找到需要查看的授权记录
2. 点击"详情"按钮查看详细信息
3. 详情弹窗显示完整的授权信息，包括复核信息

### 3. 查询条件说明
- **授权编号**: 支持精确匹配查询
- **授权状态**: 下拉选择器，从数据字典 'AUTH_STATUS' 获取
- **机构**: 树形选择器，支持多级机构选择
- **部门**: 下拉选择器，根据选择的机构动态加载
- **角色**: 下拉选择器，显示所有可用角色
- **授信类别**: 文本输入框，支持模糊匹配

## 业务规则

### 1. 数据过滤
- 页面显示状态为"已生效"(040)或其他已完成复核状态的单笔授权记录
- 支持多条件组合查询

### 2. 只读性质
- 页面主要用于查询和查看，不支持状态变更操作
- 不提供复核操作按钮（复核通过/复核退回）

### 3. 详情展示
- 详情弹窗显示完整的授权信息
- 包含复核相关信息（复核人、复核日期、复核意见）

## 与待复核页面的主要区别

### 1. 功能差异
- **移除复核操作**: 不提供复核通过/复核退回按钮
- **添加复核信息**: 显示复核通过日期、复核人等信息
- **查询条件更丰富**: 支持更多筛选维度

### 2. 数据范围
- **状态过滤**: 显示已完成复核的记录，而非复核中的记录
- **时间维度**: 关注复核通过日期而非登记日期

### 3. 操作权限
- **只读权限**: 主要用于查询和查看
- **无状态变更**: 不支持任何状态变更操作

## 参考实现

### 基于PendingSingleAuth页面
- **代码结构**: 完全复用PendingSingleAuth页面的实现模式
- **组件使用**: 使用相同的UI组件和交互模式
- **数据处理**: 采用相同的数据映射和格式化逻辑
- **错误处理**: 使用相同的错误处理和用户提示方式

### 主要调整
- **移除复核操作**: 删除复核相关的按钮和逻辑
- **增加查询条件**: 添加更多的查询筛选条件
- **调整列定义**: 添加复核通过日期列，调整操作列

## 注意事项

1. **数据安全**: 确保只显示用户有权限查看的授权记录
2. **查询性能**: 大数据量时需要考虑分页和索引优化
3. **用户体验**: 提供清晰的查询条件和结果展示
4. **数据完整性**: 确保显示的复核信息准确完整
5. **权限控制**: 严格按照权限配置控制页面访问

## 更新日志

- 2024-12-19: 初始版本创建
- 基于PendingSingleAuth页面实现
- 实现查询和详情查看功能
- 完成页面布局和交互逻辑
- 集成权限控制和数据字典
