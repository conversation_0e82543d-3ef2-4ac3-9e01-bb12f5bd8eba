/**
 * @Author: wangyw26123
 * @Description: 角色信息
 * @Date: Created in 2022-12-20 15:15:15
 * @Modifed By:
 */
import React, { useEffect, useState } from 'react';
import {
  YRClassificationLayout,
  YRForm,
  YRInput,
  YRTable,
  YRCard,
  YRDict,
  YRRadio,
  YRTextAreaPlus,
  YRButton,
  YRBadge,
  YRLink,
  YRTag,
  YRSelect
} from 'yrantd';
import { QueryString } from '@yr/util';
import type { YRTableProps } from 'yrantd';
import RoleMutexModal from './RoleMutexModal';
import { EnumRoleStatusColor, EnumUserStatus } from '@/constant/role';
import { queryAllRoleList } from '@/services/setting';
import { EnumGuardRoleTypeColor } from '../../../../constant/StyleConst';

interface RoleItem {
  roleId: string;
  roleName: string;
  roleType: string;
  roleStatus: string;
}

interface ExcludeRoleItem {
  excludeRoleId: string;
  excludeRoleName: string;
  excludeRoleType: string;
  excludeRoleStatus: string;
}

/**
 * TODO 客户名单库接口暂未开发 待联调
 * accessIdList mock准入名单李彪
 */
const accessIdList = ['张三', '李四', '王五', 'fake_111', '测试1'];

const RoleInfo = (props) => {
  const { form, mode, roleDetail, query } = props;
  const [openRoleMutexModal, setOpenRoleMutexModal] = useState<boolean>(false);
  const [roleMutexList, setRoleMutexList] = useState<RoleItem[]>([]);
  const [roleList, setRoleList] = useState<ExcludeRoleItem[]>([]);
  const [selectKeys, setSelectKeys] = useState<React.Key[]>([]);
  const [isDetail] = useState<boolean>(() => mode === 'readPretty');
  const { roleId } = query;
  const roleAccessFlagVal = YRForm.useWatch('roleAccessFlag', form);

  useEffect(() => {
    queryAllRoleList().then((res) => {
      if (res.success) {
        setRoleMutexList(filterMutexRoleList(res.data || [], roleId));
      }
    });
  }, []);

  useEffect(() => {
    if (!roleId) return;
    setRoleMutexList(filterMutexRoleList(roleMutexList, roleId));
  }, [roleId]);

  const filterMutexRoleList = (data, id) => {
    if (!id) return data;

    return data.map((role) => {
      return {
        ...role,
        // 禁止选择当前编辑的角色
        disabled: role.roleId === id
      };
    });
  };

  useEffect(() => {
    setRoleList(roleDetail.guardRoleExcludeDtos || []);
  }, [roleDetail.guardRoleExcludeDtos]);

  useEffect(() => {
    form.setFieldValue('guardRoleExcludeDtos', roleList);
  }, [roleList]);

  const onDelRole = (row) => {
    const index = roleList.findIndex((item) => item.excludeRoleId === row.excludeRoleId);

    if (index > -1) {
      const list = [...roleList];
      list.splice(index, 1);
      setRoleList(list);
    }
  };

  const roleMemberColumns: YRTableProps['columns'] = [
    {
      title: '姓名',
      dataIndex: 'userName',
      width: 220,
      render: (value) => value || CONST.null
    },
    {
      title: '手机号',
      dataIndex: 'mobile',
      width: 150,
      render: (value) => value || CONST.null
    },
    {
      title: '登录账号',
      dataIndex: 'accountNo',
      width: 220,
      render: (value) => value || CONST.null
    },
    {
      title: '关联机构',
      dataIndex: 'organName',
      width: 220,
      render: (value) => (
        <div className="ellipsis" title={value}>
          {value || CONST.null}
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'userStatus',
      width: 100,
      render: (value: string) => {
        if (!value) return CONST.null;

        return (
          <YRBadge
            status={EnumUserStatus[value]}
            text={<YRDict.Text dictkey="EnumGuardUserStatus" defaultValue={value} />}
          />
        );
      }
    }
  ];

  const columns: YRTableProps['columns'] = [
    {
      title: '角色编号',
      dataIndex: 'excludeRoleId',
      width: 220,
      render: (value) => value || CONST.null
    },
    {
      title: '角色名称',
      dataIndex: 'excludeRoleName',
      width: 220,
      render: (value) => value || CONST.null
    },
    {
      title: '角色类型',
      dataIndex: 'excludeRoleType',
      width: 220,
      render: (value: string) =>
        value ? (
          <YRTag color={EnumGuardRoleTypeColor[value]}>
            <YRDict.Text dictkey="EnumGuardRoleType" defaultValue={value} />
          </YRTag>
        ) : (
          CONST.null
        )
    },
    {
      title: '角色状态',
      dataIndex: 'excludeRoleStatus',
      width: 100,
      render: (value: string) => {
        if (!value) return CONST.null;

        return (
          <YRBadge
            status={EnumRoleStatusColor[value]}
            text={<YRDict.Text dictkey="EnumGuardRoleStatus" defaultValue={value} />}
          />
        );
      }
    }
  ];

  if (!isDetail) {
    columns.push({
      title: '操作',
      width: 100,
      dataIndex: 'oper',
      render: (_: any, row: any) => (
        <YRLink type="error" onClick={() => onDelRole(row)}>
          移除
        </YRLink>
      )
    });
  }

  const renderFormDetailInfo = () => {
    return [
      <YRForm.Item key="roleId" label="角色编号" name="roleId" initialValue={roleDetail.roleId}>
        <YRInput maxLength={32} placeholder="请输入角色编号" />
      </YRForm.Item>,
      <YRForm.Item key="roleStatus" label="角色状态" name="roleStatus" initialValue={roleDetail.roleStatus}>
        <YRDict.Select dictkey="EnumGuardRoleStatus" />
      </YRForm.Item>,
      <YRForm.Item
        key="registerOrganName"
        label="登记机构"
        name="registerOrganName"
        initialValue={roleDetail.registerOrganName}
      >
        <YRInput />
      </YRForm.Item>,
      <YRForm.Item
        key="registerUserName"
        label="登记人"
        name="registerUserName"
        initialValue={roleDetail.registerUserName}
      >
        <YRInput />
      </YRForm.Item>,
      <YRForm.Item key="registerDate" label="登记日期" name="registerDate" initialValue={roleDetail.registerDate}>
        <YRInput />
      </YRForm.Item>,
      <YRForm.Item
        key="updateOrganName"
        label="更新机构"
        name="updateOrganName"
        initialValue={roleDetail.updateOrganName}
      >
        <YRInput />
      </YRForm.Item>,
      <YRForm.Item key="updateUserName" label="更新人" name="updateUserName" initialValue={roleDetail.updateUserName}>
        <YRInput />
      </YRForm.Item>,
      <YRForm.Item key="updateDate" label="更新日期" name="updateDate" initialValue={roleDetail.updateDate}>
        <YRInput />
      </YRForm.Item>
    ];
  };

  const renderRoleMemberTable = () => {
    return (
      <YRClassificationLayout title="角色成员">
        <YRTable columns={roleMemberColumns} rowKey="userId" dataSource={roleDetail.userDtoList || []} />
      </YRClassificationLayout>
    );
  };

  const getAccessRoleType = YRForm.useWatch('roleAccessFlag', form);
  const bizLine = roleDetail.bizLine && roleDetail.bizLine.split(',');

  return (
    <YRCard title="角色信息" id="roleInfo">
      <YRClassificationLayout.Space>
        <YRClassificationLayout title="角色信息">
          <YRForm form={form} mode={mode}>
            <YRForm.Row>
              <YRForm.Item
                label="角色名称"
                name="roleName"
                initialValue={roleDetail.roleName}
                rules={[{ required: true, message: '请输入角色名称' }]}
              >
                <YRInput maxLength={200} placeholder="请输入角色名称" />
              </YRForm.Item>
              <YRForm.Item
                label="角色级别"
                name="roleLevel"
                initialValue={roleDetail.roleLevel}
                rules={[{ required: true, message: '请选择角色级别' }]}
              >
                <YRDict.Select dictkey="EnumRoleLevel" placeholder="请选择角色级别" />
              </YRForm.Item>
              <YRForm.Item
                label="角色类型"
                name="roleType"
                initialValue={roleDetail.roleType}
                rules={[{ required: true, message: '请选择角色类型' }]}
              >
                <YRDict.Select dictkey="EnumGuardRoleType" placeholder="请选择角色类型" />
              </YRForm.Item>
              <YRForm.Item
                label="是否控制分配层级"
                name="roleRelatedFlag"
                initialValue={roleDetail.roleRelatedFlag}
                rules={[{ required: true, message: '请选择是否控制分配层级' }]}
              >
                <YRRadio.Group>
                  <YRRadio value="Y">是</YRRadio>
                  <YRRadio value="N">否</YRRadio>
                </YRRadio.Group>
              </YRForm.Item>
              <YRForm.Item
                label="是否准入角色"
                name="roleAccessFlag"
                initialValue={roleDetail.roleAccessFlag}
                rules={[{ required: true, message: '请选择是否准入角色' }]}
              >
                <YRRadio.Group>
                  <YRRadio value="Y">是</YRRadio>
                  <YRRadio value="N">否</YRRadio>
                </YRRadio.Group>
              </YRForm.Item>
              {getAccessRoleType === 'Y' && (
                <YRForm.Item
                  label="准入角色类型"
                  name="roleAccessType"
                  initialValue={roleDetail.roleAccessType}
                  rules={[{ required: true, message: '请选择准入角色类型' }]}
                >
                  <YRDict.Select dictkey="EnumGuardAccessRoleType" placeholder="请选择准入角色类型" />
                </YRForm.Item>
              )}
              <YRForm.Item
                label="角色所属条线"
                name="bizLine"
                initialValue={bizLine}
                rules={[{ required: true, message: '请选择角色所属条线' }]}
              >
                <YRDict.Select mode="multiple" dictkey="EnumRoleBizLine" placeholder="请选择角色所属条线" />
              </YRForm.Item>
              {isDetail && renderFormDetailInfo()}
              <YRForm.Item
                label="角色描述"
                name="roleDesc"
                initialValue={roleDetail.roleDesc}
                rules={[{ required: true, message: '请输入角色描述' }]}
              >
                <YRTextAreaPlus maxLength={200} placeholder="请输入角色描述" />
              </YRForm.Item>
              {roleAccessFlagVal === 'Y' && (
                <YRForm.Item label="准入名单" name="accessIdList" initialValue={roleDetail?.accessIdList?.[0]}>
                  <YRSelect placeholder="请选择准入名单">
                    {accessIdList.map((item) => (
                      <YRSelect.Option value={item}>{item}</YRSelect.Option>
                    ))}
                  </YRSelect>
                </YRForm.Item>
              )}
            </YRForm.Row>
          </YRForm>
        </YRClassificationLayout>
        {isDetail && renderRoleMemberTable()}
        <YRClassificationLayout title="互斥角色">
          {!isDetail && (
            <YRButton
              type="primary"
              onClick={() => {
                setSelectKeys(roleList.map((item) => item.excludeRoleId));
                setOpenRoleMutexModal(true);
              }}
            >
              编辑
            </YRButton>
          )}
          <YRForm form={form} style={{ display: 'none' }}>
            <YRForm.Item name="guardRoleExcludeDtos">
              <YRInput />
            </YRForm.Item>
          </YRForm>
          <YRTable columns={columns} rowKey="excludeRoleId" dataSource={roleList} />
        </YRClassificationLayout>
      </YRClassificationLayout.Space>

      {/* 角色互斥 */}
      <RoleMutexModal
        visible={openRoleMutexModal}
        roleMutexList={roleMutexList}
        selectKeys={selectKeys}
        onSubmit={(keys) => {
          const selectedItem = roleMutexList.filter((item) => keys.includes(item.roleId));
          const _roleList = selectedItem.map((role) => {
            return {
              excludeRoleId: role.roleId,
              excludeRoleName: role.roleName,
              excludeRoleType: role.roleType,
              excludeRoleStatus: role.roleStatus
            };
          });
          setRoleList(_roleList);
        }}
        onCancel={() => setOpenRoleMutexModal(false)}
      />
    </YRCard>
  );
};

export default QueryString(RoleInfo);
