/*
 * @Author: DJscript
 * @Date: 2023-03-06 14:26:14
 * @LastEditTime: 2023-03-10 09:27:11
 * @FilePath: /kylin-admin/src/pages/Setting/ServiceParameter/Modal/Add.tsx
 * @Description: 新增/修改业务参数
 */
import React from 'react';
import { YRDict, YREasyUseModal, YRForm, YRInput, YRMessage, YRModal } from 'yrantd';
import { antdModal, setDict } from '@/utils/utils';
import { useRequest } from 'ahooks';
import { addBizParam, modifyBizParam } from '@/services/setting';

setDict('公共业务参数', [{ itemName: '请选择', itemKey: '999' }]);

type YRFormProps = React.ComponentProps<typeof YRForm>;

const Add: React.FC<{ type: YRFormProps['mode']; row?: { [key: string]: string } }> = ({ type, row }) => {
  const modal = YREasyUseModal.useModal();
  const [form] = YRForm.useForm();
  const { run } = useRequest(type === 'add' ? addBizParam : modifyBizParam, {
    manual: true,
    onSuccess: (res) => {
      if (res.success) {
        YRMessage.success(`${type === 'add' ? '新增' : '修改'}成功`);
        modal.resolve();
        modal.hide();
        modal.remove();
      }
    }
  });
  return (
    <YRModal
      {...antdModal(modal)}
      title={`${type === 'add' ? '新增' : '修改'}业务参数`}
      onOk={() => {
        form.validateFields().then((values: any) => {
          run({ ...values, paramId: type === 'add' ? null : row?.paramId });
        });
      }}
    >
      <YRForm form={form} initialValues={row}>
        <YRForm.Row>
          <YRForm.Item label="参数code" name="paramCode">
            <YRInput />
          </YRForm.Item>
          <YRForm.Item label="参数值" name="paramValue">
            <YRInput />
          </YRForm.Item>
          <YRForm.Item label="参数名称" name="paramName">
            <YRInput />
          </YRForm.Item>
          <YRForm.Item label="业务条线类型" name="bizLineType">
            <YRDict.Select dictkey="业务条线类型" />
          </YRForm.Item>
        </YRForm.Row>
      </YRForm>
    </YRModal>
  );
};

export default YREasyUseModal.create(Add);
