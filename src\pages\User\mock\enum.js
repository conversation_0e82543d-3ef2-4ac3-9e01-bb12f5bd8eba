export const EnumCertificateKind = {
  610001: '全国组织机构代码',
  610003: '银行机构代码',
  610005: '企业法人营业执照号码',
  610007: '国税登记证号码',
  610009: '金融许可证号码',
  610011: '基本存款账户开户登记证号码',
  610013: '政府人事部门批文号码',
  610015: '编制委员会批文号码',
  610017: '政府人事部门登记证书号码',
  610019: '编制委员会登记证书号码',
  610021: '军队、武警财务部门开户证明号码',
  610023: '社会团体登记证书号码',
  610025: '民办非企业登记证书号码',
  610027: '外地常设机构驻在地政府主管部门批文号码',
  610029: '国家主管部门颁外国驻华机构批文号码',
  610031: '国家登记机关颁外资企业驻华代表、办事处登记证号码',
  610033: '主管部门颁居民、村民、社区委员会批文号码',
  610035: '独立核算的附属机构批文号码',
  610037: '主管部门批文号码',
  610039: '财政部门证明号码',
  610041: '证券投资业务许可证号码',
  610043: '临时经营地工商行政管理部门批文号码',
  610045: '企业名称预先核准通知书号码',
  610047: '企业营业执照号码',
  610049: '个体工商户营业执照号码',
  610051: '地税登记证号码',
  610053: '宗教事务管理部门的批文或证明号码',
  610055: '借款合同号码',
  610057: '国家外汇管理部门的批复文件号码',
  610059: '主管部门许可证号码',
  610061: '建筑施工及安装合同号码',
  619996: '组织机构占位号码',
  619997: '分支机构替代号码',
  619998: '系统产生组织证件号码',
  619999: '其他机构证件标识',
  610063: '统一社会信用代码',
  610064: '中征码',
  610081: '商业登记证',
  610089: '特种行业许可证',
  610087: '外贸许可证',
  610088: '事业单位法人证书',
  Z00000: '移植的不规范证件号码',
  110001: '居民身份证',
  110003: '临时居民身份证',
  110005: '户口簿',
  110007: '中国人民解放军军人身份证件',
  110009: '中国人民武装警察身份证件',
  110011: '离休干部荣誉证',
  110013: '军官退休证',
  110015: '文职干部退休证',
  110017: '军事院校学员证',
  110019: '港澳居民往来内地通行证',
  110021: '台湾同胞往来大陆通行证',
  110023: '中华人民共和国因私护照',
  110025: '中华人民共和国因公护照',
  110027: '外国护照',
  110029: '外国人居留证',
  119998: '系统使用的个人证件识别标识,在现实中不存在',
  119011: '军官证',
  119013: '士兵证',
  119023: '香港身份证',
  119025: '澳门身份证',
  119027: '台湾身份证',
  119999: '其他个人证件识别标识'
};
