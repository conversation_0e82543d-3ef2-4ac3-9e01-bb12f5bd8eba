/*
 * @Author: DJscript
 * @Date: 2023-03-10 14:48:34
 * @LastEditTime: 2023-03-10 14:53:29
 * @FilePath: /kylin-admin/src/components/YRItem/demo.tsx
 * @Description: demo
 */
import React from 'react';
import { YRButton } from 'yrantd';
import YRItem from '.';

const demo = () => {
  return (
    <div>
      <YRItem
        item={{ name: 'dj', age: 18 }}
        leftRender={(item) => (
          <div>
            <div>{item.name}</div>
            <div>{item.age}</div>
          </div>
        )}
        rightRender={(item) => (
          <YRButton.Space maxWidth={50}>
            <YRButton type="primary">编辑</YRButton>
            <YRButton>删除</YRButton>
          </YRButton.Space>
        )}
      />
    </div>
  );
};

export default demo;
