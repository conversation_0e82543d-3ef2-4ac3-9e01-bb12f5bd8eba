/**
 * @Author: 刘发浩
 * @Description: 流程管理列表
 * @Date: Created in 2022-12-09 16:46:35
 * @Modifed By:
 */

import React, { useState } from 'react';
import { countTableWidth, useDict } from '@yr/util';
import {
  FormItemListProps,
  YRTableProps,
  YRForm,
  YRIndexPageLayout,
  YRTable,
  YRLink,
  YRTreeSelect,
  YRButton,
  YRModal,
  YRMessage,
  YREasyUseModal
} from 'yrantd';
import { ValueTypeEnum } from '@yr/multi-view-table';
import { useAntdTable, useRequest } from 'ahooks';
import { M0105 } from '@permit/organ';
// import { FormOutlined } from '@ant-design/icons';
import RulesAddModal from '../AuthManagement/components/RulesAddModal';

const AuthApproval = () => {
  useDict(['AUTH_STATUS', 'AUTH_CATEGORY', 'USER_TYPE']);
  const [form] = YRForm.useForm();

  const [organData, setOrganData] = useState([] as any[]);
  const [roleData, setRoleData] = useState([] as any[]);
  //   const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  //   const [selectedRows, setSelectedRows] = useState<string[]>([]);

  const { queryFirstLvlOrgList, queryApproveAuthInfo, queryRole, approveRejectAuthRuleInfo, approvePassAuthRuleInfo } =
    M0105.interfaces;

  const { loading: queryOrganTreeLoading } = useRequest(queryFirstLvlOrgList, {
    onSuccess: (res: any) => {
      if (res?.errorMessage === null) {
        const { data } = res;
        setOrganData([data]);
      }
    },
    defaultParams: [
      {
        queryAuthFlag: '0'
      }
    ]
  });

  const { loading: queryRoleLoading, run: queryRoleRequest } = useRequest(queryRole, {
    onSuccess: (res: any) => {
      if (res?.errorMessage === null) {
        const { data } = res;
        setRoleData(data || []);
      }
    },
    manual: true
  });

  //   const { run, loading, fresh } = useRequest(queryApproveAuthInfo, {
  //     onSuccess: (res) => {
  //       const { data, errorMessage } = res;
  //       if (!errorMessage) {
  //         setDataSource(data);
  //       }
  //     }
  //   });

  const { tableProps, run, refresh } = useAntdTable((p) => {
    return queryApproveAuthInfo({ ...p, pageNum: p.current }).then((res) => {
      return res.success ? { list: res.data.list, total: res.data.total } : { list: [], total: 0 };
    });
  });

  //
  const { run: passRun, loading: passLoading } = useRequest(approvePassAuthRuleInfo, {
    onSuccess: (res: any) => {
      if (res?.errorMessage === null) {
        YRMessage.success('通过成功', 0.5, () => {
          refresh();
        });
      }
    },
    manual: true
  });

  const { run: rejectRun, loading: rejectLoading } = useRequest(approveRejectAuthRuleInfo, {
    onSuccess: (res: any) => {
      if (res?.errorMessage === null) {
        YRMessage.success('退回成功', 0.5, () => {
          refresh();
        });
      }
    },
    manual: true
  });

  const formItemList: FormItemListProps[] = [
    {
      placeholder: '授权编号',
      key: 'authNo',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '授权类型',
      key: 'authCategory',
      type: 'dictSelect',
      position: 'show',
      dictkey: 'AUTH_CATEGORY'
    },
    {
      placeholder: '授权状态',
      key: 'authStatus',
      type: 'dictSelect',
      position: 'show',
      dictkey: 'AUTH_STATUS'
    },
    {
      placeholder: '机构',
      key: 'orgId',
      type: 'component',
      position: 'hidden',
      component: (
        <YRTreeSelect
          loading={queryOrganTreeLoading}
          style={{ width: '100%' }}
          dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
          treeData={organData}
          onSelect={(_, node) => {
            const { orgLvl, orgId } = node as { orgId: string; orgLvl: never };
            queryRoleRequest({ orgId, orgLvlList: [orgLvl] });
          }}
          fieldNames={{ label: 'orgName', value: 'orgId' }}
          placeholder="请选择机构"
          treeDefaultExpandAll
          allowClear
        />
      )
    },
    {
      placeholder: '角色',
      key: 'roleId',
      type: 'component',
      position: 'hidden',
      component: (
        <YRTreeSelect
          multiple
          loading={queryRoleLoading}
          style={{ width: '100%' }}
          dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
          treeData={roleData}
          fieldNames={{ label: 'roleName', value: 'roleNo' }}
          placeholder="请选择角色"
          treeDefaultExpandAll
          allowClear
        />
      )
    }
  ];

  const handleSearch = ({ pageNum, pageSize }) => {
    form.validateFields().then((res) => {
      run({ pageNum, pageSize, ...res });
    });
  };

  const passApproval = (authNo) => {
    YRModal.confirm({
      title: '提交复核',
      okText: '确定',
      cancelText: '取消',
      content: '确定要提交复核吗？',
      okButtonProps: {
        loading: passLoading
      },
      onOk: () => {
        passRun({ authNo });
      }
    });
  };

  const rejectApproval = (authNo) => {
    YRModal.confirm({
      title: '退回复核',
      okText: '确定',
      cancelText: '取消',
      content: '确定要退回复核吗？',
      okButtonProps: {
        loading: rejectLoading
      },
      onOk: () => {
        rejectRun({ authNo });
      }
    });
  };

  //   const rowSelection = {
  //     type: 'checkout',
  //     onChange: (selectedRowKeys, rows) => {
  //       setSelectedKeys(selectedRowKeys);
  //       setSelectedRows(rows);
  //     },
  //     selectedRowKeys: selectedKeys
  //   };

  const getTableColumns: YRTableProps['columns'] = [
    {
      title: '授权编号',
      dataIndex: 'authNo',
      width: 280,
      render: (value, record) => {
        const { authNo, authCategory } = record as any;
        if (value) {
          return (
            <YRLink
              type="primary"
              onClick={() => {
                YREasyUseModal.show(RulesAddModal, {
                  mode: 'detail',
                  authNo,
                  authCategory
                });
              }}
            >
              {value}
            </YRLink>
          );
        }
        return '-';
      }
    },
    {
      title: '机构',
      dataIndex: 'orgName',
      valueType: ValueTypeEnum.department
    },
    {
      title: '角色',
      dataIndex: 'roleName',
      width: 120
    },
    {
      title: '授权类型',
      dataIndex: 'authCategory',
      key: 'authCategory',
      valueType: ValueTypeEnum.dict,
      dictkey: 'AUTH_CATEGORY'
    },
    {
      title: '授权状态',
      dataIndex: 'authStatus',
      key: 'authStatus',
      valueType: ValueTypeEnum.dict,
      dictkey: 'AUTH_STATUS'
    },
    {
      title: '生效日期',
      dataIndex: 'effectBeginDate',
      key: 'effectBeginDate',
      valueType: ValueTypeEnum.date
    },
    {
      title: '失效日期',
      dataIndex: 'effectEndDate',
      key: 'effectEndDate',
      valueType: ValueTypeEnum.date
    },
    {
      title: '操作',
      dataIndex: 'operator',
      valueType: ValueTypeEnum.operation,
      fixed: 'right',
      render: (value, record) => {
        const { authNo } = record as any;
        return (
          <YRButton.Overflow>
            <YRLink
              type="primary"
              onClick={() => {
                passApproval(authNo);
              }}
            >
              复核通过
            </YRLink>
            <YRLink
              type="primary"
              onClick={() => {
                rejectApproval(authNo);
              }}
            >
              复核退回
            </YRLink>
          </YRButton.Overflow>
        );
      }
    }
  ];

  //   const operationRender = (
  //     <YRButton.Space>
  //       <YRButton icon={<FormOutlined />} type="primary">
  //         复核退回
  //       </YRButton>
  //       <YRButton icon={<FormOutlined />} type="primary">
  //         复核通过
  //       </YRButton>
  //     </YRButton.Space>
  //   );

  return (
    <YRIndexPageLayout>
      <YRTable
        form={form}
        rowKey="authNo"
        // loading={loading}
        // operationRender={operationRender}
        // rowSelection={rowSelection}
        columns={getTableColumns}
        handleSearch={handleSearch}
        formItemList={formItemList}
        {...tableProps}
        scroll={{
          y: document.body.clientHeight - 258,
          x: countTableWidth(getTableColumns)
        }}
      />
    </YRIndexPageLayout>
  );
};

export default AuthApproval;
