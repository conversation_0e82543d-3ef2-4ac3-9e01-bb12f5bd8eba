import type { ProSettings } from '@yr/pro-layout/lib/defaultSettings';
import type { YRConfigProviderProps } from 'yrantd/lib/yr-config-provider';

const defaultSettings: ProSettings = {
  navTheme: 'light',
  primaryColor: '#1890ff',
  headerTheme: 'light',
  layout: 'mix',
  contentWidth: 'Fluid',
  fixedHeader: false,
  fixSiderbar: true,
  colorWeak: false,
  title: '',
  splitMenus: true
};

export default defaultSettings;

// 组件全局化配置
export const defaultYRConfig: YRConfigProviderProps = {
  // componentSize 属性默认值必须要有，否则主应用控制子应用时 SecurityLayout 组件将会卸载
  componentSize: 'middle',
  yrForm: {
    labelAlign: 'left',
    bordered: true
  },
  yrTable: {
    bordered: true
  },
  yrFullPageLayout: {
    iconPlacement: 'right'
  }
};
