/*
 * yunrong.cn Inc. Copyright (c) 2014-2021 All Rights Reserved
 */

function uuid() {
  var s = [];
  var hexDigits = '0123456789ABCDEF';
  for (let i = 0; i < 32; i += 1) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }
  s[14] = '4'; // 版本4，伪随机数
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1);

  return s.join('');
}

Date.prototype.format = function(fmt) {
  var o = {
    "M+" : this.getMonth()+1,                 //月份
    "d+" : this.getDate(),                    //日
    "H+" : this.getHours(),                   //小时
    "m+" : this.getMinutes(),                 //分
    "s+" : this.getSeconds(),                 //秒
    "q+" : Math.floor((this.getMonth()+3)/3), //季度
    "S"  : this.getMilliseconds()             //毫秒
  };
  if(/(y+)/.test(fmt))
    fmt=fmt.replace(RegExp.$1, (this.getFullYear()+"").substr(4 - RegExp.$1.length));
  for(var k in o)
    if(new RegExp("("+ k +")").test(fmt))
  fmt = fmt.replace(RegExp.$1, (RegExp.$1.length==1) ? (o[k]) : (("00"+ o[k]).substr((""+ o[k]).length)));
  return fmt;
}

function getRequestHeaders() {
  var hsjryRequest = {
    ask: '968',
    answer: '969',
    serialNo: uuid(),
    idemSerialNo: uuid(),
    serviceScene: '002',
    transDateTime: new Date().format('yyyy-MM-dd HH:mm:ss'),
    tenantId: sessionStorage.tenantId||'000',
    channelNo: '002',
    sign: '1',
  };

  var hsjryUserRequest = {
    authId: '1',
    token: '1',
    operatorId: '1',
    operatorName: '1',
    organId: '1',
    organName: '1',
  };

  var hsjryRequestParse = "";
  for (let x in hsjryRequest) {
    hsjryRequestParse += x+"="+hsjryRequest[x]+"&";
  }
  hsjryRequestParse=hsjryRequestParse.substring(0,hsjryRequestParse.length-1);


  var hsjryUserRequestParse = "";
  for (let x in hsjryUserRequest) {
    hsjryUserRequestParse += x+"="+hsjryUserRequest[x]+"&";
  }
  hsjryUserRequestParse=hsjryUserRequestParse.substring(0,hsjryUserRequestParse.length-1);

  return {
    'Content-type': 'application/json; charset=utf-8',
    'Encode-Rpc-Hsjry-Request': CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(hsjryRequestParse)),
    'Encode-Rpc-Hsjry-User-Request': CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(hsjryUserRequestParse))
  }
}
