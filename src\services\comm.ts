import { request } from '@yr/util';

// 用户登录
export function userLogin(param) {
  return request('/loginController/loginByPassword', {
    param,
    module: MODULES.guardSDK,
    serviceScene: SCENES.apply,
    serviceName: '用户登录',
    notCheckLogin: true
  });
}

// 用户登出
export function userLogout(param?: any) {
  return request('/loginController/loginOut', {
    param,
    module: MODULES.guardSDK,
    serviceScene: SCENES.apply,
    serviceName: '用户登出',
    notCheckLogin: true
  });
}

// 查询产品列表
export function queryProductList(params: any) {
  return request('/hsjry/product/productQuery/queryPage', {
    method: 'POST',
    data: params,
    serviceScene: SCENES.apply,
    serviceName: '查询产品列表'
  });
}
