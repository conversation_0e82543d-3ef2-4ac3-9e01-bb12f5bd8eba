import { deleteFlowParamItem, selectPage } from '@/services/flow';
import { useAntdTable, useRequest } from 'ahooks';
import React, { forwardRef, useEffect, useImperativeHandle } from 'react';
import { FormItemListProps, YRButton, YRForm, YRLink, YRMessage, YRModal, YRTable, YRTableProps } from 'yrantd';

const ParamContent = forwardRef(({ ...props }, ref) => {
  const { activity, changeVisible, setCurrentParam } = props as any;
  const [form] = YRForm.useForm();

  useImperativeHandle(ref, () => ({
    refresh
  }));

  /** ==== 接口 ==== */
  const { tableProps, run, params, refresh } = useAntdTable(
    (p) => {
      delete p.extra;
      delete p.filters;
      return selectPage({
        ...p,
        pageNum: p.current
      }).then((res) => {
        return res.success ? { list: res.data.list, total: res.data.total } : { list: [], total: 0 };
      });
    },
    {
      manual: true
    }
  );

  // 删除参数项
  const { loading: deleteLaoding, run: deleteRequest } = useRequest(deleteFlowParamItem, {
    manual: true,
    onSuccess: (result: any) => {
      const { errorMessage, data } = result;
      if (errorMessage === null) {
        YRMessage.success('删除成功', 0.5, () => {
          refresh();
        });
      }
    }
  });

  /** ==== 副作用 ==== */

  useEffect(() => {
    activity && run({ ...params[0], paramLibraryId: activity } as any);
  }, [activity]);

  /** ==== 页面渲染数据 ==== */

  /** 查询表单 */
  const formItemList: FormItemListProps[] = [
    {
      placeholder: '参数中文名',
      key: 'paramItemName',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '参数英文名',
      key: 'paramItemKey',
      type: 'input',
      position: 'show'
    }
  ];

  /** 列表字段 */
  const getTableColumns: YRTableProps['columns'] = [
    {
      title: '参数中文名',
      dataIndex: 'paramItemName',
      width: 140
    },
    {
      title: '参数key',
      dataIndex: 'paramItemKey',
      width: 140
    },
    {
      title: '参数值类型',
      dataIndex: 'paramValueType',
      width: 140
    },
    {
      title: '操作',
      dataIndex: 'operator',
      width: 100,
      fixed: 'right',
      render: (value, row) => {
        return (
          <YRButton.Overflow>
            <YRLink
              type="primary"
              onClick={() => {
                handleEdit(row);
              }}
            >
              修改
            </YRLink>
            <YRLink
              type="primary"
              onClick={() => {
                handleDelete(row);
              }}
            >
              删除
            </YRLink>
          </YRButton.Overflow>
        );
      }
    }
  ];

  /** ==== 方法 ==== */

  /** 搜索方法 */
  const handleSearch = () => {
    const values: any = form.getFieldsValue();
    run({ ...params[0], ...values });
  };

  /** 修改方法 */
  const handleEdit = (row: any) => {
    setCurrentParam(row);
    changeVisible(true);
  };

  /** 删除方法 */
  const handleDelete = (row: any) => {
    const { paramItemName, id, paramLibraryId } = row;
    YRModal.confirm({
      title: '删除',
      content: `确定要删除【${paramItemName}】`,
      okText: '确定',
      cancelText: '取消',
      onOk() {
        deleteRequest({
          paramLibraryId,
          ids: [id]
        });
      }
    });
  };

  return (
    <YRTable
      rowKey="id"
      form={form}
      columns={getTableColumns}
      formItemList={formItemList}
      handleSearch={handleSearch}
      {...tableProps}
    />
  );
});

export default ParamContent;
