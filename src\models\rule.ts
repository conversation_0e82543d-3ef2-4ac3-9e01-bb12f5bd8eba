/**
 * 页面描述: 风险探测规则数据
 * @文件名 rule.ts
 * @filePath \src\models\rule.ts
 * @Date 2023-08-08 11:12:44
 * @Email <EMAIL>
 * <AUTHOR>
 */
import { uuid } from 'yr-loan-antd/lib/util';
import { queryDictList, variableQuery } from '@/services/setting';

export default {
  state: {
    type: 'edit',
    ruleData: {
      junctionType: 'add',
      key: uuid()
    },
    ruleDataMap: {},
    dictList: [],
    parameterList: []
  },
  effects: () => ({
    // 获取数组列表
    getDictList() {
      queryDictList({}).then((res) => {
        const that = this as any;
        if (res.success && res.data) {
          const { data } = res;
          that.setDictList(data);
        }
      });
    },
    // 获取参数列表
    getParameterList(params) {
      variableQuery(params).then((res) => {
        const that = this as any;
        if (res.success && res.data) {
          const list = res?.data?.list || [];
          that.setParameterList(list);
        }
      });
    }
  }),
  reducers: {
    setType(state, data) {
      state.type = data;
    },
    setRuleData(state, data) {
      state.ruleData = data;
    },
    setRuleDataMap(state, data) {
      state.ruleDataMap = data;
    },
    setDictList(state, data) {
      state.dictList = data;
    },
    setParameterList(state, data) {
      state.parameterList = data;
    }
  }
};
