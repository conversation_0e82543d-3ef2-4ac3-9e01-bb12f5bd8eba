/*
 * @Description: file content
 * @Author: your name
 * @Date: 2020-10-23 14:37:11
 * @LastEditors: your name
 * @LastEditTime: 2020-12-16 11:25:42
 */
import React from 'react';
import { YRButton, YREasyUseModal, YRPopconfirm } from 'yrantd';
import ModalResponse from './ModalBoxs/ModalResponse';
import { responseType } from '@/utils/data-dictionary';
import './index.less';

export default function ResponseParams(props) {
  const { data = [], treeList = [], handleOk, handleDelete } = props;
  return (
    <div className="request-cont">
      <YRButton
        icon="+ "
        style={{ width: '100%' }}
        onClick={() => {
          YREasyUseModal.show(ModalResponse, {
            title: '新增响应参数',
            type: 'add',
            handleOk
          });
        }}
      >
        响应参数
      </YRButton>
      {data.map((item, index) => {
        const { fieldName, fieldCn, valExpression } = item;
        let color;
        const typeText = responseType[item.confType];
        if (typeText === '表达式') {
          color = 'color-text3';
        } else if (typeText === '脚本') {
          color = 'color-text4';
        } else {
          color = 'color-text5';
        }
        return (
          <div className={'request-cont-block'} key={index}>
            <div className={'row'}>
              <span className={'text1'}>参数:</span>
              <span className={'text2'}>
                <span className={`label ${color}`}>{typeText}</span>
              </span>
              <span className={'text3'}>
                <span>{fieldName}</span>
                <span className={'text3-space'}>{`[${fieldCn}]`}</span>
              </span>
            </div>
            <div className={'row'}>
              <span className={'text1'}>值:</span>
              <span className={'text4'}>{valExpression}</span>
            </div>
            <div className={'actions'}>
              <a
                style={{ marginRight: 5 }}
                onClick={() => {
                  YREasyUseModal.show(ModalResponse, {
                    title: '修改响应参数',
                    type: 'edit',
                    initValues: item,
                    handleOk
                  });
                }}
              >
                修改
              </a>
              <YRPopconfirm
                title="确定要删除吗？"
                onConfirm={() => {
                  handleDelete(item);
                }}
              >
                <a>删除</a>
              </YRPopconfirm>
            </div>
          </div>
        );
      })}
    </div>
  );
}
