/*
 * @Description: file content
 * @Author: your name
 * @Date: 2020-10-27 17:58:52
 * @LastEditors: your name
 * @LastEditTime: 2020-11-12 14:08:06
 */
import React, { useEffect, useState } from 'react';
import { YRInput, YRSwitch } from 'yrantd';
import './index.less';

const { TextArea } = YRInput;

export default function MockParams(props) {
  const { data, switchFun, comPathCode, saveMockFun } = props;
  const { mockSwitch, mockData } = data;
  const [mockValue, setMockValue] = useState(mockData);
  useEffect(() => {
    setMockValue(mockData);
  }, [comPathCode]);
  const handleChange = (e) => {
    setMockValue(e.target.value);
  };
  return (
    <div className={'mock-params'}>
      <div className={'mock-params-oper'}>
        <span className={'title'}>是否Mock:</span>
        <YRSwitch
          checkedChildren="开"
          unCheckedChildren="关"
          checked={mockSwitch !== 0}
          onChange={switchFun}
          className={'switch'}
        />
      </div>
      <TextArea
        rows={10}
        value={mockValue}
        onBlur={() => {
          saveMockFun(mockValue);
        }}
        onChange={handleChange}
      />
    </div>
  );
}
