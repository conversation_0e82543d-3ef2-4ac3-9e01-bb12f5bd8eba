/**
 * @Author: wangyw26123
 * @Description: 机构编辑
 * @Date: Created in 2022-12-15 10:49:38
 * @Modifed By:
 */
import React, { useEffect, useState } from 'react';
import { Y<PERSON><PERSON>on, YRCard, YRForm, YRMessage } from 'yrantd';
import { closeWin, jsonParse, QueryString } from '@yr/util';
import moment from 'moment';
import store from '@/store';
import { checkAuth, router } from '@/utils/utils';
import { M010101 } from '@permit/organ';
import FullPageLayout from '@/components/FullPageLayout';
import OrganInfoEdit from './components/OrganInfoEdit';
import { EnumDateFormat, EnumMsg } from '@/constant/common';
import type { OrganParams } from './typing';
import Dict from '../mock/getDict';

const { addOrgan } = M010101.interfaces;
const { modifyOrgan } = M010101.E02.interfaces;
const { queryOrganInfoByOrganId } = M010101.E10.interfaces;

const OrganEdit = (props) => {
  const { parentOrganId, mode, organCopyId, subOrgan } = props.query;
  const [form] = YRForm.useForm();
  const [pageLoading, setPageLoading] = useState<boolean>(false);
  const [btnLoading, setBtnLoading] = useState<boolean>(false);
  const [organDetail, setOrganDetail] = useState<any>();
  const [, organDispatch] = store.useModel('organ');
  const userInfo = jsonParse(window.sessionStorage.getItem('userInfo') || '') || {};
  const isCopy = !!organCopyId;

  useEffect(() => {
    organDispatch.getOrganTreeList();
    organDispatch.getAcctOrganTree();
    organDispatch.getHrOrganList();
  }, []);

  useEffect(() => {
    if ((!parentOrganId && !organCopyId) || subOrgan === '001') return;
    setPageLoading(true);

    queryOrganInfoByOrganId({
      organId: parentOrganId || organCopyId
    }).then((res) => {
      setPageLoading(false);
      if (res.success) {
        setOrganDetail(res.data);
        form.resetFields();
      }
    });
  }, [parentOrganId, organCopyId, subOrgan]);

  const onSave = () => {
    if ([M010101.E01, M010101.E02, M010101.E09].every((permit) => !checkAuth(permit))) return;

    form.validateFields().then(() => {
      const values = form.getFieldsValue(true);
      const { pca, organEstablishDate, registerDate, updateDate, ownArea } = values;
      const params: OrganParams = { ...values };

      delete params.updateUserName;
      delete params.registerUserName;

      if (pca) {
        // 后端只需要区，不需要省、市
        params.areaCode = pca[2];

        delete params.pca;
      }

      if (Array.isArray(ownArea)) {
        params.ownArea = ownArea.map((arr) => arr[arr.length - 1]).toString();
      }

      if (organEstablishDate) {
        params.organEstablishDate = organEstablishDate.format(EnumDateFormat.time);
      }
      if (registerDate) {
        params.registerDate = registerDate.format(EnumDateFormat.date);
      }
      if (updateDate) {
        params.updateDate = updateDate.format(EnumDateFormat.date);
      }

      setBtnLoading(true);
      if (mode === 'add') {
        addOrgan(params).then((res) => {
          setBtnLoading(false);
          if (res.success) {
            YRMessage.success(EnumMsg.add);
            closeWin();
          }
        });
      } else {
        // 修改时更新人为当前登录人的信息
        params.updateOrganId = userInfo.organId;
        params.updateUserId = userInfo.operatorId;
        params.updateDate = moment().format('YYYY-MM-DD');

        modifyOrgan(params).then((res) => {
          debugger;
          setBtnLoading(false);
          if (res.success) {
            YRMessage.success(EnumMsg.modify);
            closeWin();
          }
        });
      }
    });
  };

  const getTitle = () => {
    if (isCopy) {
      return '机构复制';
    }

    if (mode === 'add') {
      return '机构新增';
    }

    return '机构修改';
  };

  const left = {
    title: getTitle(),
    goBack: () => router.push('/setting/organ/organ-manage')
  };

  const right = [M010101.E01, M010101.E02, M010101.E09].some((permit) => checkAuth(permit)) && (
    <YRButton type="primary" loading={btnLoading} disabled={btnLoading} onClick={onSave}>
      保存
    </YRButton>
  );

  return (
    <FullPageLayout
      loading={pageLoading}
      left={left}
      right={right}
      contentNoPadding={false}
      contentStyle={{ height: 'auto' }}
    >
      <OrganInfoEdit form={form} mode={mode} parentOrganId={parentOrganId} detail={organDetail || {}} isCopy={isCopy} />
    </FullPageLayout>
  );
};

export default Dict([
  'EnumOrganLevel',
  'EnumOrganType',
  'EnumOrganStatus',
  'EnumOrganBizLine',
  'EnumOrganNature',
  'EnumAcctOrganType',
  'EnumBoolean'
])(QueryString(OrganEdit));
