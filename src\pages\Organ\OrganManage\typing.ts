/**
 * @Author: wangyw26123
 * @Description: 请求参数类型
 * @Date: Created in 2022-12-21 10:07:39
 * @Modifed By:
 */

/**
 * 机构新增/修改 入参
 */
export interface OrganParams {
  /**
   * 机构编号
   */
  organId: string;
  /**
   * 机构名称
   */
  organName: string;
  /**
   * 机构简称
   */
  organShortName?: string;
  /**
   * 机构级别
   */
  organLevel: string;
  /**
   * 所属条线
   */
  bizLine?: string;
  /**
   * 机构类型
   */
  organType: string;
  /**
   * 机构性质
   */
  organNature: string;
  /**
   * 所属放款中心
   */
  loanOrganId: string;
  /**
   * 档案管理机构
   */
  archiveMgrOrganId: string;
  /**
   * 上级机构
   */
  parentOrganId: string;
  /**
   * 对应核心机构
   */
  acctOrganId: string;
  /**
   * 人行金融机构代码
   */
  pbcOrganCode: string;
  /**
   * 所属法人
   */
  legalRepresentativeName?: string;
  /**
   * 合同签约机构
   */
  signOrganId: string;
  /**
   * 营业执照
   */
  businessCert?: string;
  /**
   * 金融机构许可证
   */
  financialCert?: string;
  /**
   * 机构地址
   */
  organAddress?: string;
  /**
   * 机构辖区
   */
  ownArea?: string;
  /**
   * 邮政编码
   */
  zipCode?: string;
  /**
   * 机构曾用名
   */
  organUsedName?: string;
  /**
   * 成立时间
   */
  organEstablishDate?: string;
  /**
   * 负责人
   */
  chargePerson?: string;
  /**
   * 联系电话
   */
  contactTel?: string;
  /**
   * 登记机构
   */
  registerOrganId?: string;
  /**
   * 登记日期
   */
  registerDate?: string;
  /**
   * 更新机构
   */
  updateOrganId?: string;
  /**
   * 更新日期
   */
  updateDate?: string;
  /**
   * 登记人
   */
  registerUserId?: string;
  /**
   * 更新人
   */
  updateUserId?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 省区编码
   */
  provinceCode?: string;
  /**
   * 市区编码
   */
  cityCode?: string;
  /**
   * 区编码
   */
  areaCode?: string;
  [key: string]: any;
}
