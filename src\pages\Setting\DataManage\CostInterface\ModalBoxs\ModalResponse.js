/*
 * @Description: file content
 * @Author: your name
 * @Date: 2020-10-24 14:46:20
 * @LastEditors: your name
 * @LastEditTime: 2020-11-13 10:11:02
 */
import React, { Fragment } from 'react';
import { YRForm, YRInput, YRSelect, YRRow, YRCol, YRTreeSelect, YREasyUseModal, YRModal } from 'yrantd';
import LittleModal from '@/components/LittleModal';
import { responseType } from '@/utils/data-dictionary';
import RegexRules from '@/utils/regex';
import ChooseVariableModal from './ChooseVariableModal';

const FormItem = YRForm.Item;
const { Option } = YRSelect;

const formItemLayout = {
  labelCol: {
    span: 6
  },
  wrapperCol: {
    span: 18
  }
};

const formItemLayout1 = {
  labelCol: {
    span: 2
  },
  wrapperCol: {
    span: 22
  }
};

const formItemLayoutWithoutLabel = {
  labelCol: {
    span: 0
  },
  wrapperCol: {
    span: 24
  }
};
const ModalResponse = (props) => {
  const modal = YREasyUseModal.useModal();
  const [form] = YRForm.useForm();
  const { type, initValues = {}, title, children, treeList = [] } = props;
  const typeVal = YRForm.useWatch('confType', form);

  const cancel = () => {
    form.resetFields();
  };
  // 提交方法
  const submit = () => {
    const { handleOk } = props;
    form.validateFields().then((values) => {
      let params = values;
      if (values.confType === 2) {
        const found = treeList[0].childrens.find((item) => {
          return item.code === values.preConfig.value;
        });
        params = {
          confType: values.confType,
          fieldCn: values.preConfig.label,
          fieldName: values.preConfig.value,
          valExpression: found && found.expression
        };
      }
      modal.remove();
      handleOk(type, params);
    });
  };

  const transfer = (data) => {
    return data.map((item) => {
      const newItem = { title: item.name, value: item.code, key: item.code };
      if (item.childrens) {
        newItem.children = transfer(item.childrens);
      }
      return newItem;
    });
  };

  const selectVariable = (selectedItems) => {
    const itemValue = selectedItems[0];
    form.setFieldValue('fieldName', itemValue?.variableCode);
    form.setFieldValue('fieldCn', itemValue?.variableName);
  };

  const generateFormFields = () => {
    const treeData = transfer(treeList);
    return (
      <YRForm form={form}>
        <YRRow gutter={16}>
          <YRCol span={8}>
            <FormItem
              label="参数"
              {...formItemLayout}
              name="confType"
              initialValue={initValues?.confType || 0}
              rules={[{ required: true }]}
            >
              <YRSelect showSearch disabled={type !== 'add'}>
                {Object.keys(responseType).map((value) => {
                  return (
                    <Option value={Number(value)} key={value}>
                      {responseType[value]}
                    </Option>
                  );
                })}
              </YRSelect>
            </FormItem>
          </YRCol>
          {typeVal === 2 ? (
            <YRCol span={24}>
              <FormItem
                label="值"
                {...formItemLayout1}
                name="preConfig"
                initialValue={{
                  label: initValues?.fieldCn || '',
                  value: initValues?.fieldName || ''
                }}
                rules={[
                  { required: true, message: '请输入参数值' },
                  { message: '不能有空格！', pattern: RegexRules.unSpace }
                ]}
              >
                <YRTreeSelect
                  dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
                  style={{ width: '100%' }}
                  placeholder="请选择"
                  treeData={treeData}
                  showSearch
                  treeDefaultExpandAll
                  labelInValue
                  filterTreeNode={(inputValue, treeNode) =>
                    treeNode.props.title.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                  }
                />
              </FormItem>
            </YRCol>
          ) : (
            <Fragment>
              <YRCol span={8}>
                <FormItem
                  {...formItemLayoutWithoutLabel}
                  name="fieldName"
                  initialValue={initValues?.fieldName || ''}
                  rules={[
                    {
                      required: true,
                      message: '请输入参数'
                    },
                    {
                      pattern: RegexRules.unchinese,
                      message: '不能输入中文！'
                    },
                    {
                      pattern: RegexRules.uncode2,
                      message: '只能输入英文数字和下划线'
                    }
                  ]}
                >
                  <YRInput
                    placeholder="参数名"
                    maxLength={254}
                    disabled={type !== 'add'}
                    onClick={() => YREasyUseModal.show(ChooseVariableModal, { okCallback: selectVariable })}
                  />
                </FormItem>
              </YRCol>
              <YRCol span={8}>
                <FormItem
                  {...formItemLayoutWithoutLabel}
                  name="fieldCn"
                  initialValue={initValues?.fieldCn || ''}
                  rules={[
                    { required: true, message: '请输入参数说明' },
                    { message: '不能有空格！', pattern: RegexRules.unSpace }
                  ]}
                >
                  <YRInput placeholder="参数说明" maxLength={254} />
                </FormItem>
              </YRCol>
              <YRCol span={24}>
                <FormItem
                  label="值"
                  {...formItemLayout1}
                  name="valExpression"
                  initialValue={initValues?.valExpression || ''}
                  rules={[
                    { required: true, message: '请输入参数值' },
                    { message: '不能有空格！', pattern: RegexRules.unSpace }
                  ]}
                >
                  <YRInput placeholder="参数值" />
                </FormItem>
              </YRCol>
            </Fragment>
          )}
        </YRRow>
      </YRForm>
    );
  };

  return (
    <YRModal title={title} open={modal.visible} onOk={submit} onCancel={modal.remove} width={700} destroyOnClose maskClosable={false}>
      {generateFormFields()}
    </YRModal>
  );
};

export default YREasyUseModal.create(ModalResponse);
