import React from 'react';
import { YRSpace } from 'yrantd';
import styles from './index.module.less';
import AvatarDropdown from './AvatarDropdown';
const GlobalHeaderRight = (props) => {
  const { initialState } = props;

  if (!initialState || !initialState.settings) {
    return null;
  }

  const { navTheme, layout, headerTheme } = initialState.settings;
  let className = styles.right;

  if ((navTheme === 'dark' && layout === 'top') || (layout === 'mix' && headerTheme !== 'light')) {
    className = `${styles.right}  ${styles.dark}`;
  }

  return (
    <YRSpace className={className}>
      {/* 个人设置 */}
      <AvatarDropdown />
    </YRSpace>
  );
};

export default GlobalHeaderRight;
