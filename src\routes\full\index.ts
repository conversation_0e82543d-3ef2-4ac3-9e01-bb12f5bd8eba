/**
 *模块功能路由
 */
import { lazy } from 'ice';
import { EnumPageType } from '@yr/util';
import { buildRoutes } from '@/utils/micro-utils';
import approval from './approval';
// 全屏打开的功能路由
const fullRoutes = [
  {
    path: '/setting/flow/edit',
    name: '流程图编辑',
    component: lazy(() => import('@/pages/Flow/FlowEdit'))
  },
  {
    path: '/setting/flow/detail',
    name: '流程图详情',
    component: lazy(() => import('@/pages/Flow/FlowDetail'))
  },
  {
    path: '/setting/organ/organ-manage/edit',
    name: '机构编辑',
    component: lazy(() => import('@/pages/Organ/OrganManage/OrganEdit'))
  },
  {
    path: '/setting/organ/organ-manage/detail',
    name: '机构详情',
    component: lazy(() => import('@/pages/Organ/OrganManage/OrganDetail'))
  },
  {
    path: '/setting/role/role-manage/edit',
    name: '角色编辑',
    component: lazy(() => import('@/pages/Role/RoleManage/RoleEdit'))
  },
  {
    path: '/decision-page/:mode',
    name: '决策表编辑',
    component: lazy(() => import('@/pages/Flow/Decision/DecisionPage'))
  },
  {
    path: '/rule-manage/scene-detail/:mode',
    name: '场景详情',
    component: lazy(() => import('@/pages/Setting/RuleManage/Scene/Detail'))
  },
  {
    path: '/auth/copy-page',
    name: '授权复制页面',
    component: lazy(() => import('@/pages/Auth/AuthManagement/components/CopyPage'))
  },
  {
    path: '/auth/auth-approval',
    name: '授权/转授权审批',
    component: lazy(() => import('@/pages/Auth/AuthApproval'))
  },
  ...approval,
];
export default buildRoutes(fullRoutes, EnumPageType.FULL_PAGE.code);
