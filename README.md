## 微应用

## 简介

用于自定义模板，基于 react，默认只提供了最简单的目录结构和路由演示，可按需自定义模板。   
更多请参考[飞冰](https://ice.work/).

**项目提供 yrantd、yr-loan-antd 组件库和yr/util工具库**

## 准备

使用前请阅读以下文档：

1. [微前端相关文档](http://yr-document.loan3-0-base-dev.svc.cluster.local:8080/)
2. [package.json说明](http://gitlab.yunrong.cn/loanV2.1/channel/hsjry-react/hsjry-kylin/blob/dev/package.json.md)
3. [问题记录](http://yr-document.loan3-0-base-dev.svc.cluster.local:8080/07.%E9%97%AE%E9%A2%98%E8%AE%B0%E5%BD%95/%E5%BC%80%E5%8F%91%E9%83%A8%E7%BD%B2)
5. [组件说明](http://yr-components-v4.loan3-0-base-dev.svc.cluster.local:8080/changelog-cn)

请确定微应用的信息（在package.json中配置如下参数）：

```text
  // 应用唯一标识,对应appId字段
  "name": "yr-loan-admin",
  // 业务标识，标识一个唯一的业务应用。根据这个字段来分配系统的权限，所以出现菜单显示问题，则需要排查这个字段
  // 目前写成了 kylin-operation 是为了使用他的权限数据，正常开发请修改正确后使用
  "businessSign": "yr-loan-admin",
  // 应用类型：参考 scaffolds/MicroApp/src/constant/enum/Enums.js
  "defaultOpen": "NORMAL",
  // 环境类型：参考 scaffolds/MicroApp/src/constant/enum/Enums.js
  "envType": "PRODUCT",
  // 注册状态：参考 scaffolds/MicroApp/src/constant/enum/Enums.js
  "status": "ONLINE",
  // 应用资源地址，本地启动时一定要改为自己的IP和PORT,否则资源会找不到
  "appEntry": "//**************:8101",
  "version": "0.0.1",
  "description": "微应用模板",
```

## 启动工程

1. 安装nodejs
2. `yarn i -g cnpm --registry=https://registry.npm.taobao.org` 安装配置cnpm
3. `yarn i -g nrm` 安装nrm镜像源管理工具
4. `nrm add yr http://sinopia.yunrong.cn:4873/` 添加公司镜像源
5. `nrm use yr` 切换npm的镜像源为公司私服
6. `git clone kylin工程git地址` 下载工程代码
7. `cd xxx` 进入工程目录
8. `yarn install` 安装依赖(小技巧: 中途遇到Chromium安装进度条不动，可以按ctrl+ c退出，完成后再执行cnpm i puppeteer@1.15.0单独安装)
10. 运行
  - mock优先 `yarn start`
  - 不用mock `yarn run start:no-mock`
11. 浏览器打开 http://localhost:8110

## 编码规范

01. 使用 `VS Code` 编辑器，安装ESLint代码检查工具
02. VSCode 勾选自动保存格式化：`format on save`
03. 不要引入jQuery
04. class组件名和文件名一致，大写开头
05. url地址命名不要驼峰, 用`-`, url层级控制3层以内, 如`/product/baserate-edit`
06. 待补充

更多参考：[《代码质量保证》](https://www.yuque.com/hedgqh/quality/lint)

## 自定义命令

## 全局变量说明

1. `g_userInfo` 用户登录后, 在models/login.js里将用户信息缓存在sessionStorage.userInfo, 在layouts/Initial.js里取出用户信息赋值给g_userInfo,
   组件里可直接g_userInfo.operatorId取值
3. SCENES: 服务场景
4. MODULES: 请求的后台模块
5. envType,
6. CONST: constant,
7. PUBLIC_PATH: publicPath

## 目录结构

```bash

MicroAppV2                                           
├── permit                                           
│   ├── PermitGenerator                                                           
│   ├── common.ts                        # 公共权限                              
│   ├── index.ts                         # 各模块权限                              
│   └── sql                              # sql 脚本
├── public                               # 静态资源
├── src
│   ├── assets                           # 静态资源
│   ├── components                       # 公共组件
│   ├── constant                         # 公共常量
│   │   ├── enum
│   │   │   ├── EnumCertificateKind.ts
│   │   │   └── EnumClientRelation.ts
│   │   └── workplat
│   │       └── processInfo.ts           # 工作台配置常量
│   ├── routes
│   │   ├── full                         # 全屏路由
│   │   └── part                         # 菜单路由
│   ├── services                         # 模块api
│   │   ├── comm.ts
│   │   ├── common.ts                    # 公共api(不需要校验权限的接口)
│   │   ├── config.ts
│   │   └── workplat.ts                  # 工作台api
│   ├── pages                            # 主要页面
│   │   ├── Home                         # 首页
│   │   ├── Layout                       # 布局
│   ├── app.tsx                          # app 入口
│   ├── defaultSettings.ts               # layout 默认配置
│   ├── global.d.ts
│   ├── global.less                      # 全局样式
│   ├── layouts
│   ├── models                           # 主要存放页面状态
│   ├── routes.ts                        # 路由入口
│   ├── store.ts                         # 全局状态入口
│   ├── typings.d.ts
│   └── utils                            # 工具库
├── build.config.js                      # 项目配置文件                              
├── build.plugin.js                                  
├── empty-loader.js                                  
├── jsconfig.json                                    
├── package.json                                     
├── tsconfig.json
├── postcss.config.js
├── README.md                                        
└── yarn.lock

```

## 支持环境

现代浏览器及IE11。

| [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/edge/edge_48x48.png" alt="IE / Edge" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>IE / Edge | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/firefox/firefox_48x48.png" alt="Firefox" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Firefox | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/chrome/chrome_48x48.png" alt="Chrome" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Chrome | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/safari/safari_48x48.png" alt="Safari" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Safari | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/opera/opera_48x48.png" alt="Opera" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Opera |
| --------- | --------- | --------- | --------- | --------- |
| IE11, Edge| last 2 versions| last 2 versions| last 2 versions| last 2 versions
