# API调用错误修复

## 问题描述
```
Error: 异步请求[/hsjry/corp/authBaseInfoQuery/queryPage]需要module参数
```

## 错误原因
在修改`queryAuthInfoRuleList`函数时，我移除了必需的`module`和`serviceScene`参数，导致API调用失败。

## 修复方案

### 1. 添加缺失的导入
```typescript
// 修改前
import { request } from '@yr/util';

// 修改后  
import { MODULES, request, SCENES } from '@yr/util';
```

### 2. 恢复必需的参数
```typescript
// 修改前（错误的）
export function queryAuthInfoRuleList(param?: any) {
  return request('/hsjry/corp/authBaseInfoQuery/queryPage', {
    param,
    method: 'POST',
    serviceName: '查询授权规则列表'
  });
}

// 修改后（正确的）
export function queryAuthInfoRuleList(param?: any) {
  return request('/hsjry/corp/authBaseInfoQuery/queryPage', {
    param,
    method: 'POST',
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '查询授权规则列表'
  });
}
```

## 修复文件
- **src/services/auth.ts**: 添加导入和修复API调用参数

## 说明
这个修复确保API调用符合项目的request工具要求，包含所有必需的参数：
- `param`: 请求参数
- `method`: 请求方法（POST）
- `module`: 请求模块（MODULES.corp）
- `serviceScene`: 业务场景（SCENES.query）
- `serviceName`: 功能名称（用于日志）

修复后，授权列表查询功能应该可以正常工作。 