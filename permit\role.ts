/**
 * 角色管理
 */
import { YRLoanPermit } from 'yr-loan-antd';
import packageInfo from '../package.json';
import {
  addOrUpdateRole,
  allotRole,
  changeRoleStatus,
  delRole,
  queryRoleDetail,
  queryRoleLogPage,
  queryRolePage,
  queryUserList
} from '@/services/setting';
import {resetPassword} from "@/services/user";

const { buildPermit, EnumPermitType } = YRLoanPermit;

/**
 * 角色管理
 */
const M010301 = buildPermit('M010301', packageInfo, {
  interfaces: { queryRolePage, changeRoleStatus },
  E01: {
    name: '新增角色',
    path: '/hsjry/guard/base/IRole/addOrUpdateRole',
    type: EnumPermitType.Element,
    interfaces: {
      addOrUpdateRole
    }
  },
  E02: {
    name: '修改角色',
    path: '/hsjry/guard/base/IRole/addOrUpdateRole-modify',
    type: EnumPermitType.Element,
    interfaces: {
      addOrUpdateRole
    }
  },
  E03: {
    name: '删除角色',
    path: '/hsjry/guard/base/IRole/delRole',
    type: EnumPermitType.Element,
    interfaces: {
      delRole
    }
  },
  E04: {
    name: '启用角色',
    path: '/hsjry/guard/base/IRole/changeRoleStatus/start',
    type: EnumPermitType.Element
  },
  E05: {
    name: '停用角色',
    path: '/hsjry/guard/base/IRole/changeRoleStatus/disabled',
    type: EnumPermitType.Element
  },
  E06: {
    name: '恢复角色',
    path: '/hsjry/guard/base/IRole/changeRoleStatus/restore',
    type: EnumPermitType.Element
  },
  E07: {
    name: '添加成员',
    path: '/hsjry/guard/base/IRole/allotRole',
    type: EnumPermitType.Element,
    interfaces: {
      allotRole,
      queryUserList
    }
  },
  E08: {
    name: '历史记录',
    path: '/hsjry/guard/base/IRoleQuery/queryRoleLogPage',
    type: EnumPermitType.Element,
    interfaces: {
      queryRoleLogPage
    }
  },
  E09: {
    name: '角色详情',
    path: '/hsjry/guard/base/IRoleQuery/detail',
    type: EnumPermitType.Element,
    interfaces: {
      queryRoleDetail
    }
  },
  E10: {
    name: '重置密码',
    path: '/hsjry/guard/base/IUserQuery/resetPassword',
    type: EnumPermitType.Element,
    interfaces: {
      resetPassword
    }
  }
});

/**
 * 准入角色名单
 */
const M010302 = buildPermit('M010302', packageInfo, {
  interfaces: {}
});

export { M010301, M010302 };
