/*
 * yunrong.cn Inc. Copyright (c) 2014-2021 All Rights Reserved
 */

/**
 * 工作台接口
 */

import { request } from '@yr/util';

/**
 * 查询审批进度
 * @param { object } param
 */
export function queryApproveProcess(param) {
  return request('/IFlowTaskApprove/queryFlowTaskApprove', {
    param,
    module: MODULES.flow,
    serviceScene: SCENES.query,
    serviceName: '查询审批进度'
  });
}

/**
 * 查询任务详情
 * @param { object } param 入参
 */

export function taskDetail(param) {
  return request('/ICommonApprovalQuery/queryFlowApprovalApplyInfoDetail', {
    param,
    module: MODULES.flow,
    serviceScene: SCENES.query,
    serviceName: '查询任务详情'
  });
}

/**
 * 审批页面功能权限查询
 * @param { object } param 入参
 */
export function queryTaskApprovalAuthority(param) {
  return request('/IFlowTaskApprove/queryTaskApprovalAuthority', {
    param,
    module: MODULES.flow,
    serviceScene: SCENES.query,
    serviceName: '审批页面功能权限查询',
    dataPermission: true
  });
}

/**
 * 查询审批轨迹
 * @param { object } param
 */
export function queryFlowStepInfo(param) {
  return request('/IFlowTaskApprove/queryFlowStepInfo', {
    param,
    module: MODULES.flow,
    serviceScene: SCENES.query,
    serviceName: '查询审批轨迹'
  });
}

/**
 * 审批
 * @param { object } param
 */
export function doApprove(param) {
  return request('/commonApproval/approve', {
    param,
    module: MODULES.easyFlow,
    serviceScene: SCENES.apply,
    serviceName: '审批'
  });
}

export function transfer(param) {
  return request('/commonApproval/take', {
    param,
    module: MODULES.easyFlow,
    serviceScene: SCENES.apply,
    serviceName: '转派'
  });
}

/**
 * 查询流程图详情
 * @param param
 * @returns {Promise<*>}
 */
export function snakerDetail(param) {
  return request('/IConfigFlow/queryDetail', {
    param,
    module: MODULES.flow,
    serviceScene: SCENES.query,
    serviceName: '查询流程图详情'
  });
}
