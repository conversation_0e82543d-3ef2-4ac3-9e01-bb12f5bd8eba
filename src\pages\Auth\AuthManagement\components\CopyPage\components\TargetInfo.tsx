/*
 * @Author: liaokt
 * @Description: 目标源信息
 * @Date: 2024-09-25 10:21:16
 * @LastEditors: liaokt
 * @LastEditTime: 2024-10-17 14:35:09
 */
import { M0105 } from '@permit/organ';
import { useRequest } from 'ahooks';
import React, { useState } from 'react';
import { YRCard, YRDatePicker, YRDict, YRForm, YRInput, YRTreeSelect } from 'yrantd';

const ApplyInfo = (props) => {
  const { id, title, form } = props;

  const [organData, setOrganData] = useState([] as any[]);
  const [roleData, setRoleData] = useState([] as any[]);

  const { queryFirstLvlOrgList, queryRole } = M0105.interfaces;

  const { loading: queryOrganTreeLoading } = useRequest(queryFirstLvlOrgList, {
    onSuccess: (res: any) => {
      if (res?.errorMessage === null) {
        const { data } = res;
        setOrganData([data]);
      }
    },
    defaultParams: [
      {
        queryAuthFlag: '0'
      }
    ]
  });

  const { loading: queryRoleLoading, run: queryRoleRequest } = useRequest(queryRole, {
    manual: true,
    onSuccess: (res: any) => {
      if (res?.errorMessage === null) {
        const { data } = res;
        setRoleData(data || []);
      }
    }
  });

  return (
    <YRCard id={id} title={title}>
      <YRForm.Row>
        <YRForm.Item label="机构" name={['orgId']} rules={[{ required: true, message: '请选择机构' }]}>
          <YRTreeSelect
            loading={queryOrganTreeLoading}
            style={{ width: '100%' }}
            dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
            treeData={organData}
            fieldNames={{ label: 'orgName', value: 'orgId' }}
            onSelect={(_, node) => {
              const { orgLvl, orgId } = node as { orgId: string; orgLvl: never };
              queryRoleRequest({ orgId, orgLvlList: [orgLvl] });
            }}
            placeholder="请选择机构"
            treeDefaultExpandAll
            allowClear
          />
        </YRForm.Item>
        <YRForm.Item label="角色" name={['roleId']} rules={[{ required: true, message: '请选择角色' }]}>
          <YRTreeSelect
            multiple
            loading={queryRoleLoading}
            style={{ width: '100%' }}
            dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
            treeData={roleData}
            fieldNames={{ label: 'roleName', value: 'roleNo' }}
            placeholder="请选择角色"
            treeDefaultExpandAll
            allowClear
          />
        </YRForm.Item>
        <YRForm.Item label="授权状态" name={['authStatus']}>
          <YRDict.Select dictkey="AUTH_STATUS" disabled defaultValue={'010'} />
        </YRForm.Item>
        <YRForm.Item label="生效日期" name="effectBeginDate" rules={[{ required: true, message: '请输入生效日期' }]}>
          <YRDatePicker placeholder="请输入生效日期" />
        </YRForm.Item>
        <YRForm.Item label="失效日期" name="effectEndDate" rules={[{ required: true, message: '请输入失效日期' }]}>
          <YRDatePicker placeholder="请输入失效日期" />
        </YRForm.Item>
        <YRForm.Item label="登记日期" name="signDate">
          <YRDatePicker placeholder="请输入登记日期" disabled />
        </YRForm.Item>
        <YRForm.Item label="登记人" name="signName">
          <YRInput placeholder="请输入登记人" disabled />
        </YRForm.Item>
        <YRForm.Item label="登记机构" name="signOrgan">
          <YRInput placeholder="请输入登记机构" disabled />
        </YRForm.Item>
      </YRForm.Row>
    </YRCard>
  );
};

export default ApplyInfo;
