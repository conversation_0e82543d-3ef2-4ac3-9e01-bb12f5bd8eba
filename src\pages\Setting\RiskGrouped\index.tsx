/*
 * @Author: ducf
 * @E-mail: <EMAIL>
 * @Date: 2023-05-10 16:56:08
 * @Description: 风险探测分组配置查询列表
 */
import { useAntdTable } from 'ahooks';
import React from 'react';
import { deleteFlowDiagramResource } from '@/services/flow';
import { FormItemListProps, YRTableProps, YRButton, YRForm, YRIndexPageLayout, YRTable } from 'yrantd';
import { openNewTab } from '@/utils/utils';
import { queryProbeGroupPage } from '@/services/setting';

interface Props {}

const RiskGrouped: React.FC<Props> = (props) => {
  const [form] = YRForm.useForm();

  const { tableProps, search, run } = useAntdTable(
    (p) => {
      return queryProbeGroupPage({
        ...p,
        pageNum: p?.current
      })
        .then((res) => {
          return { list: res?.data?.list, total: res?.data?.total || 0 };
        })
        .catch(() => {});
    },
    {
      defaultParams: [
        {
          pageNum: 1,
          pageSize: 10,
          approveStatus: 0
        }
      ]
    }
  );

  const formItemList: FormItemListProps[] = [
    {
      placeholder: '组名称',
      key: 'groupName',
      type: 'input',
      position: 'show'
    }
  ];

  const onDel = (row) => {
    deleteFlowDiagramResource({ id: row?.id }).then((res) => {
      if (res?.success) {
        run({ current: 1, pageSize: 10 });
      }
    });
  };
  const getTableColumns: YRTableProps['columns'] = [
    {
      title: '组名称',
      dataIndex: 'groupName'
      // render: (value, row) => {
      //   if (value) {
      //     return (
      //       <YRLink type="primary" onClick={() => toPage('readPretty', row.id)}>
      //         {value}
      //       </YRLink>
      //     );
      //   }
      //   return CONST.null;
      // }
    },
    {
      title: '组描述',
      dataIndex: 'groupDesc'
    }
  ];

  const expandedRowRender = (record) => {
    const expandableColumns = [
      {
        title: '探测项',
        dataIndex: 'probeName'
      },
      {
        title: '探测项说明',
        dataIndex: 'probeDesc'
      },
      {
        title: '探测项情况',
        dataIndex: 'hitMsg'
      }
    ];
    return <YRTable columns={expandableColumns} pagination={false} dataSource={record?.riskProbePolicies || []} />;
  };

  const toPage = (type: string, id?: string) => {
    openNewTab({
      pathname: `/decision-page/${type}`,
      query: { id }
    });
  };

  const renderExtAction = (
    <YRButton type="primary" onClick={() => toPage('add')}>
      新增
    </YRButton>
  );

  return (
    <YRIndexPageLayout>
      <YRTable
        form={form}
        rowKey="id"
        columns={getTableColumns}
        formItemList={formItemList}
        // extAction={renderExtAction}
        handleSearch={(reqParams) => {
          run({ ...reqParams, current: 1, pageSize: 10 });
        }}
        expandable={{ expandedRowRender, defaultExpandedRowKeys: ['0'] }}
        // onRow={(row) => {
        //   return {
        //     onDoubleClick: () => toDetail(row)
        //   };
        // }}
        {...tableProps}
      />
    </YRIndexPageLayout>
  );
};

export default RiskGrouped;
