/*
 * @Author: AI Assistant
 * @Description: 批量授权管理页面
 * @Date: 2024-12-19
 */
import React, { useState, useEffect } from 'react';
import { YRButton, YRTable, YRForm, YRMessage, YRIndexPageLayout, YRUpload, YRModal } from 'yrantd';
import { useRequest } from 'ahooks';
import { useDict } from '@yr/util';
import { M0105 } from '@permit/organ';
import { columns, formItemList } from './useIndex';
import type { BatchQueryParams, ApiResponse, PageResponse, BatchAuthInfo, AuthFileImportInfoBatchDto } from './types';
import {
  queryBatchAuthInfoPage,
  submitBatchAuthForReview,
  deleteBatchAuthInfo,
  invalidateBatchAuthInfo,
  downloadBatchAuthTemplate,
  batchImportAuthInfo
} from '@/services/batchAuth';

const BatchAuthManagement: React.FC = () => {
  // 初始化数据字典
  useDict(['AUTH_STATUS']);

  // 数据映射函数：将API返回的数据映射为前端使用的数据结构
  const mapApiDataToFrontend = (apiData: AuthFileImportInfoBatchDto): BatchAuthInfo => {
    // 安全的日期格式化
    const formatDate = (dateString: string): string => {
      if (!dateString) return '';
      try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return '';
        return date.toISOString().split('T')[0];
      } catch (error) {
        console.warn('日期格式化失败:', dateString, error);
        return '';
      }
    };

    return {
      batchAuthId: apiData.batchAuthBaseId || '',
      batchAuthNo: apiData.batchAuthBaseId || '',
      fileName: apiData.fileName || '',
      authStatus: apiData.authStatus || '',
      operatorName: apiData.registerName || '',
      operatorId: apiData.registerId || '',
      ownOrganName: apiData.registerOrgName || '',
      ownOrganId: apiData.registerOrgId || '',
      inputTime: formatDate(apiData.registerTime),
      updateTime: apiData.registerTime || '',
      totalCount: (apiData.successNum || 0) + (apiData.failNum || 0),
      successCount: apiData.successNum || 0,
      failCount: apiData.failNum || 0,
      remark: ''
    };
  };



  const [form] = YRForm.useForm();
  const [dataSource, setDataSource] = useState<BatchAuthInfo[]>([]);

  // 分页数据
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) =>
      `共 ${total} 条记录，当前显示第 ${range[0]}-${range[1]} 条`
  });

  // 查询批量授权列表
  const {
    loading: queryLoading,
    run: queryBatchAuthList,
    refresh: refreshList
  } = useRequest(
    async (params: BatchQueryParams) => {
      return await queryBatchAuthInfoPage(params);
    },
    {
      onSuccess: (res: ApiResponse<PageResponse<AuthFileImportInfoBatchDto>>) => {
        if (res?.rpcResult === 'SUCCESS' && !res?.errorMessage) {
          const { data } = res;
          // 将API返回的数据映射为前端使用的数据结构
          const mappedData = (data.list || []).map(mapApiDataToFrontend);
          setDataSource(mappedData);
          setPagination((prev) => ({
            ...prev,
            current: data.pageNum,
            total: data.total
          }));
        } else {
          YRMessage.error(res?.errorMessage || '查询失败');
        }
      },
      onError: (error) => {
        console.error('查询批量授权列表失败:', error);
        YRMessage.error('查询失败，请稍后重试');
        setDataSource([]);
        setPagination((prev) => ({
          ...prev,
          current: 1,
          total: 0
        }));
      },
      manual: true
    }
  );

  // 提交复核
  const { run: submitRun, loading: submitLoading } = useRequest(
    async (params: { batchAuthId: string }) => {
      return await submitBatchAuthForReview(params);
    },
    {
      onSuccess: (res: any) => {
        if (res?.errorMessage === null) {
          YRMessage.success('提交复核成功', 0.5, () => {
            refreshList();
          });
        } else {
          YRMessage.error(res?.errorMessage || '提交复核失败');
        }
      },
      manual: true
    }
  );

  // 删除
  const { run: deleteRun, loading: deleteLoading } = useRequest(
    async (params: { batchAuthId: string }) => {
      return await deleteBatchAuthInfo(params);
    },
    {
      onSuccess: (res: any) => {
        if (res?.errorMessage === null) {
          YRMessage.success('删除成功', 0.5, () => {
            refreshList();
          });
        } else {
          YRMessage.error(res?.errorMessage || '删除失败');
        }
      },
      manual: true
    }
  );

  // 失效
  const { run: invalidRun, loading: invalidLoading } = useRequest(
    async (params: { batchAuthId: string }) => {
      return await invalidateBatchAuthInfo(params);
    },
    {
      onSuccess: (res: any) => {
        if (res?.errorMessage === null) {
          YRMessage.success('失效成功', 0.5, () => {
            refreshList();
          });
        } else {
          YRMessage.error(res?.errorMessage || '失效失败');
        }
      },
      manual: true
    }
  );

  // 初始化查询
  useEffect(() => {
    handleSearch();
  }, []);

  // 查询处理
  const handleSearch = () => {
    form.validateFields().then((formValues: any) => {
      const queryParams: BatchQueryParams = {
        pageNum: 1,
        pageSize: pagination.pageSize,
        authStatus: formValues.authStatus
      };

      console.log('查询参数:', queryParams);
      setPagination((prev) => ({ ...prev, current: 1 }));
      queryBatchAuthList(queryParams);
    }).catch((error) => {
      console.error('表单验证失败:', error);
      YRMessage.error('查询条件验证失败');
    });
  };

  // 分页变化处理
  const handlePaginationChange = (page: number, pageSize?: number) => {
    const newPagination = {
      ...pagination,
      current: page,
      pageSize: pageSize || pagination.pageSize
    };
    setPagination(newPagination);

    form.validateFields().then((formValues: any) => {
      const queryParams: BatchQueryParams = {
        pageNum: page,
        pageSize: pageSize || pagination.pageSize,
        authStatus: formValues.authStatus
      };
      console.log('分页查询参数:', queryParams);
      queryBatchAuthList(queryParams);
    }).catch((error) => {
      console.error('分页查询表单验证失败:', error);
      YRMessage.error('查询条件验证失败');
    });
  };

  // 批量导入处理
  const handleBatchImport = () => {
    const uploadProps = {
      name: 'file',
      accept: '.xlsx,.xls',
      showUploadList: false,
      beforeUpload: (file: File) => {
        const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                       file.type === 'application/vnd.ms-excel';
        if (!isExcel) {
          YRMessage.error('只能上传Excel文件！');
          return false;
        }
        const isLt10M = file.size / 1024 / 1024 < 10;
        if (!isLt10M) {
          YRMessage.error('文件大小不能超过10MB！');
          return false;
        }

        // 调用批量导入API
        batchImportAuthInfo({ file })
          .then((res) => {
            if (res?.rpcResult === 'SUCCESS') {
              YRMessage.success(`文件 ${file.name} 导入成功`);
              refreshList(); // 刷新列表
            } else {
              YRMessage.error(res?.errorMessage || '导入失败');
            }
          })
          .catch((error) => {
            console.error('批量导入失败:', error);
            YRMessage.error('导入失败，请稍后重试');
          });
        return false; // 阻止自动上传
      }
    };

    YRModal.info({
      title: '批量导入',
      content: (
        <div>
          <p>请选择要导入的Excel文件：</p>
          <YRUpload {...uploadProps}>
            <YRButton>选择文件</YRButton>
          </YRUpload>
        </div>
      )
    });
  };

  // 下载模板处理
  const handleDownloadTemplate = () => {
    YRMessage.info('正在下载模板文件...');

    downloadBatchAuthTemplate()
      .then((res) => {
        if (res?.rpcResult === 'SUCCESS') {
          // TODO: 处理文件下载逻辑，可能需要创建下载链接
          YRMessage.success('模板下载完成');
        } else {
          YRMessage.error(res?.errorMessage || '下载失败');
        }
      })
      .catch((error) => {
        console.error('下载模板失败:', error);
        YRMessage.error('下载失败，请稍后重试');
      });
  };

  // 批量失效处理
  const handleBatchInvalid = () => {
    YRModal.confirm({
      title: '批量失效',
      content: '确定要对所有已生效的批量授权进行失效操作吗？',
      onOk: () => {
        invalidateBatchAuthInfo({ batchType: 'all' })
          .then((res) => {
            if (res?.rpcResult === 'SUCCESS') {
              YRMessage.success('批量失效操作完成');
              refreshList(); // 刷新列表
            } else {
              YRMessage.error(res?.errorMessage || '批量失效失败');
            }
          })
          .catch((error) => {
            console.error('批量失效失败:', error);
            YRMessage.error('批量失效失败，请稍后重试');
          });
      }
    });
  };

  // 操作按钮渲染
  const operationRender = (
    <YRButton.Space>
      <YRButton type="primary" onClick={handleBatchImport}>
        批量导入
      </YRButton>
      <YRButton onClick={handleDownloadTemplate}>
        下载模板
      </YRButton>
      <YRButton onClick={handleBatchInvalid}>
        批量失效
      </YRButton>
    </YRButton.Space>
  );

  return (
    <YRIndexPageLayout>
      <YRTable
        rowKey="batchAuthId"
        form={form}
        dataSource={dataSource}
        handleSearch={handleSearch}
        loading={queryLoading}
        columns={columns(refreshList, submitRun, submitLoading, deleteRun, deleteLoading, invalidRun, invalidLoading)}
        formItemList={formItemList}
        operationRender={operationRender}
        pagination={{
          ...pagination,
          onChange: handlePaginationChange,
          onShowSizeChange: handlePaginationChange
        }}
        scroll={{
          x: 1200
        }}
      />
    </YRIndexPageLayout>
  );
};

export default BatchAuthManagement;
