/**
 * 已复核-单笔授权管理相关类型定义
 */

// 查询参数接口
export interface ReviewedSingleQueryParams {
  pageNum: number;                    // 页码（必填）
  pageSize: number;                   // 每页大小（必填）
  authNo?: string;                    // 授权编号
  authStatus?: string;                // 授权状态
  orgId?: string;                     // 机构
  deptId?: string;                    // 部门
  roleId?: string;                    // 角色
  creditCategory?: string;            // 授信类别
}

// API响应数据结构
export interface ApiResponse<T = any> {
  rpcResult: "SUCCESS" | "FAIL";
  data: T;
  errorMessage?: string;
}

// 分页响应数据
export interface PageResponse<T = any> {
  pageNum: number;
  pageSize: number;
  total: number;
  pages: number;
  list: T[];
}

// 已复核单笔授权信息
export interface ReviewedSingleAuthInfo {
  authBaseId: string;               // 授权ID
  authNo: string;                   // 授权编号
  orgId: string;                    // 机构ID
  orgName: string;                  // 机构名称
  deptId: string;                   // 部门ID
  deptName: string;                 // 部门名称
  roleId: string;                   // 角色ID
  roleName: string;                 // 角色名称
  authCategory: string;             // 授权类别
  creditCategory: string;           // 授信类别
  authStatus: string;               // 授权状态
  effectBeginDate: string;          // 生效日期（yyyy-MM-dd格式）
  effectEndDate: string;            // 失效日期（yyyy-MM-dd格式）
  approveDate: string;              // 复核通过日期（yyyy-MM-dd格式）
  operatorName: string;             // 操作人
  operatorId: string;               // 操作人ID
  ownOrganName: string;             // 登记机构
  ownOrganId: string;               // 登记机构ID
  inputTime: string;                // 登记时间
  updateTime: string;               // 更新时间
  approverName?: string;            // 复核人
  approverId?: string;              // 复核人ID
  approveRemark?: string;           // 复核意见
  flowInstanceId?: string;          // 流程实例ID
  remark?: string;                  // 备注
}

// 机构树节点
export interface OrganTreeNode {
  orgId: string;
  orgName: string;
  children?: OrganTreeNode[];
}

// 部门信息
export interface DeptInfo {
  deptId: string;
  deptName: string;
  orgId: string;
}

// 角色信息
export interface RoleInfo {
  roleId: string;
  roleName: string;
  roleCode: string;
}

// 授权详情信息
export interface AuthDetailInfo {
  authBaseId: string;               // 授权ID
  authNo: string;                   // 授权编号
  orgName: string;                  // 机构名称
  deptName: string;                 // 部门名称
  roleName: string;                 // 角色名称
  authCategory: string;             // 授权类别
  creditCategory: string;           // 授信类别
  authStatus: string;               // 授权状态
  effectBeginDate: string;          // 生效日期
  effectEndDate: string;            // 失效日期
  approveDate: string;              // 复核通过日期
  operatorName: string;             // 登记人
  ownOrganName: string;             // 登记机构
  approverName?: string;            // 复核人
  approveRemark?: string;           // 复核意见
  inputTime: string;                // 登记时间
  remark?: string;                  // 备注
}
