/*
 * @Author: liaokt
 * @Description:
 * @Date: 2024-10-16 16:55:16
 * @LastEditors: liaokt
 * @LastEditTime: 2024-10-24 14:04:42
 */
import { MODULES, request, SCENES } from '@yr/util';

/**
 * 查询所有一级机构
 * @param param
 */
export function queryFirstLvlOrgList(param?: any) {
  return request('/authCatalogQuery/queryAuthCatalogOrgTree', {
    param,
    method: 'POST',
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '查询所有一级机构'
  });
}

/**
 * 查询角色集合
 * @param param
 */
export function queryRole(param?: any) {
  return request('/authCatalogRoleQuery/queryRole', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '查询角色集合'
  });
}

/**
 * 新增机构授权类别
 * @param param
 */
export function addOrgAuthCategoryRoleInfo(param?: any) {
  return request('/authCatalog/saveAuthCatalog', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '新增机构授权类别'
  });
}

/**
 * 查询机构授权类别对应的角色列表
 * @param param
 */
export function queryAuthRoleList(param?: any) {
  return request('/authCatalogRoleQuery/queryAuthRoleList', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '查询机构授权类别对应的角色列表'
  });
}

/**
 * 新增角色
 * @param param
 */
export function addAuthCategoryRoleInfo(param?: any) {
  return request('/authCatalogRole/saveRole', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '新增角色'
  });
}

/**
 * 删除角色
 * @param param
 */
export function delAuthCategoryRoleInfo(param?: any) {
  return request('/authCatalogRole/deleteRole', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '删除角色'
  });
}

/**
 * 查询授权规则列表
 * @param param
 */
export function queryAuthInfoRuleList(param?: any) {
  return request('/authBaseInfoQuery/queryPage', {
    param,
    method: 'POST',
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '查询授权规则列表'
  });
}

/**
 * 新增授权规则
 * @param param
 */
export function addBaseAuthInfo(param?: any) {
  return request('/authBaseInfo/saveBaseInfo', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '新增授权规则'
  });
}

/**
 * 授权规则详情查询
 * @param param
 */
export function queryAuthInfoByAuthNo(param?: any) {
  return request('/authBaseInfoQuery/queryDetail', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '授权规则详情查询'
  });
}

/**
 * 授权维度动态加载查询
 * @param param
 */
export function queryDimensionLoadByAuthCode(param?: any) {
  return request('/authBaseInfoQuery/queryDimensionLoadByAuthCode', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '授权维度动态加载查询'
  });
}

/**
 * 删除授权规则
 * @param param
 */
export function delAuthInfo(param?: any) {
  return request('/authBaseInfo/deleteBaseInfo', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '删除授权规则'
  });
}

/**
 * 失效授权规则
 * @param param
 */
export function loseEffectAuthInfo(param?: any) {
  return request('/authBaseInfo/cancelBaseInfo', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '失效授权规则'
  });
}

/**
 * 授权规则提交复核
 * @param param
 */
export function submitAuthRuleInfo(param?: any) {
  return request('/authBaseInfoApprove/startFlow', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '授权规则提交复核'
  });
}

/**
 * 授权规则暂停
 * @param param
 */
export function pauseAuthInfo(param?: any) {
  return request('/authBaseInfo/stopBaseInfo', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '授权规则暂停'
  });
}

/**
 * 授权规则重启
 * @param param
 */
export function restartAuthInfo(param?: any) {
  return request('/authBaseInfo/restartBaseInfo', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '授权规则重启'
  });
}

/**
 * 授权规则复制（未启用）
 * @param param
 */
export function copyAuthInfo(param?: any) {
  return request('/base/IAuthInfoWebApi/copyAuthInfo', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '授权规则复制'
  });
}

/**
 * 授权复核列表查询（未启用）
 * @param param
 */
export function queryApproveAuthInfo(param?: any) {
  return request('/base/IAuthInfoWebApi/queryApproveAuthInfoPage', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '授权复核列表查询'
  });
}

/**
 * 复核退回
 * @param param
 */
export function approveRejectAuthRuleInfo(param?: any) {
  return request('/authBaseInfoApprove/returnFlow', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '复核退回'
  });
}

/**
 * 复核通过
 * @param param
 */
export function approvePassAuthRuleInfo(param?: any) {
  return request('/authBaseInfoApprove/submitApprove', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '复核通过'
  });
}

/**
 * 删除授权类别
 * @param param
 */
export function delOrgAuthCategoryRoleInfo(param?: any) {
  return request('/authCatalog/deleteAuthCatalog', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '删除授权类别'
  });
}

/**
 * 修改授权
 * @param param
 */
export function updateBaseAuthInfo(param?: any) {
  return request('/authBaseInfo/saveBaseInfo', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '修改授权'
  });
}

/**
 * 新增转授权
 * @param param
 */
export function addTranferAuthInfo(param?: any) {
  return request('/authBaseInfo/addTransferBaseInfo', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '新增转授权'
  });
}

/**
 * 查询转授权机构授权类别对应的角色列表（复用queryAuthRoleList，加入参）
 * @param param
 */
export function queryTransferAuthRoleList(param?: any) {
  return request('/authCatalogRoleQuery/queryAuthRoleList', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '查询转授权机构授权类别对应的角色列表'
  });
}

/**
 * 查询转授权规则列表（复用queryPage，加入参）
 * @param param
 */
export function queryTransferAuthInfoRuleList(param?: any) {
  return request('/authBaseInfoQuery/queryPage', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '查询转授权规则列表'
  });
}

// 审批提交
export function approveSubmit(param?: any) {
  return request('/authBaseInfoApprove/submitApprove', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '审批提交'
  });
}
