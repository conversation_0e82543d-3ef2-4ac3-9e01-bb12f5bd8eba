/**
 * @Author: wangyw26123
 * @Description: 角色互斥弹窗
 * @Date: Created in 2022-12-20 16:20:34
 * @Modifed By:
 */
import React, { useEffect, useState } from 'react';
import { YRButton, YRRow, YRCol, YRModal, YRTransfer } from 'yrantd';
import styles from './index.module.less';

interface RecordType {
  roleId: string;
  roleName: string;
  roleDesc: string;
}

const RoleMutexModal = (props) => {
  const { visible, onCancel, onSubmit, roleMutexList: roleData, selectKeys } = props;
  const [targetKeys, setTargetKeys] = useState<string[]>([]);

  useEffect(() => {
    setTargetKeys(selectKeys);
  }, [selectKeys]);

  const onConfirm = () => {
    onSubmit(targetKeys);
    onCancel();
  };

  const filterOption = (inputValue: string, option: RecordType) => option.roleName.indexOf(inputValue) > -1;

  const handleChange = (newTargetKeys: string[]) => {
    setTargetKeys(newTargetKeys);
  };

  return (
    <YRModal
      title="角色互斥"
      size="middle"
      open={visible}
      onCancel={onCancel}
      footer={[
        <YRButton key="cancel" onClick={() => onCancel()}>
          取消
        </YRButton>,
        <YRButton key="confirm" type="primary" onClick={() => onConfirm()}>
          确定
        </YRButton>
      ]}
    >
      <YRTransfer
        showSearch
        titles={['未选', '已选']}
        dataSource={roleData}
        className={styles['roleMutexModalTransfer']}
        filterOption={filterOption}
        targetKeys={targetKeys}
        onChange={handleChange}
        rowKey={(record) => record.roleId}
        render={(item) => (
          <YRRow>
            <YRCol span={12}>{item.roleName}</YRCol>
            <YRCol span={12}>{item.roleDesc}</YRCol>
          </YRRow>
        )}
      />
    </YRModal>
  );
};

export default RoleMutexModal;
