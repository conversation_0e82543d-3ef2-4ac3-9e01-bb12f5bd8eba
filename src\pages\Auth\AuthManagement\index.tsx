/*
 * @Author: liaokt
 * @Description:
 * @Date: 2024-10-16 09:21:54
 * @LastEditors: liaokt
 * @LastEditTime: 2024-10-25 11:04:50
 */
import { YRButton, YRFlexPageLayout, YREmpty, YRTable, YRE<PERSON>yUseModal, YRForm, YRMessage, YRTabs } from 'yrantd';
import { columns, formItemList, tabs } from './useIndex';
import { M0105 } from '@permit/organ';
import { useRequest } from 'ahooks';
import { useDict } from '@yr/util';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import OrganTree from './components/OrganTree';
import RoleTree from './components/RoleTree';
import RulesAddModal from './components/RulesAddModal';
import AddModal from './components/OrganTree/components/AddModal';
import AddRoleModal from './components/RoleTree/components/AddRoleModal';
import type { QueryParams, ApiResponse, PageResponse, AuthBaseInfo, OrganParams, RoleParams } from './types';

// 定义 RoleTree 组件的 ref 类型
interface RoleTreeRef {
  getCurrentRoleNodeInfo: () => any;
}

const AuthPage = () => {
  useDict(['AUTH_STATUS', 'AUTH_CATEGORY', 'USER_TYPE', 'AUTH_METHOD', 'BIZ_LINE', 'RESULT_DIMENSION', 'CONDITIONAL_DIMENSION']);

  const { queryFirstLvlOrgList, queryAuthInfoRuleList, queryAuthRoleList, submitAuthRuleInfo } = M0105.interfaces;

  const [form] = YRForm.useForm();

  const [organData, setOrganData] = useState([]);
  const [roleData, setRoleData] = useState([]);
  const [rulesData, setRulesData] = useState<AuthBaseInfo[]>([]);

  // 分页数据
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) =>
      `共 ${total} 条记录，当前显示第 ${range[0]}-${range[1]} 条`
  });

  // 选中机构节点的数据
  const [currentOrganParams, setCurrentOrganParams] = useState<OrganParams>({
    orgId: '',
    authCategory: '',
    authCatalogId: '',
    orgAuthFlag: ''
  });
  // 选中的角色的编号
  const [currentRole, setCurrentRole] = useState<RoleParams>({ roleId: '', authNo: '' });
  const [activeKey, setActiveKey] = useState('001');

  //   const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  //   const [selectedRows, setSelectedRows] = useState<string[]>([]);
  const containerRef = useRef(null);
  const roleTreeRef = useRef<RoleTreeRef>(null);

  const [listHeight, setListHeight] = useState(window.innerHeight);

  useEffect(() => {
    const handleResize = () => {
      setListHeight(window.innerHeight); // 更新列表高度为当前窗口高度
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // 查询机构树
  const { loading: queryOrganTreeLoading, refresh: queryOrgan } = useRequest(queryFirstLvlOrgList, {
    onSuccess: (res: any) => {
      if (res?.errorMessage === null) {
        const { data } = res as { data: never };
        setOrganData([data]);
      }
    },
    defaultParams: [
      {
        queryAuthFlag: '1'
      }
    ]
  });

  // 查询角色树
  const {
    loading: queryAuthRoleListLoading,
    run: queryRelationRole,
    refresh: refreshRoleTree
  } = useRequest(queryAuthRoleList, {
    onSuccess: (res: any) => {
      if (res?.errorMessage === null) {
        const { data } = res;
        setRoleData(data || []);
      }
    },
    manual: true
  });

  // 查询角色关联授权规则
  const {
    loading: queryAuthListLoading,
    run: queryAuthList,
    refresh: reQueryAuthList
  } = useRequest(queryAuthInfoRuleList, {
    onSuccess: (res: ApiResponse<PageResponse<AuthBaseInfo>>) => {
      if ((res?.rpcResult === 'SUCCESS' || res?.rpcResult === '000000') && !res?.errorMessage) {
        const { data } = res;
        setRulesData(data.list || []);
        setPagination((prev) => ({
          ...prev,
          current: data.pageNum,
          total: data.total
        }));
      } else {
        YRMessage.error(res?.errorMessage || '查询失败');
      }
    },
    manual: true
  });

  // 查询角色关联授权规则
  const { run: submitRun, loading: submitLoading } = useRequest(submitAuthRuleInfo, {
    onSuccess: (res: any) => {
      if (res?.errorMessage === null) {
        YRMessage.success('提交复核成功', 0.5, () => {
          reQueryAuthList();
        });
      }
    },
    manual: true
  });

  // 查询授权关联角色
  useEffect(() => {
    const { orgId, authCategory, authCatalogId } = currentOrganParams;
    setRoleData([]);
    if (orgId && authCategory && authCatalogId) {
      queryRelationRole(currentOrganParams);
      setCurrentRole({ roleId: '', authNo: '' });
      setRulesData([]);
      // 重置分页
      setPagination((prev) => ({ ...prev, current: 1, total: 0 }));
    }
  }, [currentOrganParams, queryRelationRole]);

  // 查询角色关联授权规则
  const fetchAuthList = useCallback(() => {
    const { orgId, authCategory, authCatalogId } = currentOrganParams;

    if (currentRole.roleId) {
      const queryParams: QueryParams = {
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
        roleId: currentRole.roleId,
        authCategory,
        orgId,
        authCatalogRoleId: authCatalogId
      };
      queryAuthList(queryParams);
    } else {
      // 清空数据并重置分页
      setRulesData([]);
      setPagination((prev) => ({ ...prev, current: 1, total: 0 }));
    }
  }, [currentRole, currentOrganParams, activeKey, pagination.current, pagination.pageSize, queryAuthList]);

  useEffect(() => {
    fetchAuthList();
  }, [fetchAuthList]);

  // 组件卸载时清理异步操作
  useEffect(() => {
    return () => {
      // 清理可能的异步操作
    };
  }, []);

  const handleSearch = () => {
    form.validateFields().then((formValues: any) => {
      const { orgId, authCategory, authCatalogId } = currentOrganParams;

      if (currentRole.roleId) {
        // 处理暂停截止日期查询
        let pauseDeadlineDate = '';
        if (formValues.pauseDeadlineDate) {
          pauseDeadlineDate = formValues.pauseDeadlineDate;
        }

        const queryParams: QueryParams = {
          pageNum: 1, // 搜索时重置到第一页
          pageSize: pagination.pageSize,
          roleId: currentRole.roleId,
          authCategory,
          orgId,
          authCatalogRoleId: authCatalogId,
          authStatus: formValues.authStatus,
          customerType: formValues.customerType,
          pauseDeadlineDate,
          transferAuthFlag: formValues.transferAuthFlag
        };

        setPagination((prev) => ({ ...prev, current: 1 }));
        queryAuthList(queryParams);
      }
    });
  };

  // 获取授权类别角色关联ID
  const getCurrentRoleNode = () => {
    return roleTreeRef.current?.getCurrentRoleNodeInfo();
  };

  // 分页变化处理
  const handlePaginationChange = (page: number, pageSize?: number) => {
    const newPagination = {
      ...pagination,
      current: page,
      pageSize: pageSize || pagination.pageSize
    };
    setPagination(newPagination);

    // 重新查询数据
    const { orgId, authCategory, authCatalogId } = currentOrganParams;
    if (currentRole.roleId) {
      const queryParams: QueryParams = {
        pageNum: page,
        pageSize: pageSize || pagination.pageSize,
        roleId: currentRole.roleId,
        authCategory,
        orgId,
        authCatalogRoleId: authCatalogId
      };
      queryAuthList(queryParams);
    }
  };

  //   const rowSelection = {
  //     type: 'checkout',
  //     onChange: (selectedRowKeys, rows) => {
  //       setSelectedKeys(selectedRowKeys);
  //       setSelectedRows(rows);
  //     },
  //     selectedRowKeys: selectedKeys
  //   };

  const operationRender = (
    <YRButton.Space>
      <YRButton
        type="primary"
        onClick={() => {
          YREasyUseModal.show(RulesAddModal, {
            mode: 'add',
            params: { ...currentOrganParams, ...currentRole },
            okCallback: reQueryAuthList,
            getCurrentRoleNode
          });
        }}
      >
        + 新增授权
      </YRButton>
    </YRButton.Space>
  );

  return (
    <YRFlexPageLayout ref={containerRef}>
      <YRFlexPageLayout.Sider
        title="机构列表"
        extra={
          <YRButton
            type="text"
            onClick={() => {
              YREasyUseModal.show(AddModal, {
                okCallback: queryOrgan
              });
            }}
          >
            新增授权类型
          </YRButton>
        }
      >
        <OrganTree
          // okCallback={refreshRoleTree}
          okCallback={queryOrgan}
          height={listHeight || 800}
          loading={queryOrganTreeLoading}
          data={organData}
          setData={setCurrentOrganParams}
        />
      </YRFlexPageLayout.Sider>
      <YRFlexPageLayout.Main contentNoPadding>
        <YRFlexPageLayout>
          <YRFlexPageLayout.Sider
            title="角色列表"
            extra={
              <YRButton
                type="text"
                onClick={() => {
                  YREasyUseModal.show(AddRoleModal, {
                    params: currentOrganParams,
                    okCallback: refreshRoleTree
                  });
                }}
              >
                新增授权角色
              </YRButton>
            }
          >
            <RoleTree
              okCallback={refreshRoleTree}
              data={roleData}
              loading={queryAuthRoleListLoading}
              setData={setCurrentRole}
              ref={roleTreeRef}
            />
          </YRFlexPageLayout.Sider>
          <YRFlexPageLayout.Main>
            {currentRole.roleId ? (
              <>
                <YRTabs
                  defaultActiveKey={activeKey}
                  type="card"
                  onChange={(key) => {
                    setActiveKey(key);
                    // 切换业务条线时重置分页
                    setPagination((prev) => ({ ...prev, current: 1 }));
                  }}
                  items={tabs!.map((item) => {
                    const { tab, key } = item;
                    return {
                      label: tab,
                      key,
                      children: null
                    };
                  })}
                />
                <div style={{ height: listHeight - 200 }}>
                  <YRTable
                    rowKey={'authBaseId'}
                    form={form}
                    dataSource={rulesData}
                    handleSearch={handleSearch}
                    loading={queryAuthListLoading}
                    columns={columns(reQueryAuthList, submitRun, submitLoading, getCurrentRoleNode)}
                    formItemList={formItemList}
                    operationRender={operationRender}
                    pagination={{
                      ...pagination,
                      onChange: handlePaginationChange,
                      onShowSizeChange: handlePaginationChange
                    }}
                  />
                </div>
              </>
            ) : (
              <YREmpty style={{ marginTop: '110px' }} />
            )}
          </YRFlexPageLayout.Main>
        </YRFlexPageLayout>
      </YRFlexPageLayout.Main>
    </YRFlexPageLayout>
  );
};

export default AuthPage;
