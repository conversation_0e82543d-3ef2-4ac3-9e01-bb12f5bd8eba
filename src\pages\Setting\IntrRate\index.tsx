/*
 * @Author: 伍晶晶
 * @Description: 基准利率
//  * @Date: 2023-03-17 09:18:59
 * @LastEditors: 伍晶晶
 * @LastEditTime: 2023-03-24 14:39:45
 */
import { intrRateSelectPage } from '@/services/setting';
import MultiViewTable, { ViewTableProps } from '@yr/multi-view-table';
import { SearchProps } from '@yr/multi-view-table/lib/ViewTable/type';
import { useQueryMultiViewTableData, Dict } from '@yr/util';
import React from 'react';
import { YRBadge, YRDatePicker, YRDict, YRIndexPageLayout } from 'yrantd';

const { RangePicker } = YRDatePicker;

const IntrRate = () => {
  const { tableProps, query } = useQueryMultiViewTableData(intrRateSelectPage);

  const columns: ViewTableProps['columns'] = [
    {
      title: '基本利率名称',
      dataIndex: 'baseRateName'
    },
    {
      title: '基本利率状态',
      dataIndex: 'baseRateStatus',
      render: (v) => <YRBadge status={v === 'Y' ? 'success' : 'error'} text={v === 'Y' ? '启用' : '禁用'} />
    },
    {
      title: '基本利率类型',
      dataIndex: 'baseType',
      render: (value) => <YRDict.Text dictkey="base_rate_type" defaultValue={value} />
    },
    {
      title: '五年期LPR利率',
      dataIndex: 'fiveYearLpr',
      render: (value) => <>{value ? `${value * 100}%` : CONST.null}</>
    },
    {
      title: '一年期LPR利率',
      dataIndex: 'oneYearLpr',
      render: (value) => <>{value ? `${value * 100}%` : CONST.null}</>
    },
    {
      title: '五年以上利率',
      dataIndex: 'outFiveYear',
      render: (value) => <>{value ? `${value * 100}%` : CONST.null}</>
    },
    {
      title: '生效时间',
      dataIndex: 'efficientDate'
    },
    {
      title: '发布时间',
      dataIndex: 'releaseDate'
    }
  ];

  const searchList: SearchProps['searchList'] = [
    {
      key: 'efficientDate',
      label: '生效时间',
      type: 'component',
      component: <RangePicker />
    }
  ];

  return (
    <YRIndexPageLayout>
      <MultiViewTable
        business="home"
        viewOpt={{ title: '基准利率' }}
        rowKey="reteId"
        searchOpt={{ searchList }}
        onSubmit={query}
        columns={columns}
        {...tableProps}
      />
    </YRIndexPageLayout>
  );
};

export default Dict(['base_rate_type'])(IntrRate);
