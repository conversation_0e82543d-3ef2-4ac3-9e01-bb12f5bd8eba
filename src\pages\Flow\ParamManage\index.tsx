/*
 * @Author: liaokt
 * @E-mail: <EMAIL>
 * @Date: 2023-03-22 09:17:32
 * @Description:
 */
/**
 * @Author: 刘发浩
 * @Description: 流程管理列表
 * @Date: Created in 2022-12-09 16:46:35
 * @Modifed By:
 */

import React, { useState, useRef } from 'react';
import { YRButton, YRFlexPageLayout, YREmpty, YRDict } from 'yrantd';
import ParamList from './components/ParamList';
import ParamContext from './components/ParamContent';
import ParamAddModal from './components/ParamAddModal';
import ParamLibraryAddModal from './components/ParamLibraryAddModal';

const ParamManage = () => {
  const contentRef: any = useRef();
  const treeRef: any = useRef();
  const [activity, setActivity] = useState<any>();
  const [visible, setVisible] = useState<any>(false);
  const [libraryVisible, setLibraryVisible] = useState<any>(false);
  /** 当前参数项 */
  const [currentParam, setCurrentParam] = useState<any>({});

  /** 新增参数项回调函数 */
  const onAddOk = () => {
    if (contentRef.current) {
      contentRef.current.refresh();
    }
  };

  /** 新增参数库回调函数 */
  const onAddLibraryOk = () => {
    if (treeRef.current) {
      treeRef.current.typeRequest();
    }
  };

  /** ==== 接口 ==== */
  return (
    <YRFlexPageLayout direction={'horizontal'}>
      <YRFlexPageLayout.Sider
        title="参数库"
        extra={
          <YRButton
            type={'text'}
            onClick={() => {
              setLibraryVisible(true);
            }}
          >
            新增
          </YRButton>
        }
      >
        <ParamList ref={treeRef} activity={activity} setActivity={setActivity} />
      </YRFlexPageLayout.Sider>
      <YRFlexPageLayout.Main
        title={'参数信息'}
        extra={
          activity && (
            <YRButton
              type={'primary'}
              onClick={() => {
                setVisible(true);
              }}
            >
              新增
            </YRButton>
          )
        }
      >
        {activity ? (
          <ParamContext
            ref={contentRef}
            activity={activity}
            changeVisible={setVisible}
            setCurrentParam={setCurrentParam}
          />
        ) : (
          <YREmpty style={{ marginTop: '60px' }} />
        )}
      </YRFlexPageLayout.Main>
      <ParamAddModal
        visible={visible}
        activity={activity}
        currentParam={currentParam}
        okCallback={onAddOk}
        changeVisible={setVisible}
        setCurrentParam={setCurrentParam}
      />
      <ParamLibraryAddModal visible={libraryVisible} changeVisible={setLibraryVisible} onOkCallback={onAddLibraryOk} />
    </YRFlexPageLayout>
  );
};

export default ParamManage;
