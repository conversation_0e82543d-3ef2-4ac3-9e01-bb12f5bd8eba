/**
 * @Author: wangyw26123
 * @Description: 高级-节点配置
 * @Date: Created in 2022-04-27 14:08:32
 * @Modifed By:
 */

import React, { useState } from 'react';
import { YRSelect, YRForm, YRInput, YRMessage, YRAutoComplete, YREditableTable, YRSpace, YRInputNumber } from 'yrantd';
import { useAntdTable, useDebounceEffect, useRequest } from 'ahooks';
import { queryFlowDiagramAndVersion, queryServiceTaskDelegates } from '@/services/flow';
import { EditableColumnsType } from 'yrantd/lib/yr-editable-table/declare';
import store from '@/store';
import FlowModal from '../../../components/FlowModal';
import ServiceParamsAddModal from './components/ServiceParamsAddModal';
import AddDecisionModal from './components/AddDecisionModal';

const FormItem = YRForm.Item;
const { Option } = YRSelect;

const callactivitycalleddelementtypeList = [
  {
    value: '流程编号',
    key: 'key'
  },
  {
    value: '流程 id',
    key: 'id'
  }
];

const NodeConfig = (props) => {
  const {
    globalDisabled,
    targetData: { nodeFormData, name },
    form
  } = props;

  const [state] = store.useModel('flow');
  const { globalFormData } = state;
  // 子流程使用的引用元素弹窗
  const [visible, setVisible] = useState<any>(false);
  // 子流程选择的引用元素
  const [currentSonElement, setCurrentSonElement] = useState<any>({});
  // 子流程当前选中节点
  const [currentSonId, setCurrentSonId] = useState<any>();
  // 子流程引用元素列表
  const [sonList, setSonList] = useState<any>([]);
  // 子流程选择的引用类型
  const [elementType, setElementType] = useState<any>();
  // 服务节点参数数据
  const [dataSource, setDataSource] = useState<any>([]);
  // 服务节点弹窗
  const [serviceVisible, setServiceVisible] = useState<Boolean>(false);
  // 服务节点执行程序配置
  const [serviceConfig, setServiceConfig] = useState<any>([]);
  // 服务节点是否展示参数列表
  const [serviceFlag, setServiceFlag] = useState<boolean>(false);
  // 引入决策表弹窗
  const [tableVisible, setTableVisible] = useState<boolean>(false);
  // 当前选中决策表
  const [currentTable, setCurrentTable] = useState<any>({});
  // 监听引用类型
  const referenceType = YRForm.useWatch(['properties', 'callactivitycalledelementtype'], form);
  const callactivitycalledelement = YRForm.useWatch(['properties', 'callactivitycalledelement'], form);
  const tableId = YRForm.useWatch(['properties', 'decisiontaskdecisiontablereference', 'id'], form);
  const { signaldefinitions } = globalFormData || [];

  /** 查询引用元素接口 */
  const {
    tableProps,
    run,
    params,
    data: tableData
  } = useAntdTable(
    (p) => {
      return queryFlowDiagramAndVersion({ ...p, pageNum: p.current }).then((res) => {
        return res.success ? { list: res.data.list, total: res.data.total } : { list: [], total: 0 };
      });
    },
    {
      manual: true
    }
  );

  // 查询服务节点执行程序枚举
  const { loading: limitLoading, run: queryTask } = useRequest(queryServiceTaskDelegates, {
    manual: true,
    onSuccess: (result) => {
      const { errorMessage, data } = result;
      if (errorMessage === null) {
        setServiceConfig(data);
      }
    }
  });

  /** 当为服务节点时，调用接口，查询执行程序枚举 */
  useDebounceEffect(() => {
    if (name === 'CUSTOM-NODE-SERVICE') {
      queryTask();
    }
  }, [name]);

  /** 查询所有引用元素接口 */
  useDebounceEffect(() => {
    queryFlowDiagramAndVersion({}).then((res) => {
      res.success && setSonList(res?.data?.list);
    });
  }, []);

  /** 修改引用类型时事件 */
  useDebounceEffect(() => {
    if (referenceType && elementType) {
      form.setFieldsValue({ properties: { callactivitycalledelement: null, delement: null } });
    } else {
      setElementType(referenceType);
    }
  }, [referenceType]);

  /** 设置当前选中引用元素，寻找当前引用元素节点 */
  useDebounceEffect(() => {
    if (callactivitycalledelement && sonList?.length > 0) {
      setCurrentSonId([callactivitycalledelement]);
      handleFindElementByNo(callactivitycalledelement);
    }
  }, [callactivitycalledelement, sonList]);

  /** 判断服务节点是否存在参数，如果有参数的话，展示表格 */
  useDebounceEffect(() => {
    if (nodeFormData) {
      const _params = nodeFormData?.properties?.servicetaskdefinition?.params || [];
      setServiceFlag(true);
      _params && setDataSource(_params);
    }
  }, [nodeFormData]);

  /** 服务节点表格配置 */
  const defaultColumns: EditableColumnsType<any> = [
    {
      title: '字段英文名',
      dataIndex: 'field',
      key: 'field',
      editable: false,
      width: 80
    },
    {
      title: '字段中文名',
      dataIndex: 'label',
      key: 'label',
      editable: false,
      width: 80
    },
    {
      title: '参数值',
      dataIndex: 'stringValue',
      key: 'stringValue',
      required: true,
      editable: true,
      width: 80,
      renderFormItem: (form, { record, rowIndex, dataIndex, key }) => {
        const { variable } = record;
        const { values, isSelect, isNumber, isDecimal } = variable || {};

        let flag = <></>;
        if (isSelect) {
          flag = (
            <YRSelect>
              {values?.map((item) => {
                return <Option value={item?.value}>{item?.display}</Option>;
              })}
            </YRSelect>
          );
        } else if (isNumber) {
          flag = <YRInputNumber style={{ width: '100%' }} precision={0} />;
        } else if (isDecimal) {
          flag = <YRInputNumber style={{ width: '100%' }} step={0.01} stringMode />;
        } else {
          flag = <YRInput />;
        }
        return flag;
      }
    }
  ];

  /** 从数组中获取节点 */
  const getNodeByKey = (key: React.Key, tree: any[]) => {
    let currentKey: React.Key;
    const recutision = (treeData, param) => {
      if (!treeData) return null;
      for (let i = 0; i < treeData?.length; i++) {
        if (treeData[i].versionId === param) {
          currentKey = treeData[i];
          break;
        }
        if (treeData[i].versions) {
          recutision(treeData[i].versions, key);
        }
      }
      return currentKey;
    };
    const ret = recutision(tree, key);
    return ret;
  };

  /** 子流程选择引用元素 */
  const handleSelectElememt = (values: any) => {
    if (values) {
      const data = values[0];
      setCurrentSonElement({ ...data });
      // 版本 key | 编号 id
      const currentValue = referenceType === 'key' ? data?.modelNumber : data?.versionId;
      let text = '';
      // console.log(values, '----------------- currentValue ----------------');

      if (referenceType === 'key') {
        text = data?.modelName;
      } else {
        text = data?.modelName + ':' + data?.version;
      }

      form.setFieldsValue({
        properties: { callactivitycalledelement: currentValue, delement: text }
      });
    }
  };

  /** 查询编号引用元素 */
  const handleFindElementByNo = (no: string) => {
    // 1. 当前情况为编号
    if (referenceType === 'key') {
      const data = sonList?.filter((item: any) => {
        return no === item?.modelNumber;
      });
      console.log(data);

      form.setFieldsValue({
        properties: { delement: data[0]?.modelName }
      });
    } else {
      const data: any = getNodeByKey(no, sonList);
      console.log(data, 'xxxx');
      form.setFieldsValue({
        properties: { delement: data?.modelName + ': ' + data?.version }
      });
    }
  };

  // 服务节点添加参数事件
  const onOk = (vlaues: any) => {
    const list = [...dataSource];
    list.push({ ...vlaues });
    setDataSource([...list]);
  };

  // 服务节点删除参数事件
  const handleDelete = (index: any) => {
    const list = [...dataSource];
    list.splice(index, 1);
    setDataSource([...list]);
  };

  // 获取决策表信息
  const getDecisionInfo = (values: any) => {
    const { modelNumber, modelName, id } = values[0] || {};
    setCurrentTable(values[0]);
    form.setFieldsValue({
      properties: { decisiontaskdecisiontablereference: { name: modelName, id, key: modelNumber } }
    });
    setTableVisible(false);
  };

  return (
    <>
      {name === 'CUSTOM-NODE-TIMER' && (
        <FormItem
          name={['properties', 'dueDate', 'value']}
          label="持续时间"
          initialValue={nodeFormData?.properties?.dueDate?.value}
          rules={[
            {
              required: true,
              pattern: new RegExp(/^[1-9]\d*$/, 'g'),
              message: '请输入正整数'
            }
          ]}
        >
          <YRInput
            disabled={globalDisabled}
            addonAfter={
              <FormItem
                name={['properties', 'dueDate', 'type']}
                initialValue={nodeFormData?.properties?.dueDate?.type || '1'}
                rules={[
                  {
                    required: true,
                    message: '请选择单位'
                  }
                ]}
                noStyle
              >
                <YRSelect disabled={globalDisabled} style={{ width: 70 }} placeholder="请选择">
                  <Option value="1">分</Option>
                  <Option value="2">时</Option>
                  <Option value="3">天</Option>
                </YRSelect>
              </FormItem>
            }
          />
        </FormItem>
      )}
      {(name === 'CUSTOM-NODE-SIGNALCATCH' || name === 'CUSTOM-NODE-SIGNALTHROW') && (
        <FormItem
          name={['properties', 'signalref']}
          label="信号编号"
          tooltip="如果下拉框为空，请先去全局配置创建信号"
          initialValue={nodeFormData?.properties?.signalref}
        >
          <YRSelect disabled={globalDisabled}>
            {signaldefinitions?.length > 0 &&
              signaldefinitions.map((item: any) => {
                return (
                  <Option value={item.id} key={item.id}>
                    {item.name}
                  </Option>
                );
              })}
          </YRSelect>
        </FormItem>
      )}
      {name === 'CUSTOM-NODE-SUBPROCESS' && [
        <FormItem
          name={['properties', 'callactivitycalledelementtype']}
          label="引用类型"
          initialValue={nodeFormData?.properties?.callactivitycalledelementtype}
          tooltip={{
            title:
              '当选用流程编号时，父流程默认关联的是子流程的最新发布版本，子流程升级，父流程自动升级，若要指定版本请选择流程id'
          }}
        >
          <YRSelect disabled={globalDisabled}>
            {callactivitycalleddelementtypeList?.length > 0 &&
              callactivitycalleddelementtypeList.map((item: any) => {
                return (
                  <Option value={item.key} key={item.key}>
                    {item.value}
                  </Option>
                );
              })}
          </YRSelect>
        </FormItem>,
        <FormItem
          name={['properties', 'delement']}
          label="引用元素"
          initialValue={nodeFormData?.properties?.callactivitycalledelement}
        >
          <YRInput
            disabled={globalDisabled}
            placeholder="请选择引用元素"
            onClick={() => {
              if (referenceType) {
                setVisible(true);
              } else {
                YRMessage.info('请先选择引用类型');
              }
            }}
          />
        </FormItem>,
        <FormItem
          name={['properties', 'callactivitycalledelement']}
          initialValue={nodeFormData?.properties?.callactivitycalledelement}
          hidden
        >
          <YRInput
            disabled={globalDisabled}
            placeholder="请选择引用元素"
            onClick={() => {
              if (referenceType) {
                setVisible(true);
              } else {
                YRMessage.info('请先选择引用类型');
              }
            }}
          />
        </FormItem>
      ]}
      {name === 'CUSTOM-NODE-SERVICE' && [
        <YRSpace direction={'vertical'} size={'large'}>
          <FormItem
            label="执行程序"
            name={['properties', 'servicetaskdefinition', 'servicetaskclass']}
            initialValue={nodeFormData?.properties?.servicetaskdefinition?.servicetaskclass}
            rules={[{ required: true, message: '执行程序不能为空' }]}
          >
            <YRSelect
              disabled={globalDisabled}
              style={{ width: '100%' }}
              placeholder="请选择"
              onChange={(e) => {
                serviceConfig?.forEach((item) => {
                  if (item?.className?.value === e) {
                    item?.params?.length > 0 ? setServiceFlag(true) : setServiceFlag(false);
                    const data = item?.params?.map((_item) => {
                      return {
                        field: _item?.field?.value,
                        label: _item?.field?.display,
                        variable: _item?.variable
                      };
                    });
                    setDataSource(data);
                  }
                });
              }}
            >
              {serviceConfig?.map((item) => {
                return <Option value={item?.className?.value}>{item?.className?.display}</Option>;
              })}
            </YRSelect>
          </FormItem>
          {serviceFlag && (
            <YREditableTable
              form={form}
              name={['properties', 'servicetaskdefinition', 'params']}
              showIndex={false}
              mode={globalDisabled ? 'readPretty' : 'editable'}
              dataSource={dataSource}
              columns={defaultColumns}
              rowKey={(row, index) => index}
              hiddenToolbar
            />
          )}
        </YRSpace>
      ]}
      {name === 'CUSTOM-NODE-DECISION' && [
        <FormItem
          name={['properties', 'decisiontaskdecisiontablereference', 'name']}
          label={'决策表引入'}
          initialValue={nodeFormData?.properties?.decisiontaskdecisiontablereference?.name}
        >
          <YRInput
            placeholder="请引入决策表"
            onClick={() => {
              setTableVisible(true);
            }}
            disabled={globalDisabled}
          />
        </FormItem>,
        <FormItem
          name={['properties', 'decisiontaskdecisiontablereference', 'id']}
          initialValue={nodeFormData?.properties?.decisiontaskdecisiontablereference?.id}
          hidden
        >
          <YRInput />
        </FormItem>,
        <FormItem
          name={['properties', 'decisiontaskdecisiontablereference', 'key']}
          initialValue={nodeFormData?.properties?.decisiontaskdecisiontablereference?.key}
          hidden
        >
          <YRInput />
        </FormItem>
      ]}
      <ServiceParamsAddModal visible={serviceVisible} changeVisible={setServiceVisible} onOK={onOk} />
      <FlowModal
        visible={visible}
        changeVisible={setVisible}
        onOk={handleSelectElememt}
        form={form}
        tableProps={tableProps}
        run={run}
        params={params}
        currentSonId={currentSonId}
        currentSonElement={currentSonElement}
      />
      <AddDecisionModal
        visible={tableVisible}
        changeVisible={setTableVisible}
        onOK={getDecisionInfo}
        tableId={tableId}
        currentTable={currentTable}
      />
    </>
  );
};

export default NodeConfig;
