import { uuid } from '@yr/util';
import { YRMessage } from 'yrantd';

// 处理规则数据反显展示
export function getRuleData(ruleData) {
  let treeData = {}; // 数据
  const treeDataMap = {}; // redux 数据映射
  // 未有数据时
  if (
    !ruleData ||
    Object.keys(ruleData).length === 0 ||
    (!ruleData?.ruleExpressDtoList && !ruleData?.ruleExpressParentDtoList)
  ) {
    // 生成唯一标识
    const key = uuid();
    // 组装数据
    treeData = {
      key,
      junctionType: 'and'
    };
    treeDataMap[key] = { junctionType: 'and' };
  } else {
    const loop = function (data) {
      const key = uuid();
      if (data.junctionType) {
        data.key = key;
        data.criterions = [...(data?.ruleExpressDtoList || []), ...(data?.ruleExpressParentDtoList || [])];
        delete data.ruleExpressDtoList;
        delete data.ruleExpressParentDtoList;
        // 是否有子集 遍历
        if (data.criterions && data.criterions.length > 0) {
          data.criterions = data.criterions.map((item) => {
            return loop(item);
          });
        }
        treeDataMap[key] = data;
      } else {
        data.key = key;
        treeDataMap[key] = data;
      }
      return data;
    };
    treeData = loop(JSON.parse(JSON.stringify(ruleData)));
  }
  return { treeData, treeDataMap };
}

// 处理规则数据给接口
export function formatRuleData(treeData, treeDataMap) {
  let ruleData = {}; // 数据
  let includeEmptyData = false; // 当前条件中是否有未填表达式
  let emptyParent = false; // 当前条件中是否有联合条件下无子表达式
  // 未有数据时
  if (!treeData || Object.keys(treeData).length === 0 || !treeData?.criterions || treeData?.criterions?.length < 1) {
    YRMessage.error('请添加规则条件');
    return;
  } else {
    const loop = function (data) {
      let newData = { ...data };
      const mapData = { ...(treeDataMap[data.key] || {}) };
      if (newData?.key) {
        delete newData?.key;
      }
      if (data?.junctionType) {
        newData.junctionType = mapData?.junctionType;
        newData.ruleExpressDtoList = (newData?.criterions || [])
          .filter((i) => !i?.junctionType)
          .map((item) => {
            const mapItemData = { ...(treeDataMap[item.key] || {}) };
            if (mapItemData?.key) {
              delete mapItemData?.key;
            }
            return {
              ...mapItemData
            };
          });
        newData.ruleExpressParentDtoList = (newData?.criterions || []).filter((i) => i?.junctionType);
        // 判断联合条件下是否不存在表达式
        if (!newData?.ruleExpressDtoList || newData?.ruleExpressDtoList.length < 1) {
          emptyParent = true;
        }
        // 判断当前页面是否有未填表达式
        if (
          newData?.ruleExpressDtoList.find((item) => {
            const leftEmpty = !item?.leftVariableRuleDto || Object.keys(item?.leftVariableRuleDto).length < 1;
            const opEmpty = !item?.op;
            const rightEmpty =
              !['为空', '不为空'].includes(item?.op) &&
              (!item?.rightVariableRuleDto ||
                Object.keys(item?.rightVariableRuleDto).length < 1 ||
                (!item?.rightVariableRuleDto?.value && !item?.rightVariableRuleDto?.variableCode));
            return leftEmpty || opEmpty || rightEmpty;
          })
        ) {
          includeEmptyData = true;
        }
        // 删除多余数据
        if (newData?.criterions) {
          delete newData?.criterions;
        }
        // 是否有子集 遍历
        if (newData.ruleExpressParentDtoList && newData.ruleExpressParentDtoList.length > 0) {
          newData.ruleExpressParentDtoList = newData.ruleExpressParentDtoList.map((item) => {
            return loop(item);
          });
        }
      } else {
        newData = mapData;
      }
      return newData;
    };
    ruleData = loop(treeData);
  }
  if (emptyParent) {
    YRMessage.error('联合条件下必须有表达式');
    return;
  }
  if (includeEmptyData) {
    YRMessage.error('规则条件表达式不能为空');
    return;
  }
  return ruleData;
}

// if 增加或者删除公共处理方法
function commToDo({ ruleData, id, obj, type, key }) {
  const data = JSON.parse(JSON.stringify(ruleData));
  // 是否找到
  let isFind = false;
  // 递归寻找
  function loop(loopData) {
    // 是否是
    if (loopData.key === id) {
      isFind = true;
      if (type === 'add') {
        loopData.criterions = Array.isArray(loopData.criterions) ? [...loopData.criterions, obj] : [obj];
      } else if (type === 'remove') {
        loopData.criterions = loopData.criterions.filter((item) => item.key !== key);
      }
    } else {
      loopData.criterions &&
        loopData.criterions.forEach((item) => {
          if (item.junctionType && !isFind) {
            loop(item);
          }
        });
    }
  }
  loop(data);
  return data;
}

// 增加条件
export function addIfOne({ ruleData, id, obj }) {
  return commToDo({ ruleData, id, obj, type: 'add' });
}

// 删除某个
export function removeIfOne({ ruleData, parentId, id }) {
  return commToDo({ ruleData, id: parentId, key: id, type: 'remove' });
}
