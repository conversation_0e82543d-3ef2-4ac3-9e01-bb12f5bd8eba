/*
 * @Author: DJscript
 * @Date: 2023-02-20 16:37:25
 * @LastEditTime: 2023-02-21 15:34:49
 * @FilePath: /kylin-admin/src/pages/Organ/OrganManage/components/MappingMechanismInfo.tsx
 * @Description: 映射机构信息
 */
import React, { useEffect, useRef } from 'react';
import { YRButton, YRClassificationLayout, YRForm, YRLink, YRSelect, YRSpace, YRTable, YRTreeSelect } from 'yrantd';
import EditPlusTable from '@/components/EditPlusTable';
import type { EditPlusTableProps } from '@/components/EditPlusTable';

type IMappingMechanismInfoProps = {
  mode: EditPlusTableProps['IEditPlusTableProps']['mode'];
  list: any[];
};

const MappingMechanismInfo: React.FC<IMappingMechanismInfoProps> = ({ mode, list }) => {
  const form = YRForm.useFormInstance();

  const organType = YRForm.useWatch(['organType'], form);
  const editRef = useRef<EditPlusTableProps['EditRefType']>(null);
  const columns: EditPlusTableProps['IEditPlusTableProps']['columns'] = [
    {
      dataIndex: 'relationType',
      title: '映射机构类型',
      valueType: 'dictSelect',
      dictkey: '映射机构类型',
      formItemProps: {
        rules: [{ required: organType === '001', message: '请选择映射机构类型' }]
      },
      componentProps: ({ rowIndex }) => ({
        onChange(v) {
          const organMapList = form.getFieldValue('organMapList') || [];
          if (['001', '002', '003'].includes(v)) {
            organMapList[rowIndex].organMapSource = '001';
          }
          form.setFieldValue('organMapList', [...organMapList]);
        }
      })
    },
    {
      dataIndex: 'relationOrganName',
      title: '映射机构名称',
      valueType: 'component',
      formItemProps: {
        rules: [{ required: organType === '001', message: '请选择映射机构名称' }]
      },
      component: (p) => {
        return (
          <YRTreeSelect
            {...p}
            showSearch
            treeData={[
              {
                value: '1',
                title: '你好',
                children: [
                  {
                    value: '2',
                    title: '👋'
                  }
                ]
              }
            ]}
          />
        );
      }
    },
    {
      dataIndex: 'relationOrganId',
      title: '映射机构编号',
      valueType: 'input',
      formItemProps: {
        rules: [{ required: organType === '001', message: '请输入映射机构编号' }]
      },
      componentProps: {
        disabled: true
      }
    },
    {
      title: '操作',
      width: 60,
      render(value, record, index, fn) {
        return (
          <YRLink type="primary" onClick={() => fn.del()}>
            删除
          </YRLink>
        );
      }
    }
  ];

  useEffect(() => {
    list && form.setFieldValue('organMapList', list);
  }, [list]);
  return (
    <YRClassificationLayout title="映射机构">
      <YRSpace direction="vertical" block>
        {mode !== 'readPretty' && (
          <YRButton type="primary" onClick={() => editRef.current?.add({ relationOrganId: 10086, organMapSource: '' })}>
            新增
          </YRButton>
        )}
        <YRForm.Item name="organMapList" noStyle>
          <EditPlusTable mode={mode} editRef={editRef} columns={columns} />
        </YRForm.Item>
      </YRSpace>
    </YRClassificationLayout>
  );
};

export default MappingMechanismInfo;
