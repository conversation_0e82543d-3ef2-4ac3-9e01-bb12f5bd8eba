/**
 * @页面描述: 用户管理权限
 * @文件名 user.ts
 * @Path permit\user.ts
 * @Date 2023-03-22 09:22:27
 * <AUTHOR>
 */

import packageInfo from '../package.json';
import { YRLoanPermit } from 'yr-loan-antd';
import { resetPassword } from '@/services/user';

const { buildPermit, EnumPermitType } = YRLoanPermit;
/**
 * 用户管理
 */
const M0104 = buildPermit('M0104', packageInfo, {
  interfaces: {}
});
const M010401 = buildPermit('M010401', packageInfo, {
  interfaces: {},
  E01: {
    name: '超管权限移交',
    type: EnumPermitType.Element,
    interfaces: {}
  },
  E10: {
    name: '重置密码',
    path: '/hsjry/guard/base/IUser/resetPassword',
    type: EnumPermitType.Element,
    interfaces: {
      resetPassword
    }
  }
});
const M010402 = buildPermit('M010402', packageInfo, {
  interfaces: {}
});
export { M0104, M010401, M010402 };
