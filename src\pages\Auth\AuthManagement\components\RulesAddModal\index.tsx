/* eslint-disable @typescript-eslint/indent */
/* eslint-disable no-nested-ternary */
/*
 * @Author: liaokt
 * @Description: 授权规则新增弹窗
 * @Date: 2024-02-27 16:10:32
 * @LastEditors: liaokt
 * @LastEditTime: 2024-10-25 08:58:51
 */
import {
  YRButton,
  YRCard,
  YRCol,
  YRDict,
  YREasyUseModal,
  YRForm,
  YRInput,
  YRInputNumber,
  YRMessage,
  YRModal,
  YRRow,
  YRSelect,
  YRSpace,
  YRTable,
  YRTag
} from 'yrantd';
import React, { useEffect, useState, useRef } from 'react';
import { uuid } from 'yr-loan-antd/lib/util';
import { queryDimensionLoadByAuthCode } from '@/services/auth';
import ProductSelectModal from './ProductSelectModal';
import styles from './index.module.less';

// 产品信息接口
interface ProductInfo {
  productId: string;
  productName: string;
  bizLine: string;
  [key: string]: any;
}

// 维度信息接口
interface DimensionInfo {
  id: string;
  conditionDimensionName: string;
  resultDimensionName?: string;
  resultValue: number | string; // 支持数字和字符串
  isDefault?: boolean;
  order: number;
  dimensionType?: 'RESULT' | 'CONDITION'; // 新增维度类型标记
  selectedProducts?: ProductInfo[]; // 新增已选产品列表
}

// 简化版本的组件，逐步恢复功能
const AuthRuleAddModal = (props) => {
  const { mode = 'add', params, okCallback } = props;

  const [form] = YRForm.useForm();
  const [dimensions, setDimensions] = useState<DimensionInfo[]>([]);
  const [conditionDimensionOptions, setConditionDimensionOptions] = useState<any[]>([]);
  const [resultDimensionOptions, setResultDimensionOptions] = useState<any[]>([]);
  const [productModalVisible, setProductModalVisible] = useState<boolean>(false);
  const [currentDimensionId, setCurrentDimensionId] = useState<string>('');
  const modal = YREasyUseModal.useModal();
  const isMountedRef = useRef(true);

  // 加载维度选项
  const loadDimensionOptions = (authCategory: string) => {
    if (!authCategory) return;

    // 手动添加产品组选项确保其可用
    setConditionDimensionOptions(prev => {
      // 检查是否已存在产品组选项
      if (!prev.some(item => item.itemName === '产品组')) {
        console.log('添加产品组选项到维度列表');
        return [...prev, { itemKey: 'product_group', itemName: '产品组' }];
      }
      return prev;
    });

    // 调用维度动态加载接口
    queryDimensionLoadByAuthCode({ authCode: authCategory }).then((res) => {
      if (!isMountedRef.current) return;
      
      if (res?.rpcResult === 'SUCCESS' && !res?.errorMessage) {
        const { data } = res;
        
        // 处理条件维度选项
        if (data?.conditionDimensions) {
          const options = data.conditionDimensions.map((item: any) => ({
            itemKey: item.code || item.value,
            itemName: item.name || item.label
          }));
          
          // 确保产品组选项存在
          if (!options.some(item => item.itemName === '产品组')) {
            options.push({ itemKey: 'product_group', itemName: '产品组' });
          }
          
          console.log('设置条件维度选项:', options);
          setConditionDimensionOptions(options);
        }
        
        // 处理结果维度选项
        if (data?.resultDimensions) {
          setResultDimensionOptions(data.resultDimensions.map((item: any) => ({
            itemKey: item.code || item.value,
            itemName: item.name || item.label
          })));
        }
      } else {
        YRMessage.error(res?.errorMessage || '加载维度选项失败');
      }
    }).catch((error) => {
      if (!isMountedRef.current) return;
      
      console.error('加载维度选项失败:', error);
      YRMessage.error('加载维度选项失败');
      
      // 如果接口调用失败，使用默认选项
      setConditionDimensionOptions([
        { itemKey: 'resultType', itemName: '结果类型' },
        { itemKey: 'customerType', itemName: '客户类型' },
        { itemKey: 'productType', itemName: '产品类型' },
        { itemKey: 'riskLevel', itemName: '风险等级' },
        { itemKey: 'amount', itemName: '授权金额' },
        { itemKey: 'product_group', itemName: '产品组' } // 确保有产品组选项
      ]);
      
      setResultDimensionOptions([
        { itemKey: 'approve', itemName: '同意' },
        { itemKey: 'reject', itemName: '拒绝' },
        { itemKey: 'pending', itemName: '待定' },
        { itemKey: 'condition', itemName: '有条件通过' }
      ]);
    });
  };

  // 初始化表单数据
  useEffect(() => {
    if (mode === 'add' && params) {
      // 从合并后的params中获取正确的字段
      const {
        parentOrgName, // 机构名称
        authNo, // 授权编号 (来自角色)
        roleName, // 角色名称 (来自角色)
        roleId, // 角色ID (来自角色)
        authCategory // 授权类别 (来自机构)
      } = params || {};

      // 初始化基本信息
      const authRuleNo = `AUTH_${Date.now()}`;

      form.setFieldsValue({
        authRuleNo,
        authCatalogNo: authNo,
        orgName: parentOrgName,
        roleName: roleName || roleId,
        authCategory
      });

      // 初始化默认的结果维度
      const defaultDimension: DimensionInfo = {
        id: uuid(),
        conditionDimensionName: '', // 维度名称为空
        resultDimensionName: '',
        resultValue: 0,
        isDefault: true,
        order: 1,
        dimensionType: 'RESULT' // 使用专门的字段标记维度类型
      };
      setDimensions([defaultDimension]);

      // 加载维度选项
      loadDimensionOptions(authCategory);
    }

    // 清理函数
    return () => {
      isMountedRef.current = false;
    };
  }, [params, mode, form]);

  // 添加维度
  const addDimension = () => {
    const newDimension: DimensionInfo = {
      id: uuid(),
      conditionDimensionName: '',
      resultDimensionName: '',
      resultValue: '', // 条件维度初始值为空
      isDefault: false,
      order: dimensions.length + 1,
      dimensionType: 'CONDITION' // 新增的维度默认为条件维度
    };
    setDimensions([...dimensions, newDimension]);
  };

  // 删除维度
  const deleteDimension = (id: string) => {
    const dimension = dimensions.find((d) => d.id === id);
    if (dimension?.isDefault) {
      YRMessage.warning('默认维度不能删除');
      return;
    }
    setDimensions(dimensions.filter((d) => d.id !== id));
  };

  // 移动维度顺序
  const moveDimension = (id: string, direction: 'up' | 'down') => {
    const currentIndex = dimensions.findIndex((d) => d.id === id);
    if (
      (direction === 'up' && currentIndex === 0) ||
      (direction === 'down' && currentIndex === dimensions.length - 1)
    ) {
      return;
    }

    const newDimensions = [...dimensions];
    const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    [newDimensions[currentIndex], newDimensions[targetIndex]] = [newDimensions[targetIndex], newDimensions[currentIndex]];

    // 更新order字段
    newDimensions.forEach((dim, index) => {
      dim.order = index + 1;
    });

    setDimensions(newDimensions);
  };

  // 更新维度数据
  const updateDimension = (id: string, field: keyof DimensionInfo, value: any) => {
    setDimensions(dimensions.map((d) =>
      (d.id === id ? { ...d, [field]: value } : d)));
  };

  // 打开产品选择弹窗
  const openProductSelectModal = (dimensionId: string) => {
    console.log('打开产品选择弹窗, dimensionId:', dimensionId);
    setCurrentDimensionId(dimensionId);
    setProductModalVisible(true);
  };

  // 处理产品选择确认
  const handleProductSelectOk = (selectedProducts: ProductInfo[]) => {
    const dimension = dimensions.find((d) => d.id === currentDimensionId);
    if (dimension) {
      // 更新维度的已选产品列表
      updateDimension(currentDimensionId, 'selectedProducts', selectedProducts);
      
      // 将产品ID列表作为字符串存储到resultValue字段
      const productIds = selectedProducts.map(p => p.productId).join(',');
      updateDimension(currentDimensionId, 'resultValue', productIds);
    }
    
    setProductModalVisible(false);
  };

  // 表单提交
  const onSubmit = () => {
    form.validateFields().then(() => {
      // 验证维度数据
      const hasEmptyDimensions = dimensions.some((d) =>
        !d.conditionDimensionName || (d.dimensionType === 'RESULT' && !d.resultDimensionName));

      if (hasEmptyDimensions) {
        YRMessage.error('请完善维度信息');
        return;
      }

      YRMessage.success('测试提交成功（包含维度数据验证）', 0.5, () => {
        if (isMountedRef.current) {
          okCallback && okCallback();
          modal.remove();
        }
      });
    }).catch(() => {
      // 表单验证失败时的处理
    });
  };

  // 获取当前维度选中的产品列表
  const getSelectedProducts = (dimensionId: string) => {
    const dimension = dimensions.find((d) => d.id === dimensionId);
    return dimension?.selectedProducts || [];
  };

  // 根据当前维度ID获取选中的产品ID列表
  const getSelectedProductIds = (dimensionId: string) => {
    const dimension = dimensions.find((d) => d.id === dimensionId);
    if (dimension?.resultValue && typeof dimension.resultValue === 'string') {
      return dimension.resultValue.split(',').filter(id => id);
    }
    return [];
  };

  // 维度表格列配置
  const dimensionColumns = [
    {
      title: '序号',
      key: 'index',
      width: 60,
      render: (_, record: DimensionInfo, index: number) => index + 1
    },
    {
      title: '维度类型',
      key: 'dimensionType',
      render: (_, record: DimensionInfo) => {
        return record.dimensionType === 'RESULT' ? '结果维度' : '条件维度';
      }
    },
    {
      title: '维度名称',
      dataIndex: 'conditionDimensionName',
      key: 'conditionDimensionName',
      render: (value, record: DimensionInfo) => {
        if (mode === 'detail') {
          return <span>{value}</span>;
        }
        
        // 根据维度类型显示不同的字典选项
        const isResultDimension = record.dimensionType === 'RESULT';
        const dictkey = isResultDimension ? 'RESULT_DIMENSION' : 'CONDITIONAL_DIMENSION';
        
        // 对于条件维度，需要根据业务规则过滤选项
        if (!isResultDimension) {
          // 获取当前授权标签
          const authLabel = form.getFieldValue('authLabel');
          
          // 检查是否存在客户类型维度及其选择的值
          const customerTypeDimension = dimensions.find(d => 
            d.dimensionType === 'CONDITION' && d.conditionDimensionName === '001'
          );
          const customerTypeValue = customerTypeDimension?.resultValue;
          
          return (
            <YRDict.Select
              dictkey={dictkey}
              value={value}
              placeholder="请选择维度名称"
              style={{ width: '100%' }}
              onChange={(val) => updateDimension(record.id, 'conditionDimensionName', val)}
              filterOption={(itemKey, item) => {
                // 默认排除"合作项目"和"合作类型"
                if (itemKey === 'cooperation_project' || itemKey === 'cooperation_type') {
                  // 规则2：授权标签为"专项阈值"且存在个人/公司客户时显示"合作项目"
                  if (itemKey === 'cooperation_project' && 
                      authLabel === 'specialThreshold' && 
                      (customerTypeValue === 'personal' || customerTypeValue === 'company')) {
                    return true;
                  }
                  // 规则3：存在合作方客户时显示"合作类型"
                  if (itemKey === 'cooperation_type' && customerTypeValue === 'partner') {
                    return true;
                  }
                  return false;
                }
                return true;
              }}
            />
          );
        }
        
        return (
          <YRDict.Select
            dictkey={dictkey}
            value={value}
            placeholder="请选择维度名称"
            style={{ width: '100%' }}
            onChange={(val) => updateDimension(record.id, 'conditionDimensionName', val)}
          />
        );
      }
    },
    {
      title: '维度取值',
      key: 'dimensionValue',
      render: (_, record: DimensionInfo) => {
        if (mode === 'detail') {
          return <span>{record.resultValue}</span>;
        }

        // 检查是否是客户类型条件维度
        const isCustomerTypeDimension = record.dimensionType === 'CONDITION' && record.conditionDimensionName === '001';

        // 检查是否是产品组条件维度（增加多种可能的产品组编码判断）
        const isProductGroupDimension = record.dimensionType === 'CONDITION' && 
          (record.conditionDimensionName === 'product_group' || 
           record.conditionDimensionName === '005' ||  // 考虑可能的编码
           (conditionDimensionOptions.some(item => 
             item.itemName === '产品组' && item.itemKey === record.conditionDimensionName)));
        
        if (isCustomerTypeDimension) {
          // 客户类型使用字典下拉框
          return (
            <YRDict.Select
              dictkey="USER_TYPE"
              value={record.resultValue || undefined}
              placeholder="请选择客户类型"
              style={{ width: '100%' }}
              onChange={(val) => updateDimension(record.id, 'resultValue', val)}
            />
          );
        } else if (isProductGroupDimension) {
                     // 产品组使用产品选择弹窗
           const selectedProducts = getSelectedProducts(record.id);
           return (
             <div>
               <YRButton 
                 type="default"
                 block
                 onClick={() => openProductSelectModal(record.id)}
                 className={styles.dimensionValueInput}
               >
                 {selectedProducts.length > 0 
                   ? `已选择 ${selectedProducts.length} 个产品` 
                   : '点击选择产品'}
               </YRButton>
               <div className={styles.productTagsContainer}>
                 {selectedProducts.length > 0 && (
                   <>
                     {selectedProducts.slice(0, 3).map((product) => (
                       <YRTag key={product.productId}>
                         {product.productName}
                       </YRTag>
                     ))}
                     {selectedProducts.length > 3 && <YRTag>+{selectedProducts.length - 3}</YRTag>}
                   </>
                 )}
               </div>
             </div>
           );
        } else {
          // 其他维度使用数值输入框
          return (
            <YRInputNumber
              value={record.resultValue}
              placeholder="请输入取值"
              style={{ width: '100%' }}
              min={0}
              precision={0}
              disabled={record.dimensionType === 'CONDITION' && !record.conditionDimensionName}
              onChange={(val) => updateDimension(record.id, 'resultValue', val || 0)}
            />
          );
        }
      }
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: DimensionInfo) => (
        mode === 'detail' ? null : (
          <YRSpace>
            <YRButton
              type="link"
              size="small"
              onClick={() => moveDimension(record.id, 'up')}
              disabled={dimensions.findIndex((d) => d.id === record.id) === 0}
            >
              ↑
            </YRButton>
            <YRButton
              type="link"
              size="small"
              onClick={() => moveDimension(record.id, 'down')}
              disabled={dimensions.findIndex((d) => d.id === record.id) === dimensions.length - 1}
            >
              ↓
            </YRButton>
            <YRButton
              type="link"
              size="small"
              danger
              onClick={() => deleteDimension(record.id)}
              disabled={record.isDefault}
            >
              删除
            </YRButton>
          </YRSpace>
        )
      )
    }
  ];

  // 组件卸载时的清理函数
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  return (
    <>
      <YRModal
        title={`授权规则${mode === 'add' ? '新增' : mode === 'edit' ? '修改' : '详情'}`}
        open={modal.visible}
        onCancel={() => {
          isMountedRef.current = false;
          modal.hide();
        }}
        afterClose={() => {
          isMountedRef.current = false;
          modal.remove();
        }}
        okText={mode === 'detail' ? '关闭' : '确定'}
        width="80%"
        onOk={mode === 'detail' ? () => {
          isMountedRef.current = false;
          modal.hide();
        } : onSubmit}
        destroyOnClose
      >
        <YRForm
          form={form}
          layout="vertical"
          disabled={mode === 'detail'}
        >
          {/* 基本信息区域 */}
          <YRCard title="基本信息" style={{ marginBottom: 16 }}>
            <YRRow gutter={16}>
              <YRCol span={8}>
                <YRForm.Item
                  label="授权规则编号"
                  name="authRuleNo"
                  rules={[{ required: true, message: '授权规则编号不能为空' }]}
                >
                  <YRInput placeholder="系统自动生成" disabled />
                </YRForm.Item>
              </YRCol>
              <YRCol span={8}>
                <YRForm.Item
                  label="授权目录编号"
                  name="authCatalogNo"
                  rules={[{ required: true, message: '授权目录编号不能为空' }]}
                >
                  <YRInput placeholder="系统反显" disabled />
                </YRForm.Item>
              </YRCol>
              <YRCol span={8}>
                <YRForm.Item
                  label="机构"
                  name="orgName"
                  rules={[{ required: true, message: '机构不能为空' }]}
                >
                  <YRInput placeholder="系统反显" disabled />
                </YRForm.Item>
              </YRCol>
            </YRRow>
            <YRRow gutter={16}>
              <YRCol span={8}>
                <YRForm.Item
                  label="角色"
                  name="roleName"
                  rules={[{ required: true, message: '角色不能为空' }]}
                >
                  <YRInput placeholder="系统反显" disabled />
                </YRForm.Item>
              </YRCol>
              <YRCol span={8}>
                <YRForm.Item
                  label="授权类别"
                  name="authCategory"
                  rules={[{ required: true, message: '授权类别不能为空' }]}
                >
                  <YRInput placeholder="系统反显" disabled />
                </YRForm.Item>
              </YRCol>
              <YRCol span={8}>
                <YRForm.Item
                  label="授权标签"
                  name="authLabel"
                  rules={[{ required: true, message: '请选择授权标签' }]}
                >
                  <YRSelect placeholder="请选择授权标签">
                    <YRSelect.Option value="productThreshold">产品阈值</YRSelect.Option>
                    <YRSelect.Option value="comprehensiveThreshold">综合阈值</YRSelect.Option>
                    <YRSelect.Option value="specialThreshold">专项阈值</YRSelect.Option>
                  </YRSelect>
                </YRForm.Item>
              </YRCol>
            </YRRow>
          </YRCard>

          {/* 维度信息区域 */}
          <YRCard title="维度信息">
            <YRRow gutter={16} style={{ marginBottom: 16 }}>
              <YRCol span={8}>
                {mode !== 'detail' && (
                  <YRButton type="primary" onClick={addDimension}>
                    + 添加维度
                  </YRButton>
                )}
              </YRCol>
            </YRRow>
            <YRTable
              columns={dimensionColumns}
              dataSource={dimensions}
              rowKey="id"
              pagination={false}
              size="small"
              scroll={{ x: 'max-content' }}
              showIndex={false}
            />
          </YRCard>
        </YRForm>
      </YRModal>

      {/* 产品选择弹窗 */}
      <ProductSelectModal
        visible={productModalVisible}
        onCancel={() => setProductModalVisible(false)}
        onOk={handleProductSelectOk}
        selectedProductIds={getSelectedProductIds(currentDimensionId)}
      />
    </>
  );
};

export default YREasyUseModal.create(AuthRuleAddModal);
