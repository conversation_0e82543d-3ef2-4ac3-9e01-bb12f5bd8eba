/**
 * @页面描述: 流程管理 一级菜单
 * @文件名 flow.ts
 * @Path src\routes\part\flow.ts
 * @Date 2023-03-30 14:54:55
 * <AUTHOR>
 */
import { M02, M0201, M0202, M0203 } from '@permit/flow';
import { lazy } from 'ice';

const partRoutes = [
  {
    path: '/flow',
    name: '流程管理',
    permit: M02,
    children: [
      {
        path: '/',
        exact: true,
        redirect: '/flow/flow-management'
      },
      {
        path: '/flow-management',
        permit: M0201,
        name: '流程定义',
        component: lazy(() => import('@/pages/Flow/FlowIndex'))
      },
      {
        path: '/parameter-management',
        permit: M0202,
        name: '参数库',
        component: lazy(() => import('@/pages/Flow/ParamManage/index'))
      },
      {
        path: '/decision',
        permit: M0203,
        name: '决策表',
        component: lazy(() => import('@/pages/Flow/Decision/index'))
      }
    ]
  }
];
export default partRoutes;
