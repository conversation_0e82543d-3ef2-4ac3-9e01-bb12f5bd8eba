import React, { useState } from 'react';
import { Resizable } from 're-resizable';
import { YRLayout, YRSpin, YRSelect, YRRow, YRCol, YRTooltip, YRDropdown, YRIcon } from 'yrantd';
import TopBar from './TopBar';
import List from './List';
import Tree from './Tree';
import './index.less';

const { Sider } = YRLayout;
const { Option } = YRSelect;

/**
 * 公用左侧列表组件
 * @param {string} title 标题
 * @param {boolean} loading
 * @param {boolean} hideCreate 是否隐藏+
 * @param {boolean} hideSearch 是否隐藏搜索
 * @param {boolean} enable 是否可以拖拽
 * @param {string} contentType 内容类型 list | tree
 * @param {object} currentItem 当前选中的item
 * @param {tree} treeData 树
 * @param {Array} list 列表数据
 * @param {object} fieldNames 自定义 list 结构中的字段{ name: 'name', desc: 'desc', key:'id' }
 * @param {function} search 搜索方法
 * @param {function} create 创建方法
 * @param {function} setCurrentItem 选择item
 * @param {ReactNode} topMenu 顶部右侧menu菜单
 * @param {ReactNode} topExtra 顶部右侧menu菜单
 * @param {function} itemMenu 单个item menu菜单
 * @param {String} tag 组件包裹的元素 默认Layout
 * @param {String} showToolBar 是否展示toolbar
 * @example topMenu itemMenu
 *           <Menu>
 *             <Menu.Item onClick={e => this.handleCancel("showAdd", true)}>
 *               创建分组
 *             </Menu.Item>
 *           </Menu>
 * @returns ReactNode
 */
const LeftList = (props) => {
  const [searchValue, setSearchValue] = useState('');
  const {
    showDesc = false,
    title,
    loading = false,
    fieldNames = { name: 'name', desc: 'desc', key: 'id' },
    contentType = 'list',
    search,
    create,
    topMenu,
    topExtra,
    itemMenu,
    selectItemMenu,
    treeData,
    list,
    currentItem = {},
    setCurrentItem,
    showToolBar = true,
    showSelect = false,
    customDom = null,
    optionData = [],
    addGroup = null,
    selectValue = '',
    selectProduct = null,
    initExpandedKey,
    tag,
    enable,
    hideCreate = false,
    hideSearch = false,
    showSelectSearch = false,
    ...rest
  } = props;
  const handleSearch = (value) => {
    search(value);
    setSearchValue(value);
  };

  /**
   * 根据contentType 展示对应内容
   * @returns ReactNode
   */
  const getConent = (type) => {
    switch (type) {
      case 'list':
        return (
          <List
            list={list}
            itemMenu={itemMenu}
            fieldNames={fieldNames}
            currentItem={currentItem}
            setCurrentItem={setCurrentItem}
            showDesc={showDesc}
          />
        );

      case 'tree':
        return (
          <Tree
            searchValue={searchValue}
            treeData={treeData}
            initExpandedKey={initExpandedKey}
            search={search}
            {...rest}
          />
        );
      case 'custom':
        return customDom || null;
      default:
        return (
          <List
            list={list}
            itemMenu={itemMenu}
            fieldNames={fieldNames}
            currentItem={currentItem}
            setCurrentItem={setCurrentItem}
            showDesc={showDesc}
          />
        );
    }
  };
  const Tag = tag || Sider;
  return (
    <Tag width="200px" theme="light" className={`${props.className} common-left-slider`}>
      <Resizable
        handleWrapperClass="resizable-handle"
        handleClasses={{ right: 'right-handle' }}
        enable={enable}
        width={'200px'}
        minWidth={200}
        maxWidth={400}
        style={{ zIndex: 8 }}
        defaultSize={{
          width: '200px',
          height: '100%'
        }}
      >
        {showToolBar ? (
          <div className="top-bar">
            <TopBar
              title={title}
              search={handleSearch}
              hideCreate={hideCreate}
              hideSearch={hideSearch}
              create={create}
              topMenu={topMenu}
              topExtra={topExtra}
              {...rest}
            />
          </div>
        ) : null}
        {showSelect ? (
          <div className="select-wrap">
            <div style={{ display: 'flex', flexDirection: 'row' }}>
              <span style={{ flex: 1, width: 'calc(100% - 30px)' }}>
                <YRSelect
                  style={{ width: '100%' }}
                  onSelect={selectProduct}
                  value={selectValue}
                  showSearch={showSelectSearch}
                  filterOption={(input, option) => {
                    return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                  }}
                >
                  {optionData.length
                    ? optionData.map((item) => {
                      return <Option value={item.productCode}>{item.productName}</Option>;
                    })
                    : null}
                </YRSelect>
              </span>
              <span style={{ width: 32 }}>
                <YRDropdown trigger="click" overlay={selectItemMenu}>
                  <div className="icon-menu add-wrap" onClick={(e) => e.stopPropagation()}>
                    <YRIcon icon="yunrongXXcheck" fontSize="14px" />
                  </div>
                </YRDropdown>
              </span>
            </div>
          </div>
        ) : null}
        <YRSpin spinning={loading}>{getConent(contentType)}</YRSpin>
      </Resizable>
    </Tag>
  );
};

export default LeftList;
