/**
 * @Author: wangyw26123
 * @Description: 功能权限
 * @Date: Created in 2022-12-08 20:10:04
 * @Modifed By:
 */

import React, { useEffect, useMemo, useRef, useState } from 'react';
import {
  YRFlexPageLayout,
  YRForm,
  YRInput,
  YRLink,
  YRSpin,
  YRMessage,
  YRModal,
  YRButton,
  YREmpty,
  YRItem,
  YRSpace
} from 'yrantd';
import debounce from 'lodash-es/debounce';
import classNames from 'classnames';
import EditPermitGroupModal from './EditPermitGroupModal';
import PermitTree from './PermitTree';
import styles from './index.module.less';
import { EnumMsg } from '@/constant/common';
import { M0102 } from '@permit/permit';
import { checkAuth } from '@/utils/utils';

const { queryPermitGroupList } = M0102.interfaces;
const { deletePermitGroup } = M0102.E02.interfaces;
const { modifyPermitGroup } = M0102.E03.interfaces;
const { Search } = YRInput;

interface PermitGroupItem {
  operatorId: string;
  ownOrganId: string;
  permitGroupId: string;
  permitGroupName: string;
}

const FunctionPermit = () => {
  const [form] = YRForm.useForm();
  const permitTreeRef = useRef<{ queryPermitByGroupId: () => void }>();
  const [visiblePermitGroupModal, setVisiblePermitGroupModal] = useState<boolean>(false);
  const [permitGrouploading, setPermitGrouploading] = useState<boolean>(false);
  const [groupList, setGroupList] = useState<PermitGroupItem[]>([]);
  const [permitIds, setPermitIds] = useState<string[]>([]);
  const [btnLoading, setBtnLoading] = useState<boolean>(false);
  const [activePermitGroup, setActivePermitGroup] = useState<PermitGroupItem>({
    operatorId: '',
    ownOrganId: '',
    permitGroupId: '',
    permitGroupName: ''
  });

  useEffect(() => {
    queryGroupList();
  }, []);

  const queryGroupList = (groupName?: string) => {
    setPermitGrouploading(true);
    queryPermitGroupList({ groupName }).then((res) => {
      setPermitGrouploading(false);
      if (res.success) {
        setGroupList(res.data);
        setActivePermitGroup(res.data[0] || {});
      }
    });
  };

  const onDelPermitGroup = (val) => {
    if (!checkAuth(M0102.E02)) return;

    YRModal.confirm({
      title: `确认删除 ${val.permitGroupName} 权限组吗？`,
      onOk() {
        deletePermitGroup({ permitGroupId: val.permitGroupId }).then((res) => {
          if (res.success) {
            YRMessage.success(EnumMsg.delete);
            queryGroupList();
          }
        });
      }
    });
  };

  const onsubmit = () => {
    if (!checkAuth(M0102.E03)) return;

    form.validateFields().then((values: any) => {
      setBtnLoading(true);
      modifyPermitGroup({
        permitGroupId: activePermitGroup.permitGroupId,
        permitGroupName: values.permitGroupName,
        permitIdList: permitIds
      }).then((res) => {
        setBtnLoading(false);
        if (res.success) {
          YRMessage.success(EnumMsg.save);
          permitTreeRef?.current?.queryPermitByGroupId();
        }
      });
    });
  };

  const onSearch = useMemo(() => {
    return debounce((val: string) => {
      queryGroupList(val);
    }, 500);
  }, []);

  const getPermitIds = (keys: string[]) => {
    setPermitIds(keys);
  };

  const onActiveGroup = useMemo(() => {
    return debounce((val) => {
      setActivePermitGroup(val);
    }, 300);
  }, []);

  const renderPermitGroup = () => {
    if (!groupList.length) {
      return <YREmpty />;
    }

    return groupList.map((item) => {
      return (
        <YRItem
          key={item.permitGroupId}
          title={item.permitGroupName}
          className={classNames(styles.groupListItem, {
            [styles.active]: activePermitGroup.permitGroupId === item.permitGroupId
          })}
          onClick={() => onActiveGroup(item)}
          operateRender={[
            <YRLink key="del" check={M0102.E02} onClick={() => onDelPermitGroup(item)}>
              删除
            </YRLink>
          ]}
        />
      );
    });
  };

  return (
    <YRFlexPageLayout>
      <YRFlexPageLayout.Sider
        title="功能权限"
        extra={
          <YRLink type="primary" check={M0102.E01} onClick={() => setVisiblePermitGroupModal(true)}>
            新增
          </YRLink>
        }
        contentNoPadding
      >
        <YRSpin spinning={permitGrouploading} wrapperClassName={styles.permitGroupLoading}>
          <Search
            placeholder="请输入权限组名称"
            className="px-12 my-8"
            onChange={(e) => onSearch(e.target.value)}
            allowClear
          />
          <div className={styles.permitGroupList}>{renderPermitGroup()}</div>
        </YRSpin>
      </YRFlexPageLayout.Sider>
      <YRFlexPageLayout.Main
        title={
          <span className="ellipsis" style={{ maxWidth: '90%' }} title={activePermitGroup.permitGroupName}>
            {activePermitGroup.permitGroupName}
          </span>
        }
      >
        <YRSpace direction="vertical">
          <YRButton.Space>
            <YRButton key="save" type="primary" check={M0102.E03} disabled={btnLoading} onClick={onsubmit}>
              保存
            </YRButton>
            <YRButton
              key="del"
              check={M0102.E02}
              danger
              disabled={btnLoading}
              onClick={() => onDelPermitGroup(activePermitGroup)}
            >
              删除
            </YRButton>
          </YRButton.Space>
          {/* 权限树 */}
          <PermitTree
            ref={permitTreeRef}
            form={form}
            permitGroupId={activePermitGroup.permitGroupId}
            getPermitIds={getPermitIds}
          />
        </YRSpace>

        {/* 新增权限组弹窗 */}
        <EditPermitGroupModal
          visible={visiblePermitGroupModal}
          onCancel={() => setVisiblePermitGroupModal(false)}
          onOk={() => {
            setVisiblePermitGroupModal(false);
            queryGroupList();
          }}
        />
      </YRFlexPageLayout.Main>
    </YRFlexPageLayout>
  );
};

export default FunctionPermit;
