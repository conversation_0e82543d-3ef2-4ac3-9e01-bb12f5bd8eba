import React from 'react';
import { ValueTypeEnum } from '@yr/multi-view-table';
import { FormItemListProps, YRTableProps, YRButton, YRLink, YRTreeSelect, YRSelect, YRDict } from 'yrantd';
import type { ReviewedSingleAuthInfo } from './types';

// 表单项配置
const formItemList: FormItemListProps[] = [
  {
    placeholder: '授权编号',
    key: 'authNo',
    type: 'input',
    position: 'show'
  },
  {
    placeholder: '授权状态',
    key: 'authStatus',
    type: 'dictSelect',
    position: 'show',
    dictkey: 'AUTH_STATUS'
  },
  {
    placeholder: '机构',
    key: 'orgId',
    type: 'component',
    position: 'show',
    component: (
      <YRTreeSelect
        style={{ width: '100%' }}
        dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
        placeholder="请选择机构"
        allowClear
        treeDefaultExpandAll
        // treeData 需要从props传入
      />
    )
  },
  {
    placeholder: '部门',
    key: 'deptId',
    type: 'component',
    position: 'show',
    component: (
      <YRSelect
        style={{ width: '100%' }}
        placeholder="请选择部门"
        allowClear
        // options 需要从props传入
      />
    )
  },
  {
    placeholder: '角色',
    key: 'roleId',
    type: 'component',
    position: 'show',
    component: (
      <YRSelect
        style={{ width: '100%' }}
        placeholder="请选择角色"
        allowClear
        // options 需要从props传入
      />
    )
  },
  {
    placeholder: '授信类别',
    key: 'creditCategory',
    type: 'input',
    position: 'show'
  }
];

// 表格列定义
const columns: (
  detailCallback: (record: ReviewedSingleAuthInfo) => void
) => YRTableProps['columns'] = (
  detailCallback
) => {
  return [
    {
      title: '授权编号',
      dataIndex: 'authNo',
      key: 'authNo',
      width: 180,
      fixed: 'left'
    },
    {
      title: '机构',
      dataIndex: 'orgName',
      key: 'orgName',
      width: 150,
      ellipsis: true
    },
    {
      title: '部门',
      dataIndex: 'deptName',
      key: 'deptName',
      width: 120,
      ellipsis: true
    },
    {
      title: '角色',
      dataIndex: 'roleName',
      key: 'roleName',
      width: 120,
      ellipsis: true
    },
    {
      title: '授权分类',
      dataIndex: 'authCategory',
      key: 'authCategory',
      width: 120,
      render: (value) => <YRDict.Text dictkey={'AUTH_CATEGORY'} defaultValue={value} />
    },
    {
      title: '授信类别',
      dataIndex: 'creditCategory',
      key: 'creditCategory',
      width: 120,
      ellipsis: true
    },
    {
      title: '授权状态',
      dataIndex: 'authStatus',
      key: 'authStatus',
      width: 120,
      valueType: ValueTypeEnum.dict,
      dictkey: 'AUTH_STATUS'
    },
    {
      title: '复核通过日期',
      dataIndex: 'approveDate',
      key: 'approveDate',
      width: 130,
      valueType: ValueTypeEnum.date
    },
    {
      title: '失效日期',
      dataIndex: 'effectEndDate',
      key: 'effectEndDate',
      width: 120,
      valueType: ValueTypeEnum.date
    },
    {
      title: '操作',
      dataIndex: 'operator',
      valueType: ValueTypeEnum.operation,
      fixed: 'right',
      width: 100,
      render: (value, record) => {
        return (
          <YRLink
            type="primary"
            onClick={() => detailCallback(record as ReviewedSingleAuthInfo)}
          >
            详情
          </YRLink>
        );
      }
    }
  ];
};

export { columns, formItemList };
