/*
 * @Author: 伍晶晶
 * @Description: 汇率
//  * @Date: 2023-03-17 09:18:59
 * @LastEditors: liaokt
 * @LastEditTime: 2024-04-07 11:19:24
 */
import { rateSelectPage } from '@/services/setting';
import { setDict } from '@/utils/utils';
import MultiViewTable, { ViewTableProps } from '@yr/multi-view-table';
import { useQueryMultiViewTableData, Dict } from '@yr/util';
import React from 'react';
import { YRDict, YRIndexPageLayout, YRMoneyShow } from 'yrantd';
import { SearchListItemTypeEnum, SearchProps } from '@yr/multi-view-table/lib/ViewTable/type';

const Enum_Currency = [
  { itemName: '人民币', itemKey: 'CNY' },
  { itemName: '欧元', itemKey: 'EUR' },
  { itemName: '英镑', itemKey: 'GBP' },
  { itemName: '美元', itemKey: 'USD' },
  { itemName: '港币', itemKey: 'HKD' },
  { itemName: '日元', itemKey: 'JPY' }
];
setDict('EnumCurrency', Enum_Currency);

const ExchangeRate = () => {
  const { tableProps, query } = useQueryMultiViewTableData(rateSelectPage);

  const columns: ViewTableProps['columns'] = [
    {
      title: '币种',
      dataIndex: 'currency',
      render: (value) => {
        return <YRDict.Text dictkey="currency" defaultValue={value} />;
      }
    },
    {
      title: '现汇买入价',
      dataIndex: 'spotExchangeBuyPrice',
      render: (v) => <YRMoneyShow>{v}</YRMoneyShow>
    },
    {
      title: '现钞买入价格',
      dataIndex: 'cashBuyPrice',
      render: (v) => <YRMoneyShow>{v}</YRMoneyShow>
    },
    {
      title: '现汇卖出价',
      dataIndex: 'spotExchangeSellPrice',
      render: (v) => <YRMoneyShow>{v}</YRMoneyShow>
    },
    {
      title: '现钞卖出价格',
      dataIndex: 'cashSellPrice',
      render: (v) => <YRMoneyShow>{v}</YRMoneyShow>
    },
    {
      title: '中间价',
      dataIndex: 'price',
      render: (v) => <YRMoneyShow>{v}</YRMoneyShow>
    },
    {
      title: '折算单位',
      dataIndex: 'convertUnit',
      render: (value) => value || CONST.null
    },
    {
      title: '生效日期',
      dataIndex: 'efficientDate',
      render: (value) => value || CONST.null
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      render: (value) => value || CONST.null
    },
    {
      title: '修改时间',
      dataIndex: 'updateTime',
      render: (value) => value || CONST.null
    }
  ];

  const searchList: SearchProps['searchList'] = [
    {
      key: 'currency',
      label: '币种',
      type: SearchListItemTypeEnum.dictSelect,
      dictkey: 'currency'
    }
  ];

  return (
    <YRIndexPageLayout>
      <MultiViewTable
        business="home"
        viewOpt={{ title: '汇率' }}
        onSubmit={query}
        columns={columns}
        searchOpt={{
          searchList
        }}
        {...tableProps}
      />
    </YRIndexPageLayout>
  );
};

export default Dict(['currency'])(ExchangeRate);
