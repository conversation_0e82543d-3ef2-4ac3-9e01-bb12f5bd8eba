/*
 * @Author: liaokt
 * @E-mail: <EMAIL>
 * @Date: 2023-04-10 09:11:34
 * @Description: 高级配置-用户节点规则配置
 */
import { useDebounceEffect } from 'ahooks';
import React, { useState, useEffect } from 'react';
import { YRDropAndSearchSelector, YREditableTable, YRForm, YRSpace } from 'yrantd';
import { queryUserTaskTaskListener } from '@/services/flow';

interface ItemDataProps {
  event: string;
  className: string;
}

const eventOptions = [
  {
    value: 'create',
    label: '任务创建后'
  },
  {
    value: 'assignment',
    label: '任务分配时'
  },
  {
    value: 'complete',
    label: '任务完成时'
  },
  {
    value: 'delete',
    label: '任务删除时'
  }
];

const UserNodeConfig = (props: any) => {
  const {
    globalDisabled,
    targetData: { nodeFormData },
    globalFormData,
    targetData,
    form
  } = props;
  const [dataSource, setDataSource] = useState<any>([]);
  // 获取当前数据，用于赋值到 form 上
  // const tasklisteners = YRForm.useWatch('tasklisteners', form);

  // 监听表格变化，如果新增加了新的任务监听器，则赋值再 ['properties', 'tasklisteners']
  // useDebounceEffect(() => {
  //   form.setFieldsValue({ properties: { tasklisteners } });
  // }, [tasklisteners]);

  // 获取到任务监听器的详情之后，进行数据赋值，并且反显,并同步
  useDebounceEffect(() => {
    if (nodeFormData) {
      const { properties } = nodeFormData;
      const tasklistenerDetail = properties?.tasklisteners;
      tasklistenerDetail && setDataSource([...tasklistenerDetail]);
    }
  }, [nodeFormData]);
  const [classOptions, setClassOptions] = useState<any>([]);
  const getUserTask = async () => {
    const res = await queryUserTaskTaskListener();
    if (res?.success) {
      const newData = res?.data?.map((item) => item?.className)?.map((item: any) => {
        return {
          label: item?.display,
          value: item?.value
        };
      });
      setClassOptions(newData);
    }
  };
  useEffect(() => {
    getUserTask();
  }, []);
  const defaultColumns = [
    {
      title: '时机',
      dataIndex: 'event',
      width: 130,
      renderFormItem: (_form, { record }) => {
        return (
          <YRDropAndSearchSelector
            disabled={globalDisabled}
            style={{ width: '100%' }}
            options={eventOptions}
            valueField={'value'}
            labelField={'label'}
            placeholder={'请选择时机'}
          />
        );
      }
    },
    {
      title: '执行程序',
      dataIndex: 'className',
      width: 130,
      renderFormItem: (_form, { record }) => {
        return (
          <YRDropAndSearchSelector
            disabled={globalDisabled}
            style={{ width: '100%' }}
            options={classOptions}
            valueField={'value'}
            labelField={'label'}
            placeholder={'请选择时机'}
          />
        );
      }
    }
  ];

  // 新增事件
  const onAdd = () => {
    const list = [...dataSource];
    list.push({
      event: '',
      className: ''
    });
    setDataSource([...list]);
  };

  // 删除操作
  const onDelete = (index: number) => {
    const list = [...dataSource];
    list.splice(index, 1);
    setDataSource([...list]);
  };

  return (
    <YRSpace block direction={'vertical'}>
      {/* <YRForm.Item name={['properties', 'tasklisteners']} hidden /> */}
      <YREditableTable
        form={form}
        showIndex={false}
        name={['properties', 'tasklisteners']}
        dataSource={dataSource}
        scroll={{ y: '246px' }}
        columns={defaultColumns}
        hiddenToolbar={globalDisabled}
        rowKey={(row, index) => index}
        loading={!nodeFormData}
        onBeforeAdd={() => onAdd()}
        onAfterDelete={(index) => {
          onDelete(index);
        }}
        toolbar={{
          buttonsProps: {
            add: {
              text: '新增',
              type: 'primary'
            }
          }
        }}
      />
    </YRSpace>
  );
};

export default UserNodeConfig;
