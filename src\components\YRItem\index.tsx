/*
 * @Author: DJscript
 * @Date: 2023-03-10 14:42:27
 * @LastEditTime: 2023-03-10 14:46:12
 * @FilePath: /kylin-admin/src/components/YRItem/index.tsx
 * @Description: 用于列表中的item
 */
import React, { CSSProperties } from 'react';
import { YRCol, YRRow, YRTypography } from 'yrantd';

const { Text } = YRTypography;

const YRItem = <T extends unknown = unknown>({
  item,
  width = 'auto',
  leftRender,
  rightRender
}: {
  item: T;
  width?: CSSProperties['width'];
  rightRender: (arg: T) => React.ReactNode;
  leftRender: (arg: T) => React.ReactNode;
}) => {
  return (
    <YRRow justify="space-between" style={{ width }}>
      <YRCol span={20}>
        <Text ellipsis>{rightRender(item)}</Text>
      </YRCol>
      <YRCol flex="0 0 auto" span={4} onClick={(e) => e.stopPropagation()}>
        {leftRender(item)}
      </YRCol>
    </YRRow>
  );
};

export default YRItem;
