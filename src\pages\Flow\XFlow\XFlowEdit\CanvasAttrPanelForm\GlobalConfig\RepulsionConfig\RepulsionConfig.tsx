/*
 * @Author: liaokt
 * @E-mail: <EMAIL>
 * @Date: 2023-04-06 17:06:24
 * @Description: 互斥组
 */

import store from '@/store';
import { useDebounceEffect, useUpdateEffect } from 'ahooks';
import React, { useState } from 'react';
import { YRButton, YREditableTable, YRForm, YRInput, YRLink, YRSelect, YRSpace, YRTable } from 'yrantd';

const FormItem = YRForm.Item;

const RepulsionConfig = (props: any) => {
  const {
    globalDisabled,
    targetData: { nodeFormData },
    globalFormData,
    targetData
  } = props;
  const form = YRForm.useFormInstance();
  // 互斥组数据
  const [dataSource, setDataSource] = useState<any>([]);
  // 互斥组下拉框选项
  const [selectList, setSelectList] = useState<any>([]);
  const exclusionGroups = YRForm.useWatch(['exclusionGroups'], form);
  const exclusionGroup = YRForm.useWatch(['exclusionGroup'], form);
  // 互斥组列
  const defaultColumns = [
    {
      title: '互斥组',
      dataIndex: 'repulsionList',
      width: 130,
      renderFormItem: (_form, { record }) => {
        return (
          <YRSelect
            disabled={globalDisabled}
            style={{ width: '100%' }}
            options={selectList}
            mode={'multiple'}
            placeholder={'请选择节点'}
          />
        );
      }
    }
  ];

  // 获取互斥组下拉列表
  useDebounceEffect(() => {
    if (targetData && targetData?.length > 0) {
      const list = targetData
        ?.filter((item: any) => {
          const {
            nodeFormData: { properties }
          } = item || {};
          const { multiinstance_condition } = properties || {};
          // 需要或签
          return (
            item?.name === 'CUSTOM-NODE-TASK' && multiinstance_condition === '${orSign.completionCondition(execution)}'
          );
        })
        .map((_item) => {
          return { label: _item?.label, value: _item?.id, key: _item?.id };
        });
      setSelectList(list);
    }
  }, [targetData]);

  useUpdateEffect(() => {
    const list: any = [];
    exclusionGroup?.forEach((item) => {
      list.push(item?.repulsionList);
    });
    form.setFieldValue('exclusionGroups', list);
  }, [exclusionGroup]);

  useDebounceEffect(() => {
    if (exclusionGroups?.length > 0) {
      const list = exclusionGroups?.map((_item) => {
        return { repulsionList: _item };
      });
      setDataSource([...list]);
    }
  }, [exclusionGroups]);

  useDebounceEffect(() => {
    const _exclusionGroups = globalFormData?.exclusionGroups;
    if (_exclusionGroups?.length > 0) {
      const list = _exclusionGroups?.map((_item) => {
        return { repulsionList: _item };
      });
      setDataSource(list);
    }
  }, [globalFormData]);

  // 新增操作
  const add = () => {
    const list = [...dataSource];
    list.push({ repulsionList: [] });
    setDataSource([...list]);
  };

  // 删除操作
  const onDelete = (index: number) => {
    const list = [...dataSource];
    list.splice(index, 1);
    setDataSource([...list]);
  };

  return (
    <YRSpace direction="vertical" block>
      <YRForm.Item name="exclusionGroups" hidden />
      <YREditableTable
        form={form}
        name={'exclusionGroup'}
        showIndex={false}
        scroll={{ y: '246px' }}
        dataSource={dataSource}
        columns={defaultColumns}
        hiddenToolbar={globalDisabled}
        rowKey={(row, index) => {
          return index;
        }}
        onBeforeAdd={() => add()}
        onAfterDelete={(index) => {
          onDelete(index);
        }}
        toolbar={{
          buttonsProps: {
            add: {
              text: '新增',
              type: 'primary'
            }
          }
        }}
      />
    </YRSpace>
  );
};

export default RepulsionConfig;
