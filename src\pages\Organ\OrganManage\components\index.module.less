/**
 * @Author: wangyw26123
 * @Description: 机构管理
 * @Date: Created in 2022-12-14 13:14:41
 * @Modifed By:
 */

// 机构管理-机构树
.organTreeLoading {
  height: 100%;

  :global {
    .ant-spin-container {
      height: inherit;
    }
  }

  .organTree {
    max-height: calc(100vh - 130px);
    overflow-y: auto;
    overflow-x: hidden;

    :global {
      .ant-tree-list .ant-tree-treenode {
        width: 100%;

        .ant-tree-node-content-wrapper {
          width: calc(100% - 25px);
        }
      }
    }
  }
}
