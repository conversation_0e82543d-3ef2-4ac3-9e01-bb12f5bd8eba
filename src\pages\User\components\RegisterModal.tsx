/* eslint-disable no-nested-ternary */
/**
 * @页面描述: 用户注册
 * @文件名 RegisterModal.tsx
 * @Path src\pages\Organ\OrganManage\components\RegisterModal.tsx
 * @Date 2023-03-21 10:25:31
 * <AUTHOR>
 */
import React, { ComponentProps, useState } from 'react';
import {
  YRDict,
  YREasyUseModal,
  YRForm,
  YRInput,
  YRLink,
  YRModal,
  YRSpace,
  YRSteps,
  YRButton,
  YRResult,
  YRText,
  YRMessage,
  YRSelect,
  YRTooltip
} from 'yrantd';
import { antdModal, md5Crypto, setDict } from '../../../utils/utils';
import { useCountDown, useDebounceEffect, useUpdateEffect } from 'ahooks';
// import { regex } from '@yr/util';
import { queryBelongHrOrganList, queryThirdUser, registerUser } from '../../../services/user';
import moment from 'moment';
import { regex } from '@yr/util';
import { userLogout } from '../../../services/comm';

setDict('EnumBoolean', [
  { itemKey: 'Y', itemName: '是' },
  { itemKey: 'N', itemName: '否' }
]);

setDict('EnumStation', [
  { itemKey: '客户经理', itemName: '客户经理' },
  { itemKey: '其他', itemName: '其他' }
]);

setDict('EnumBank', [
  { itemName: '苏州银行', itemKey: '000' },
  { itemName: '村镇银行', itemKey: '001' }
]);

setDict('EnumCertificateKind', [
  {
    itemKey: '110001',
    itemName: '居民身份证'
  },
  {
    itemKey: '110003',
    itemName: '临时居民身份证'
  }
]);
const RegisterModal = () => {
  const modal = YREasyUseModal.useModal();
  const [form] = YRForm.useForm();
  const [step, setStep] = useState(0);
  const [time, setTime] = useState(0);
  const [countdown] = useCountDown({
    targetDate: time,
    onEnd: () => {}
  });
  // 手机号--是否本行员工为[是]时，自动从hr系统查询且不可编辑
  const isEmployeeBank = YRForm.useWatch('selfBankMemberFlag', form);
  // 监测用户姓名 登录账号改变
  const userName = YRForm.useWatch('userName', form);
  const account = YRForm.useWatch('account', form);
  // 监测hr机构变更
  const ownHrOrganId = YRForm.useWatch('ownHrOrganId', form);
  // 监测岗位
  const post = YRForm.useWatch('post', form);
  // hr机构下的业务Id
  const [bussinessIdList, setBussinessIdList] = useState<any[]>([]);

  const [btnLoading, setBtnLoading] = useState(false);
  // const [treeData, setTreeData] = useState<any[]>([]);
  // useEffect(() => {
  //   queryRegOrganTree({}).then((res) => {
  //     if (res?.success) {
  //       setTreeData(res.data?.organTreeDto);
  //     }
  //   });
  // }, []);

  // hr机构变更 查询出相应的业务机构Id 第一次进来不执行
  useUpdateEffect(() => {
    if (ownHrOrganId) {
      queryBelongHrOrganList({ hrOrganId: ownHrOrganId }).then((res) => {
        if (res?.success) {
          setBussinessIdList(res.data ?? []);
        }
      });
    }
  }, [ownHrOrganId]);

  // 名称和账号变化时，调三方接口查询用户信息 防抖useEffect
  //   useDebounceEffect(
  //     () => {
  //       if (userName && account) {
  //         const param = {
  //           userName,
  //           account
  //         };
  //         queryThirdUser(param).then((res) => {
  //           if (res && res.success && res.data?.length > 0) {
  //             const { isOwn, userMobile, userEmail, userPosition, organId, organName } = res.data[0];
  //             form.setFieldValue('selfBankMemberFlag', isOwn ? 'Y' : 'N');
  //             form.setFieldValue('mobile', isOwn ? userMobile : '');
  //             form.setFieldValue('email', isOwn ? userEmail : '');
  //             form.setFieldValue('post', isOwn ? userPosition : '');
  //             form.setFieldValue('ownHrOrganId', isOwn ? organId : '');
  //             form.setFieldValue('ownHrOrganName', isOwn ? organName : '');
  //           } else {
  //             form.setFieldValue('selfBankMemberFlag', 'N');
  //           }
  //         });
  //       }
  //     },
  //     [account, userName],
  //     {
  //       wait: 1000
  //     }
  //   );

  // 1.根据机构Id查出的业务Id筛选出不可选的所属机构 2.改为直接返回列表
  // const flatTreeData = (treeDataList: [] | object) => {
  //   const list = Array.isArray(treeDataList) ? treeDataList : [treeDataList];
  //   list.forEach((item: any) => {
  //     if (!bussinessIdList.includes(item.organId)) {
  //       item.disabled = true;
  //     }
  //     if (item?.children && Array.isArray(item.children)) {
  //       flatTreeData(item.children);
  //     }
  //   });
  //   setTreeData(list);
  // };

  // 如果业务Id变化 那么disabled需要重新渲染 但第一次不需要
  // useUpdateEffect(() => {
  //   flatTreeData(treeData);
  // }, [bussinessIdList]);

  const Com = (
    <YRForm.Row>
      <YRForm.Item name="tenantId" label="所属租户" rules={[{ required: true }]}>
        <YRDict.Select dictkey="EnumBank" />
      </YRForm.Item>
      <YRForm.Item name="account" label="登录账号" rules={[{ required: true }]}>
        <YRInput />
      </YRForm.Item>
      <YRForm.Item name="userName" label="用户姓名" rules={[{ required: true }]}>
        <YRInput />
      </YRForm.Item>
      <YRForm.Item
        name="selfBankMemberFlag"
        label="是否本行员工"
        rules={[{ required: true }]}
        help={step === 0 && <YRText type="warning">请先填写登录账号和用户姓名</YRText>}
      >
        <YRDict.Select disabled dictkey="EnumBoolean" />
      </YRForm.Item>
      <YRForm.Item name="mobile" label="手机号码" rules={[{ required: true }]}>
        <YRInput maxLength={11} disabled={isEmployeeBank === 'Y'} />
      </YRForm.Item>
      <YRForm.Item name="certificateKind" label="证件类型">
        <YRDict.Select dictkey="EnumCertificateKind" />
      </YRForm.Item>
      <YRForm.Item name="certificateNo" label="证件号码" rules={[{ pattern: regex.idCard, message: '身份证格式错误' }]}>
        <YRInput />
      </YRForm.Item>
      {/* <YRForm.Item name="organId" label="所属机构" rules={[{ required: true }]}> */}
      {/*  <YRTreeSelect */}
      {/*    treeData={Array.isArray(treeData) ? treeData : [treeData]} */}
      {/*    fieldNames={{ label: 'organName', value: 'organId' }} */}
      {/*    placeholder="请选择机构" */}
      {/*    disabled={!userName || !account} */}
      {/*  /> */}
      {/* </YRForm.Item> */}
      <YRForm.Item name="organId" label="所属机构" rules={[{ required: true }]}>
        <YRSelect
          placeholder="请选择机构"
          disabled={!userName || !account}
          // options={bussinessIdList}
          // fieldNames={{ label: 'organName', value: 'organId' }}
        >
          {bussinessIdList?.map((item) => (
            <YRSelect.Option value={item.organId} key={item.organId}>
              {step === 0 ? <YRTooltip title={item.organName}>{item.organName}</YRTooltip> : item.organName}
            </YRSelect.Option>
          ))}
        </YRSelect>
      </YRForm.Item>
      {isEmployeeBank === 'Y' &&
        post === '客户经理' && [
          <YRForm.Item name="email" label="电子邮箱" rules={[{ required: true, message: '请输入邮箱地址' }]}>
            <YRInput />
          </YRForm.Item>,
          <YRForm.Item name="level" label="客户经理级别">
            <YRInput maxLength={4} />
          </YRForm.Item>,
          <YRForm.Item name="bankTellerFlag" label="是否柜员" rules={[{ required: true }]} initialValue={'N'}>
            <YRDict.Select dictkey="EnumBoolean" disabled />
          </YRForm.Item>
        ]}
      {/* <YRForm.Item name="password" label="登录密码" rules={[{ required: true, message: '登录密码必填' }]}> */}
      {/*  <YRInput.Password maxLength={16} autoComplete={'off'} /> */}
      {/* </YRForm.Item> */}
      {/* <YRForm.Item */}
      {/*  name="checkPassword" */}
      {/*  label="确认密码" */}
      {/*  rules={[ */}
      {/*    { required: true, message: '请填写确认密码' }, */}
      {/*    { */}
      {/*      validator: (rule, value, callback) => { */}
      {/*        if (!value) { */}
      {/*          callback(); */}
      {/*        } else if (value !== form.getFieldValue('password') && value) { */}
      {/*          callback('确认密码与登录密码不一致'); */}
      {/*        } else { */}
      {/*          callback(); */}
      {/*        } */}
      {/*      } */}
      {/*    } */}
      {/*  ]} */}
      {/* > */}
      {/*  <YRInput.Password maxLength={16} autoComplete={'off'} /> */}
      {/* </YRForm.Item> */}
      <YRForm.Item name="post" label="岗位" rules={[{ required: true }]}>
        <YRDict.Select dictkey="EnumStation" />
      </YRForm.Item>
      {post === '其他' && isEmployeeBank === 'Y' && (
        <YRForm.Item name="上级领导" label="上级领导">
          <YRInput disabled />
        </YRForm.Item>
      )}
      <YRForm.Item name="ownHrOrganId" hidden />
      <YRForm.Item name="ownHrOrganName" label="所属HR机构">
        <YRInput />
      </YRForm.Item>
      <YRForm.Item name="smsCode" label="短信验证码" rules={[{ required: true }]}>
        <YRInput
          suffix={
            step === 0 && (
              <YRLink disabled={countdown !== 0} type="primary" onClick={() => setTime(Date.now() + 60000)}>
                {countdown === 0 ? '发送验证码' : `${Math.round(countdown / 1000)}s`}
              </YRLink>
            )
          }
        />
      </YRForm.Item>
    </YRForm.Row>
  );

  const componentItems = [
    {
      title: '注册账号',
      content: Com,
      okText: '信息确认',
      cancelText: '取消'
    },
    {
      title: '信息确认',
      content: Com,
      okText: '注册',
      cancelText: '上一步'
    }
  ];
  const items: ComponentProps<typeof YRSteps>['items'] = componentItems.map((item) => ({ title: item.title }));
  const modalFn = antdModal(modal);
  const btn =
    step < 2 ? (
      <YRButton.Space>
        <YRButton
          onClick={() => {
            if (step === 0) {
              modalFn.afterClose();
              modalFn.onCancel();
            } else if (step === 1) {
              setStep((s) => s - 1);
            }
          }}
        >
          {componentItems[step].cancelText}
        </YRButton>
        <YRButton
          type="primary"
          onClick={() => {
            form
              .validateFields()
              .then((values: { [key: string]: any }) => {
                setBtnLoading(true);
                if (step === 0) {
                  if (isEmployeeBank === 'Y') {
                    setStep((s) => s + 1);
                  } else {
                    YRMessage.warn('必须使用人力资源号作为登录账号');
                  }
                } else if (step === 1) {
                  // const {password, checkPassword} = values
                  const params = {
                    ...values,
                    //   password: md5Crypto(password),
                    //   checkPassword: md5Crypto(checkPassword),
                    accountEffectiveDate: moment('9999-12-31 23:59:59').format('YYYY-MM-DD HH:mm:ss')
                  };
                  registerUser(params).then((res) => {
                    if (res?.success) {
                      if (res.data.result) {
                        setStep((s) => s + 1);
                      } else {
                        YRMessage.error(res.data.errorMessage);
                      }
                    }
                  });
                }
              })
              .catch((err) => {
                // eslint-disable-next-line no-console
                console.log(err);
              })
              .finally(() => setBtnLoading(false));
          }}
          loading={btnLoading}
        >
          {componentItems[step].okText}
        </YRButton>
      </YRButton.Space>
    ) : null;

  return (
    <YRModal
      footer={step === 0 ? btn : step === 1 ? btn : false}
      {...modalFn}
      title={<>注册信息 {step === 1 && <YRLink style={{ marginLeft: 50, color: '#aaa' }}>不是我的账号?</YRLink>}</>}
      maskClosable={false}
      bodyStyle={{ maxHeight: 600, overflow: 'auto' }}
    >
      <YRForm form={form} mode={step === 0 ? 'add' : 'readPretty'}>
        <YRSpace direction="vertical" block size={20}>
          {step !== 2 && (
            <div style={{ display: 'flex', justifyContent: 'center' }}>
              <YRSteps style={{ maxWidth: '80%' }} current={step} items={items} />
            </div>
          )}
          {step === 2 ? (
            <YRResult
              title="恭喜你，注册成功！"
              subTitle="您已注册成功，可立即登录"
              status="success"
              extra={[
                <YRButton
                  type="primary"
                  onClick={() => {
                    userLogout().then(() => {
                      sessionStorage.removeItem('userInfo');
                      window.location.href = '/login/login.html';
                    });
                    // modalFn.onOk();
                    // modalFn.afterClose();
                    // modalFn.onCancel();
                  }}
                >
                  立即登录
                </YRButton>
              ]}
            />
          ) : (
            componentItems[step].content
          )}
        </YRSpace>
      </YRForm>
    </YRModal>
  );
};

export default YREasyUseModal.create(RegisterModal);
