/**
 * @Author: wangyw26123
 * @Description: 样式布局
 * @Date: Created in 2022-04-24 14:09:38
 * @Modifed By:
 */
@import 'src/common';

// 流程配置/流程决策 公共样式
.common {
  height: calc(100vh - 160px);
  overflow: hidden;
  .leftContent {
    height: 100%;
    border-right: 1px solid @yr-neutral-5;
    .tabs {
      height: calc(100% - 45px);
      overflow: auto;
      cursor: pointer;
      .activeItem {
        background: @blue-1;
      }
      div {
        height: 40px;
        padding: 10px 24px;
        border-bottom: 1px solid @yr-neutral-5;
        strong {
          font-size: 16px;
        }
      }
    }
  }
  .rightContent {
    .rightContentWapper {
      padding: 10px 24px;
      .upload {
        display: inline-block;
      }
    }
  }
}

.header {
  height: 45px;
  padding: 5px 24px;
  line-height: 35px;
  font-size: 18px;
  border-bottom: 1px solid @yr-neutral-5;
  &[bold='bold'] {
    font-weight: bold;
  }
}

// 流程编辑
.configEdit {
  height: calc(100vh - 56px);
  :global {
    .yr-xflow-container {
      height: 100% !important;
      border: none !important;
    }

    .xflow-node-panel-collpase-nodes {
      .ant-collapse-content {
        overflow: hidden;
        .ant-collapse-content-box {
          height: calc(100vh - 170px);
          width: 200px;
          overflow-y: scroll;
        }
      }
    }

    .x6-node-selected {
      .customeNode {
        box-shadow: 0 0 4px 4px @yr-primary-2;
      }
    }
    .xflow-node-panel-custom {
      padding: 5px 4px 0 4px;
      > div {
        margin-bottom: 8px;
      }
    }
    .yr-xflow-info-panel-collpase {
      z-index: 2020;
    }
    // 垂直的 Radio.Group
    .dict-radio-group,
    .dict-checkbox-group {
      > label {
        display: block;
      }
    }
    // 表单标题为空格时隐藏必填标记和冒号
    .ant-form-item-label {
      label[title=' '] {
        &::after,
        &::before {
          content: '';
        }
      }
    }
  }
  .xFlowCollapse {
    :global {
      .ant-collapse-item {
        border: 0;
        > .ant-collapse-content {
          background: @yr-neutral-1;
        }
      }
    }

    .xFlowCollapseHeader {
      display: flex;
      justify-content: space-between;
      .headerTitle {
        display: flex;
        align-items: center;
        .headerTitleText {
          margin-right: 5px;
        }
      }
    }
  }
  .panelTable {
    :global {
      .ant-table-small {
        border: 0;
        .ant-table-body {
          overflow: auto !important;
        }
        .ant-table-row-cell-break-word {
          padding: 4px 0 4px 4px !important;
        }
      }
    }
  }
}

// 自定义节点
.customeNode {
  display: flex;
  align-items: center;
  text-align: center;
  border-radius: 6px;
  background: @yr-neutral-1;
  padding: 8px;
  cursor: pointer;
  box-shadow: 0 0 2px 2px @yr-neutral-4;

  .icon {
    width: 36px;
    margin-right: 5px;
  }
  .name {
    display: flex;
    flex: 4;
    height: 100%;
    flex-direction: column;
    justify-content: center;
    line-height: inherit;
    margin-right: 5px;
    text-align: left;
    strong {
      font-size: 14px;
    }
    span {
      font-size: 12px;
    }
  }
  .extra {
    flex: 1;
    .extraTag {
      margin: 0;
      padding: 0 2px;
      line-height: inherit;
      transform: scale(0.9);
    }
  }
  &.CUSTOM-NODE-START,
  &.CUSTOM-NODE-END,
  &.CUSTOM-NODE-TASK {
    border-left-color: @blue-6;
  }
  &.CUSTOM-NODE-JUDGE,
  &.CUSTOM-NODE-INTERRUPT,
  &.CUSTOM-NODE-PARALLEL {
    border-left-color: @yr-neutral-6;
  }
}

// 全局面板-概览
.overviewIndex {
  width: 100%;
  height: 100%;
  overflow: auto;
  padding: 5px 16px 0;
  > div {
    margin-bottom: 12px;
  }
  .customeNode {
    &:hover {
      transform: scale(1.03);
    }
  }
}
