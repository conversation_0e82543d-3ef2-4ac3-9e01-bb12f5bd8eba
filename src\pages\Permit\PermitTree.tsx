/**
 * @Author: wangyw26123
 * @Description: 权限树
 * @Date: Created in 2022-12-26 14:28:54
 * @Modifed By:
 */
import React, { useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import { YRTree, YRTag, YRClassificationLayout, YRForm, YRInput, YRSpin, YREmpty } from 'yrantd';
import { getKylinBaseConfigItemFromStorage } from '@yr/util';
import { queryAllPermit } from '@/services/setting';
import { PermitTypeIcon } from './constant';
import styles from './index.module.less';
import { M0102 } from '@permit/permit';
import { useLocation } from 'ice';

// 权限组接口访问权限可见
const adminPermitGroupInterfaceAccessVisible = !!getKylinBaseConfigItemFromStorage(
  'admin_permit_group_interface_access_visible'
);
const { queryPermitGroupWithPermit } = M0102.interfaces;

interface CacheAllTreeDataItem extends PermitTreeListItem {
  /**
   * 是否拥有此权限
   */
  hasPermit: boolean;
  /**
   * 父级权限
   */
  parent?: CacheAllTreeDataItem;
}

interface PermitTreeListItem {
  /**
   * 权限名称
   */
  title: string;
  /**
   * 权限唯一标识
   */
  key: string;
  /**
   * 权限路径
   */
  path: string;
  /**
   * 父级权限
   */
  parentKey: string;
  /**
   * 权限类型
   * 1: 系统/菜单权限/虚拟组权限
   * 2: 按钮和其他页面元素权限
   * 3: 接口数据权限
   * 4: 接口访问权限
   */
  permitType: '1' | '2' | '3' | '4';
  /**
   * 子级权限
   */
  children?: PermitTreeListItem;
}

interface PermitTreeProps {
  /**
   * 权限组Id
   */
  permitGroupId: string;
  /**
   * form 表单实例
   */
  form?: any;
  /**
   * 选中项发生变化是触发
   * @param keys
   */
  getPermitIds?: (keys: string[]) => void;
  /**
   * 权限树加载之前触发
   */
  beforeLoad?: () => void;
  /**
   * 权限树加载之后触发
   */
  afterLoad?: () => void;
  /**
   * 是否禁止权限树选中
   */
  disabled?: boolean;

  /**
   * roleDetail 下发角色反显权限菜单
   */
  roleDetail?: { [key: string]: any };
}

const cacheAllTreeData: CacheAllTreeDataItem[] = [];
let excludedPermit: string[] = [];

const PermitTree = forwardRef((props: PermitTreeProps, ref) => {
  const { permitGroupId, form, getPermitIds, beforeLoad, afterLoad, disabled, roleDetail } = props;
  const [loading, setLoading] = useState<boolean>(false);
  const [allTreeData, setAllTreeData] = useState<PermitTreeListItem[]>([]);
  const [hasPermits, setHasPermits] = useState<string[]>([]);
  const [selectedPermitIds, setSelectedPermitIds] = useState<string[]>([]);
  const { query } = useLocation() as any;

  useEffect(() => {
    setLoading(true);
    // 获取权限树
    beforeLoad?.();
    queryAllPermit({ permitTypeList: ['1', '2', '4'] }).then((result) => {
      setLoading(false);
      afterLoad?.();
      if (result && result.success) {
        const permitTreeDtoList: PermitTreeListItem[] = (result.data && result.data.permitTreeDtoList) || [];
        setAllTreeData(permitTreeDtoList);
        expandTree(JSON.parse(JSON.stringify(permitTreeDtoList)), null, cacheAllTreeData);
      }
    });
  }, []);

  useEffect(() => {
    if (!permitGroupId) return;
    queryPermitByGroupId();
  }, [permitGroupId]);

  useEffect(() => {
    getPermitIds?.(processFnPermitIds());
  }, [selectedPermitIds]);

  useEffect(() => {
    syncCheckedKey(hasPermits);
  }, [hasPermits, allTreeData]);

  const queryPermitByGroupId = () => {
    setLoading(true);
    beforeLoad?.();
    // 查询权限组权限
    queryPermitGroupWithPermit({ permitGroupId }).then((res) => {
      setLoading(false);
      afterLoad?.();
      if (res && res.success) {
        form.setFieldValue('permitGroupName', res.data.permitGroupName);
        setHasPermits(res.data?.hasPermits || []);
      }
    });
  };

  /**
   * 将响应回来的树状数据全部展开并关联父级引用
   * @param data  树状数据
   * @param parent 父级引用
   * @param cacheTreeData 待添加的数据容器
   */
  const expandTree = (data, parent, cacheTreeData) => {
    if (data && Array.isArray(data)) {
      data.forEach((item) => {
        item.parent = parent;
        cacheTreeData.push(item);

        expandTree(item.children, item, cacheTreeData);
      });
    }
  };

  const syncCheckedKey = (keys: string[]) => {
    const userHasPermits: string[] = [...keys];
    const reg = /I\d+$/;
    let userAuth: string[] = [];

    if (!userHasPermits.length || !cacheAllTreeData.length) {
      setSelectedPermitIds(userHasPermits);
      return;
    }

    // 从缓存的全部权限中标记出用户拥有的权限
    userHasPermits.forEach((key) => {
      const nodeItem = cacheAllTreeData.find((item) => item.key === key);

      if (nodeItem) {
        nodeItem.hasPermit = true;
      }
    });

    // 过滤用户没有的权限
    const notPermit: CacheAllTreeDataItem[] = cacheAllTreeData.filter((item) => {
      // 如果权限树没有显示接口权限，那么这里就不过滤接口权限
      if (!adminPermitGroupInterfaceAccessVisible && reg.test(item.key)) return false;
      return !item.hasPermit;
    });

    // 查询出所有当前没有权限的父级权限id
    notPermit.forEach((item) => {
      const loop = (node) => {
        userAuth.push(node.key);
        node.parent && loop(node.parent);
      };

      item.parent && loop(item.parent);
    });

    userAuth = [...new Set([...userAuth])];
    // 从用户权限中删除没有的权限(让复选框处于半选或不选状态)
    if (userAuth.length) {
      let _id = 0;
      for (let i = 0; i < userHasPermits.length; i++) {
        if (userAuth.includes(userHasPermits[i])) {
          userHasPermits.splice(i, 1);

          i--;
          _id++;
        }

        if (_id > userAuth.length) break;
      }
    }

    // 找出被过滤掉的用户权限
    const _excludedPermit: string[] = [];
    keys.forEach((key) => {
      if (!userHasPermits.includes(key)) {
        _excludedPermit.push(key);
      }
    });

    excludedPermit = _excludedPermit;

    setSelectedPermitIds(userHasPermits);

    // 恢复默认值
    cacheAllTreeData.forEach((item) => {
      item.hasPermit = false;
    });
  };

  /**
   * 权限选择
   * @param checkedKeys
   * @param e
   */
  const handleCheckTree = (checkedKeys, e) => {
    if (!adminPermitGroupInterfaceAccessVisible) {
      const checkedNodes = e?.checkedNodes || [];
      checkedNodes.forEach((checkedNode) => {
        const currentPreCommonPermits = checkedNode?.props?.currentPreCommonPermits || [];
        const currentPreCommonPermitIds =
          currentPreCommonPermits.map((currentPreCommonPermit) => currentPreCommonPermit.key) || [];
        if (checkedKeys && checkedKeys.length > 0) {
          checkedKeys = checkedKeys.concat(currentPreCommonPermitIds);
        }
      });
    }
    getCurrentChildrenNode(e.node);
    getCurrentParentNode(e.node);
    // 应该把全选和半选的所有节点下的接口访问权限加入进来，即 node.interfaceAccessPermits
    setSelectedPermitIds(Array.from(new Set(checkedKeys)));
  };

  /**
   * 监听用户行为，用户选择的节点中包含被排除的权限时，说明用户手动操作过，这时提交的权限id由用户决定
   */
  const getCurrentChildrenNode = (node) => {
    if (!excludedPermit.length) return;

    const filterPermits = [...excludedPermit];
    const {
      props: { eventKey, children, currentPreCommonPermits }
    } = node;
    let childrenPermits = [eventKey];

    (currentPreCommonPermits || []).forEach((item) => {
      childrenPermits.push(item.key);
    });

    const loop = (data) => {
      if (Array.isArray(data)) {
        data.forEach((item) => {
          childrenPermits.push(item.key);

          if (adminPermitGroupInterfaceAccessVisible) {
            childrenPermits.push(item.key, ...(item.props?.currentPreCommonPermits || []));
          }

          if (item.props?.children) loop(item.props.children);
        });
      }
    };

    loop(children);

    childrenPermits = [...new Set(childrenPermits)];

    for (let i = 0; i < filterPermits.length; i++) {
      if (childrenPermits.includes(filterPermits[i])) {
        filterPermits.splice(i, 1);

        i--;
      }
    }

    excludedPermit = filterPermits;
  };

  /**
   * 获取当前节点的直接父级节点Id
   * @param node
   */
  const getCurrentParentNode = (node) => {
    if (!excludedPermit.length) return;

    const parentPermits = [...excludedPermit];
    const {
      props: { eventKey }
    } = node;
    const currentNode = cacheAllTreeData.find((item) => item.key === eventKey);

    if (currentNode) {
      const loop = (parent) => {
        if (parent) {
          if (parent.key) {
            const index = parentPermits.indexOf(parent.key);

            if (index > -1) {
              parentPermits.splice(index, 1);
            }
          }

          loop(parent.parent);
        }
      };

      loop(currentNode.parent);
    }

    excludedPermit = parentPermits;
  };

  const processFnPermitIds = () => {
    // return [...new Set([...excludedPermit, ...selectedPermitIds])];
    return [...new Set([...selectedPermitIds])];
  };

  const renderTreeNodes = (tree, preCommonPermits = []) => {
    // permitType: 4(接口访问权限)
    if (!adminPermitGroupInterfaceAccessVisible && tree && tree.length > 0) {
      tree = tree.filter((o) => o.permitType !== '4') || [];
    }
    return tree.map((item) => {
      let functionPermits = item.children || [];
      if (!adminPermitGroupInterfaceAccessVisible && item.children && item.children.length > 0) {
        functionPermits = item.children.filter((o) => o.permitType !== '4') || [];
      }
      // 当前节点下的接口访问权限集合
      let interfaceAccessPermits = [];
      if (!adminPermitGroupInterfaceAccessVisible && item.children && item.children.length > 0) {
        interfaceAccessPermits = item.children.filter((o) => o.permitType === '4') || [];
      }
      const currentPreCommonPermits = interfaceAccessPermits.concat(preCommonPermits);
      // 过滤出接口访问权限
      if (functionPermits && functionPermits.length > 0) {
        return (
          <YRTree.TreeNode
            icon={<YRTag color={PermitTypeIcon[item.permitType].color}>{PermitTypeIcon[item.permitType].text}</YRTag>}
            title={item.title}
            key={item.key}
            currentPreCommonPermits={currentPreCommonPermits}
          >
            {renderTreeNodes(functionPermits, currentPreCommonPermits)}
          </YRTree.TreeNode>
        );
      }
      return (
        <YRTree.TreeNode
          icon={<YRTag color={PermitTypeIcon[item.permitType].color}>{PermitTypeIcon[item.permitType].text}</YRTag>}
          title={item.title}
          key={item.key}
          currentPreCommonPermits={currentPreCommonPermits}
        />
      );
    });
  };

  useImperativeHandle(ref, () => {
    return {
      queryPermitByGroupId
    };
  });

  return (
    <YRSpin spinning={loading}>
      <YRClassificationLayout.Space>
        {/* 下发角色不允许修改 */}
        {query?.acrossTenantFlag !== 'Y' && (
          <YRClassificationLayout key="基本信息" title="基本信息">
            <YRForm form={form} layout="vertical" bordered={false}>
              <YRForm.Item
                label="权限组名称"
                name="permitGroupName"
                rules={[{ required: true, message: '请输入权限组名称' }]}
              >
                <YRInput maxLength={200} disabled={disabled} style={{ width: '50%' }} placeholder="请输入权限组名称" />
              </YRForm.Item>
            </YRForm>
          </YRClassificationLayout>
        )}
        <YRClassificationLayout key="权限菜单" title="权限菜单">
          {/* eslint-disable-next-line no-nested-ternary */}
          {query?.acrossTenantFlag !== 'Y' ? (
            allTreeData.length > 0 ? (
              <YRTree
                showLine
                showIcon
                checkable
                disabled={disabled}
                checkedKeys={selectedPermitIds}
                onCheck={handleCheckTree}
                className={styles.permitTreeNodeIcon}
              >
                {renderTreeNodes(allTreeData)}
              </YRTree>
            ) : (
              <YREmpty />
            )
          ) : (
            <YRTree
              showLine
              showIcon
              disabled
              checkable
              className={styles.permitTreeNodeIcon}
              treeData={(allTreeData as any) || []}
              checkedKeys={roleDetail?.hasPermits || []}
            />
          )}
        </YRClassificationLayout>
      </YRClassificationLayout.Space>
    </YRSpin>
  );
});

export default PermitTree;
