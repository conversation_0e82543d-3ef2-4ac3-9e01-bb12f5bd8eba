class easyJs {
  constructor(element) {
    this.element = element;
  }

  // 事件绑定
  addEventListener(type, handler) {
    if (this.element.addEventListener) {
      this.element.addEventListener(type, handler, false);
    } else if (this.element.attachEvent) {
      this.element.attachEvent(`on${type}`, handler);
    } else {
      this.element[`on${type}`] = handler;
    }
  }

  // 事件删除
  removeEventListener(type, handler) {
    if (this.element.removeEventListener) {
      this.element.removeEventListener(type, handler, false);
    } else if (this.element.detachEvent) {
      this.element.detachEvent(`on${type}`, handler);
    } else {
      this.element[`on${type}`] = null;
    }
  }

  // 获取样式
  getStyle(attr) {
    if (window.getComputedStyle) {
      return window.getComputedStyle(this.element, false)[attr];
    } else {
      return this.element.currentStyle[attr];
    }
  }
}
export default easyJs;
