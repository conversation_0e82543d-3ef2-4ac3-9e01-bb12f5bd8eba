// 业务阶段枚举
export const businessStageItem = [
  {
    value: '001',
    label: '评级阶段'
  },
  {
    value: '002',
    label: '准入阶段'
  },
  {
    value: '003',
    label: '授信阶段'
  },
  {
    value: '004',
    label: '合同阶段'
  },
  {
    value: '005',
    label: '出账阶段'
  }
];

// 业务阶段名称
export const businessStageNames = {
  '001': '评级阶段',
  '002': '准入阶段',
  '003': '授信阶段',
  '004': '合同阶段',
  '005': '出账阶段'
};

// 业务环节枚举d
export const businessTacheItem = {
  '001': [
    {
      value: '001',
      label: '评级新增'
    },
    {
      value: '002',
      label: '评级提交'
    },
    {
      value: '003',
      label: '评级审查'
    },
    {
      value: '004',
      label: '评级审批'
    }
  ],
  '002': [
    {
      value: '005',
      label: '准入环节'
    }
  ],
  '003': [
    {
      value: '006',
      label: '授信新增'
    },
    {
      value: '007',
      label: '授信提交'
    },
    {
      value: '008',
      label: '授信审查'
    },
    {
      value: '009',
      label: '授信审批'
    }
  ],
  '004': [
    {
      value: '010',
      label: '合同新增'
    },
    {
      value: '011',
      label: '合同提交'
    },
    {
      value: '012',
      label: '合同审查'
    },
    {
      value: '013',
      label: '合同审批'
    }
  ],
  '005': [
    {
      value: '014',
      label: '出账新增'
    },
    {
      value: '015',
      label: '出账提交'
    },
    {
      value: '016',
      label: '出账审查'
    },
    {
      value: '017',
      label: '出账审批'
    }
  ]
};

// 业务环节名称及所属阶段
export const tacheNameAndStage = {
  '001': {
    name: '评级新增',
    stage: '001'
  },
  '002': {
    name: '评级提交',
    stage: '001'
  },
  '003': {
    name: '评级审查',
    stage: '001'
  },
  '004': {
    name: '评级审批',
    stage: '001'
  },
  '005': {
    name: '准入环节',
    stage: '002'
  },
  '006': {
    name: '授信新增',
    stage: '003'
  },
  '007': {
    name: '授信提交',
    stage: '003'
  },
  '008': {
    name: '授信审查',
    stage: '003'
  },
  '009': {
    name: '授信审批',
    stage: '003'
  },
  '010': {
    name: '合同新增',
    stage: '004'
  },
  '011': {
    name: '合同提交',
    stage: '004'
  },
  '012': {
    name: '合同审查',
    stage: '004'
  },
  '013': {
    name: '合同审批',
    stage: '004'
  },
  '014': {
    name: '出账新增',
    stage: '005'
  },
  '015': {
    name: '出账提交',
    stage: '005'
  },
  '016': {
    name: '出账审查',
    stage: '005'
  },
  '017': {
    name: '出账审批',
    stage: '005'
  }
};

// 产品树
export const productOptions = [
  {
    value: 'A',
    label: '公司类',
    selectable: false,
    children: [
      {
        value: 'A1',
        label: '流动资金贷款',
        selectable: false,
        children: [
          {
            value: 'A11',
            label: '短期流动资金贷款'
          },
          {
            value: 'A12',
            label: '出口退税账户托管贷款'
          },
          {
            value: 'A13',
            label: '中长期流动资金贷款'
          },
          {
            value: 'A14',
            label: '法人账户透支'
          }
        ]
      },
      {
        value: 'A2',
        label: '固定资产贷款',
        selectable: false,
        children: [
          {
            value: 'A21',
            label: '基本建设项目贷款'
          },
          {
            value: 'A22',
            label: '技术改造项目贷款'
          },
          {
            value: 'A23',
            label: '并购项目贷款'
          },
          {
            value: 'A24',
            label: '其他类项目贷款'
          }
        ]
      },
      {
        value: 'A3',
        label: '进口押汇',
        selectable: false,
        children: [
          {
            value: 'A31',
            label: '信用证项下进口押汇'
          },
          {
            value: 'A32',
            label: '代收项下进口押汇'
          },
          {
            value: 'A33',
            label: '汇款项下进口押汇'
          }
        ]
      },
      {
        value: 'A4',
        label: '进口信用证',
        selectable: false,
        children: [
          {
            value: 'A41',
            label: '进口信用证开立'
          }
        ]
      },
      {
        value: 'A5',
        label: '保理业务',
        selectable: false,
        children: [
          {
            value: 'A51',
            label: '买方保理'
          },
          {
            value: 'A52',
            label: '卖方保理'
          }
        ]
      },
      {
        value: 'A6',
        label: '票据贴现业务',
        selectable: false,
        children: [
          {
            value: 'A61',
            label: '银行承兑汇票贴现'
          },
          {
            value: 'A61',
            label: '商业承兑汇票贴现'
          }
        ]
      },
      {
        value: 'A7',
        label: '债券投资',
        selectable: false,
        children: [
          {
            value: 'A71',
            label: '美元债'
          },
          {
            value: 'A72',
            label: '短期融资券'
          },
          {
            value: 'A73',
            label: '超短期融资券'
          },
          {
            value: 'A74',
            label: '中期票据'
          }
        ]
      }
    ]
  },
  {
    value: 'B',
    label: '同业类',
    selectable: false,
    children: [
      {
        value: 'B1',
        label: '现金及同业业务(资金业务)',
        selectable: false,
        children: [
          {
            value: 'B11',
            label: '存放同业'
          },
          {
            value: 'B12',
            label: '同业借款'
          },
          {
            value: 'B13',
            label: '同业拆借'
          },
          {
            value: 'B14',
            label: '债券逆回购'
          },
          {
            value: 'B15',
            label: '票据逆回购'
          },
          {
            value: 'B16',
            label: '债券借贷'
          }
        ]
      },
      {
        value: 'B2',
        label: '衍生品/贵金属/外汇',
        selectable: false,
        children: [
          {
            value: 'B21',
            label: '外汇远期'
          },
          {
            value: 'B22',
            label: '贵金属远期'
          },
          {
            value: 'B23',
            label: '外汇掉期'
          },
          {
            value: 'B24',
            label: '贵金属掉期'
          },
          {
            value: 'B25',
            label: '贵金属拆借'
          }
        ]
      },
      {
        value: 'B3',
        label: '债券和债权融资',
        selectable: false,
        children: [
          {
            value: 'B31',
            label: '国家债券'
          },
          {
            value: 'B32',
            label: '政策性金融债券'
          },
          {
            value: 'B33',
            label: '商业银行普通金融债'
          },
          {
            value: 'B34',
            label: '证券公司金融债'
          },
          {
            value: 'B35',
            label: '证券公司短期融资券'
          },
          {
            value: 'B36',
            label: '同业人民币存单'
          },
          {
            value: 'B37',
            label: '同业美元存单'
          }
        ]
      },
      {
        value: 'B4',
        label: '债券回购',
        selectable: false,
        children: [
          {
            value: 'B41',
            label: '专项债券质押式回购'
          }
        ]
      },
      {
        value: 'B5',
        label: '基金投资',
        selectable: false,
        children: [
          {
            value: 'B51',
            label: '公募基金-货币市场基金'
          },
          {
            value: 'B52',
            label: '公募基金-债券基金'
          },
          {
            value: 'B53',
            label: '公募基金-FOF'
          }
        ]
      },
      {
        value: 'B6',
        label: '理财产品',
        selectable: false,
        children: [
          {
            value: 'B61',
            label: '理财子公募理财产品'
          }
        ]
      },
      {
        value: 'B7',
        label: '资产管理计划',
        selectable: false,
        children: [
          {
            value: 'B71',
            label: '信托计划'
          },
          {
            value: 'B72',
            label: '券商资产管理计划'
          },
          {
            value: 'B73',
            label: '基金资产管理计划'
          }
        ]
      },
      {
        value: 'B8',
        label: '资产支持债券ABS',
        selectable: false,
        children: [
          {
            value: 'B81',
            label: '企业资产证券化(交易所)'
          },
          {
            value: 'B82',
            label: '信贷资产证券化(银行间)'
          },
          {
            value: 'B83',
            label: '资产支持票据ABN(银行间)'
          }
        ]
      }
    ]
  },
  {
    value: 'C',
    label: '个人类',
    selectable: false,
    children: [
      {
        value: 'C1',
        label: '按揭类',
        selectable: false,
        children: [
          {
            value: 'C11',
            label: '个人一手住房贷款'
          }
        ]
      },
      {
        value: 'C2',
        label: '消费类',
        selectable: false,
        children: [
          {
            value: 'C21',
            label: '个人综合消费贷款'
          },
          {
            value: 'C22',
            label: '个人消费贷款'
          }
        ]
      },
      {
        value: 'C3',
        label: '经营类',
        selectable: false,
        children: [
          {
            value: 'C31',
            label: '个人生产经营贷款'
          }
        ]
      }
    ]
  }
];

// 根据节点编号获取对应文本名称
export const getNodeLabel = (nodeId, treeList) => {
  let nodeLabel = '';
  treeList.forEach((item) => {
    if (item?.value === nodeId) {
      nodeLabel = item?.label;
    } else if (item?.children && getNodeLabel(nodeId, item?.children)) {
      nodeLabel = getNodeLabel(nodeId, item?.children);
    }
  });
  return nodeLabel;
};
