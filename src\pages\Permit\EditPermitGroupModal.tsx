/**
 * @Author: wangyw26123
 * @Description: 新增权限组
 * @Date: Created in 2022-12-27 09:44:55
 * @Modifed By:
 */
import React, { useEffect, useState } from 'react';
import { YRButton, YRForm, YRModal, YRInput, YRMessage } from 'yrantd';
import { EnumMsg } from '@/constant/common';
import { M0102 } from '@permit/permit';

const { addPermitGroup } = M0102.E01.interfaces;

const EditPermitGroupModal = (props) => {
  const { visible, onCancel, onOk } = props;
  const [btnLoading, setBtnLoading] = useState<boolean>(false);
  const [form] = YRForm.useForm();

  useEffect(() => {
    visible && form.resetFields();
  }, [visible]);

  const onsubmit = () => {
    form.validateFields().then((values) => {
      setBtnLoading(true);

      addPermitGroup({
        permitGroupName: values.permitGroupName
      }).then((res) => {
        setBtnLoading(false);

        if (res.success) {
          YRMessage.success(EnumMsg.add);
          onOk();
        }
      });
    });
  };

  return (
    <YRModal
      title="新增权限组"
      open={visible}
      onCancel={onCancel}
      footer={[
        <YRButton key="cancel" onClick={onCancel}>
          取消
        </YRButton>,
        <YRButton
          key="confirm"
          type="primary"
          check={M0102.E01}
          disabled={btnLoading}
          loading={btnLoading}
          onClick={onsubmit}
        >
          确定
        </YRButton>
      ]}
    >
      <YRForm form={form}>
        <YRForm.Row>
          <YRForm.Item
            column="block"
            label="权限组名称"
            name="permitGroupName"
            rules={[{ required: true, message: '请输入权限组名称' }]}
          >
            <YRInput maxLength={200} placeholder="请输入权限组名称" />
          </YRForm.Item>
        </YRForm.Row>
      </YRForm>
    </YRModal>
  );
};

export default EditPermitGroupModal;
