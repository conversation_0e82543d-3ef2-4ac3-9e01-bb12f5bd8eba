### **1.1.1**授权配置状态

| **名词** | **名词解释**                                                 |
| -------- | ------------------------------------------------------------ |
| 待提交   | 内部授权岗在所选角色下新增授权配置保存后，或者复制基础授权保存后，授权状态为待提交 |
| 复核中   | 内部授权岗将待提交或复核退回的数据提交至复核阶段时，授权状态为复核中 |
| 复核退回 | 授权复核不通过时，授权状态由复核中更新为复核退回             |
| 已生效   | 授权复核通过，授权状态由复核中更新为已生效；对已暂停的授权记录，操作重启，则将已暂停更新为已生效；已暂停的授权记录，暂停截止日期之后，自动更新为已生效。 |
| 已失效   | 操作失效，可将已生效状态的基础授权变更为已失效；若基础授权中设置了失效日期，则在到期后，自动更新为已失效 |
| 已暂停   | 对已生效的基础授权操作暂停，将已生效更新为已暂停             |

 

### **1.1.2** ***\*授权配置\****

授权管理用于省行（总行）的内部授权岗位对省内各机构的审批角色进行授权规则配置。

#### 1.1.2.1 单笔授权管理

 

授权管理用于省行（总行）的内部授权岗位对省行的各部门以及省行（总行）下各市行（分行）/各市行（分行）内的角色进行基础授权。

##### 1.1.2.1.1 授权信息列表

###### 1.1.2.1.1.1 功能描述

本功能默认展示已生效的基础授权。提供新增授权、详情、删除、失效、提交复核、修改、暂停、重启功能，可以通过查询条件快速筛选，以列表形式展示。

###### 1.1.2.1.1.2 资源权限

| 类型 | 资源名称     | 所属功能         | 权限说明                 |
| ---- | ------------ | ---------------- | ------------------------ |
| 列表 | 授权信息列表 | 授权管理         | 可见角色：总行内部授权岗 |
| 按钮 | 查询         | 基础授权信息列表 | 可见角色：总行内部授权岗 |
| 按钮 | 重置         | 基础授权信息列表 | 可见角色：总行内部授权岗 |
| 按钮 | 新增授权     | 基础授权信息列表 | 可见角色：总行内部授权岗 |
| 按钮 | 详情         | 基础授权信息列表 | 可见角色：总行内部授权岗 |
| 按钮 | 删除         | 基础授权信息列表 | 可见角色：总行内部授权岗 |
| 按钮 | 失效         | 基础授权信息列表 | 可见角色：总行内部授权岗 |
| 按钮 | 提交复核     | 基础授权信息列表 | 可见角色：总行内部授权岗 |
| 按钮 | 修改         | 基础授权信息列表 | 可见角色：总行内部授权岗 |
| 按钮 | 暂停         | 基础授权信息列表 | 可见角色：总行内部授权岗 |
| 按钮 | 重启         | 基础授权信息列表 | 可见角色：总行内部授权岗 |

###### 1.1.2.1.1.3 业务流程图

不适用。

###### 1.1.2.1.1.4 业务规则

1、界面展示。

1）界面分为三部分展示，包括机构列表、角色列表和基础授权列表。

2）机构列表。

①展示全行的机构树（含各部门），按机构树层级展示。每一个机构下，展示本机构已配置的具体授信类别，授信类别指授信业务审批授权和放款业务审批授权（本阶段暂无放款授权）。如果未配置具体授权类别，则不展示。

②提供【新增授信类别】操作功能。

3）角色列表。

①展示当前选择机构，已配置的具体授信类别的授权角色清单，包含机构授权和角色授权两种。如果未配置具体授权角色，则展示为空。

②提供【新增授权角色】操作功能。

4）基础授权列表。

①默认展示已选授权角色下的所有基础授权信息，按照授权状态（已生效的排前面）、生效时间排序（逆序排序）。

②按业务条线分页签展示，具体条线包括：

③提供【新增授权】、【详情】、【修改】、【删除】、【失效】、【提交复核】、【暂停】、【重启】操作功能。

2、操作规则。

1）机构列表。

①新增授信类别。用于选择某一机构的授信类别，以及该授信类别的授权方式。授权方式指给机构授权和给机构下的具体角色授权。

2）角色列表。

①新增授权角色。用于新增具体授信类别下的授权角色，授权角色支持多选。

3）授权列表。

①新增授权。用于新增授权角色下的基础授权信息，包含基本信息、维度信息和结果类型三部分。其中，基本信息指本条基础授权的归属条线、生效日期、失效日期等基本信息；维度信息指本条授权规则的条件维度，指：客户类型、产品类型、主担保方式、合作项目等；结果类型指本条授权规则的结果类型以及对应结果值，指：专项阈值金额、客户授信总金额等。

² 授权详情页面，维度信息模块提供【新增维度】操作功能。用于添加授权规则的具体条件维度，支持录入多个条件维度信息。

②详情。用于查看并修改授权规则详情信息。

③修改。用于修改授权规则的详情信息。

④删除。用于删除授权规则的详情信息。

⑤失效。用于将生效的授权规则置为失效，失效后，该授权规则将失去作用。

⑥提交复核。用于将未提交的授权规则的详情信息提交复核。

⑦暂停。用于将授权规则的详情信息作暂停处理。

⑧重启。用于将已暂停的授权规则作重启处理。

3）当操作详情、修改、删除、失效、提交复核、暂停、重启时，需先选择一条数据，否则提示“请选择一条信息！”。

###### 1.1.2.1.1.5 界面原型

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml53512\wps5.jpg) 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml53512\wps6.jpg) 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml53512\wps7.jpg) 

###### 1.1.2.1.1.6 输入输出

| ***\*要素名称\**** | ***\*输入格式\**** | ***\*数据类型\**** | ***\*是否必输\**** | ***\*数据来源\**** | ***\*只读\**** | ***\*备注\****                                     |
| ------------------ | ------------------ | ------------------ | ------------------ | ------------------ | -------------- | -------------------------------------------------- |
| **查询条件**       |                    |                    |                    |                    |                |                                                    |
| 客户类型           | 下拉框             | 字符               | 否                 | 手工选择           | 否             | 个人客户、对公客户、同业客户、集团客户、合作方客户 |
| 授权状态           | 下拉框             | 字符               | 否                 | 手工选择           | 否             | 待提交、复核中、复核退回、已生效、已失效、已暂停   |
| 授权类别           | 下拉框             | 字符               | 否                 | 手工选择           | 否             | 授信业务审批授权放款审查授权                       |
| 暂停截至日期       | 日期框             | 字符               | 否                 | 手工选择           | 否             | 列表形式时，展示该查询条件                         |
| **输出列表**       |                    |                    |                    |                    |                |                                                    |
| 授权类别           | 文本框             | 字符               | 是                 | 系统反显           |                |                                                    |
| 角色               | 文本框             | 字符               | 是                 | 系统反显           |                |                                                    |
| 授权维度           | 文本框             | 字符               | 是                 | 系统反显           |                |                                                    |
| 结果类型           | 文本框             | 字符               | 是                 | 系统反显           |                |                                                    |
| 结果               | 文本框             | 字符               | 是                 | 系统反显           |                |                                                    |
| 客户类型           | 文本框             | 字符               | 是                 | 系统反显           |                |                                                    |
| 状态               | 文本框             | 字符               | 是                 | 系统反显           |                |                                                    |
| 暂停截至日期       | 日期框             | 日期               | 是                 | 系统反显           |                |                                                    |
| 生效日期           | 文本框             | 字符               | 是                 | 系统反显           |                |                                                    |
| 失效日期           | 日期框             | 日期               | 是                 | 系统反显           |                |                                                    |
| 登记日期           | 文本框             | 字符               | 是                 | 系统反显           |                |                                                    |
| 登记人             | 文本框             | 字符               | 是                 | 系统反显           |                |                                                    |
| 登记机构           | 文本框             | 字符               | 是                 | 系统反显           |                |                                                    |
| **按钮**           |                    |                    |                    |                    |                |                                                    |
| 查询               | 按钮               |                    |                    |                    |                |                                                    |
| 重置               | 按钮               |                    |                    |                    |                |                                                    |
| 新增授权           | 按钮               |                    |                    |                    |                |                                                    |
| 详情               | 按钮               |                    |                    |                    |                |                                                    |
| 删除               | 按钮               |                    |                    |                    |                |                                                    |
| 失效               | 按钮               |                    |                    |                    |                |                                                    |
| 提交复核           | 按钮               |                    |                    |                    |                |                                                    |
| 修改               | 按钮               |                    |                    |                    |                |                                                    |
| 暂停               | 按钮               |                    |                    |                    |                |                                                    |
| 重启               | 按钮               |                    |                    |                    |                |                                                    |

##### 1.1.2.1.2 新增授权

###### 1.1.2.1.2.1 功能描述

本功能支持总行内部授权岗新增授权规则。

###### 1.1.2.1.2.2 资源权限

同授权信息列表-资源权限-新增授权信息按钮。

###### 1.1.2.1.2.3 业务流程图

不适用。

###### 1.1.2.1.2.4 业务规则

1、操作新增按钮后，新增详情页面要素初始化规则：

（1）详见输入输出。

（2）其中维度[结果类型]为默认维度，不可删除。

2、业务操作规则：

新增授权时，通过+与-进行条件维度信息的维护，通过↑↓进行维度顺序调整。

3、保存规则：

（1）当授信类别为:授信业务审批授权、放款审查授权,则结果类型为必选项，在选择结果类型后需填入金额，默认为0。若没有选择结果类型，则不允许保存，在保存时红字提示“没有结果类型的授权不允许保存！”。

（2）必须存在至少一项条件维度，若不存在任何条件维度，则保存不成功，系统红字提示“请至少增加一项条件维度！”。

（3）系统校验本条授权规则是否有选择重复的维度名称，若有则保存不成功，则在保存时会提示“条件维度xx重复”。

（4）规则保存时，针对不同的条件维度和结果维度组合，互斥校验如下：

①　条件维度[客户类型]必选，否则报错：条件维度[客户类型]必输，请核实；

②　条件维度[客户类型]为“个人客户”时，才可配置条件维度[贷款用途]。否则报错：[客户类型]为“个人客户”时，才可配置条件维度[贷款用途]，请核实；

③　条件维度[客户类型]为“公司客户”时，才可配置条件维度[贷款组织形式]。否则报错：[客户类型]为“公司客户”时，才可配置条件维度[贷款组织形式]，请核实；

④　条件维度[客户类型]为“个人客户”或“公司客户”时，才可配置条件维度[是否存量]。否则报错：[客户类型]为“个人客户”或“公司客户”时，才可配置条件维度[是否存量]，请核实；

⑤　条件维度[客户类型]为“个人客户”或“公司客户”时，才可配置条件维度[是否重组]。否则报错：[客户类型]为“个人客户”或“公司客户”时，才可配置条件维度[是否重组]，请核实；

⑥　条件维度[客户类型]为“个人客户”或“公司客户”时，才可配置条件维度[主担保方式]。否则报错：[客户类型]为“个人客户”或“公司客户”时，才可配置条件维度[主担保方式]，请核实；

⑦　条件维度[客户类型]为“公司客户”时，才可配置条件维度[是否担保额度]。否则报错：[客户类型]为“公司客户”时，才可配置条件维度[是否担保额度]，请核实；

⑧　条件维度[客户类型]为“合作方”时，才可配置条件维度[合作类型]。否则报错：[客户类型]为“合作方”时，才可配置条件维度[合作类型]，请核实；

⑨　授权类型为“专项阈值”，且条件维度[客户类型]为“个人客户”或“公司客户”时，才可配置条件维度[合作项目]。否则报错：授权类型为“专项阈值”，且条件维度[客户类型]为“个人客户”或“公司客户”时，才可配置条件维度[合作项目]，请核实；

⑩　[授权类型]为“产品阈值”且条件维度[客户类型]为“个人客户”时，才可配置结果维度[授信金额（用途+担保）]。否则报错：[授权类型]为“产品阈值”且条件维度[客户类型]为“个人客户”时，才可配置结果维度[授信金额（用途+担保）]，请核实；

⑪　[授权类型]为“产品阈值”且条件维度[客户类型]为“公司客户”时，才可配置结果维度[授信金额（贷款组织形式+担保）]。否则报错：[授权类型]为“产品阈值”且条件维度[客户类型]为“公司客户”时，才可配置结果维度[授信金额（贷款组织形式+担保）]，请核实；

⑫　[授权类型]为“产品阈值”且条件维度[客户类型]为“公司客户”时，才可配置结果维度[授信金额（产品组+担保）]。否则报错：[授权类型]为“产品阈值”且条件维度[客户类型]为“公司客户”时，才可配置结果维度[授信金额（产品组+担保）]，请核实；

⑬　[授权类型]为“综合阈值”且条件维度[客户类型]为“个人客户”或“公司客户”时，才可配置结果维度[授信金额（担保）]。否则报错：[授权类型]为“综合阈值”且条件维度[客户类型]为“个人客户”或“公司客户”时，才可配置结果维度[授信金额（担保）]，请核实；

⑭　[授权类型]为“专项阈值”且条件维度[客户类型]为“个人客户”或“公司客户”时，才可配置结果维度[专项阈值金额]。否则报错：[授权类型]为“专项阈值”且条件维度[客户类型]为“个人客户”或“公司客户”时，才可配置结果维度[专项阈值金额]，请核实；

⑮　[授权类型]为“综合阈值”，条件维度[客户类型]为“个人客户”或“公司客户”且条件维度[是否重组]为“是”时，才可配置结果维度[重组授信总金额]。否则报错：[授权类型]为“综合阈值”，条件维度[客户类型]为“个人客户”或“公司客户”且条件维度[是否重组]为“是”时，才可配置结果维度[重组授信总金额]，请核实；

⑯　[授权类型]为“综合阈值”时，才可配置结果维度[客户授信总金额]。否则报错：[授权类型]为“综合阈值”时，才可配置结果维度[客户授信总金额]，请核实；

（5）[是否重组]配置为“是”时，[是否存量]一定要配置为“是”。否则报错：[是否重组]配置为“是”时，[是否存量]一定要配置为“是”，请核实；

4、新增成功后处理：

生成一条授权状态为待提交的授权规则信息，并展示在待提交授权规则列表中。

5、其他规则：

1）针对阈值标签为“专项阈值”的基础授权，不允许进行转授权。

2）其他阈值标签的基础授权，允许进行转授权。

 

###### 1.1.2.1.2.5 界面原型

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml53512\wps8.jpg) 

###### 1.1.2.1.2.6 输入输出

| ***\*要素名称\**** | ***\*输入格式\**** | ***\*数据类型\**** | ***\*是否必输\**** | ***\*只读\**** | ***\*数据来源\**** | ***\*备注\****                                               |
| ------------------ | ------------------ | ------------------ | ------------------ | -------------- | ------------------ | ------------------------------------------------------------ |
| **基本信息**       |                    |                    |                    |                |                    |                                                              |
| 授权规则编号       | 文本框             | 字符               | 是                 | 是             | 系统反显           |                                                              |
| 授权目录编号       | 文本框             | 字符               | 是                 | 是             | 系统反显           |                                                              |
| 机构               | 文本框             | 字符               | 是                 | 是             | 系统反显           | 默认授权角色对应授信类别对应的机构                           |
| 角色               | 文本框             | 字符               | 是                 | 是             | 系统反显           | 默认授权角色                                                 |
| 授权类别           | 文本框             | 字符               | 是                 | 是             | 系统反显           | 默认授权角色对应授权类别                                     |
| 授权标签           | 下拉框             | 字符               | 是                 | 否             | 手工选择           | 产品阈值、综合阈值、专项阈值                                 |
| 维度信息           |                    |                    |                    |                |                    |                                                              |
| 条件维度名称       | 下拉框             | 字符               | 是                 | 否             | 手工选择           | 1、若授信类别为“授信业务审批授权”时，对应码值详见授权应用中的条件维度 |
| 结果维度名称       | 下拉框             | 字符               | 是                 | 否             | 手工选择           | 若授信类别为“授信业务审批授权”时，维度名称为“结果类型”时显示该字段，具体码值详见授权应用中的结果维度 |
| 结果取值           | 文本框             | 数值               | 是                 | 否             | 手工录入           | 默认0                                                        |
| 状态               | 文本框             | 字符               | 是                 | 是             | 系统反显           | 默认为待提交不允许修改                                       |
| 生效日期           | 日期框             | 日期               | 是                 | 否             | 手工录入           |                                                              |
| 失效日期           | 日期框             | 日期               | 否                 | 否             | 手工录入           |                                                              |
| 登记日期           | 文本框             | 字符               | 是                 | 是             | 系统反显           | 取当前系统日期                                               |
| 登记人             | 文本框             | 字符               | 是                 | 是             | 系统反显           | 取当前登录用户                                               |
| 登记机构           | 文本框             | 字符               | 是                 | 是             | 系统反显           | 取当前登录用户所属机构                                       |
| **按钮**           |                    |                    |                    |                |                    |                                                              |
| ➕                  | 按钮               |                    |                    |                |                    |                                                              |
| ➖                  | 按钮               |                    |                    |                |                    |                                                              |
| ↑                  | 按钮               |                    |                    |                |                    |                                                              |
| ↓                  | 按钮               |                    |                    |                |                    |                                                              |
| 保存               | 按钮               |                    |                    |                |                    |                                                              |
| 返回               | 按钮               |                    |                    |                |                    |                                                              |

 

##### 1.1.2.1.3 详情

###### 1.1.2.1.3.1 功能描述

本功能支持总行内部授权岗查看授权规则详情。

###### 1.1.2.1.3.2 资源权限

同授权信息列表-资源权限-详情按钮。

###### 1.1.2.1.3.3 业务流程图

不适用。

###### 1.1.2.1.3.4 业务规则

页面所有信息同新增界面，反显不允许修改。

###### 1.1.2.1.3.5 输入输出

同新增授权-输入输出，反显不可修改。

##### 1.1.2.1.4 修改

###### 1.1.2.1.4.1 功能描述

本功能支持总行内部授权岗修改已维护的授权规则。

###### 1.1.2.1.4.2 资源权限

同授权信息列表-资源权限-修改按钮。

###### 1.1.2.1.4.3 业务流程图

不适用。

###### 1.1.2.1.4.4 业务规则

1、系统校验授权规则数据的状态≠待提交or复核退回，则修改失败，提示"该授权状态为”xxx状态”不能修改!"；反之，跳转维护页面，进行修改保存操作。

###### 1.1.2.1.4.5 输入输出

同新增授权-输入输出。

 

##### 1.1.2.1.5 删除

###### 1.1.2.1.5.1 功能描述

本功能支持总行内部授权岗删除已维护的授权规则。

###### 1.1.2.1.5.2 资源权限

同授权信息列表-资源权限-删除按钮。

###### 1.1.2.1.5.3 业务流程图

不适用。

###### 1.1.2.1.5.4 业务规则

1、系统校验授权规则数据的状态≠待提交or复核退回，则删除失败，提示"该授权状态为”xxx状态”不能删除!"；反之，删除成功。

###### 1.1.2.1.5.5 输入输出

无。

##### 1.1.2.1.6 失效

###### 1.1.2.1.6.1 功能描述

本功能支持总行内部授权岗失效已生效的授权规则。

###### 1.1.2.1.6.2 资源权限

同授权信息列表-资源权限-失效按钮。

###### 1.1.2.1.6.3 业务流程图

不适用。

###### 1.1.2.1.6.4 业务规则

1、系统判断所选的数据进行状态是否为已生效，若不为已生效，则提示该授权状态为'"XXX状态"'不能失效!"。

2、失效后处理：将所选的授权规则信息的授权状态更新为已失效。

3、自动失效：若授权信息中失效日期不为空，系统自动按照[失效日期]=系统日期失效授权，自动将授权状态更新为已失效。

###### 1.1.2.1.6.5 输入输出

无。

##### 1.1.2.1.7 提交复核

###### 1.1.2.1.7.1 功能描述

本功能支持总行内部授权岗将待提交授权规则提交至授权复核阶段。

###### 1.1.2.1.7.2 资源权限

同授权信息列表-资源权限-提交复核按钮。

###### 1.1.2.1.7.3 业务流程图

不适用。

###### 1.1.2.1.7.4 业务规则

1、系统校验授权状态≠待提交、复核退回，提示"该授权状态为“XXX状态不能提交!"。

2、系统校验授权结果是否为空，若为空，则提示"提交复核失败，请输入授权结果类型!"。

3、提交成功后处理：将所选的授权规则信息的授权状态更新为复核中，并展示在复核中授权规则列表中。

###### 1.1.2.1.7.5 输入输出

无。

##### 1.1.2.1.8 暂停

###### 1.1.2.1.8.1 功能描述

本功能支持总行内部授权岗针对已生效的授权数据进行暂停操作。

###### 1.1.2.1.8.2 资源权限

同授权信息列表-资源权限-暂停按钮。

###### 1.1.2.1.8.3 业务流程图

不适用。

###### 1.1.2.1.8.4 业务规则

1、确认规则：系统校验截至日期必输，否则提示“请输入截至日期”。

2、暂停成功后处理：授权状态更新为已暂停。暂停日期超过截止日期后，授权规则重新置为已生效。

###### 1.1.2.1.8.5 输入输出

 

| ***\*要素名称\**** | ***\*输入格式\**** | ***\*数据类型\**** | ***\*是否必输\**** | ***\*只读\**** | ***\*数据来源\**** | ***\*备注\**** |
| ------------------ | ------------------ | ------------------ | ------------------ | -------------- | ------------------ | -------------- |
| **输入要素**       |                    |                    |                    |                |                    |                |
| 截至日期           | 日期框             | 字符串             | 是                 | 否             | 手工录入           |                |
| **操作按钮**       |                    |                    |                    |                |                    |                |
| 确认               |                    | 按钮               |                    |                |                    |                |
| 取消               |                    | 按钮               |                    |                |                    |                |

##### 1.1.2.1.9 重启

###### 1.1.2.1.9.1 功能描述

本功能支持总行内部授权岗针对已暂停的授权数据进行重启操作。

###### 1.1.2.1.9.2 资源权限

同授权信息列表-资源权限-重启按钮。

###### 1.1.2.1.9.3 业务流程图

不适用。

###### 1.1.2.1.9.4 业务规则

1、重启成功后处理：授权状态更新为已生效。

###### 1.1.2.1.9.5 输入输出

 

无

##### 1.1.2.1.10 导出

###### 1.1.2.1.10.1 功能描述

本功能支持总行内部授权岗按机构导出授权数据。

###### 1.1.2.1.10.2 资源权限

同授权信息列表-资源权限-导出按钮。

###### 1.1.2.1.10.3 业务流程图

不适用。

###### 1.1.2.1.10.4 业务规则

1、点击导出按钮，弹出机构树，录入生效日期（非必输），选中要导出授权规则的机构，可多选，点击确定后，判断是否录入生效日期，未录入则导出所选机构下所有的授权配置数据，若已录入生效日期，则导出所选机构下授权配置的生效日期大于等于录入生效日期的所有授权配置数据，导出数据按机构、状态排序。

###### 1.1.2.1.10.5 输入输出

 

无