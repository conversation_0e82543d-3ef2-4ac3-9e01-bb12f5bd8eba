/**
 * @Author: wangyw26123
 * @Description: 边属性面板tabs集合
 * @Date: Created in 2022-04-25 16:18:51
 * @Modifed By:
 */

import React, { useEffect, useState } from 'react';
import { YRTabs } from 'yrantd';
import { checkAuth } from '@/utils/utils';

import BaseInfoIndex from './BaseInfo/BaseInfoIndex';

const { TabPane } = YRTabs;

const styles = {
  height: 'calc(100vh - 200px)',
  overflow: 'auto'
};

let edgeTabPanels = [
  {
    title: '基本信息',
    key: 'baseInfo',
    check: null,
    component(props) {
      return (
        <TabPane forceRender tab={this.title} key={this.key}>
          <div style={styles}>
            <BaseInfoIndex {...props} />
          </div>
        </TabPane>
      );
    }
  }
];

edgeTabPanels = edgeTabPanels.filter((item) => (item.check ? checkAuth(item.check) : true));

const FormIndex = (props) => {
  const [activeKey, setActiveKey] = useState();
  const {
    targetData: { edgeTabs = [], id }
  } = props;

  useEffect(() => {
    setActiveKey('baseInfo');
  }, [id]);
  const newEdgeTabPanels = edgeTabPanels.filter((item) => edgeTabs.includes(item.key));
  const renderEdgeTab = newEdgeTabPanels.map((com) => com.component(props)) || [];

  return (
    <YRTabs size="small" defaultActiveKey="baseInfo" activeKey={activeKey} onChange={(e) => setActiveKey(e)}>
      {renderEdgeTab}
    </YRTabs>
  );
};

export default FormIndex;
