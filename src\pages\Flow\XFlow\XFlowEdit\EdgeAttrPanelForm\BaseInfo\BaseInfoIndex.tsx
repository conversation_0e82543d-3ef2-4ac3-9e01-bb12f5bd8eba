/**
 * @Author: wangyw26123
 * @Description: 边-基本信息表单
 * @Date: Created in 2022-04-27 13:49:31
 * @Modifed By:
 */

import React from 'react';
import { checkAuth } from '@/utils/utils';
import XFlowCollapse from '@/pages/Flow/XFlow/components/XFlowCollapse';
import EdgeInfo from './EdgeInfo';

const { Panel } = XFlowCollapse;

let components = [
  {
    title: '基本信息',
    key: 'edgeInfo',
    check: null,
    component(props) {
      return (
        <Panel forceRender key={this.key} header={this.title}>
          <EdgeInfo {...props} />
        </Panel>
      );
    }
  }
];

components = components.filter((item) => (item.check ? checkAuth(item.check) : true));

const BaseInfoIndex = (props) => {
  const {
    targetData: { edgePanel }
  } = props;

  const coms = components.filter((item) => edgePanel.includes(item.key)) || [];
  const edgePanels = coms.map((com) => com.component(props)) || [];

  return <XFlowCollapse defaultActiveKey={coms[0].key}>{edgePanels}</XFlowCollapse>;
};

export default BaseInfoIndex;
