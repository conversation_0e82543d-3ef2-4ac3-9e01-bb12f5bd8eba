/**
 * @Author: liufh23623
 * @Description: 流程图-新增流程图机构映射弹窗
 * @Date: Created in 2022-12-15 16:58:45
 * @Modifed By:
 */
import React, { useEffect, useState } from 'react';
import {
  YRForm,
  YRInput,
  YRModal,
  YRButton,
  YRTreeSelect,
  YRMessage,
  YRClassificationLayout,
  YREditableTable,
  YRDict
} from 'yrantd';
import { EditableColumnType, ValueTypeEnum, ToolbarProps } from 'yrantd/lib/yr-editable-table/declare';

import { M02 } from '@permit/flow';

const { addFlowDiagramResource } = M02.E01.interfaces;

interface DataType {
  flowName: string;
  flowNumber: string;
  flowDesc?: string;
  sceneType: string;
}

const defaultOrganItem = {
  flowName: undefined,
  flowNumber: undefined,
  flowDesc: undefined,
  sceneType: undefined
};

const OrganMapModal = (props) => {
  const { visible, flowInfo, onCancel: cancelProps, callback, mode } = props;
  const [title, setTitle] = useState<string>('');
  const [forceRender, setForceRender] = useState<number>(0);
  const [formDisabled, setFormDisabled] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<DataType[]>([{ ...defaultOrganItem }]);
  const [form] = YRForm.useForm();

  useEffect(() => {
    setTitle('新增流程图基本信息');
    setFormDisabled(false);
  }, []);

  useEffect(() => {
    // 重置编辑表格数据
    if (visible) {
      setDataSource([{ ...flowInfo }]);
      setForceRender(forceRender + 1);
    }
  }, [visible]);

  const onSubmit = () => {
    form.validateFields().then((values: any) => {
      const params = { modelType: '1', ...values };

      addFlowDiagramResource(params).then((res) => {
        if (res.success) {
          YRMessage.success('新增成功!');
          form.resetFields();
          callback();
        }
      });
    });
  };

  const onCancel = () => {
    form.resetFields();
    cancelProps();
  };

  return (
    <YRModal
      title={title}
      open={visible}
      onCancel={onCancel}
      footer={[
        <YRButton onClick={onCancel}>取消</YRButton>,
        <YRButton type="primary" onClick={onSubmit}>
          确定
        </YRButton>
      ]}
    >
      <YRForm form={form}>
        <YRForm.Row column={1}>
          <YRForm.Item
            name="modelName"
            label="流程名称"
            initialValue={flowInfo.modelName}
            rules={[{ required: true, message: '请输入流程名称' }]}
          >
            <YRInput />
          </YRForm.Item>
          <YRForm.Item
            name="modelNumber"
            label="流程编号"
            initialValue={flowInfo.modelNumber}
            rules={[{ required: true, message: '请输入流程编号' }]}
          >
            <YRInput />
          </YRForm.Item>
          <YRForm.Item
            name="modelDesc"
            label="流程描述"
            initialValue={flowInfo.modelDesc}
            rules={[{ required: true, message: '请输入流程描述' }]}
          >
            <YRInput />
          </YRForm.Item>
          <YRForm.Item
            name="sceneType"
            label="流程类型"
            initialValue={flowInfo.sceneType}
            rules={[{ required: true, message: '请输入流程类型' }]}
          >
            <YRDict.Select dictkey="flow_scene_type" />
          </YRForm.Item>
        </YRForm.Row>
      </YRForm>
    </YRModal>
  );
};

export default OrganMapModal;
