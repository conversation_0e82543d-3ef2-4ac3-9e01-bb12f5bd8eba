/**
 * 页面描述:场景配置页
 * @文件名 index.tsx
 * @filePath \src\pages\Setting\RuleManage\Scene\Detail\index.tsx
 * @Date 2023-08-16 10:04:49
 * @Email <EMAIL>
 * <AUTHOR>
 */
import React, { useEffect, useRef, useState } from 'react';
import {
  YRClassificationLayout,
  YRForm,
  YRFullPageLayout,
  YRButton,
  YRInput,
  YRTextAreaPlus,
  YRCard,
  YRMessage,
  YRFlexPageLayout
} from 'yrantd';
import { useLocation, useParams } from 'ice';
import { useRequest } from 'ahooks';
import { closeWin } from '@yr/util';
import RuleConfig from './RuleConfig';
import StageTree from '../components/StageTree';
import { saveOrModifyScene } from '@/services/setting';
import { formatRuleList } from '../util';

const titleOption = {
  add: '新增',
  edit: '修改',
  readPretty: '查看',
  copy: '复制'
};

const Detail = (props) => {
  const [form] = YRForm.useForm();
  const { query } = useLocation() as any;
  const { mode = 'add' } = useParams() as any;
  const { treeId } = query;
  const ruleConfigRef = useRef() as any;
  const [activeNode, setActiveNode] = useState({}) as any; // 当前所选阶段环节
  const [stageList, setStageList] = useState([]); // 阶段列表
  const [tacheList, setTacheList] = useState([]); // 环节列表

  /** 保存接口 */
  const { loading: submitLoading, run: submitRequest } = useRequest(saveOrModifyScene, {
    manual: true,
    onSuccess: (result, params) => {
      const { errorMessage, data } = result as any;
      if (errorMessage === null) {
        YRMessage.success('保存成功');
        closeWin();
      }
    }
  });

  // 新增时传入树编号
  useEffect(() => {
    if (treeId) {
      form.setFieldValue('treeId', treeId);
    }
  }, [treeId]);

  // 保存
  const submit = () => {
    form
      .validateFields()
      .then((values: any) => {
        const { genericList, productList, exemptList } = ruleConfigRef?.current || {};
        const formatGenericList = formatRuleList(tacheList, genericList);
        const formatProductList = formatRuleList(tacheList, productList);
        const sceneRuleDtoList = [...formatGenericList, ...formatProductList];
        if (!genericList || genericList?.length < 1) {
          YRMessage.error('通用规则条目列表不能为空');
          return;
        }
        if (formatGenericList.find((item) => !item?.operateType)) {
          YRMessage.error('请选择通用规则列表中的策略项');
          return;
        }
        if (formatProductList.find((item) => !item?.bizTypeList || (item?.bizTypeList || []).length < 1)) {
          YRMessage.error('请选择产品规则列表中的业务品种');
          return;
        }
        if (formatProductList.find((item) => !item?.operateType)) {
          YRMessage.error('请选择产品规则列表中的策略项');
          return;
        }
        // 过滤出符合当前所选业务阶段及环节的豁免条目
        const exemptGroupDtoList = exemptList.map((item) => {
          const exemptRuleDtoList = (item?.exemptRuleDtoList || []).filter((i) => tacheList.includes(i?.businessTache));
          return {
            ...item,
            exemptRuleDtoList
          };
        });
        const emptyExemptGroupIndex = exemptGroupDtoList.findIndex((item) => {
          return !tacheList.every((i) => item?.exemptRuleDtoList.find((v) => v?.businessTache === i && v?.flag));
        });
        if (emptyExemptGroupIndex >= 0) {
          YRMessage.error(`豁免规则第${emptyExemptGroupIndex + 1}行豁免组中存在空豁免项`);
          return;
        }
        const params = {
          ...values,
          sceneRuleDtoList,
          exemptGroupDtoList
        };
        submitRequest(params);
      })
      .catch((errorInfo) => {
        form.scrollToField(errorInfo.errorFields[0].name);
      });
  };

  // 页面标题
  const getTopLeft = () => {
    return {
      title: `${titleOption[mode]}场景配置`,
      goBack: () => {}
    };
  };

  // 页面操作项
  const getTopRight = () => {
    return mode === 'readPretty' ? null : (
      <YRButton type="primary" loading={submitLoading} onClick={submit}>
        保存
      </YRButton>
    );
  };

  return (
    <YRFullPageLayout
      left={getTopLeft()}
      span={24}
      contentStyle={{ padding: 0, paddingTop: 10 }}
      loading={ruleConfigRef?.current?.detailLoading}
      right={getTopRight()}
    >
      <YRFlexPageLayout>
        <YRFlexPageLayout.Sider title="阶段及环节目录">
          <StageTree setActiveNode={setActiveNode} stageList={stageList} tacheList={tacheList} />
        </YRFlexPageLayout.Sider>
        <YRFlexPageLayout.Main title="规则信息">
          <YRForm
            form={form}
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 20 }}
            mode={mode === 'readPretty' ? 'readPretty' : 'edit'}
          >
            <YRClassificationLayout.Space>
              <YRClassificationLayout title="基本信息">
                <YRForm.Item name="treeId" label="树编号" hidden>
                  <YRInput />
                </YRForm.Item>
                <YRForm.Row column={1}>
                  <YRForm.Item name="sceneId" label="场景编号" hidden={['add', 'copy'].includes(mode)}>
                    <YRInput disabled />
                  </YRForm.Item>
                  <YRForm.Item
                    name="sceneCode"
                    label="场景码"
                    rules={[
                      { required: true },
                      {
                        max: 128,
                        message: '长度不能超过128字节!'
                      }
                    ]}
                  >
                    <YRInput />
                  </YRForm.Item>
                  <YRForm.Item
                    name="sceneName"
                    label="场景名称"
                    rules={[
                      { required: true },
                      {
                        max: 128,
                        message: '长度不能超过128字节!'
                      }
                    ]}
                  >
                    <YRInput />
                  </YRForm.Item>
                  <YRForm.Item
                    name="sceneDescription"
                    label="场景描述"
                    rules={[
                      {
                        max: 1000,
                        message: '长度不能超过1000字节!'
                      }
                    ]}
                    span={24}
                  >
                    <YRTextAreaPlus maxLength={1000} />
                  </YRForm.Item>
                </YRForm.Row>
              </YRClassificationLayout>
              <YRClassificationLayout title="规则配置">
                <RuleConfig
                  ref={ruleConfigRef}
                  form={form}
                  activeNode={activeNode}
                  stageList={stageList}
                  setStageList={setStageList}
                  tacheList={tacheList}
                  setTacheList={setTacheList}
                />
              </YRClassificationLayout>
            </YRClassificationLayout.Space>
          </YRForm>
        </YRFlexPageLayout.Main>
      </YRFlexPageLayout>
    </YRFullPageLayout>
  );
};

export default Detail;
