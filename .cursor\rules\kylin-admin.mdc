---
description: 
globs: 
alwaysApply: true
---

## 项目概述

### 基本信息
- **项目名称**: kylin-admin (后台配置系统)
- **项目类型**: 微前端子应用
- **技术栈**: React 16.14 + ice.js 2.6 + TypeScript + Ant Design 4.x
- **组件库**: yrantd 4.2.99 + yr-loan-antd 1.1.2 + @yr/pro-layout
- **构建工具**: ice.js + webpack 5
- **包管理器**: yarn
- **端口**: 8111

### 架构特点
- 基于 ice.js 的微前端架构，支持独立运行和嵌入基座
- 使用 @ice/stark 实现微前端集成
- 采用 @ice/store 进行状态管理
- 支持权限控制和路由守卫

## 编码规范

### 1. 文件命名规范
- **组件文件**: PascalCase，如 `UserManage.tsx`
- **工具文件**: camelCase，如 `utils.ts`
- **样式文件**: kebab-case，如 `index.module.less`
- **常量文件**: camelCase，如 `common.ts`
- **路由文件**: kebab-case，如 `user-manage`

### 2. 目录结构规范
```
src/
├── components/          # 公共组件
├── pages/              # 页面组件
├── layouts/            # 布局组件
├── routes/             # 路由配置
│   ├── full/          # 全屏路由
│   └── part/          # 菜单路由
├── services/          # API 服务
├── models/            # 状态管理
├── utils/             # 工具函数
├── constant/          # 常量定义
└── assets/            # 静态资源
```

### 3. 组件开发规范

#### React 组件
```typescript
// 使用函数组件 + Hooks
import React, { useState, useEffect } from 'react';
import { YRButton, YRTable } from 'yrantd';

interface Props {
  title: string;
  data: any[];
}

const ComponentName: React.FC<Props> = ({ title, data }) => {
  const [loading, setLoading] = useState(false);
  
  return (
    <div>
      {/* 组件内容 */}
    </div>
  );
};

export default ComponentName;
```

#### 样式规范
- 优先使用 CSS Modules: `import styles from './index.module.less';`
- 引用公共样式: `@import './common.less';`
- 使用 yrantd 提供的样式变量和 mixins

### 4. 导入规范
```typescript
// 1. React 相关
import React from 'react';
import { useState, useEffect } from 'react';

// 2. 第三方库
import { useSetState } from 'ahooks';
import moment from 'moment';

// 3. 组件库 (按优先级)
import { YRButton, YRTable, YRForm } from 'yrantd';
import { Button, Table } from 'antd';

// 4. 内部模块
import { router, checkAuth } from '@/utils/utils';
import store from '@/store';

// 5. 类型定义
import type { ProSettings } from '@yr/pro-layout/lib/defaultSettings';

// 6. 样式文件
import styles from './index.module.less';
```

## 技术栈使用规范

### 1. 组件库优先级
1. **yrantd**: 优先使用，公司定制组件库
2. **yr-loan-antd**: 业务组件库
3. **@yr/pro-layout**: 布局组件
4. **antd**: 基础组件库，作为补充

### 2. 状态管理
- 使用 `@ice/store` 进行全局状态管理
- 页面级状态使用 `useState` 或 `useSetState`
- 复杂状态逻辑使用 `useReducer`

### 3. 路由管理
- 全屏页面放在 `routes/full/`
- 菜单页面放在 `routes/part/`
- 使用 `lazy` 进行路由懒加载
- 路由权限通过 `buildRoutes` 处理

### 4. API 调用
```typescript
// 使用 tryCatch 包装异步请求
import { tryCatch } from '@/utils/utils';

const fetchData = tryCatch(async (params) => {
  return await api.getData(params);
});

// 使用
const [error, data] = await fetchData({ id: 1 });
if (error) {
  // 处理错误
  return;
}
// 使用 data
```

## 开发约定

### 1. 权限控制
- 使用 `checkAuth(uri)` 检查权限
- 组件级权限使用 `YRAuthCheck`
- 路由级权限在路由配置中声明

### 2. 微前端集成
- 使用 `isInIcestark()` 判断是否在基座中运行
- 嵌入基座时不使用自身布局和主题
- 通过 `@ice/stark-data` 进行应用间通信

### 3. 主题配置
- 主题配置在 `defaultSettings.ts` 中
- 使用 `YRConfigProvider` 包装应用
- 支持动态主题切换

### 4. 工具函数
- 公共工具函数放在 `utils/utils.ts`
- 业务相关工具函数按模块分类
- 使用 TypeScript 提供类型支持

## 代码质量

### 1. ESLint 配置
- 使用 `@yr/spec` 提供的 ESLint 配置
- 支持 React + TypeScript 规则
- 配置全局变量: `envType`, `CONST`, `SCENES` 等

### 2. 类型定义
- 优先使用 TypeScript
- 为组件 Props 定义接口
- 为 API 响应定义类型
- 使用泛型提高代码复用性

### 3. 性能优化
- 使用 `React.memo` 优化组件渲染
- 使用 `useCallback` 和 `useMemo` 优化计算
- 路由懒加载减少初始包大小

## 常用模式

### 1. 表格页面
```typescript
const TablePage: React.FC = () => {
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10 });

  const columns = [
    // 表格列定义
  ];

  const fetchData = async () => {
    setLoading(true);
    // 获取数据逻辑
    setLoading(false);
  };

  return (
    <YRTable
      dataSource={dataSource}
      columns={columns}
      loading={loading}
      pagination={pagination}
    />
  );
};
```

### 2. 表单页面
```typescript
const FormPage: React.FC = () => {
  const [form] = YRForm.useForm();

  const onFinish = async (values) => {
    // 提交逻辑
  };

  return (
    <YRForm form={form} onFinish={onFinish}>
      {/* 表单项 */}
    </YRForm>
  );
};
```

### 3. 弹窗组件
```typescript
const ModalComponent: React.FC<Props> = ({ visible, onCancel, onOk }) => {
  return (
    <YRModal
      title="标题"
      open={visible}
      onCancel={onCancel}
      onOk={onOk}
    >
      {/* 弹窗内容 */}
    </YRModal>
  );
};
```

## 注意事项

### 1. 兼容性
- 支持 IE11+ 和现代浏览器
- 使用 Babel 进行代码转换
- 注意 CSS 兼容性

### 2. 安全性
- 使用 `encryptPassword` 加密敏感信息
- 支持 MD5 和国密 SM2 加密
- 权限校验必须在前后端同时进行

### 3. 国际化
- 使用 `zhCN` 作为默认语言
- 组件库已配置中文语言包

### 4. 调试工具
- 开发环境集成 Stagewise Toolbar
- 支持 React DevTools
- 使用 source map 进行调试

## 禁止事项

1. **不要直接修改** `node_modules` 中的文件
2. **不要使用** jQuery 或其他 DOM 操作库
3. **不要在组件中** 直接操作 DOM
4. **不要忽略** TypeScript 类型检查
5. **不要提交** 包含敏感信息的代码
6. **不要使用** 过时的生命周期方法
7. **不要在循环中** 使用 Hooks

## 环境配置

### 1. 开发环境
- Node.js: 建议使用 16.x 或 18.x (当前支持 >=8.0.0)
- yarn: 1.22.22+
- 浏览器: Chrome 49+, IE 11+

### 2. 启动命令
```bash
# 开发模式 (带 mock)
yarn start

# 开发模式 (不带 mock)
yarn run start:no-mock

# 权限 SQL 生成模式
yarn run start:permit-sql

# 构建
yarn build
yarn build:product
yarn build:project
```

### 3. 代码检查
```bash
# ESLint 检查
yarn eslint

# ESLint 自动修复
yarn eslint:fix

# 样式检查
yarn stylelint

# 完整检查
yarn lint
```

## API 开发规范

### 1. 服务层结构
```typescript
// services/moduleName.ts
import { request } from '@yr/util';

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message: string;
}

export interface UserInfo {
  id: string;
  name: string;
  // 其他字段
}

// 获取用户列表
export const getUserList = (params: any): Promise<ApiResponse<UserInfo[]>> => {
  return request.get('/api/user/list', { params });
};

// 创建用户
export const createUser = (data: Partial<UserInfo>): Promise<ApiResponse> => {
  return request.post('/api/user/create', data);
};
```

### 2. 错误处理
```typescript
import { YRMessage } from 'yrantd';
import { tryCatch } from '@/utils/utils';

const handleApiCall = tryCatch(async (params) => {
  return await api.getData(params);
});

const fetchData = async () => {
  const [error, result] = await handleApiCall(params);

  if (error) {
    YRMessage.error('获取数据失败');
    return;
  }

  if (result.success) {
    setData(result.data);
  } else {
    YRMessage.error(result.message || '操作失败');
  }
};
```

## 权限系统

### 1. 权限配置
```typescript
// permit/index.ts
import { YRLoanPermit } from 'yr-loan-antd';

const { buildPermit } = YRLoanPermit;

// 模块权限定义
export const M01 = buildPermit('M01', packageInfo, {
  interfaces: {
    'user.list': '用户列表查询',
    'user.create': '用户创建',
    'user.update': '用户更新',
    'user.delete': '用户删除'
  }
});
```

### 2. 权限使用
```typescript
import { checkAuth } from '@/utils/utils';
import { YRAuthCheck } from 'yrantd';

// 函数式权限检查
const hasPermission = checkAuth('user.create');

// 组件式权限检查
<YRAuthCheck auth="user.create">
  <YRButton type="primary">新增用户</YRButton>
</YRAuthCheck>
```

## 样式开发规范

### 1. CSS Modules
```less
// index.module.less
@import '@/common.less';

.container {
  padding: 16px;
  background: @white;

  .header {
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 500;
  }

  .content {
    .flex-center();
  }
}
```

### 2. 主题变量使用
```less
// 使用 yrantd 提供的变量
@import (reference) 'yrantd/lib/style/yr-style/color.less';
@import (reference) 'yrantd/lib/style/yr-style/layout.less';

.custom-button {
  background: @primary-color;
  border-radius: @border-radius-base;
  padding: @padding-sm @padding-md;
}
```

## 测试规范

### 1. 单元测试
```typescript
// __tests__/utils.test.ts
import { md5Crypto, isObjectEmpty } from '@/utils/utils';

describe('utils', () => {
  test('md5Crypto should encrypt password correctly', () => {
    const password = '123456';
    const encrypted = md5Crypto(password);
    expect(encrypted).toBe('e10adc3949ba59abbe56e057f20f883e');
  });

  test('isObjectEmpty should return true for empty object', () => {
    expect(isObjectEmpty({})).toBe(true);
    expect(isObjectEmpty({ a: 1 })).toBe(false);
  });
});
```

### 2. 组件测试
```typescript
// __tests__/components/UserTable.test.tsx
import React from 'react';
import { render, screen } from '@testing-library/react';
import UserTable from '@/components/UserTable';

describe('UserTable', () => {
  test('renders user table correctly', () => {
    const mockData = [
      { id: '1', name: '张三', email: '<EMAIL>' }
    ];

    render(<UserTable dataSource={mockData} />);

    expect(screen.getByText('张三')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });
});
```

## 部署配置

### 1. 环境变量
```javascript
// build.config.js 中的环境配置
const envType = process.env.envType || 'LOCAL';
const publicPath = process.env.publicPath || '/child/kylin-admin/';

// 不同环境的配置
const envConfig = {
  LOCAL: {
    apiUrl: 'http://localhost:8080',
    publicPath: 'kylin-admin'
  },
  PROJECT: {
    apiUrl: 'https://project-api.example.com',
    publicPath: '/child/kylin-admin/'
  },
  PRODUCT: {
    apiUrl: 'https://api.example.com',
    publicPath: '/child/kylin-admin/'
  }
};
```

### 2. 构建优化
```javascript
// build.config.js
module.exports = {
  // 代码分割
  splitChunks: {
    chunks: 'all',
    cacheGroups: {
      vendor: {
        test: /[\\/]node_modules[\\/]/,
        name: 'vendors',
        chunks: 'all'
      }
    }
  },

  // 压缩配置
  optimization: {
    minimize: true,
    minimizer: ['...']
  },

  // 外部依赖
  externals: {
    'react': 'React',
    'react-dom': 'ReactDOM'
  }
};
```

## 更新日志

- 2024-01-XX: 初始版本创建
- 项目版本: 4.0.0
- 基于 ice.js 2.6.10 和 React 16.14.0

## 参考文档

- [ice.js 官方文档](https://ice.work/)
- [yrantd 组件库文档](http://yr-components-v4.loan3-0-base-dev.svc.cluster.local:8080/changelog-cn)
- [微前端相关文档](http://yr-document.loan3-0-base-dev.svc.cluster.local:8080/)
- [Ant Design 文档](https://ant.design/)
- [React Hooks 文档](https://reactjs.org/docs/hooks-intro.html)

