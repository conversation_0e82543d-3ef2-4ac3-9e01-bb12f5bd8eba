/**
 * @Author: wangyw26123
 * @Description: 角色管理
 * @Date: Created in 2022-12-09 16:46:35
 * @Modifed By:
 */

import React, { useState } from 'react';
import { isInIcestark } from '@ice/stark-app';
import type { FormItemListProps, YRTableProps } from 'yrantd';
import { YRButton, YRForm, YRIndexPageLayout, YRLink, YRMessage, YRModal, YRTable } from 'yrantd';
import { queryRoleDetail } from '@/services/setting';
import { EnumOperType } from '@/constant/role';
import { EnumMsg } from '@/constant/common';
import { checkAuth, openNewTab } from '@/utils/utils';
import RoleLogModal from './components/RoleLogModal';
import UserListModal from './components/UserListModal';
import Dict from '../../Organ/mock/getDict';
import { M010301 } from '@permit/role';
import { EnumGuardRoleTypeColor, EnumRoleBizLineColor, EnumRoleStatusColor } from '../../../constant/StyleConst';
import { ValueTypeEnum } from 'yrantd/lib/yr-table/declare';
import { useAntdTable } from 'ahooks';

const { queryRolePage, changeRoleStatus } = M010301.interfaces;
const { delRole } = M010301.E03.interfaces;

const RoleManage = () => {
  const [form] = YRForm.useForm();
  // 查询表格数据
  const { tableProps, run, refresh } = useAntdTable(
    (p) =>
      queryRolePage({ ...p, pageNum: p?.current }).then((res) => {
        return { list: res?.data?.list, total: res?.data?.total };
      }),
    {
      defaultParams: [
        {
          pageSize: 30,
          current: 1
        }
      ]
    }
  );

  const [openRoleLog, setOpenRoleLog] = useState<boolean>(false);
  const [currentRoleItem, setCurrentRoleItem] = useState<{ roleId: string }>({
    roleId: ''
  });
  const [userListVisible, setUserListVisible] = useState<boolean>(false);

  const formItemList: FormItemListProps[] = [
    {
      placeholder: '角色名称',
      key: 'roleName',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '角色编号',
      key: 'roleId',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '角色级别',
      key: 'roleLevel',
      type: 'dictSelect',
      dictkey: 'EnumRoleLevel',
      position: 'show'
    },
    {
      placeholder: '状态',
      key: 'roleStatus',
      type: 'dictSelect',
      dictkey: 'EnumGuardRoleStatus',
      position: 'show'
    },
    {
      placeholder: '归属条线',
      key: 'bizLine',
      type: 'dictSelect',
      dictkey: 'EnumRoleBizLine',
      position: 'show'
    }
  ];

  // 变更角色状态
  const onChangeRoleStatus = async (val, status, msg) => {
    if ([M010301.E04, M010301.E05, M010301.E06].every((permit) => !checkAuth(permit))) return;
    let message = msg;

    if (status === EnumOperType.DISABLE) {
      const result = await queryRoleDetail({ roleId: val.roleId });
      if (result.success) {
        const userDtoList = result.data?.userDtoList || [];
        if (userDtoList.length) {
          message = '角色正在使用中，是否停用？';
        }
      }
    }

    YRModal.confirm({
      title: message,
      onOk() {
        changeRoleStatus({
          roleId: val.roleId,
          roleStatus: status
        }).then((res) => {
          if (res.success) {
            YRMessage.success(EnumMsg.operator);
            refresh();
          }
        });
      }
    });
  };

  // 删除角色
  const onDelete = (row: any) => {
    if (!checkAuth(M010301.E03)) return;
    YRModal.confirm({
      title: '确认删除此角色吗, 删除后不可恢复',
      onOk() {
        delRole({ roleId: row.roleId }).then((res) => {
          if (res.success) {
            YRMessage.success(EnumMsg.delete);
            refresh();
          }
        });
      }
    });
  };

  // 添加成员
  const addUser = (row?: any) => {
    setCurrentRoleItem(row);
    setUserListVisible(true);
  };
  // 新增或修改角色
  const onAdd = (mode: string, row?: any) => {
    if (checkAuth(M010301.E01) || checkAuth(M010301.E02) || checkAuth(M010301.E09)) {
      openNewTab({
        pathname: '/setting/role/role-manage/edit',
        query: {
          roleId: row?.roleId,
          mode,
          acrossTenantFlag: row?.acrossTenantFlag
        }
      });
    }
  };

  // 表格列
  const columns: YRTableProps['columns'] = [
    {
      title: '角色编号',
      dataIndex: 'roleId',
      valueType: ValueTypeEnum.id,
      to: (value) => ({
        pathname: '/setting/role/role-manage/edit',
        query: { mode: 'readPretty', id: value },
        needBasename: isInIcestark()
      }),
      check: M010301.E09
    },
    {
      title: '角色名称',
      dataIndex: 'roleName',
      width: 220
    },
    {
      title: '角色级别',
      dataIndex: 'roleLevel',
      valueType: ValueTypeEnum.tag,
      color: EnumGuardRoleTypeColor,
      dictkey: 'EnumRoleLevel'
    },
    {
      title: '角色类型',
      dataIndex: 'roleType',
      valueType: ValueTypeEnum.tag,
      color: EnumGuardRoleTypeColor,
      dictkey: 'EnumGuardRoleType'
    },
    {
      title: '角色所属条线',
      dataIndex: 'bizLine',
      valueType: ValueTypeEnum.tag,
      color: EnumRoleBizLineColor,
      dictkey: 'EnumRoleBizLine'
    },
    {
      title: '角色描述',
      dataIndex: 'roleDesc',
      width: 220
    },
    {
      title: '角色状态',
      dataIndex: 'roleStatus',
      valueType: ValueTypeEnum.status,
      dictkey: 'EnumGuardRoleStatus',
      color: EnumRoleStatusColor,
      fixed: 'right'
    },
    {
      title: '操作',
      dataIndex: 'operator',
      valueType: ValueTypeEnum.operation,
      fixed: 'right',
      render: (_, row) => (
        <YRButton.Overflow>
          {row.roleStatus === EnumOperType.NOT_ENABLE && (
            <YRLink
              type="primary"
              onClick={() => onChangeRoleStatus(row, EnumOperType.ENABLE, '确认启用吗?')}
              check={M010301.E04}
            >
              启用
            </YRLink>
          )}
          {row.roleStatus === EnumOperType.ENABLE && (
            <YRLink
              type="primary"
              onClick={() => onChangeRoleStatus(row, EnumOperType.DISABLE, '确认停用吗?')}
              check={M010301.E05}
            >
              停用
            </YRLink>
          )}
          {row.roleStatus === EnumOperType.DISABLE && (
            <YRLink
              type="primary"
              onClick={() => onChangeRoleStatus(row, EnumOperType.ENABLE, '确认恢复吗?')}
              check={M010301.E06}
            >
              恢复
            </YRLink>
          )}
          {row.roleStatus === EnumOperType.ENABLE && (
            <YRLink type="primary" onClick={() => addUser(row)} check={M010301.E07}>
              添加成员
            </YRLink>
          )}
          {row.acrossTenantFlag !== 'Y' && (
            <YRLink type="primary" onClick={() => onAdd('edit', row)} check={M010301.E02}>
              修改
            </YRLink>
          )}
          {row.roleStatus !== EnumOperType.ENABLE && (
            <YRLink type="primary" onClick={() => onDelete(row)} check={M010301.E02}>
              删除
            </YRLink>
          )}
        </YRButton.Overflow>
      )
    }
  ];

  const operationRender = (
    <YRButton.Space>
      <YRButton key="add" type="primary" check={M010301.E01} onClick={() => onAdd('add')}>
        新增
      </YRButton>
      <YRButton key="history" check={M010301.E08} onClick={() => setOpenRoleLog(true)}>
        历史记录
      </YRButton>
    </YRButton.Space>
  );

  return (
    <YRIndexPageLayout>
      <YRTable
        business="home"
        form={form}
        rowKey="roleId"
        columns={columns}
        handleSearch={() => {
          const vlaues = form.getFieldsValue() as object;
          run({ ...vlaues, current: 1, pageSize: 30 });
        }}
        formItemList={formItemList}
        operationRender={operationRender}
        onRow={(row) => {
          return {
            onDoubleClick: () => onAdd('readPretty', row)
          };
        }}
        {...tableProps}
      />
      <RoleLogModal visible={openRoleLog} onCancel={() => setOpenRoleLog(false)} />
      <UserListModal
        roleId={currentRoleItem.roleId}
        visible={userListVisible}
        onOk={() => {
          setUserListVisible(false);
          refresh();
        }}
        onCancel={() => setUserListVisible(false)}
      />
    </YRIndexPageLayout>
  );
};

export default Dict(['EnumRoleLevel', 'EnumGuardRoleType', 'EnumGuardRoleStatus', 'EnumRoleBizLine'])(RoleManage);
