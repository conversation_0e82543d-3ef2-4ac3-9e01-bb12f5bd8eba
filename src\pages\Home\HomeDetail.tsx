import React from 'react';
import { Link } from 'ice';
import { YRFullPageLayout, YRButton } from 'yrantd';
import { router } from '@/utils/utils';

const HomeDeatil = () => {
  const left = {
    title: '详情',
    goBack: () => router.goBack(),
    extInfo: [{ key: '001', value: '消费贷' }],
    status: 'success',
    statusText: '显示成功'
  };

  const rightCom = <YRButton type="primary">按钮</YRButton>;

  return (
    <YRFullPageLayout span={24} left={left} right={rightCom}>
      我是详情{' '}
      <YRButton type="primary">
        <Link to="/">返回</Link>
      </YRButton>
    </YRFullPageLayout>
  );
};

export default HomeDeatil;
