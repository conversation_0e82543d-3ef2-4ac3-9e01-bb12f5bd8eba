/*
 * @Description: file content
 * @Author: zy
 * @Date: 2020-07-20 17:10:13
 * @LastEditors: your name
 * @LastEditTime: 2020-11-20 13:40:12
 */

import React, { useState, useEffect } from 'react';
import { YRForm, YRButton, YRInput, YRSelect, YRRadio, YRModal, YRRow, YRCol, YRInputNumber, YRSteps } from 'yrantd';
import {
  threeComPathName,
  threeClientType,
  returnType,
  isDependentType,
  isScreatType,
  isAsyncType
} from '@/utils/data-dictionary';
import RegexRules from '@/utils/regex';
import RequestParams from '../RequestParams';
import ResponseParams from '../ResponseParams';
import MockParams from '../MockParams';
import './index.less';

const FormItem = YRForm.Item;
const { Step } = YRSteps;
const { Option } = YRSelect;
const RadioGroup = YRRadio.Group;
const { TextArea } = YRInput;

/**
 * 新增编辑
 */
const ModalAdd = (props) => {
  const { handleCancel, visible, initValues, type, supplierList, treeList, sysFieldList, interfaceList } = props;
  const [form] = YRForm.useForm();
  const isAdd = type === 'add';
  const [current, setCurrent] = useState(0);
  const [newInterface, setNewInterface] = useState({});
  const [requestParams, setRequestParams] = useState([]);
  const [requestHeaders, setRequestHeaders] = useState([]);
  const [accessParams, setAccessParams] = useState([]);
  const [sdkParams, setSdkParams] = useState([]);
  const [respFieldConfs, setRespFieldConfs] = useState([]);
  const [cacheKey, setCacheKey] = useState('');
  const checkedClientType = YRForm.useWatch('clientType', form) || 'http';
  const checkedAsyncType = YRForm.useWatch('asyncSwitch', form) || 0;
  const checkedDependType = YRForm.useWatch('isDependent', form) || 0;

  useEffect(() => {
    if (!visible) {
      setCurrent(0);
      setNewInterface({ ...initValues });
      setRequestParams([]);
      setRequestHeaders([]);
      setSdkParams([]);
      setAccessParams([]);
      setRespFieldConfs([]);
    }
  }, [visible]);

  // 提交方法
  const submit = () => {
    const { handleOk } = props;
    let params = {};
    if (type === 'add') {
      params = {
        ...newInterface,
        cacheKey,
        requestParams,
        requestHeaders,
        accessParams,
        sdkParams: sdkParams.map((item) => {
          return {
            sdkParam: item.paramName,
            sdkParamName: item.paramNameCn,
            val: item.val,
            valWay: item.valWay,
            isRequired: item.isRequired,
            isInParam: item.isInParam
          };
        }),
        respFieldConfs: respFieldConfs.map((item) => {
          const { confType, ...rest } = item;
          return {
            ...rest,
            type: confType === 1 ? 1 : 0,
            confType: confType === 2 ? 2 : 1
          };
        })
      };
      // const allRequest = [
      //   ...params.requestParams,
      //   ...params.requestHeaders,
      //   ...params.sdkParams
      // ];
      // if (allRequest.length > 0 && !cacheKey) {
      //   message.error("请求参数中至少有一个是缓存参数");
      //   return;
      // }
      const found = supplierList.find((item) => {
        return item.comCode === newInterface.comCode;
      });
      params.comName = found.comName;
      const newParams = { threeDataConf: params };
      handleOk(type, newParams);
    } else {
      form.validateFields().then((values) => {
        params = values;
        const found = supplierList.find((item) => {
          return item.comCode === values.comCode;
        });
        params.comName = found.comName;
        handleOk(type, params);
      });
    }
  };

  function saveBaseInfo() {
    form.validateFields().then((values) => {
      setNewInterface((prevInter) => {
        return { ...initValues, ...prevInter, ...values };
      });
      setCurrent((prevCurrent) => prevCurrent + 1);
    });
  }

  function next() {
    if (current === 0) {
      saveBaseInfo();
    } else {
      setCurrent((prevCurrent) => prevCurrent + 1);
    }
  }

  function prev() {
    setCurrent((prevCurrent) => prevCurrent - 1);
  }

  const getBaseContent = () => {
    return (
      <YRForm layout="vertical" form={form}>
        <FormItem label="是否异步接口" name={'asyncSwitch'} initialValue={initValues.asyncSwitch || 0} hidden>
          <RadioGroup className={'lh'}>
            {Object.keys(isAsyncType).map((value) => {
              return (
                <YRRadio value={Number(value)} key={value}>
                  {isAsyncType[value]}
                </YRRadio>
              );
            })}
          </RadioGroup>
        </FormItem>
        <FormItem label="是否依赖" name={'isDependent'} initialValue={initValues?.isDependent || 0} hidden>
          <RadioGroup className="lh">
            {Object.keys(isDependentType).map((value) => {
              return (
                <YRRadio value={Number(value)} key={value}>
                  {isDependentType[value]}
                </YRRadio>
              );
            })}
          </RadioGroup>
        </FormItem>
        <FormItem label="是否加密" name={'encryptSwitch'} initialValue={initValues?.encryptSwitch || 0} hidden>
          <RadioGroup className={'lh'}>
            {Object.keys(isScreatType).map((value) => {
              return (
                <YRRadio value={Number(value)} key={value}>
                  {isScreatType[value]}
                </YRRadio>
              );
            })}
          </RadioGroup>
        </FormItem>
        <YRRow gutter={32}>
          <YRCol span={8}>
            <FormItem
              label="厂商名称"
              name={'comCode'}
              initialValue={initValues?.comCode || ''}
              rules={[{ required: true, message: '请输入厂商名称!' }]}
            >
              <YRSelect disabled>
                {supplierList.map((item) => {
                  return (
                    <Option value={item.comCode} key={item.comCode}>
                      {item.comName}
                    </Option>
                  );
                })}
              </YRSelect>
            </FormItem>
          </YRCol>
          <YRCol span={8}>
            <FormItem
              label="接口名称"
              name={'comPathName'}
              initialValue={initValues?.comPathName || ''}
              rules={[
                { required: true, message: '请输入接口名称!' },
                { max: 20, message: '长度不能超过20！' },
                {
                  message: '不能有空格！',
                  pattern: /^[\S]+$/
                }
              ]}
            >
              <YRInput placeholder="请输入" disabled={!isAdd} />
            </FormItem>
          </YRCol>
          <YRCol span={8}>
            <FormItem
              label="接口编号"
              name={'comPathCode'}
              initialValue={initValues?.comPathCode || ''}
              rules={[
                {
                  required: true,
                  message: '请输入英文字母、数字或下划线!',
                  pattern: RegexRules.code1
                },
                { max: 20, message: '长度不能超过20！' }
              ]}
            >
              <YRInput placeholder="请输入" disabled={!isAdd} />
            </FormItem>
          </YRCol>
          <YRCol span={8}>
            <FormItem
              label="访问路径"
              name={'accessPath'}
              initialValue={initValues?.accessPath || ''}
              rules={[
                { required: true, message: '请输入访问路径!' },
                {
                  message: '不能有空格！',
                  pattern: /^[\S]+$/
                }
              ]}
            >
              <YRInput placeholder="请选择" />
            </FormItem>
          </YRCol>
          <YRCol span={8}>
            <FormItem
              label="缓存时间(毫秒)"
              name={'expireTime'}
              initialValue={initValues.expireTime || 0}
              rules={[{ required: true, message: '请输入缓存时间!' }]}
            >
              <YRInputNumber min={0} style={{ width: '100%' }} />
            </FormItem>
          </YRCol>
          <YRCol span={8}>
            <FormItem label="连接超时时间(毫秒)" name={'connectTimeout'} initialValue={initValues.connectTimeout || 0}>
              <YRInputNumber min={0} style={{ width: '100%' }} />
            </FormItem>
          </YRCol>
          <YRCol span={8}>
            <FormItem label="读超时时间(毫秒)" name={'readTimeout'} initialValue={initValues.readTimeout || 0}>
              <YRInputNumber min={0} style={{ width: '100%' }} />
            </FormItem>
          </YRCol>
          <YRCol span={8}>
            <FormItem label="编码格式" name={'charset'} initialValue={initValues.charset || ''}>
              <YRInput placeholder="请输入" />
            </FormItem>
          </YRCol>
          {checkedAsyncType === 1 ? (
            <YRCol span={8}>
              <FormItem
                label="异步查证接口Code"
                name={'asyncQueryComPathCode'}
                initialValue={initValues.asyncQueryComPathCode || 0}
              >
                <YRInput />
              </FormItem>
            </YRCol>
          ) : null}
          {checkedDependType === 1 ? (
            <YRCol span={8}>
              <FormItem
                label="依赖接口"
                name={'comPathCodeR'}
                initialValue={initValues?.comPathCodeR || ''}
                rules={[
                  {
                    required: true,
                    message: '请选择唯一依赖标识!'
                  }
                ]}
              >
                <YRSelect showSearch>
                  {interfaceList.map((item) => {
                    return (
                      <Option value={item.comPathCode} key={item.comPathCode}>
                        {item.comPathName}
                      </Option>
                    );
                  })}
                </YRSelect>
              </FormItem>
            </YRCol>
          ) : null}
          <YRCol span={8}>
            <FormItem
              label="接入方式"
              name={'clientType'}
              initialValue={initValues?.clientType || 'http'}
              rules={[{ required: true, message: '请选择接入方式!' }]}
            >
              <RadioGroup disabled={!isAdd} className={'lh'}>
                {Object.keys(threeClientType).map((value) => {
                  return (
                    <YRRadio value={value} key={value}>
                      {threeClientType[value]}
                    </YRRadio>
                  );
                })}
              </RadioGroup>
            </FormItem>
          </YRCol>
          {checkedClientType === 'http' && (
            <YRCol span={8}>
              <FormItem
                label="请求方式"
                name={'requestMethod'}
                initialValue={initValues?.requestMethod || 'POST'}
                rules={[{ required: true, message: '请选择请求方式!' }]}
              >
                <RadioGroup className={'lh'}>
                  {Object.keys(threeComPathName).map((value) => {
                    return (
                      <YRRadio value={value} key={value}>
                        {threeComPathName[value]}
                      </YRRadio>
                    );
                  })}
                </RadioGroup>
              </FormItem>
            </YRCol>
          )}
          <YRCol span={8}>
            <FormItem label="返回类型" name={'returnType'} initialValue={initValues?.returnType || 'json'}>
              <YRSelect>
                {Object.keys(returnType).map((val) => {
                  return (
                    <Option value={val} key={val}>
                      {returnType[val]}
                    </Option>
                  );
                })}
              </YRSelect>
            </FormItem>
          </YRCol>
          <YRCol span={24}>
            <FormItem label="请求报文体" name={'requestBody'} initialValue={initValues?.requestBody || ''}>
              <TextArea rows={4} />
            </FormItem>
          </YRCol>
        </YRRow>
      </YRForm>
    );
  };

  function saveMockFun(value) {
    setNewInterface({ ...newInterface, mockData: value });
  }
  function deleteHadSDKData(value, key) {
    const index = accessParams.findIndex((item) => {
      return item[key] === value;
    });
    if (index > -1) {
      accessParams.splice(index, 1);
    }
    const index1 = sdkParams.findIndex((item) => {
      return item[key] === value;
    });
    if (index1 > -1) {
      sdkParams.splice(index1, 1);
    }
  }
  function deleteHadHttpData(value, key) {
    const index = requestParams.findIndex((item) => {
      return item[key] === value;
    });
    if (index > -1) {
      requestParams.splice(index, 1);
    }
    const index1 = requestHeaders.findIndex((item) => {
      return item[key] === value;
    });
    if (index1 > -1) {
      requestHeaders.splice(index1, 1);
    }
    const index2 = accessParams.findIndex((item) => {
      return item[key] === value;
    });
    if (index2 > -1) {
      accessParams.splice(index2, 1);
    }
  }
  function calcuModify(prevParams, values, key) {
    const newParams = [...prevParams];
    const index = newParams.findIndex((item) => {
      return item[key] === values[key];
    });
    if (index > -1) {
      newParams.splice(index, 1, values);
    }
    return newParams;
  }

  // 选择http,0(请求头)，放在requestHeaders；选择http,1(路径参数)，放在requestParams；选择sdk,放在sdkParams
  function saveRequestParams(modalType, values) {
    const { isCache, ...rest } = values;
    if (isCache) {
      setCacheKey((prevCache) => {
        let newCache = prevCache.split(',');
        newCache.push(rest.paramName);
        newCache = newCache.filter((item, i, self) => item && self.indexOf(item) === i);
        return newCache.join(',');
      });
    } else {
      setCacheKey((prevCache) => {
        const newCache = prevCache.split(',').filter((val) => {
          return val !== rest.paramName;
        });
        return newCache.join(',');
      });
    }

    if (newInterface.clientType === 'http') {
      // if (modalType === "add") {
      // 如果是编辑的话 先找到数据删掉，再添加，因为请求头，调用方等保存在不同的列表操作麻烦，不如先删除再做增加的操作
      if (modalType === 'edit') {
        deleteHadHttpData(values.paramName, 'paramName');
      }
      if (rest.type === 0) {
        setRequestHeaders((prevParams) => {
          return prevParams.concat([rest]);
        });
      } else if (rest.type === 3) {
        setAccessParams((prevParams) => {
          return prevParams.concat([rest]);
        });
      } else {
        setRequestParams((prevParams) => {
          return prevParams.concat([rest]);
        });
      }
    } else if (newInterface.clientType === 'sdk') {
      // 同 HTTP 编辑时先删掉数据再添加
      if (modalType === 'edit') {
        deleteHadSDKData(values.paramName, 'paramName');
      }
      // if (modalType === "add") {
      if (rest.type === 3) {
        setAccessParams((prevParams) => {
          return prevParams.concat([rest]);
        });
      } else {
        setSdkParams((prevParams) => {
          return prevParams.concat([rest]);
        });
      }
    }
  }

  function calcuDelete(prevParams, values, key) {
    const newParams = [...prevParams];
    const index = newParams.findIndex((item) => {
      return item[key] === values[key];
    });
    if (index > -1) {
      newParams.splice(index, 1);
    }
    return newParams;
  }

  function deleteRequestParams(values) {
    if (newInterface.clientType === 'http') {
      if (values.type === 0) {
        setRequestHeaders((prevParams) => {
          return calcuDelete(prevParams, values, 'paramName');
        });
      } else if (values.type === 3) {
        setAccessParams((prevParams) => {
          return calcuDelete(prevParams, values, 'paramName');
        });
      } else {
        setRequestParams((prevParams) => {
          return calcuDelete(prevParams, values, 'paramName');
        });
      }
    } else if (values.type === 3) {
      setAccessParams((prevParams) => {
        return calcuDelete(prevParams, values, 'paramName');
      });
    } else {
      setSdkParams((prevParams) => {
        return calcuDelete(prevParams, values, 'paramName');
      });
    }
  }

  function saveResponseParams(modalType, values) {
    if (modalType === 'add') {
      setRespFieldConfs((prevParams) => {
        return prevParams.concat([values]);
      });
    } else {
      setRespFieldConfs((prevParams) => {
        return calcuModify(prevParams, values, 'fieldName');
      });
    }
  }

  function deleteResponseParams(values) {
    setRespFieldConfs((prevParams) => {
      return calcuDelete(prevParams, values, 'fieldName');
    });
  }

  function getStepsContent() {
    let resqData;
    if (newInterface.clientType === 'http') {
      resqData = [...accessParams, ...requestParams, ...requestHeaders];
    } else {
      resqData = [...accessParams, ...sdkParams];
    }
    const steps = [
      {
        title: '基本信息',
        content: getBaseContent()
      },
      {
        title: '请求参数配置',
        content: (
          <RequestParams
            sysFieldList={sysFieldList}
            handleOk={saveRequestParams}
            handleDelete={deleteRequestParams}
            data={resqData}
            cacheKey={cacheKey}
            clientType={newInterface.clientType}
          />
        )
      },
      {
        title: '响应参数配置',
        content: (
          <ResponseParams
            data={respFieldConfs}
            treeList={treeList}
            handleOk={saveResponseParams}
            handleDelete={deleteResponseParams}
          />
        )
      }
    ];
    return (
      <div>
        <YRSteps current={current}>
          {steps.map((item) => (
            <Step key={item.title} title={item.title} />
          ))}
        </YRSteps>
        <div className={'steps-content'}>{steps[current].content}</div>
        <div className={'steps-action'}>
          <YRButton onClick={handleCancel}>取消</YRButton>
          {current > 0 && (
            <YRButton className={'space'} onClick={prev}>
              上一步
            </YRButton>
          )}
          {current < steps.length - 1 && (
            <YRButton type="primary" onClick={next} className={'space'}>
              下一步
            </YRButton>
          )}
          {current === steps.length - 1 && (
            <YRButton type="primary" onClick={submit} className={'space'}>
              保存
            </YRButton>
          )}
        </div>
      </div>
    );
  }

  const title = isAdd ? '新增接口' : '编辑接口';
  return (
    <YRModal
      title={title}
      visible={visible}
      onOk={submit}
      onCancel={handleCancel}
      maskClosable={false}
      width={900}
      okText="保存"
      footer={isAdd ? null : undefined}
    >
      {isAdd ? getStepsContent() : getBaseContent()}
    </YRModal>
  );
};
export default ModalAdd;
