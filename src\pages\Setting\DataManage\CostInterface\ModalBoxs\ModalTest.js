/*
 * @Description: file content
 * @Author: zy
 * @Date: 2020-07-20 17:10:13
 * @LastEditors: your name
 * @LastEditTime: 2020-10-27 17:11:47
 */

import React, { Fragment, useState, useEffect } from 'react';
import { YRForm, YRButton, YRInput, YRModal, YRSpin, YRSteps } from 'yrantd';
import './index.less';
import { threeConfTextGet, threeConfText } from '@/services/setting';

const FormItem = YRForm.Item;
const { Step } = YRSteps;
const { TextArea } = YRInput;

/**
 * 测试
 */
const ModalTest = (props) => {
  const [form] = YRForm.useForm();
  const { handleCancel, visible, initValues, loading = false, dispatch } = props;
  const [current, setCurrent] = useState(0);
  const [testResult, setTestResult] = useState({});
  const [interfaceDetail, setInterfaceDetail] = useState({});
  const [testParams, setTestParams] = useState({});
  const { getFieldDecorator } = form;

  useEffect(() => {
    if (initValues && initValues.comPathCode) {
      threeConfTextGet({ comPathCode: initValues.comPathCode }).then((res) => {
        if (res?.data) {
          setInterfaceDetail(res?.data?.returnData);
        }
      });
    }
  }, [initValues]);

  // 提交方法
  const test = () => {
    form.validateFields().then((values) => {
      const params = { params: values };
      if (initValues) {
        params.comPathCode = initValues.comPathCode;
      }
      setTestParams(params.params);
      threeConfText({ ...params }).then((res) => {
        if (res?.data) {
          form.setFieldsValue(testParams);
          setCurrent((prevCurrent) => prevCurrent + 1);
          setTestResult(res?.data?.returnData);
        }
      });
    });
  };

  function prev() {
    setCurrent((prevCurrent) => prevCurrent - 1);
  }

  function getHttpItems(values) {
    return values.map((item) => {
      const paramNameCn =
        item.paramNameCn && typeof item.paramNameCn === 'string' ? item.paramNameCn.replace(/(\([a-zA-Z]*\))/, '') : '';
      return (
        <FormItem
          label={paramNameCn}
          key={item.paramName}
          name={`${item.paramName}`}
          initialValue={testParams[item.paramName]}
        >
          <YRInput />
        </FormItem>
      );
    });
  }

  function getSdkItems(values) {
    return (
      values &&
      values.map((item) => {
        const sdkParamName =
          item.sdkParamName && typeof item.sdkParamName === 'string'
            ? item.sdkParamName.replace(/(\([a-zA-Z]*\))/, '')
            : '';
        return (
          <FormItem
            label={sdkParamName}
            key={item.sdkParam}
            name={`${item.sdkParam}`}
            initialValue={testParams[item.sdkParam]}
          >
            <YRInput />
          </FormItem>
        );
      })
    );
  }

  function getTestContent() {
    const { params = [], sdkParams = [], accessPath } = interfaceDetail;
    return (
      <YRSpin tip="测试中..." spinning={loading}>
        <YRForm layout="vertical" form={form}>
          <FormItem label="接口" name="accessPath" initialValue={accessPath || ''}>
            <TextArea />
          </FormItem>
          {initValues.clientType === 'http' ? getHttpItems(params) : getSdkItems(sdkParams)}
        </YRForm>
      </YRSpin>
    );
  }

  function getTestResult() {
    return (
      <div className="test-result">
        <div className="title">测试结果</div>
        <div className="result">{JSON.stringify(testResult)}</div>
      </div>
    );
  }

  function getStepsContent() {
    const steps = [
      {
        title: '填写测试信息',
        content: getTestContent()
      },
      {
        title: '测试结束',
        content: getTestResult()
      }
    ];
    return (
      <div>
        <YRSteps current={current} size={window.size}>
          {steps.map((item) => (
            <Step key={item.title} title={item.title} />
          ))}
        </YRSteps>
        <div className="steps-content">{steps[current].content}</div>
        <div className="steps-action">
          {current === 0 ? (
            <Fragment>
              <YRButton onClick={handleCancel}>取消</YRButton>
              <YRButton type="primary" onClick={test} className="space">
                测试
              </YRButton>
            </Fragment>
          ) : (
            <Fragment>
              <YRButton className="space" onClick={prev}>
                上一步
              </YRButton>
              <YRButton type="primary" onClick={handleCancel} className="space">
                完成
              </YRButton>
            </Fragment>
          )}
        </div>
      </div>
    );
  }

  return (
    <YRModal title="测试" visible={visible} onCancel={handleCancel} maskClosable={false} width={600} footer={null}>
      {initValues && getStepsContent()}
    </YRModal>
  );
};

export default ModalTest;
