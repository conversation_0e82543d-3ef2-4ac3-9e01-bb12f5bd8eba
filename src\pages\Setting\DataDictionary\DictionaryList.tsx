/*
 * @Author: DJscript
 * @Date: 2023-03-06 09:06:09
 * @LastEditTime: 2023-03-30 14:00:26
 * @FilePath: /kylin-admin/src/pages/Setting/DataDictionary/DictionaryList.tsx
 * @Description: 数据字典列表
 */
import React from 'react';
import { YREmpty, YRInput, YRItem, YRSpace, YRSpin, YRTree } from 'yrantd';

import { useAntd } from './Context';

import { TreeDataNode } from 'yrantd/lib/yr-tree';
import { DefaultNode, KeyEnum, TreeNode } from './Interface';

const DictionaryList = () => {
  const antd = useAntd();
  const [selectedKey, setSelectedKey] = antd.selectedState;
  const { data: list, mutate: setList } = antd.antdTreeProps;
  const [searchVal, setSearchVal] = React.useState('');

  return (
    <YRSpace direction="vertical" block>
      <YRInput.Search
        allowClear
        placeholder="请输入字典名称"
        onSearch={(val) => {
          setSearchVal(val);
          if (val) {
            const newList = antd.antdTreeProps.originalList.filter(
              (item) => item[KeyEnum.KEY].includes(val) || item[KeyEnum.LABEL].includes(val)
            );
            setList(newList);
          } else {
            setList(antd.antdTreeProps.originalList);
          }
        }}
      />
      <YRSpin spinning={antd.antdTreeProps.loading}>
        {list?.length ? (
          <YRTree
            blockNode
            fieldNames={{ title: KeyEnum.LABEL, key: KeyEnum.KEY, children: 'children' }}
            titleRender={(nodeData: TreeNode & TreeDataNode) => {
              return (
                <YRItem
                  key={nodeData[KeyEnum.KEY]}
                  title={`${nodeData[KeyEnum.LABEL]}（${nodeData[KeyEnum.KEY]}）`}
                  highlightText={searchVal}
                />
              );
            }}
            onSelect={(_, info) => {
              setSelectedKey(info.node);
            }}
            height={700}
            treeData={[{ ...DefaultNode, children: list }] as any}
            defaultExpandedKeys={[DefaultNode[KeyEnum.KEY]!]}
            selectedKeys={[selectedKey[KeyEnum.KEY]!]}
          />
        ) : (
          <YREmpty />
        )}
      </YRSpin>
    </YRSpace>
  );
};

export default DictionaryList;
