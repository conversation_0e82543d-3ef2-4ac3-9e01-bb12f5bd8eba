/**
 * 页面描述: 规则条件组件
 * @文件名 RuleContext.tsx
 * @filePath \src\pages\Setting\RuleManager\Entry\components\RuleContext.tsx
 * @Date 2023-08-08 16:12:21
 * @Email <EMAIL>
 * <AUTHOR>
 */
import React, { Fragment } from 'react';
import { omit } from 'lodash-es';
import store from '@/store';
import { addIfOne, removeIfOne } from '../util/helper';
import CommLayout from './CommLayout';
import Join from './Join';
import Condition from './Condition';
import style from '../index.module.less';

const RuleContext = (props) => {
  const [ruleState, ruleDispatch] = store.useModel('rule');
  const { ruleData, ruleDataMap } = ruleState;
  let total = 0;

  // 添加条件 条件ID
  const addItem = (obj, id) => {
    const outData = addIfOne({ ruleData, id, obj });
    ruleDispatch.setRuleData(outData);
  };

  // 删除节点
  const removeItem = (parentId, id) => {
    const outData = removeIfOne({ ruleData, parentId, id });
    const newData = omit(ruleDataMap, [id]);
    ruleDispatch.setRuleData(outData);
    ruleDispatch.setRuleDataMap(newData);
  };

  /**
   * @param {*} ifData          数据
   * @param {*} parentId        父级ID
   * @param {*} level           横向等级
   * @param {*} parentNumber    父级行号
   *                            number  自身行号
   */
  const loop = (ifData, parentId, level, parentNumber) => {
    level++;
    total++;
    const totalNumber = total;
    if (ifData?.junctionType) {
      const isSingle = !(ifData?.criterions && ifData?.criterions?.length > 0);
      return (
        <Fragment key={ifData.key}>
          <Join
            svgId={'ifSvg'}
            removeItem={removeItem}
            isSingle={isSingle}
            id={ifData.key}
            parentId={parentId}
            addItem={addItem}
            level={level}
            parentNumber={parentNumber}
            number={total}
          />
          {ifData?.criterions &&
            ifData?.criterions.map((item) => {
              return loop(item, ifData.key, level, totalNumber);
            })}
        </Fragment>
      );
    } else {
      return (
        <Condition
          svgId={'ifSvg'}
          removeItem={removeItem}
          key={ifData.key}
          id={ifData.key}
          parentId={parentId}
          level={level}
          parentNumber={parentNumber}
          number={total}
        />
      );
    }
  };

  return (
    <CommLayout>
      {ruleData && Object.keys(ruleData).length > 0 && loop(ruleData, 'root', 0, 0)}
      <svg
        xmlns="http://www.w3.org/2000/svg"
        version="1.1"
        className={style['if-context-svg']}
        width="100%"
        height="100%"
        id="ifSvg"
      />
    </CommLayout>
  );
};

export default RuleContext;
