/**
 * @Author: 刘发浩
 * @Description: 流程管理列表
 * @Date: Created in 2022-12-09 16:46:35
 * @Modifed By:
 */

import React, { useState, useEffect } from 'react';
import { countTableWidth } from '@yr/util';
import {
  FormItemListProps,
  YRTableProps,
  YRDict,
  YRButton,
  YRForm,
  YRIndexPageLayout,
  YRTable,
  YRLink,
  YRConfirmBtn,
  YRTag,
  YRUpload,
  YRMessage,
  YRSpace,
  YREasyUseModal
} from 'yrantd';
import { UploadOutlined } from '@ant-design/icons';
import { pickBy } from 'lodash-es';
import { openNewTab, router } from '@/utils/utils';
import FlowMapModal from './components/FlowMapModal';
import { M02 } from '@permit/flow';
import Dict from '@/pages/Organ/mock/getDict';
import { flowSceneTypeColor } from '../../constant/StyleConst';
import FlowSettingModal from './components/FlowSettingModal';
import { fileImport } from '@/services/flow';

const { selectPageForMaxVersion } = M02.interfaces;
const { deleteFlowDiagramResource } = M02.E02.interfaces;

interface Pagination {
  total: number;
  pageNum: number;
  pageSize: number;
}

const defaultPagination = {
  pageNum: 1,
  pageSize: 10
};

const FlowIndex = () => {
  const [form] = YRForm.useForm();
  const [dataSource, setDataSource] = useState([]);
  const [pageLoading, setPageLoading] = useState(false);
  const [pagination, setPagination] = useState<Pagination>({
    total: 0,
    ...defaultPagination
  });
  const [flowModalVisible, setFlowModalVisible] = useState(false);
  const [mode, setMode] = useState<'add' | 'edit'>('add');
  const [currentFlowInfo, setCurrentFlowInfo] = useState({});

  const formItemList: FormItemListProps[] = [
    {
      placeholder: '流程编号',
      key: 'modelNumber',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '流程名称',
      key: 'modelName',
      type: 'input',
      position: 'show'
    },
    {
      placeholder: '流程类型',
      key: 'sceneType',
      type: 'dictSelect',
      dictkey: 'flow_scene_type',
      position: 'show'
    }
  ];

  const queryList = (param = {}) => {
    const params: any = { ...pickBy(param), modelType: '1' };
    setPageLoading(true);
    selectPageForMaxVersion(params).then((res: any) => {
      setPageLoading(false);
      setDataSource(res?.data?.list || []);
      setPagination({
        pageNum: res?.data?.pageNum,
        pageSize: res?.data?.pageSize,
        total: res?.data?.total
      });
    });
  };

  useEffect(() => {
    handleSearch({
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    });
  }, []);

  const handleSearch = ({ pageNum, pageSize }) => {
    form.validateFields().then((res) => {
      queryList({ pageNum, pageSize, ...res });
    });
  };

  const toDetail = (row: any) => {
    openNewTab({
      pathname: '/setting/flow/detail',
      query: { id: row?.id }
    });
  };

  const onDel = (row) => {
    deleteFlowDiagramResource({ id: row?.id }).then((res) => {
      if (res?.success) {
        handleSearch(defaultPagination);
      }
    });
  };

  const onEdit = (row) => {
    openNewTab({
      pathname: '/setting/flow/edit',
      query: { id: row?.id }
    });
  };

  const getTableColumns: YRTableProps['columns'] = [
    {
      title: '流程编号',
      dataIndex: 'modelNumber',
      width: 120,
      render: (value, row) => {
        if (value) {
          return (
            <YRLink type="primary" onClick={() => toDetail(row)}>
              {value}
            </YRLink>
          );
        }
        return CONST.null;
      }
    },
    {
      title: '流程名称',
      dataIndex: 'modelName',
      width: 220
    },
    {
      title: '流程类型',
      dataIndex: 'sceneType',
      width: 80,
      render: (value: string) => {
        if (!value) return CONST.null;
        return (
          <YRTag color={flowSceneTypeColor[value]}>
            <YRDict.Text dictkey="flow_scene_type" defaultValue={value} />
          </YRTag>
        );
      }
    },
    {
      title: '流程说明',
      dataIndex: 'modelDesc',
      width: 220
    },
    {
      title: '最新版本',
      dataIndex: 'version',
      width: 80
    },
    {
      title: '操作',
      dataIndex: 'operator',
      width: 100,
      fixed: 'right',
      render: (value, row) => {
        const { modelNumber } = row;
        return (
          <YRButton.Overflow>
            <YRLink type="primary" onClick={() => YREasyUseModal.show(FlowSettingModal, { flowNumber: modelNumber })}>
              配置
            </YRLink>
            <YRLink type="primary" onClick={() => onEdit(row)}>
              修改
            </YRLink>
            <YRLink type="error">
              <YRConfirmBtn msg="确定删除吗？" type="pop" onConfirm={() => onDel(row)} check={M02.E02}>
                删除
              </YRConfirmBtn>
            </YRLink>
          </YRButton.Overflow>
        );
      }
    }
  ];

  // 导入文件
  const customRequest = ({ file, fileList }) => {
    const reg = /xls|xlsx$/;
    let param = file;
    // if (!reg.test(file?.name)) {
    //   YRMessage.warn('请上传xls、xlsx类型文件');
    //   return;
    // }
    if (file instanceof File) {
      param = new FormData();
      param.append('file', file);
    }
    fileImport(param).then((res) => {
      if (res.success) {
        YRMessage.success('上传成功');
        queryList();
      }
    });
  };

  const renderExtAction = (
    <YRSpace>
      <YRButton
        type="primary"
        onClick={() => {
          setFlowModalVisible(true);
          setMode('add');
        }}
        check={M02.E01}
      >
        新增
      </YRButton>
      <YRUpload
        fileList={[]}
        beforeUpload={() => false}
        customRequest={customRequest}
        listType="text"
        onChange={customRequest}
      >
        <YRButton icon={<UploadOutlined />}>导入</YRButton>
      </YRUpload>
    </YRSpace>
  );

  return (
    <YRIndexPageLayout>
      <YREasyUseModal.Provider>
        <YRTable
          form={form}
          rowKey="id"
          loading={pageLoading}
          dataSource={dataSource}
          columns={getTableColumns}
          handleSearch={handleSearch}
          formItemList={formItemList}
          extAction={renderExtAction}
          pagination={pagination}
          scroll={{
            y: document.body.clientHeight - 258,
            x: countTableWidth(getTableColumns)
          }}
          onRow={(row) => {
            return {
              onDoubleClick: () => toDetail(row)
            };
          }}
        />

        <FlowMapModal
          visible={flowModalVisible}
          flowInfo={currentFlowInfo}
          mode={''}
          onCancel={() => {
            setFlowModalVisible(false);
          }}
          callback={() => {
            setFlowModalVisible(false);
            handleSearch(defaultPagination);
          }}
        />
      </YREasyUseModal.Provider>
    </YRIndexPageLayout>
  );
};

export default Dict(['flow_scene_type', 'flow_diagram_status'])(FlowIndex);
