import React, { useEffect } from 'react';
import { PageLoading } from '@yr/pro-layout';
import store from '@/store';
import { changeTheme } from '@/utils/utils';

/**
 * 登录页面
 */

const Login = () => {
  const [, loginDispatchers] = store.useModel('login');
  const [layoutState] = store.useModel('layout');
  const { initialState } = layoutState;

  useEffect(() => {
    changeTheme(initialState.settings.primaryColor);
  }, []);

  useEffect(() => {
    const { login } = loginDispatchers;
    const { username, password, picCode, tenantId } = sessionStorage;
    sessionStorage.removeItem('username');
    sessionStorage.removeItem('password');
    sessionStorage.removeItem('picCode');
    sessionStorage.removeItem('tenantId');
    // 调用登录
    login({ loginAccount: username, password, picCode, tenantId });
  }, []);

  return <PageLoading />;
};

export default Login;
