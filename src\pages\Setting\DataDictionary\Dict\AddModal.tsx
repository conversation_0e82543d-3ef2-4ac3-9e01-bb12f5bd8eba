/*
 * @Author: 伍晶晶
 * @Description: 字典弹窗
 * @Date: 2023-03-20 14:21:54
 * @LastEditors: 伍晶晶
 * @LastEditTime: 2023-03-20 20:26:59
 */
import { antdModal } from '@/utils/utils';
import React from 'react';
import { YREasyUseModal, YRForm, YRInput, YRModal, YRRadio } from 'yrantd';

type YRFormProps = React.ComponentProps<typeof YRForm>;

const AddModal: React.FC<{ type: YRFormProps['mode']; row?: any }> = ({ type, row }) => {
  const modal = YREasyUseModal.useModal();
  const [form] = YRForm.useForm();
  return (
    <YRModal
      {...antdModal(modal)}
      title={`${type === 'add' ? '新增' : '编辑'}字典`}
      onOk={() => {
        form.validateFields().then((values: any) => {
          modal.resolve({ ...values, dictId: row?.dictId });
          modal.hide();
          modal.remove();
        });
      }}
    >
      <YRForm initialValues={row} form={form}>
        <YRForm.Row>
          <YRForm.Item label="字典中文名" name="dictName" rules={[{ required: true }]}>
            <YRInput maxLength={32} />
          </YRForm.Item>
          <YRForm.Item label="字典英文名" name="dictKey" rules={[{ required: true }]}>
            <YRInput maxLength={32} disabled={type === 'edit'} />
          </YRForm.Item>
          <YRForm.Item label="是否是单级字典" name="isChildNode" initialValue="Y" rules={[{ required: true }]}>
            <YRRadio.Group disabled={type === 'edit'}>
              <YRRadio value="N">否</YRRadio>
              <YRRadio value="Y">是</YRRadio>
            </YRRadio.Group>
          </YRForm.Item>
        </YRForm.Row>
      </YRForm>
    </YRModal>
  );
};

export default YREasyUseModal.create(AddModal);
