import React from 'react';
import { YRList, YRDropdown, YRTooltip, YRIcon } from 'yrantd';

export default (props) => {
  const { itemMenu, list, fieldNames, setCurrentItem, currentItem = {}, showDesc } = props;
  function getItem(item) {
    const name = item[fieldNames.name];
    const desc = item[fieldNames.desc];
    const { key } = fieldNames;
    const hasMenu = item.groupType !== 'all_var' && itemMenu && itemMenu(item);
    return (
      <YRList.Item
        actions={
          hasMenu && [
            <YRDropdown trigger="click" overlay={itemMenu(item)}>
              <div className="icon-menu">
                <YRIcon icon="yunrongXXcheck" fontSize="14px" />
              </div>
            </YRDropdown>
          ]
        }
        className={currentItem[key] === item[key] ? 'active' : ''}
      >
        <div
          className="box"
          onClick={() => {
            setCurrentItem(item);
          }}
        >
          <YRTooltip placement="bottomLeft" title={name}>
            <div className={'list-item-title'}>{name}</div>
          </YRTooltip>

          {(desc || showDesc) && <div className={'list-item-desc'}>{desc}</div>}
        </div>
      </YRList.Item>
    );
  }
  return <YRList className="list-box" size={window.size} bordered={false} dataSource={list} renderItem={getItem} />;
};
