<!--
  ~ yunrong.cn Inc. Copyright (c) 2014-2021 All Rights Reserved
  -->

<!DOCTYPE html>
 <html lang="en">
 <head>
     <meta charset="UTF-8">
     <meta name="viewport" content="width=device-width, initial-scale=1.0">
     <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
     <meta http-equiv= "Pragma"   content= "no-cache" />
     <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE8" />
     <meta http-equiv= "Cache-Control"   content= "no-cache" />
     <meta http-equiv= "Expires"   content= "0" />
     <title>欢迎登录</title>
     <link rel="icon" href="/favicon.png" type="image/x-icon">
     <link rel="stylesheet" type="text/css" href="login.css" />
 </head>
 <body>
     <div class="login-content">
         <div class="login-header">
             <div id="canvasId"></div>
             <div class="login_box">
                <h3>科技让金融更普惠</h3>
                <form action="#" name="f" method="post">
                  <div class="input-outer">
                    <span class="u-tenant">租户:</span>
                    <select id="login_tenant" autocomplete="off" class="text" type="text" style="color: #fff;width: 250px;">
                    </select>
                  </div>
                    <div class="input-outer">
                        <span class="u-user"></span>
                        <input name="login-name" autocomplete="off" class="text"  type="text" placeholder="请输入账户" oninput="checkInput(this)">
                    </div>
                    <div class="input-outer">
                        <span class="u-password"></span>
                        <input name="login-pass" class="text" value="" type="password" placeholder="请输入密码" oninput="checkInput(this)" onkeydown="handleKeyDown(event)">
                    </div>
                    <div class="input-outer pic-code">
                        <input name="pic-code" class="text" placeholder="请输入验证码" style="margin-left:10px" onkeydown="handleKeyDown(event)">
                        <img id="picCode" onclick="getPicCode()" />
                    </div>
                    <div class="mb2"><a class="act-but submit" href="javascript:;" onclick="login()">登录</a></div>
                    <div id="msg" class="login-err"></div>
                </form>
            </div>
         </div>
     </div>
     <script type="text/javascript" src="jquery-1.11.0.min.js"></script>
     <script type="text/javascript" src="polygonizr.js"></script>
     <script type="text/javascript" src="base64.js"></script>
     <script type="text/javascript" src="crypto-js.js"></script>
     <script type="text/javascript" src="util.js"></script>
     <script type="text/javascript">
        $('#canvasId').polygonizr();
        function checkInput(thiz){
            thiz.value && $(thiz).parent().removeClass('has-err');
        }
        function login() {
            var username = $('input[name="login-name"]').val();
            var password = $('input[name="login-pass"]').val();
            var picCode = $('input[name="pic-code"]').val();
            var options=$("#login_tenant option:selected");//获取当前选择项.
            var tenantId = options.val();
            if(!username){
                $('form').find('.input-outer').eq(0).addClass('has-err');
                return;
            }
            if(!password){
                $('form').find('.input-outer').eq(1).addClass('has-err');
                return;
            }
            if($('#picCode').attr('src') && !picCode) {
                $('form').find('.input-outer').eq(2).addClass('has-err');
                return;
            }
            if(username && password){
                sessionStorage.username = username;
                sessionStorage.password = password;
                sessionStorage.picCode = picCode;
                sessionStorage.tenantId = tenantId;
                location.href = '/login' + location.search;
            }
        }

        function handleKeyDown(e) {
            if(e.keyCode === 13) {
                login()
            }
        }

        if(sessionStorage.loginErr) {
            $("#msg").html(sessionStorage.loginErr);
        }

        if(sessionStorage.loginErrCode === '*************') {
            $('.pic-code').css('display', 'flex');
            getPicCode();
        }
        else {
            $('.pic-code').hide();
            $('#picCode').attr('src', '');
        }

        function getPicCode() {
            $.ajax({
                type: 'post',
                url: '/hsjry/admin/admin/v1/picCode/loginCode',
                data: JSON.stringify({
                    code: $('input[name="login-name"]').val() || sessionStorage.loginErrAccount || 'no'
                }),
                headers: getRequestHeaders(),
                success: function(res) {
                    $('#picCode').attr('src', res.data);
                }
            })
        }
     </script>
     <script type="text/javascript">
       $(function() {
         queryTenant();
       });

       function queryTenant() {
         var result = [{'value':'000-金华银行','key':'000'},{'value':'001-柳州银行','key':'001'},{'value':'002-宁波银行','key':'002'}];
         for(let i=0; i<result.length; i++){
           //先创建好select里面的option元素
           var option = document.createElement("option");
           $(option).val(result[i].key);
           $(option).text(result[i].value);
           $(option).css('color', '#000000');
           $('#login_tenant').append(option);
         }
       }
     </script>
 </body>
 </html>
