/*
 * @Description: file content
 * @Author: your name
 * @Date: 2020-10-13 14:02:44
 * @LastEditors: your name
 * @LastEditTime: 2020-11-20 11:12:49
 */
import React, { Fragment, useEffect, useState } from 'react';
import { YRInput, YRList, YRMenu, YRDropdown, YREmpty, YRUpload, YRMessage, YRPopconfirm, YRLayout, YRTooltip, YRIcon } from 'yrantd';
import { useModalChange } from '@/utils/utils';
import ModalSupplier from './ModalSupplier';
import CostInterface from './CostInterface';
import CommonLeftSlider from '@/components/CommonLeftSlider';
import { queryByComCode, threeComList, threeComDel, threeComModify } from '@/services/setting';
import './index.less';

const { Search } = YRInput;
const { Content } = YRLayout;

function SupplierMaintenance(props) {
  const { handleShow, handleCancel, modalMessage } = useModalChange();
  const [currentSupplier, setCurrentSupplier] = useState({});
  const [supplierList, setSupplierList] = useState([]);
  const [searchValue, setSearchValue] = useState('');
  const [activeTabKey, setActiveTabKey] = useState('2');
  const [loading, setLoading] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const [interfaceList, setInterfaceList] = useState([]);

  useEffect(() => {
    getList();
  }, [refresh]);
  function refreshSupplier() {
    setRefresh(!refresh);
  }
  useEffect(() => {
    if (currentSupplier?.comCode) {
      queryByComCode({ comCode: currentSupplier.comCode }).then((res) => {
        if (res?.data) {
          const code = currentSupplier.comCode;
          if (code) {
            const found = res.data.find((item) => {
              return item.comCode === code;
            });
            if (found) {
              const { comPathInfos = [] } = found;
              if (comPathInfos.length > 0) {
                comPathInfos.unshift({
                  comPathName: '全部',
                  comPathCode: ''
                });
              }
              setInterfaceList(comPathInfos);
            }
          }
        }
      });
    }
  }, [currentSupplier?.comCode]);

  function getList() {
    setLoading(true);
    threeComList().then((res) => {
      setLoading(false);
      if (res?.data) {
        setSupplierList(res.data);
        setCurrentSupplier(res?.data[0] || {});
      }
    });
  }

  function search(value) {
    setSearchValue(value);
  }

  function handleDelete(code) {
    threeComDel({ comCode: code }).then((res) => {
      if (!res?.errorMessage) {
        YRMessage.success('操作成功');
      }
      getList();
    });
  }

  function getCurrent(value) {
    setCurrentSupplier(value);
  }

  function handleOk(type, values) {
    threeComModify({ ...values }).then((res) => {
      if (!res?.errorMessage) {
        YRMessage.success('操作成功');
      }
      getList();
      handleCancel();
    });
  }

  const data = supplierList.filter((item) => {
    const index = item.comName.indexOf(searchValue);
    if (index === -1) {
      return false;
    }
    return true;
  });
  const itemMenu = (item) => (
    <YRMenu>
      <YRMenu.Item>
        <a
          onClick={() => {
            handleShow('edit', {
              comName: item.comName,
              comCode: item.comCode,
              description: item.description,
              id: item.id
            });
          }}
        >
          修改
        </a>
      </YRMenu.Item>
      <YRMenu.Item>
        <YRPopconfirm
          title="你确定要删除吗？"
          onConfirm={() => {
            handleDelete(item.comCode);
          }}
          okText="确定"
          cancelText="取消"
        >
          <a>删除</a>
        </YRPopconfirm>
      </YRMenu.Item>
    </YRMenu>
  );
  return (
    <YRLayout>
      <CommonLeftSlider
        title="厂商"
        currentItem={currentSupplier}
        setCurrentItem={getCurrent}
        list={data}
        fieldNames={{ name: 'comName', desc: 'desc', key: 'id' }}
        search={search}
        create={() => {
          handleShow('add');
        }}
        itemMenu={itemMenu}
        loading={loading}
      />
      <Content className="layout-column-right">
        {data.length > 0 ? (
          <Fragment>
            <div className="column-right-top">
              <span className="title">
                {currentSupplier.comName}（{currentSupplier.comCode}）
              </span>
              <span className="time">最近更新：{currentSupplier.updateTime}</span>
            </div>
            {currentSupplier.comCode && (
              <CostInterface
                refresh={activeTabKey === '2'}
                currentSupplier={currentSupplier}
                interfaceList={interfaceList}
                supplierList={supplierList}
                refreshSupplier={refreshSupplier}
              />
            )}
          </Fragment>
        ) : (
          <YREmpty style={{ paddingTop: '100px' }} />
        )}
      </Content>
      {modalMessage.isShow && (
        <ModalSupplier
          handleOk={handleOk}
          handleCancel={() => {
            handleCancel(modalMessage.editType);
          }}
          visible={modalMessage.isShow}
          type={modalMessage.editType}
          initValues={modalMessage.editObj}
        />
      )}
    </YRLayout>
  );
}

export default SupplierMaintenance;
