authDimType (授权维度类型)
根据代码分析，authDimType 主要包含以下枚举值：

1. 结果类型相关
creditResultType - 授信业务审批授权
loanResultType - 放款审查授权
2. 产品相关
productCollect - 产品组/产品集合
3. 担保方式相关
guarMethodCollect - 担保方式集合
4. 其他维度类型
customerType - 客户类型
productType - 产品类型
riskLevel - 风险等级
amount - 授权金额
🔹 firstDimType (一级维度类型)
firstDimType 通常是 authDimType 对应的中文名称或描述，主要包含：

1. 业务类型维度
结果类型 - 对应 creditResultType/loanResultType
产品组 - 对应 productCollect
担保方式 - 对应 guarMethodCollect
2. 客户维度
客户类型 - 对应 customerType
3. 产品维度
产品类型 - 对应 productType
4. 风险维度
风险等级 - 对应 riskLevel
授权金额 - 对应 amount