# 批量授权管理页面

## 页面概述

批量授权管理页面用于管理批量导入的授权数据，支持批量导入、查询、审核等功能。

## 功能特性

### 1. 查询功能
- **授权状态筛选**: 支持按授权状态筛选批量授权记录
- **分页查询**: 支持分页显示，提高查询性能
- **实时刷新**: 支持手动刷新数据

### 2. 批量操作
- **批量导入**: 支持Excel文件批量导入授权数据
- **下载模板**: 提供标准的Excel导入模板
- **批量失效**: 支持批量失效已生效的授权记录

### 3. 单条记录操作
根据授权状态显示不同的操作按钮：

#### 待提交状态 (010)
- **详情**: 查看批量授权详细信息
- **删除**: 删除批量授权记录
- **提交复核**: 提交至复核流程

#### 复核中状态 (020)
- **详情**: 查看批量授权详细信息

#### 复核退回状态 (050)
- **详情**: 查看批量授权详细信息
- **删除**: 删除批量授权记录
- **提交复核**: 重新提交至复核流程

#### 已生效状态 (040)
- **详情**: 查看批量授权详细信息

## 数据结构

### 批量授权信息 (BatchAuthInfo)
```typescript
interface BatchAuthInfo {
  batchAuthId: string;      // 批量授权ID
  batchAuthNo: string;      // 批量授权编号
  fileName: string;         // 导入文件名称
  authStatus: string;       // 授权状态
  operatorName: string;     // 登记人
  operatorId: string;       // 登记人ID
  ownOrganName: string;     // 登记机构
  ownOrganId: string;       // 登记机构ID
  inputTime: string;        // 登记日期
  updateTime: string;       // 更新时间
  totalCount: number;       // 总记录数
  successCount: number;     // 成功记录数
  failCount: number;        // 失败记录数
  remark?: string;          // 备注
}
```

## 技术实现

### 组件结构
```
BatchAuthManagement/
├── index.tsx           # 主组件
├── types.ts           # 类型定义
├── useIndex.tsx       # 表格配置和工具函数
└── README.md          # 文档说明
```

### 主要依赖
- **yrantd**: UI组件库
- **ahooks**: React Hooks工具库
- **@yr/util**: 工具函数库

### 权限控制
- 页面权限: `M010502` (批量授权管理)
- 接口权限: 通过 `M0105.interfaces` 获取

## 使用说明

### 1. 批量导入流程
1. 点击"下载模板"按钮获取标准模板
2. 按照模板格式填写授权数据
3. 点击"批量导入"按钮上传Excel文件
4. 系统自动解析并创建批量授权记录

### 2. 审核流程
1. 导入完成后，记录状态为"待提交"
2. 点击"提交复核"按钮提交审核
3. 审核通过后状态变为"已生效"
4. 审核退回后可重新提交

### 3. 文件要求
- **格式**: 支持 .xlsx 和 .xls 格式
- **大小**: 不超过 10MB
- **内容**: 按照标准模板格式填写

## 已实现功能

### 1. 接口集成 ✅
- ✅ 批量授权查询接口：`POST /authBaseInfoBatchQueryController/queryInfoPage`
- 🔄 批量导入接口（待实现）
- 🔄 提交复核接口（待实现）
- 🔄 删除接口（待实现）
- 🔄 失效接口（待实现）

### 2. 数据映射 ✅
- ✅ API响应数据结构映射
- ✅ 字段名称映射
- ✅ 日期格式化处理
- ✅ 数据安全性验证

### 3. 错误处理 ✅
- ✅ API调用错误处理
- ✅ 数据验证错误处理
- ✅ 用户友好的错误提示
- ✅ 调试信息输出

## 待实现功能

### 1. 详情弹窗
- 批量授权详情展示
- 导入结果详情
- 错误信息展示

### 2. 高级功能
- 导入进度显示
- 导入结果统计
- 错误数据导出
- 批量操作确认

## API集成详情

### 查询接口
- **接口路径**: `POST /authBaseInfoBatchQueryController/queryInfoPage`
- **请求参数**:
  ```typescript
  {
    pageNum: number;        // 页码
    pageSize: number;       // 每页大小
    authStatus?: string;    // 授权状态（可选）
  }
  ```
- **响应数据**:
  ```typescript
  {
    rpcResult: "SUCCESS" | "FAIL";
    data: {
      pageNum: number;
      pageSize: number;
      total: number;
      pages: number;
      list: AuthFileImportInfoBatchDto[];
    };
  }
  ```

### 数据字段映射
| API字段 | 前端字段 | 说明 |
|---------|----------|------|
| batchAuthBaseId | batchAuthNo | 批量授权编号 |
| fileName | fileName | 导入文件名称 |
| authStatus | authStatus | 授权状态 |
| registerName | operatorName | 登记人 |
| registerOrgName | ownOrganName | 登记机构 |
| registerTime | inputTime | 登记日期（格式化） |
| successNum | successCount | 成功笔数 |
| failNum | failCount | 失败笔数 |

## 注意事项

1. **权限控制**: 确保用户具有相应的操作权限
2. **数据校验**: 导入时需要严格校验数据格式和内容
3. **错误处理**: 提供友好的错误提示和处理机制
4. **性能优化**: 大批量数据导入时需要考虑性能问题
5. **审计日志**: 记录所有操作日志便于追溯

## 更新日志

- 2024-12-19: 初始版本创建
- 页面基础功能实现
- 表格展示和基础操作
- 文件上传和模板下载框架
