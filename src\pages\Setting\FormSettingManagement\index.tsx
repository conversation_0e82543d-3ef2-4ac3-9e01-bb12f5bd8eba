import React, { useState } from 'react';
import { YRFlexPageLayout, YRLink } from 'yrantd';
import { ModulesTree, FormSettingPage } from './components/index';
import './index.less';

const FormSettingManagement = () => {
  const [selectedModulesNodeId, setSelectedModulesNodeId] = useState('');

  const onSelectModulesNode = ({
    selectedNodeKey
  }: {
    selectedNodeKey: string;
    selectedNodes: Record<string, any>;
  }) => {
    setSelectedModulesNodeId(selectedNodeKey);
  };

  const onAddFormSetting = () => {};

  const addLink = <YRLink type="primary">新增</YRLink>;

  return (
    <YRFlexPageLayout direction={'horizontal'}>
      <YRFlexPageLayout.Sider title="模块目录">
        <ModulesTree selectedCallBack={onSelectModulesNode} />
      </YRFlexPageLayout.Sider>
      <YRFlexPageLayout.Main title={'表单配置'} extra={addLink}>
        <FormSettingPage queryDataId={selectedModulesNodeId} />
      </YRFlexPageLayout.Main>
    </YRFlexPageLayout>
  );
};

export default FormSettingManagement;
