# 待复核-单笔授权管理页面

## 页面概述

待复核-单笔授权管理页面用于处理处于复核中状态的单笔授权记录，支持查询、详情查看、复核通过和复核退回等功能。

## 功能特性

### 1. 查询功能
- **授权编号**: 支持按授权编号精确查询
- **机构**: 支持按机构树形结构选择查询
- **部门**: 支持按部门下拉选择查询
- **角色**: 支持按角色下拉选择查询
- **授权类别**: 支持按授权类别下拉选择查询（从数据字典获取）
- **分页查询**: 支持分页显示，提高查询性能

### 2. 列表展示
按照原型图要求显示以下字段：
- **授权编号**: 唯一标识，固定在左侧
- **机构**: 授权所属机构
- **部门**: 授权所属部门
- **角色**: 授权关联角色
- **授权分类**: 授权类别（显示中文名称）
- **授信类别**: 授信业务类别
- **授权状态**: 当前授权状态（显示中文名称）
- **生效日期**: 授权生效日期
- **失效日期**: 授权失效日期

### 3. 操作功能
每条记录提供以下操作按钮：
- **详情**: 查看授权详细信息
- **复核退回**: 将授权退回给申请人重新处理
- **复核通过**: 通过复核，授权生效

## 数据结构

### 待复核单笔授权信息 (PendingSingleAuthInfo)
```typescript
interface PendingSingleAuthInfo {
  authBaseId: string;       // 授权ID
  authNo: string;           // 授权编号
  orgId: string;            // 机构ID
  orgName: string;          // 机构名称
  deptId: string;           // 部门ID
  deptName: string;         // 部门名称
  roleId: string;           // 角色ID
  roleName: string;         // 角色名称
  authCategory: string;     // 授权类别
  creditCategory: string;   // 授信类别
  authStatus: string;       // 授权状态
  effectBeginDate: string;  // 生效日期
  effectEndDate: string;    // 失效日期
  operatorName: string;     // 操作人
  ownOrganName: string;     // 登记机构
  // ... 其他字段
}
```

## 技术实现

### 组件结构
```
PendingSingleAuth/
├── index.tsx           # 主组件
├── types.ts           # 类型定义
├── useIndex.tsx       # 表格配置和工具函数
└── README.md          # 文档说明
```

### 主要依赖
- **yrantd**: UI组件库
- **ahooks**: React Hooks工具库
- **@yr/util**: 工具函数库和数据字典

### 权限控制
- 页面权限: `M010503` (待复核-单笔)
- 接口权限: 通过 `M0105.interfaces` 获取

## 使用说明

### 1. 查询流程
1. 在查询条件区域输入或选择查询条件
2. 点击"查询"按钮执行查询
3. 点击"重置"按钮清空查询条件

### 2. 复核流程
1. 在列表中找到需要复核的授权记录
2. 点击"详情"按钮查看详细信息
3. 根据业务规则决定复核结果：
   - 点击"复核通过"：授权生效
   - 点击"复核退回"：退回给申请人修改

### 3. 查询条件说明
- **授权编号**: 支持模糊匹配
- **机构**: 树形选择器，支持多级机构选择
- **部门**: 下拉选择器，根据选择的机构动态加载
- **角色**: 下拉选择器，显示所有可用角色
- **授权类别**: 下拉选择器，从数据字典 'AUTH_CATEGORY' 获取

## 业务规则

### 1. 数据过滤
- 页面只显示状态为"复核中"(020)的授权记录
- 支持多条件组合查询

### 2. 操作权限
- 只有具有复核权限的用户才能进行复核操作
- 复核操作需要记录操作日志

### 3. 状态流转
- 复核通过：状态从"复核中"变为"已生效"
- 复核退回：状态从"复核中"变为"复核退回"

## 待实现功能

### 1. 接口集成
- 待复核授权查询接口
- 机构树查询接口
- 部门列表查询接口
- 角色列表查询接口
- 复核通过接口
- 复核退回接口

### 2. 功能增强
- 批量复核功能
- 复核意见输入
- 复核历史记录
- 导出功能

### 3. 用户体验优化
- 查询条件联动（机构-部门）
- 操作确认提示优化
- 加载状态优化

## 注意事项

1. **数据安全**: 复核操作需要严格的权限控制
2. **操作审计**: 所有复核操作需要记录详细日志
3. **业务规则**: 严格按照业务流程进行状态流转
4. **用户体验**: 提供清晰的操作反馈和错误提示
5. **性能优化**: 大数据量时需要考虑分页和查询优化

## 更新日志

- 2024-12-19: 初始版本创建
- 实现基础查询和复核功能
- 完成页面布局和交互逻辑
- 集成权限控制和数据字典
