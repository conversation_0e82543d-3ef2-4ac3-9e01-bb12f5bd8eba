/**
 * @Author: wangyw26123
 * @Description: 功能权限-权限组
 * @Date: Created in 2022-06-01 14:46:45
 * @Modifed By:
 */
import React from 'react';
import classNames from 'classnames';
import styles from './index.module.less';

/**
 * 权限组列表单元组件
 * @param props
 * @constructor
 */
interface GroupListItemProps {
  /**
   * 标题
   */
  title: string;
  /**
   * 激活状态
   */
  active: boolean;
  /**
   * 点击事件
   * @param e
   */
  onClick: (e) => void;
  /**
   * 右侧扩展
   */
  extra?: React.ReactNode;
}
const GroupListItem = (props: GroupListItemProps) => {
  const { title, extra, active, ...restProps } = props;

  return (
    <div
      className={classNames(styles.groupListItem, {
        [styles.active]: active
      })}
      {...restProps}
    >
      <div className={styles.groupListItemTitle} title={title}>
        {title}
      </div>
      <div className={styles.groupListItemExtra}>{extra}</div>
    </div>
  );
};

export default GroupListItem;
