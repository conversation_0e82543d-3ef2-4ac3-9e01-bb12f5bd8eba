/**
 * 页面描述: 比较运算符
 * @文件名 ComparisonOperator.tsx
 * @filePath \src\pages\Setting\RuleManager\Entry\components\ComparisonOperator.tsx
 * @Date 2023-08-09 14:35:43
 * @Email <EMAIL>
 * <AUTHOR>
 */
import React from 'react';
import { YRDropdown, YRInput, YRMenu, YRTooltip } from 'yrantd';
import { operatorItem, operatorNameItem } from '../config';
import style from '../index.module.less';
import store from '@/store';
import InputValue from './InputValue';

const ComparisonOperator = (props) => {
  const { changeType, rightValue, op } = props;
  const [ruleState, ruleDispatch] = store.useModel('rule');
  const { type } = ruleState;

  // 设置比较运算符
  const setOperator = ({ item }) => {
    if (!item || !item?.props) return;
    const data = item?.props?.data || {};
    changeType('op', data?.name);
  };

  const operatorMenu = (
    <YRMenu className="parameter-value" style={{ maxHeight: 300, overflowY: 'scroll' }} onClick={setOperator}>
      {operatorItem.map((list) => {
        return (
          <YRMenu.Item key={list.name} data={list}>
            {list.label}
          </YRMenu.Item>
        );
      })}
    </YRMenu>
  );

  return (
    <>
      {!op ? (
        <YRDropdown disabled={type === 'detail'} overlay={operatorMenu} placement="bottomLeft">
          <span className={style['operator']}>请输入比较运算符</span>
        </YRDropdown>
      ) : (
        <>
          <YRDropdown disabled={type === 'detail'} overlay={operatorMenu} placement="bottomLeft">
            <span className={style['operator']}>{op}</span>
          </YRDropdown>
          {!['为空', '不为空'].includes(op) && (
            <span className={style['input-value']}>
              <InputValue rightValue={rightValue || {}} changeType={changeType} />
              <span style={{ margin: '0 0 0 5px', color: '#0068c4' }}>{operatorNameItem[op].desc}</span>
            </span>
          )}
        </>
      )}
    </>
  );
};

export default ComparisonOperator;
