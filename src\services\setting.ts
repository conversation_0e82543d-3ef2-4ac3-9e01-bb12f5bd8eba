/**
 * @Author: wangyw26123
 * @Description: 设置模块相关接口
 * @Date: Created in 2022-12-13 16:31:32
 * @Modifed By:
 */

import { request } from '@yr/util';

/**
 * 查询机构树
 * @param param
 */
export function queryUserOrganTree(param?: any) {
  return request('/base/IOrganQuery/queryOrganTreeWithPermit', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '查询机构树'
  });
}

/**
 * 查询hr机构列表
 * @param param
 */
export function queryHrOrganList(param?: any) {
  return request('/base/IOrganQuery/queryHrOrganList', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '查询hr机构列表'
  });
}

/**
 * 机构新增
 * @param param
 */
export function addOrgan(param) {
  return request('/base/IOrgan/addOrgan', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '机构新增'
  });
}

/**
 * 修改机构
 * @param param
 */
export function modifyOrgan(param) {
  return request('/base/IOrgan/modifyOrgan', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '修改机构'
  });
}

/**
 * 修改机构状态
 * @param param
 */
export function changeOrganStatus(param) {
  return request('/base/IOrgan/changeOrganStatus', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '修改机构状态'
  });
}

/**
 * 删除机构树
 * @param param
 */
export function delOrgan(param) {
  return request('/base/IOrgan/delOrgan', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '机构停启用'
  });
}

/**
 * 查询机构详情
 * @param param
 */
export function queryOrganInfoByOrganId(param) {
  return request('/base/IOrganQuery/queryOrganInfoByOrganId', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '查询机构详情'
  });
}

/**
 * 机构层级调整
 * @param param
 */
export function changeOrganLevelApprove(param) {
  return request('/management/changeOrganLevelApprove', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '机构层级调整'
  });
}

/**
 * 查询账务机构映射
 * @param param
 */
export function queryOrganRelationPage(param) {
  return request('/base/IAcctOrganQuery/queryOrganRelationPage', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '查询账务机构映射'
  });
}

/**
 * 查询账务机构树
 * @param param
 */
export function queryAcctOrganTree(param?: any) {
  return request('/base/IAcctOrganQuery/queryOrganTree', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '查询账务机构树'
  });
}

/**
 * 新增账务机构映射
 * @param param
 */
export function addOrgRelateList(param) {
  return request('/base/IOrganAcctRelate/addOrgRelateList', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '新增账务机构映射'
  });
}

/**
 * 更新账务机构映射
 * @param param
 */
export function updateOrgRelateList(param) {
  return request('/base/IOrganAcctRelate/updateOrgRelateList', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '更新账务机构映射'
  });
}

/**
 * 删除账务机构映射
 * @param param
 */
export function delOrgRelate(param) {
  return request('/base/IOrganAcctRelate/delOrgRelate', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '删除账务机构映射'
  });
}

/**
 * 角色查询
 * @param param
 */
export function queryRolePage(param) {
  return request('/base/IRoleQuery/queryPage', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '角色查询'
  });
}

/**
 * 角色新增/修改
 * @param param
 */
export function addOrUpdateRole(param) {
  return request('/base/IRole/addOrUpdateRole', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '角色新增/修改'
  });
}

/**
 * 角色记录查询
 * @param param
 */
export function queryRoleLogPage(param) {
  return request('/base/IRoleQuery/queryRoleLogPage', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '角色记录查询'
  });
}

/**
 * 修改角色状态
 * @param param
 */
export function changeRoleStatus(param) {
  return request('/base/IRole/changeRoleStatus', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '修改角色状态'
  });
}

/**
 * 角色删除
 * @param param
 */
export function delRole(param) {
  return request('/base/IRole/delRole', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '角色删除'
  });
}

/**
 * 角色查询-全量数据
 * @param param
 */
export function queryAllRoleList(param?: any) {
  return request('/base/IRoleQuery/queryList', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '角色查询-全量数据'
  });
}

/**
 * 角色详情查询
 * @param param
 */
export function queryRoleDetail(param?: any) {
  return request('/base/IRoleQuery/detail', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '角色详情查询'
  });
}

/**
 * 查询当前操作人权限下的用户
 * @param param
 */
export function queryUserList(param?: any) {
  return request('/base/IUserQuery/queryUserList', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '查询当前操作人权限下的用户'
  });
}

/**
 * 添加修改成员
 * @param param
 */
export function allotRole(param?: any) {
  return request('/base/IRole/allotRole', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '添加修改成员'
  });
}

/**
 * 查询当前权限下的权限组列表
 * @param param
 */
export function queryPermitGroupList(param?: any) {
  return request('/base/IPermitQuery/queryPermitGroupList', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '查询当前权限下的权限组列表'
  });
}

/**
 * 新增权限组
 * @param param
 */
export function addPermitGroup(param) {
  return request('/base/IPermit/addPermitGroup', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '新增权限组'
  });
}

/**
 * 修改权限组
 * @param param
 */
export function modifyPermitGroup(param) {
  return request('/base/IPermit/modifyPermitGroup', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '修改权限组'
  });
}

/**
 * 删除权限组
 * @param param
 */
export function deletePermitGroup(param) {
  return request('/base/IPermit/deletePermitGroup', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '删除权限组'
  });
}

/**
 * 查询全部权限
 * @param param
 */
export function queryAllPermit(param) {
  return request('/base/IPermitQuery/queryAllPermit', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '查询全部权限'
  });
}

/**
 * 查询权限组权限
 * @param param
 */
export function queryPermitGroupWithPermit(param) {
  return request('/base/IPermitQuery/queryPermitGroupWithPermit', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '查询权限组权限'
  });
}

/**
 * 查询数据字典列表
 * @param param
 */
export function queryDictList(
  param: Partial<{
    dictId: string;
    dictKey: string;
    createTime: string;
    updateTime: string;
    dictType: string;
    dictName: string;
    isChildNode: string;
    pageNum: number;
  }>
) {
  return request('/IMdataDictQuery/queryDictList', {
    param,
    module: MODULES.mdata,
    serviceScene: SCENES.query,
    serviceName: '查询数据字典列表'
  });
}

/**
 * 更新数据字典
 * @param param
 */
export function modifyDict(param: {
  dictId: string;
  dictKey: string;
  createTime?: string;
  updateTime?: string;
  dictType?: string;
  dictName: string;
  isChildNode: string;
  pageNum?: number;
}) {
  return request('/IMdataDict/modifyDict', {
    param,
    module: MODULES.mdata,
    serviceScene: SCENES.query,
    serviceName: '更新数据字典'
  });
}

/**
 * 删除数据字典
 * @param param
 */
export function deleteDict(param: { dictId: string }) {
  return request('/IMdataDict/deleteDict', {
    param,
    module: MODULES.mdata,
    serviceScene: SCENES.query,
    serviceName: '删除数据字典'
  });
}

/**
 * 分页查询数据字典项
 * @param param
 */
export function selectPage(param: {
  dictKey: string;
  createTime?: string;
  updateTime?: string;
  enableStatus?: string;
  parentItemKey?: string;
  itemKey?: string;
  itemName?: string;
  isChildNode?: string;
  pageNum: number;
  pageSize: number;
}) {
  return request('/IMdataDictItemQuery/selectPage', {
    param,
    module: MODULES.mdata,
    serviceScene: SCENES.query,
    serviceName: '分页查询数据字典项'
  });
}

/**
 * 批量查询数据字典
 * @param param
 */
export function batchQueryDictItem(param: { dictKeys: string[] }) {
  return request('/IMdataDictItemQuery/batchQueryDictItem', {
    param,
    module: MODULES.mdata,
    serviceScene: SCENES.query,
    serviceName: '批量查询数据字典'
  });
}

/**
 * 新增数据字典
 * @param param
 */
export function addDict(param: {
  dictKey: string;
  createTime?: string;
  updateTime?: string;
  dictType?: string;
  dictName: string;
  isChildNode: string;
  pageNum?: number;
}) {
  return request('/IMdataDict/addDict', {
    param,
    module: MODULES.mdata,
    serviceScene: SCENES.query,
    serviceName: '新增数据字典'
  });
}

/**
 * 新增数据字典项
 * @param param
 */
export function addDictItem(param: { itemKey: string; itemName: string; isChildNode: 'Y' | 'N'; dictKey: string }) {
  return request('/IMdataDictItem/addDictItem', {
    param,
    module: MODULES.mdata,
    serviceScene: SCENES.query,
    serviceName: '新增数据字典项'
  });
}

/**
 * 更新数据字典项
 * @param param
 */
export function modifyDictItem(param: { dictKey: string; itemKey: string; itemName: string; isChildNode: 'Y' | 'N' }) {
  return request('/IMdataDictItem/modifyDictItem', {
    param,
    module: MODULES.mdata,
    serviceScene: SCENES.query,
    serviceName: '更新数据字典项'
  });
}

/**
 * 分页查询业务配置
 * @param param
 */
export function selectPageIMdataBizParamQuery(
  param: Partial<{
    paramId: string;
    createTime: string;
    updateTime: string;
    operatorId: string;
    ownOrganId: string;
    organName: string;
    bizLineType: string;
    paramCode: string;
    paramValue: string;
    paramName: string;
    enableStatus: string;
    deleted: string;
    remark: string;
    pageNum: number;
    pageSize: number;
  }>
) {
  return request('/IMdataBizParamQuery/selectPage', {
    param,
    module: MODULES.mdata,
    serviceScene: SCENES.query,
    serviceName: '分页查询业务配置'
  });
}

interface IBizParam {
  paramId: string;
  createTime?: string;
  updateTime?: string;
  operatorId?: string;
  ownOrganId?: string;
  organName?: string;
  bizLineType: string;
  paramCode: string;
  paramValue: string;
  paramName: string;
  enableStatus?: string;
  deleted?: string;
  remark?: string;
  pageNum?: number;
  pageSize?: number;
}

/**
 * 新增业务参数配置
 * @param param
 */
export function addBizParam(param: IBizParam) {
  return request('/IMdataBizParam/addBizParam', {
    param,
    module: MODULES.mdata,
    serviceScene: SCENES.query,
    serviceName: '新增业务参数配置'
  });
}

/**
 * 更新业务参数配置
 * @param param
 */
export function modifyBizParam(param: Omit<IBizParam, 'paramId'>) {
  return request('/IMdataBizParam/modifyBizParam', {
    param,
    module: MODULES.mdata,
    serviceScene: SCENES.query,
    serviceName: '更新业务参数配置'
  });
}

/**
 * 删除业务参数配置
 * @param param
 */
export function deleteBizParam(param: Pick<IBizParam, 'paramId'>) {
  return request('/IMdataBizParam/deleteBizParam', {
    param,
    module: MODULES.mdata,
    serviceScene: SCENES.query,
    serviceName: '删除业务参数配置'
  });
}

/**
 * 启用停用业务参数配置
 * @param param
 */
export function changeBizParamStatus(param: Pick<IBizParam, 'paramId'> & { enableStatus: 'Y' | 'N' }) {
  return request('/IMdataBizParam/changeBizParamStatus', {
    param,
    module: MODULES.mdata,
    serviceScene: SCENES.query,
    serviceName: '启用停用业务参数配置'
  });
}

/**
 * 数据字典项启用停用
 * @param param
 */
export function changeDictItemStatus(param: { enableStatus: 'Y' | 'N'; itemId: string }) {
  return request('/IMdataDictItem/changeDictItemStatus', {
    param,
    module: MODULES.mdata,
    serviceScene: SCENES.query,
    serviceName: '数据字典项启用停用'
  });
}

/**
 * 分页查询汇率
 * @param param
 */
export function rateSelectPage(param) {
  return request('/IMdataErateInfoQuery/selectPage', {
    param,
    module: MODULES.mdata,
    serviceScene: SCENES.apply,
    serviceName: '分页查询汇率'
  });
}

/**
 * 查询父级机构列表
 * @param param
 */
export function queryParentOrganList(param) {
  return request('/base/IOrganQuery/queryParentOrganList', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '查询父级机构列表'
  });
}

/**
 * 查询最新的操作记录列表
 * @param param
 */
export function queryLastModifyRecordList(param) {
  return request('/IMdataHolidayInfoQuery/queryLastModifyRecordList', {
    param,
    module: MODULES.mdata,
    serviceScene: SCENES.apply,
    serviceName: '查询最新的操作记录列表'
  });
}
/**
 * 假期维护
 * @param param
 */
export function modifyHolidayInfo(param) {
  return request('/IMdataHolidayInfo/modifyHolidayInfo', {
    param,
    module: MODULES.mdata,
    serviceScene: SCENES.apply,
    serviceName: '假期维护'
  });
}

/**
 * 查询假期维护信息列表
 * @param param
 */
export function queryHolidayByYearMonth(param) {
  return request('/IMdataHolidayInfoQuery/queryHolidayByYearMonth', {
    param,
    module: MODULES.mdata,
    serviceScene: SCENES.apply,
    serviceName: '查询假期维护信息列表'
  });
}
/**
 * 分页查询利率
 * @param param
 */
export function intrRateSelectPage(param) {
  return request('/IMdataIntrRateQuery/selectPage', {
    param,
    module: MODULES.mdata,
    serviceScene: SCENES.apply,
    serviceName: '分页查询利率'
  });
}

// 查询下发角色权限组
export function queryTenantPermitGroup(param?: any) {
  return request('/base/IPermitQuery/queryTenantPermitGroup', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '查询下发角色权限组'
  });
}

// 查询风险探测分组列表
export function queryProbeGroupPage(param?: any) {
  return request('/v1/probeGroupQuery/selectPage', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '查询风险探测分组列表'
  });
}

// 查询风险探测场景列表
export function queryProbeScene(param?: any) {
  return request('/v1/probeSceneQuery/selectPage', {
    param,
    module: MODULES.corp,
    serviceScene: SCENES.query,
    serviceName: '查询风险探测场景列表'
  });
}

// 规则条目和场景树查询
export function queryRuleTree(param?: any) {
  return request('/v1/treeQuery/queryTree', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '规则条目和场景树查询'
  });
}

// 规则条目分页查询
export function ruleItemPage(param?: any) {
  return request('/v1/ruleQuery/ruleItemPage', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '规则条目分页查询'
  });
}

// 规则条目新增修改
export function saveOrModifyRuleItem(param?: any) {
  return request('/v1/ruleOperate/saveOrModifyRuleItem', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.apply,
    serviceName: '规则条目新增修改'
  });
}

// 规则条目启用停用删除
export function ruleStatusChange(param?: any) {
  return request('/v1/ruleOperate/ruleStatusChange', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.apply,
    serviceName: '规则条目启用停用删除'
  });
}

// 规则历史版本查询
export function historyRecordQuery(param?: any) {
  return request('/v1/ruleQuery/historyRecordQuery', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '规则历史版本查询'
  });
}

// 引用记录查询
export function quoteRecord(param?: any) {
  return request('/v1/ruleQuery/quoteRecord', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '引用记录查询'
  });
}

// 场景分页查询
export function scenePage(param?: any) {
  return request('/v1/sceneQuery/scenePage', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '场景分页查询'
  });
}

// 场景配置查询
export function sceneConfig(param?: any) {
  return request('/v1/sceneQuery/sceneConfig', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '场景配置查询'
  });
}

// 场景新增修改
export function saveOrModifyScene(param?: any) {
  return request('/v1/sceneOperate/saveOrModifyScene', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '场景新增修改'
  });
}

// 场景详情查询
export function sceneDetail(param?: any) {
  return request('/v1/sceneQuery/sceneDetail', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '场景详情查询'
  });
}

// 场景启用停用删除
export function sceneStatusChange(param?: any) {
  return request('/v1/sceneOperate/sceneStatusChange', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '场景启用停用删除'
  });
}

export function queryByComCode(param?: any) {
  return request('/dc/threeConf/queryByComCode', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '数据源管理'
  });
}

export function threeConfTextGet(param?: any) {
  return request('/dc/threeConf/test/get', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '数据源管理'
  });
}

export function threeConfText(param?: any) {
  return request('/dc/threeConf/test', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '数据源管理'
  });
}

export function threeComList(param?: any) {
  return request('/dc/threeCom/list', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '数据源管理'
  });
}

export function threeComDel(param?: any) {
  return request('/dc/threeCom/del', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '数据源管理'
  });
}

export function threeComModify(param?: any) {
  return request('/dc/threeCom/modify', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '数据源管理'
  });
}

export function dcDict(param?: any) {
  return request('/dc/dict', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '数据源管理'
  });
}

export function threeConfPage(param?: any) {
  return request('/dc/threeConf/page', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '数据源管理'
  });
}

export function threeQueryTree(param?: any) {
  return request('/dc/three/queryTree', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '数据源管理'
  });
}

export function queryRespConf(param?: any) {
  return request('/dc/threeConf/queryRespConf', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '数据源管理'
  });
}

export function threeConfRemove(param?: any) {
  return request('/dc/threeConf/remove', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '数据源管理'
  });
}

export function threeConfSwitch(param?: any) {
  return request('/dc/threeConf/switch', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '数据源管理'
  });
}

export function threeConfSetMock(param?: any) {
  return request('/dc/threeConf/setMock', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '数据源管理'
  });
}

export function addThreeConf(param?: any) {
  return request('/v1/threeOperate/addThreeConf', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '数据源管理'
  });
}

export function threeConfSave(param?: any) {
  return request('/dc/threeConf/save', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '数据源管理'
  });
}

export function modifyRespConf(param?: any) {
  return request('/v1/threeOperate/modifyRespConf', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '数据源管理'
  });
}

// 变量列表查询
export function variableQuery(param?: any) {
  return request('/v1/variableQuery/queryPage', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '变量列表查询'
  });
}

// 变量新增修改
export function saveVariable(param?: any) {
  return request('/v1/variableOperate/saveVariable', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '变量新增修改'
  });
}

// 变量删除
export function deleteVariable(param?: any) {
  return request('/v1/variableOperate/deleteVariable', {
    param,
    module: { cname: '风险探测', prefix: '/hsjry/risk/detection' },
    serviceScene: SCENES.query,
    serviceName: '变量删除'
  });
}
