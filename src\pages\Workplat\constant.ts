/*
 * yunrong.cn Inc. Copyright (c) 2014-2020 All Rights Reserved
 */

/**
 * 非进件审批状态枚举
 */
const EnumCommonApprovalStatus = {
  TODO: { code: '010', desc: '待审批', color: 'default' },
  DOING: { code: '020', desc: '审批中', color: 'processing' },
  ADOPT: { code: '030', desc: '审批通过', color: 'success' },
  REFUSE: { code: '040', desc: '审批拒绝', color: 'error' }
};

/**
 * 非进件审批状态枚举
 */
const EnumCommonApprovalStatusColor = {
  '010': { desc: '待审批', color: 'default' },
  '020': { desc: '审批中', color: 'processing' },
  '030': { desc: '审批通过', color: 'success' },
  '040': { desc: '审批拒绝', color: 'error' }
};

/**
 * 平衡校验标识
 */
const EnumBool = {
  N: { code: 'N', desc: '否' },
  Y: { code: 'Y', desc: '是' }
};

const EnumCommonStatus = {
  '010': { desc: '草稿', color: 'default' },
  '020': { desc: '完成', color: 'success' }
};

/**
 * 审核结果
 */
const EnumApproveResultColor = {
  '001': { color: 'process', desc: '审批中' },
  D: { color: 'error', desc: '拒绝' },
  A: { color: 'success', desc: '通过' },
  C: { color: 'invalid', desc: '取消' }
};

// 按钮权限
const EnumButtonAuthority = {
  TRANSFER: '001', // 取消
  REJECT: '002' // 驳回
};

export {
  EnumCommonStatus,
  EnumCommonApprovalStatus,
  EnumBool,
  EnumCommonApprovalStatusColor,
  EnumApproveResultColor,
  EnumButtonAuthority
};
