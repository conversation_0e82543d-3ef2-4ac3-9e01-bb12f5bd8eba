/*
 * @Author: DJscript
 * @Date: 2023-02-09 09:49:22
 * @LastEditTime: 2023-02-21 09:13:01
 * @FilePath: /kylin-admin/src/components/EditPlusTable/Cell.tsx
 * @Description: ...
 */
import React, { forwardRef, useMemo } from 'react';
import { YRDatePicker, YRDict, YRForm, YRInput, YRRadio, YRSelect } from 'yrantd';
import { pick, isEmpty, omit } from 'lodash-es';
import moment from 'moment';
import styles from './index.module.less';

const run = (p1, p2) => (typeof p1 === 'function' ? p1(p2) : p1);

const Com = forwardRef((props: any, ref) => {
  switch (props.valueType) {
    case 'input':
      return <YRInput {...props} ref={ref} />;
    case 'select':
      return (
        <YRSelect ref={ref} {...props}>
          {props?.options?.map((item) => (
            <YRSelect.Option value={item.value} key={item.value}>
              {item.label}
            </YRSelect.Option>
          ))}
        </YRSelect>
      );
    case 'dictSelect':
      return <YRDict.Select ref={ref} {...props} />;
    case 'radio':
      return <YRRadio ref={ref} {...props} />;
    case 'datePicker':
      return (
        <YRDatePicker ref={ref} {...omit(props, ['mode'])} value={props.value ? moment(props.value) : props.value} />
      );
    case 'component':
      return props.component({ ...omit(props, ['component']), ref });
    default:
      return <YRInput ref={ref} {...props} />;
  }
});

const ComRead = (props) => {
  const value = props?.record?.[props?.dataIndex];
  switch (props.valueType) {
    case 'select':
      return props?.options?.find((item) => item.value === value)?.label || '-';
    case 'dictSelect':
      return <YRDict.Text dictkey={props?.dictkey} defaultValue={value} />;
    case 'radio':
      return typeof value === 'boolean' && value ? '是' : '否';
    default:
      return value || props.children || '-';
  }
};

const Cell = (props: any = {}) => {
  const _formItemProps = props.formItemProps ? props.formItemProps : {};
  const _componentProps = props.componentProps ? props.componentProps : {};
  const exportParams = {
    record: props.record,
    rowIndex: props.rowIndex,
    dataIndex: props.dataIndex
  };
  const formItemProps = run(_formItemProps, exportParams);
  const componentProps = run(_componentProps, exportParams);
  const defaultProps = {
    ...pick(props, ['options', 'dictkey', 'mode', 'valueType', 'component']),
    ...componentProps
  };
  return props.valueType && ['add', 'edit'].includes(props.mode) ? (
    <td {...props} className={styles.editorTable}>
      <YRForm.Item
        normalize={
          ['datePicker'].includes(props?.valueType)
            ? (value) => (value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : value)
            : undefined
        }
        initialValue={
          !isEmpty(formItemProps.initialValue) ? formItemProps.initialValue : props.record?.[props.dataIndex]
        }
        {...formItemProps}
        name={[props.fieldName, props?.rowIndex, props.dataIndex].flat(Infinity).filter((item) => item !== undefined)}
      >
        <Com {...defaultProps} style={{ width: '100%' }} />
      </YRForm.Item>
    </td>
  ) : (
    <td {...props}>
      <ComRead {...props}>{props?.record?.[props?.dataIndex] ?? props.children}</ComRead>
    </td>
  );
};

export default Cell;
