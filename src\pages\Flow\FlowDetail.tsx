/**
 * @Author: wangyw26123
 * @Description: 流程图编辑
 * @Date: Created in 2022-04-22 10:22:21
 * @Modifed By:
 */

import React, { useState, useRef, useEffect } from 'react';
import { YRButton, YRDict, YREmpty, YRMessage } from 'yrantd';
import { useSetState } from 'ahooks';
import { checkAuth, downloadFileFromFilestream, getDictList, openNewTab, downloadByUrl, router } from '@/utils/utils';
import { jsonParse } from 'yr-loan-antd/lib/util';
import styles from './index.module.less';
import XflowInstance from '@/pages/Flow/XFlow/XflowInstance';
import { EnumYRXFlowViewMode } from '@yr/xflow/lib/declare';
import store from '@/store';
import {
  YRXFlow,
  YRXFlowAttrPanel,
  YRXFlowNodeAttrPanel,
  YRXFlowCanvasAttrPanel,
  YRXFlowEdgeAttrPanel,
  YRXFlowInfoPanel,
  graphRender
} from '@yr/xflow';
import { BaseNode, State } from './XFlow/Interface';

import CustomeNode from '@/pages/Flow/XFlow/components/CustomeNode';
import FullPageLayout from '@/components/FullPageLayout';
import NodeAttrPanel from './XFlow/XFlowEdit/NodeAttrPanel';
import CanvasAttrPanel from './XFlow/XFlowEdit/CanvasAttrPanel';
import EdgeAttrPanel from './XFlow/XFlowEdit/EdgeAttrPanel';
import { fileExport } from '@/services/flow';

import { M02 } from '@permit/flow';

const { queryFlowDiagramResourceDetail } = M02.interfaces;

const pageTitle = {
  add: '新建流程图',
  modify: '修改流程图',
  detail: '流程图详情'
};

const position = {
  width: 300,
  top: 40,
  bottom: 0,
  right: 0
};

const FlowDetail = (props) => {
  const {
    location: {
      query: { id }
    }
  } = props;
  const [state, setState] = useSetState<State>({
    isDisabled: false,
    btnLoading: false,
    pageLoading: false,
    flowName: '',
    flowNumber: '',
    id: '',
    flowDesc: '',
    sceneType: '',
    status: '',
    updateTime: '',
    version: '',
    modelResource: '',
    graphData: {
      edges: [],
      nodes: []
    }
  });

  const graphApp = useRef<any>();

  const [flowState, dispatchers] = store.useModel('flow');

  const { globalFormData, processBaseInfo } = flowState;
  const { setProcessBaseInfo } = dispatchers;

  const handleQueryFlowDiagramResourceDetail = () => {
    if (id) {
      // setState({ pageLoading: true });
      queryFlowDiagramResourceDetail({ id, modelType: '1' }).then((res) => {
        setState({ pageLoading: false });
        if (res?.success) {
          let graphData = { nodes: [], edges: [] };
          const modelResource = res?.data?.modelResource;
          if (modelResource) {
            graphData = jsonParse(res?.data?.modelResource)?.graphData;
            graphData.nodes = graphData.nodes.map((node) => ({
              ...node,
              component: CustomeNode
            }));
          }
          const processInfo = {
            ...res?.data,
            graphData
          };
          setState(processInfo);

          setProcessBaseInfo(processInfo);
        }
      });
    }
  };

  const onToEdit = () => {
    router.push({
      pathname: '/setting/flow/edit',
      query: { id }
    });
  };

  // 导出
  const exportFile = () => {
    const params = {
      processId: state?.modelNumber || '',
      version: state?.version || ''
    };
    fileExport(params).then((res) => {
      if (res?.data) {
        YRMessage.success('导出成功');
        downloadByUrl(res?.data, '流程图');
      }
    });
  };

  const getRightOper = () => {
    const com = (
      <>
        <YRButton type="primary" onClick={exportFile}>
          导出
        </YRButton>
        <YRButton
          type="primary"
          // check="/process/manage/edit"
          onClick={onToEdit}
          check={M02.E04}
        >
          修改
        </YRButton>
      </>
    );
    return com;
  };

  // xflow初始化完成后调用
  const onLoad = async (app) => {
    graphApp.current = app;
    const instance = await app.getGraphInstance();
    XflowInstance.setApp(app, instance);

    console.log(graphApp);

    await graphRender(graphApp.current, state.graphData);

    // 点亮节点
    XflowInstance?.highlightNode({
      nodeId: 'node-3658d1ba-937c-4711-ae51-06fa3d230aab',
      stroke: 'red',
      strokeWidth: 10
    });
  };

  const getLeft = () => {
    const extInfo = [
      {
        key: '流程编号：',
        value: state?.modelNumber || CONST.null
      },
      {
        key: '流程名称：',
        value: state?.modelName || CONST.null
      },
      {
        key: '流程状态：',
        value: state?.status || CONST.null
      },
      {
        key: '流程版本：',
        value: state?.version || CONST.null
      },
      {
        key: '更新时间：',
        value: state?.updateTime || CONST.null
      }
    ];

    return {
      title: '流程图详情',
      extInfo,
      goBack: () => router.push('/setting/flow')
    };
  };

  useEffect(() => {
    handleQueryFlowDiagramResourceDetail();
  }, [id]);

  return (
    <FullPageLayout loading={state.pageLoading} left={getLeft()} right={getRightOper()}>
      <div className={styles.configEdit}>
        {state.graphData.nodes.length > 0 ? (
          <YRXFlow
            onLoad={onLoad}
            mode={EnumYRXFlowViewMode.VIEW}
            displayConfig={{
              ATTR_PANEL: true,
              INFO_PANEL: true
            }}
            canvasGraphConfig={(config) => {
              config.setX6Config({
                connecting: {
                  router: 'manhattan'
                },
                interacting: {
                  nodeMovable: false
                }
              });
            }}
          >
            <YRXFlowAttrPanel position={position}>
              <YRXFlowNodeAttrPanel>
                {/* 节点属性面板 */}
                <NodeAttrPanel globalDisabled />
              </YRXFlowNodeAttrPanel>
              <YRXFlowEdgeAttrPanel>
                {/* 边属性面板 */}
                <EdgeAttrPanel globalDisabled />
              </YRXFlowEdgeAttrPanel>
              <YRXFlowCanvasAttrPanel>
                {/* 画布属性面板 */}
                <CanvasAttrPanel globalDisabled />
              </YRXFlowCanvasAttrPanel>
            </YRXFlowAttrPanel>
          </YRXFlow>
        ) : (
          <YREmpty />
        )}
      </div>
    </FullPageLayout>
  );
};

export default FlowDetail;
