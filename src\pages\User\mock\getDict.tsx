import React from 'react';
import res from './dict.json';

const getDict = (dictKeys: string[]) => {
  let dicts = {};
  if (res && res.data.dictItemMap) {
    dicts = res.data.dictItemMap;
    dictKeys.forEach((dictkey) => {
      const dictValue = dicts[dictkey];
      if (typeof dictValue !== 'undefined' && dictValue != null) {
        sessionStorage[`dict_${dictkey}`] = JSON.stringify(dictValue);
      }
    });
  }

  return (Com) => {
    return (props) => <Com dictMap={dicts} {...props} />;
  };
};

export default getDict;
