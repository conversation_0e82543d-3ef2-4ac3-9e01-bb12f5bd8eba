/*
 * @Author: liaokt
 * @Description:
 * @Date: 2024-10-18 10:07:31
 * @LastEditors: liaokt
 * @LastEditTime: 2024-10-18 16:54:09
 */
/**
 * @Author: wangyw26123
 * @Description: 高级-派件配置
 * @Date: Created in 2022-04-27 14:08:32
 * @Modifed By:
 */

import React, { useEffect, useState } from 'react';
import { YRSelect, YRForm, YRRadio } from 'yrantd';

const FormItem = YRForm.Item;
const { Option } = YRSelect;

const radioStyle = {
  display: 'block',
  height: '30px',
  lineHeight: '22px'
};

const AuthConfig = (props) => {
  const {
    targetData: { nodeFormData },
    globalDisabled
  } = props;

  return (
    <>
      <FormItem
        name={['properties', 'auth']}
        label="授权开关"
        initialValue={nodeFormData?.properties?.auth || '0'}
        rules={[
          {
            required: true,
            whitespace: true,
            message: '请选择授权开关'
          }
        ]}
      >
        <YRRadio.Group disabled={globalDisabled}>
          <YRRadio style={radioStyle} value="1">
            开
          </YRRadio>
          <YRRadio style={radioStyle} value="0">
            关
          </YRRadio>
        </YRRadio.Group>
      </FormItem>
    </>
  );
};

export default AuthConfig;
