/*
 * @Author: DJscript
 * @Date: 2023-03-02 10:01:19
 * @LastEditTime: 2023-03-29 16:41:14
 * @FilePath: /kylin-admin/src/pages/Setting/DataDictionary/index.tsx
 * @Description: 数据字典
 */
import { queryDictList } from '@/services/setting';
import { useEventListener, useRequest } from 'ahooks';
import React, { useMemo, useRef } from 'react';
import { YREasyUseModal, YRFlexPageLayout } from 'yrantd';
import { AntdContextProvider } from './Context';
import DictIndex from './Dict';
import DictItemIndex from './DictItem';
import DictionaryList from './DictionaryList';

import { DefaultNode, KeyEnum, TreeNode } from './Interface';

type AntdContextProviderValue = React.ComponentProps<typeof AntdContextProvider>['value'];

const DataDictionary = () => {
  const [selectedKey, setSelectedKey] = React.useState<Partial<TreeNode>>(DefaultNode);

  const originalList = useRef<TreeNode[]>([]);

  const handleSuccess = (res) => {
    originalList.current = res;
  };

  const antdTreeState = useRequest<TreeNode[], any>(
    (p) => queryDictList(p).then((res) => (res.success ? res.data : [])),
    {
      onSuccess: handleSuccess
    }
  );

  const memoProviderValue = useMemo<AntdContextProviderValue>(
    () => ({
      antdTreeProps: { ...antdTreeState, originalList: originalList.current },
      selectedState: [selectedKey, setSelectedKey]
    }),
    [antdTreeState, selectedKey, setSelectedKey]
  );

  useEventListener('keydown', (ev) => {
    if (ev.code === 'Escape' && selectedKey[KeyEnum.KEY] !== 'all') {
      setSelectedKey(DefaultNode);
    }
  });

  return (
    <YRFlexPageLayout>
      <YREasyUseModal.Provider>
        <AntdContextProvider value={memoProviderValue}>
          <YRFlexPageLayout.Sider title="字典列表">
            <DictionaryList />
          </YRFlexPageLayout.Sider>
          <YRFlexPageLayout.Main
            breadcrumb={{
              items: [DefaultNode, ...(selectedKey[KeyEnum.KEY] !== 'all' ? [selectedKey] : [])],
              fieldNames: { label: KeyEnum.LABEL, key: KeyEnum.KEY },
              disabled: (item) => item === selectedKey,
              onClick: (item) => {
                setSelectedKey(item);
              }
            }}
          >
            {selectedKey[KeyEnum.KEY] === 'all' ? <DictIndex /> : <DictItemIndex />}
          </YRFlexPageLayout.Main>
        </AntdContextProvider>
      </YREasyUseModal.Provider>
    </YRFlexPageLayout>
  );
};

export default DataDictionary;
