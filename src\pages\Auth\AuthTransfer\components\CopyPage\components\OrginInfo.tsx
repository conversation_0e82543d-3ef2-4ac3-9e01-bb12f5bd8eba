/*
 * @Author: liaokt
 * @Description: 引入源信息
 * @Date: 2024-09-25 10:21:16
 * @LastEditors: liaokt
 * @LastEditTime: 2024-10-17 19:06:52
 */
import React from 'react';
import { YRCard, YRClassificationLayout, YRDict, YRForm, YRInput, YRTable } from 'yrantd';
import { columns } from '../../../useIndex';

const ApplyInfo = (props) => {
  const { id, title } = props;

  return (
    <YRCard id={id} title={title}>
      <YRClassificationLayout.Space>
        <YRClassificationLayout title="基本信息">
          <YRForm.Row>
            <YRForm.Item label="机构" name={['']}>
              <YRInput disabled />
            </YRForm.Item>
            <YRForm.Item label="角色" name={['thirdApplyId']}>
              <YRInput disabled />
            </YRForm.Item>
            <YRForm.Item label="授权类型" name={['thirdApplyId']}>
              <YRDict.Select dictkey="AUTH_CATEGORY" disabled />
            </YRForm.Item>
          </YRForm.Row>
        </YRClassificationLayout>
        <YRClassificationLayout title="基础授权信息">
          <YRTable
            dataSource={[]}
            loading={false}
            columns={columns(() => {})?.filter((item) => {
              return item?.dataIndex !== 'operator';
            })}
            pagination={false}
          />
        </YRClassificationLayout>
      </YRClassificationLayout.Space>
    </YRCard>
  );
};

export default ApplyInfo;
