import React, { useState } from 'react';
import {
  YRModal,
  YRTable,
  YRTableProps,
  YRForm,
  YREasyUseModal,
  YRTreeSelect,
  YRMoneyShow,
  YRLink,
  YRDict,
  YRMessage,
  YRTooltip
} from 'yrantd';
import { useAntdTable, useRequest } from 'ahooks';
import { variableQuery, queryRuleTree } from '@/services/setting';

const ChooseVariableModal = (props) => {
  const modal = YREasyUseModal.useModal();
  const [form] = YRForm.useForm();
  const { okCallback } = props;
  const [treeData, setTreeData] = useState([]);
  const [selectedIds, setSelectedIds] = useState([]);
  const [selectedItems, setSelectedItems] = useState([]);

  // 查询规则条目列表接口
  const { loading: queryLoading, run: queryTree } = useRequest(queryRuleTree, {
    onSuccess: (res) => {
      const { errorMessage, data } = res;
      if (data && data.length > 0) {
        setTreeData(data);
      }
    },
    defaultParams: [{ type: '001' }],
    debounceWait: 500
  });

  const { tableProps, run, params } = useAntdTable((p) => {
    delete p.extra;
    delete p.filters;
    return variableQuery({
      ...p,
      pageNum: p.current
    }).then((res) => {
      return res.success
        ? { list: res?.data?.list || [], total: res?.data?.total || 0 }
        : {
          list: [],
          total: 0
        };
    });
  }, {});

  const rowSelection = {
    type: 'radio',
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedIds(selectedRowKeys);
      setSelectedItems(selectedRows);
    },
    selectedRowKeys: selectedIds
  };

  const generateColumns = () => {
    const columns = [
      {
        title: '树名称',
        dataIndex: 'treeName',
        render: (value) => {
          return (
            <YRTooltip onlyShow title={value}>
              <div className="ellipsis">{value || CONST.null}</div>
            </YRTooltip>
          );
        }
      },
      {
        title: '变量code',
        dataIndex: 'variableCode',
        render: (value) => {
          return (
            <YRTooltip onlyShow title={value}>
              <div className="ellipsis">{value || CONST.null}</div>
            </YRTooltip>
          );
        }
      },
      {
        title: '变量名称',
        dataIndex: 'variableName',
        render: (value) => {
          return (
            <YRTooltip onlyShow title={value}>
              <div className="ellipsis">{value || CONST.null}</div>
            </YRTooltip>
          );
        }
      }
    ];

    return columns;
  };

  // 查询条件
  const formItemList = [
    {
      placeholder: '树编号',
      key: 'treeId',
      type: 'component',
      position: 'show',
      width: 250,
      component: (
        <YRTreeSelect
          showSearch
          style={{ width: 250 }}
          dropdownStyle={{ width: 250, maxHeight: 500, overflow: 'auto' }}
          placeholder="请选择树编号"
          allowClear
          fieldNames={{ label: 'treeName', value: 'treeId', children: 'treeDtoList' }}
          filterTreeNode={(inputValue, treeNode) =>
            treeNode.treeName.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
          }
          treeData={treeData}
          onSelect={(value) => {
            run({ ...params[0], treeId: value });
          }}
        />
      )
    }
  ];

  // 确认选择
  const submit = () => {
    if (!selectedIds || selectedIds.length === 0 || selectedItems.length === 0) {
      YRMessage.error('请选择参数');
      return;
    }
    okCallback(selectedItems);
    modal.remove();
  };

  return (
    <YRModal
      title="选择参数"
      onOk={submit}
      onCancel={modal.remove}
      open={modal.visible}
      destroyOnClose
      maskClosable={false}
      width={'60%'}
    >
      <YRForm form={form}>
        <YRTable
          {...tableProps}
          form={form}
          formItemList={formItemList}
          handleSearch={() => {
            const formValues = form.getFieldsValue() || {};
            run({ ...params[0], ...formValues });
          }}
          rowKey={'variableId'}
          rowSelection={rowSelection}
          columns={generateColumns()}
        />
      </YRForm>
    </YRModal>
  );
};

export default YREasyUseModal.create(ChooseVariableModal);
