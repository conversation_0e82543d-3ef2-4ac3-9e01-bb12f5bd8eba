@import '@/common.less';

.authRuleModal {
  .ant-modal-body {
    padding: 16px;
    max-height: 70vh;
    overflow-y: auto;
  }

  .ant-card {
    border-radius: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    
    .ant-card-head {
      background: #fafafa;
      border-bottom: 1px solid #f0f0f0;
      
      .ant-card-head-title {
        font-weight: 500;
        font-size: 14px;
        color: #262626;
      }
    }
    
    .ant-card-body {
      padding: 16px;
    }
  }

  .ant-form-item {
    margin-bottom: 16px;
  }

  .ant-table {
    .ant-table-thead > tr > th {
      background: #fafafa;
      font-weight: 500;
      color: #262626;
    }
    
    .ant-table-tbody > tr > td {
      padding: 8px 12px;
    }
  }

  .dimension-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
  }
}

.productTagsContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 8px;
}

.inputClickable {
  cursor: pointer;
}

.dimensionValueInput {
  width: 100%;
} 