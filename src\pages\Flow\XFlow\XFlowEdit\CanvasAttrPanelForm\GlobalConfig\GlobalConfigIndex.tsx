/**
 * @Author: wangyw26123
 * @Description: 全局-基本信息
 * @Date: Created in 2022-04-25 16:22:47
 * @Modifed By:
 */

import React, { useEffect, useState } from 'react';
import { YRButton, YRForm, YRInput } from 'yrantd';
import { PlusCircleOutlined } from '@ant-design/icons';
import store from '@/store';
import XFlowCollapse from '../../../components/XFlowCollapse';
import BasicInfo from './BasicInfo/BasicInfo';
import SignalConfig from './SignalConfig/SignalConfig';
import IncludeGroup from './IncludeGroup/IncludeGroup';
import RepulsionConfig from './RepulsionConfig/RepulsionConfig';
import ParamLibrary from './ParamsLibrary/index';

const { Panel } = XFlowCollapse;
const FormItem = YRForm.Item;

const signGroup = [
  {
    title: '基本信息',
    key: 'baseInfo',
    check: null,
    component(props) {
      return (
        <Panel forceRender key={this.key} header={this.title}>
          <BasicInfo {...props} />
        </Panel>
      );
    }
  },
  {
    title: '信号定义',
    key: 'signaldefinitions',
    check: null,
    component(props) {
      return (
        <Panel forceRender key={this.key} header={this.title}>
          <SignalConfig {...props} />
        </Panel>
      );
    }
  },
  {
    title: '互斥组',
    key: 'exclusionGroups',
    check: null,
    component(props) {
      return (
        <Panel forceRender key={this.key} header={this.title}>
          <RepulsionConfig {...props} />
        </Panel>
      );
    }
  },
  {
    title: '相同组',
    key: 'includeGroups',
    check: null,
    component(props) {
      return (
        <Panel forceRender key={this.key} header={this.title}>
          <IncludeGroup {...props} />
        </Panel>
      );
    }
  },
  {
    title: '参数库',
    key: 'paramLibrary',
    check: null,
    component(props) {
      return (
        <Panel forceRender key={this.key} header={this.title}>
          <ParamLibrary {...props} />
        </Panel>
      );
    }
  }
];

const BaseInfo = (props) => {
  const [state, dispatchers] = store.useModel('flow');
  const nodePanels = signGroup.map((com) => com.component(props)) || [];

  // console.log('GlobalConfigIndex', props, state, globalFormData);
  return (
    <>
      <XFlowCollapse defaultActiveKey={'baseInfo'}>{nodePanels}</XFlowCollapse>
    </>
  );
};

export default BaseInfo;
