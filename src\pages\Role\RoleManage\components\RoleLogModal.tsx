/**
 * @Author: wangyw26123
 * @Description: 角色管理-角色修改记录
 * @Date: Created in 2022-12-09 16:46:35
 * @Modifed By:
 */

import React, { useState, useEffect } from 'react';
import { countTableWidth } from '@yr/util';
import type { FormItemListProps, YRTableProps } from 'yrantd';
import { YRForm, YRTable, YRLink, YRModal, YRDict, YRTag } from 'yrantd';
import { M010301 } from '@permit/role';
import { checkAuth, openNewTab } from '@/utils/utils';
import { EnumGuardRoleTypeColor, EnumRoleBizLineColor, EnumRoleLevelColor } from '../../../../constant/StyleConst';

interface Pagination {
  total: number;
  pageNum: number;
  pageSize: number;
}

const { queryRoleLogPage } = M010301.E08.interfaces;
const defaultPagination = {
  pageNum: 1,
  pageSize: 10
};

const RoleManage = (props) => {
  const { visible, onCancel } = props;
  const [form] = YRForm.useForm();
  const [dataSource, setDataSource] = useState([]);
  const [pageLoading, setPageLoading] = useState(false);
  const [pagination, setPagination] = useState<Pagination>({
    total: 0,
    ...defaultPagination
  });

  const formItemList: FormItemListProps[] = [
    {
      placeholder: '角色名称',
      key: 'roleName',
      type: 'input',
      position: 'show'
    }
  ];

  const queryList = (param: any) => {
    if (!checkAuth(M010301.E08)) return;
    setPageLoading(true);
    queryRoleLogPage({
      ...param,
      operateType: '003'
    }).then((res: any) => {
      setPageLoading(false);
      if (res.success) {
        setPagination({
          pageNum: param.pageNum,
          pageSize: param.pageSize,
          total: res.data.total
        });
        setDataSource(res.data?.list || []);
      }
    });
  };

  useEffect(() => {
    visible && handleSearch(defaultPagination);
  }, [visible]);

  const handleSearch = ({ pageNum, pageSize }) => {
    form.validateFields().then((values) => {
      queryList({
        pageNum: pageNum || pagination.pageNum,
        pageSize: pageSize || pagination.pageSize,
        ...values
      });
    });
  };

  const onAdd = (mode: string, row?: any) => {
    if (checkAuth(M010301.E09)) {
      openNewTab({
        pathname: '/setting/role/role-manage/edit',
        query: {
          roleId: row?.roleId,
          mode
        }
      });
    }
  };

  const getTableColumns: YRTableProps['columns'] = [
    {
      title: '角色编号',
      dataIndex: 'roleId',
      width: 220,
      render: (value, row) => {
        if (value) {
          return (
            <YRLink type="primary" pseudoCheck={M010301.E09} onClick={() => onAdd('readPretty', row)}>
              {value}
            </YRLink>
          );
        }

        return CONST.null;
      }
    },
    {
      title: '角色名称',
      dataIndex: 'roleName',
      width: 220,
      render: (value) => value || CONST.null
    },
    {
      title: '角色级别',
      dataIndex: 'roleLevel',
      width: 120,
      render: (value: string) =>
        value ? (
          <YRTag color={EnumRoleLevelColor[value]}>
            <YRDict.Text dictkey="EnumRoleLevel" defaultValue={value} />
          </YRTag>
        ) : (
          CONST.null
        )
    },
    {
      title: '角色类型',
      dataIndex: 'roleType',
      width: 120,
      render: (value: string) =>
        value ? (
          <YRTag color={EnumGuardRoleTypeColor[value]}>
            <YRDict.Text dictkey="EnumGuardRoleType" defaultValue={value} />
          </YRTag>
        ) : (
          CONST.null
        )
    },
    {
      title: '角色所属条线',
      dataIndex: 'bizLine',
      width: 120,
      render: (value: string) =>
        value ? (
          <YRTag color={EnumRoleBizLineColor[value]}>
            <YRDict.Text dictkey="EnumRoleBizLine" defaultValue={value} />
          </YRTag>
        ) : (
          CONST.null
        )
    },
    {
      title: '操作人',
      dataIndex: 'operatorName',
      width: 120,
      render: (value) => value || CONST.null
    },
    {
      title: '操作机构',
      dataIndex: 'ownOrganId',
      width: 220,
      render: (value) => value || CONST.null
    },
    {
      title: '角色描述',
      dataIndex: 'roleDesc',
      width: 220,
      render: (value) => (
        <span className="ellipsis" style={{ display: 'block' }} title={value}>
          {value || CONST.null}
        </span>
      )
    }
  ];

  return (
    <YRModal title="历史记录" size="large" open={visible} onCancel={onCancel} footer={false}>
      <YRTable
        form={form}
        rowKey={(record) => `${record.roleId}_${record.version}`}
        loading={pageLoading}
        dataSource={dataSource}
        columns={getTableColumns}
        handleSearch={handleSearch}
        formItemList={formItemList}
        pagination={pagination}
        scroll={{
          y: document.body.clientHeight - 458,
          x: countTableWidth(getTableColumns)
        }}
      />
    </YRModal>
  );
};

export default RoleManage;
