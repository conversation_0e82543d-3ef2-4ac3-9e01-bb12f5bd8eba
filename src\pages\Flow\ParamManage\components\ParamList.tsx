/**
 * 参数库列表树
 */
import { useRequest } from 'ahooks';
import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { YREmpty, YRInput, YRItem, YRLink, YRMessage, YRModal, YRSpace, YRSpin, YRTree } from 'yrantd';
import { deleteFlowParamLibrary, queryFlowParamLibraryList } from '@/services/flow';
import { CaretDownOutlined } from '@ant-design/icons';
import { DataNode } from 'antd/lib/tree';
import Dict from '@/pages/Organ/mock/getDict';
import ParamLibraryAddModal from './ParamLibraryAddModal';
import styles from './index.module.less';

const ParamList = forwardRef(({ ...props }, ref) => {
  const { activity, setActivity } = props as any;
  /** ==== 基本数据 ==== */
  const [treeData, setTreeData] = useState<any>([]);
  const [flowType, setFlowType] = useState<any>();
  /** 当前选中节点 */
  const [selectedIds, setSelectedIds] = useState<any>([]);
  /** 是否点击操作 */
  const [isOperate, setIsOperate] = useState<any>(false);
  /** 当前参数库 */
  const [currentLibrary, setCurrentLibrary] = useState<any>({});
  const [libraryVisible, setLibraryVisible] = useState<any>(false);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);

  useImperativeHandle(ref, () => ({
    typeRequest
  }));

  /** ==== 接口 ==== */

  /** 获取参数库列表接口 */
  const { loading: typeLaoding, run: typeRequest } = useRequest(queryFlowParamLibraryList, {
    onSuccess: (result: any) => {
      const { errorMessage, data } = result;
      if (errorMessage === null) {
        setTreeData(data);
      }
    },
    debounceWait: 1000
  });

  /** 删除参数库接口 */
  const { loading: deleteLaoding, run: deleteRequest } = useRequest(deleteFlowParamLibrary, {
    manual: true,
    onSuccess: (result: any, params: any) => {
      const { errorMessage, data } = result;
      if (errorMessage === null) {
        YRMessage.success('删除成功', 0.1, () => {
          typeRequest();
          if (params[0]?.id === activity) {
            setActivity(undefined);
          }
        });
      }
    }
  });

  /** ==== 副作用 ==== */

  /** 获取字典值 */
  useEffect(() => {
    const type = sessionStorage.getItem('dict_flow_scene_type');
    setFlowType(type);
  }, []);

  /** ==== 方法 ==== */

  /** 从数据字典中获取值 */
  const getOptions = (list: any, dictNum: string) => {
    const arr = JSON.parse(list);
    const value = arr?.filter((item) => {
      return item.itemKey === dictNum;
    });
    return value[0]?.itemName;
  };

  /** 扩展方法 */
  const onExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys);
    setAutoExpandParent(false);
  };

  /** 搜索事件 */
  const onSearch = (value: string) => {
    typeRequest({ libraryName: value });
  };

  /** 修改成功事件 */
  const onEditLibraryOk = () => {
    typeRequest();
  };

  /** 点击修改事件 */
  const handleEdit = (value) => {
    setCurrentLibrary(value);
    setLibraryVisible(true);
  };

  /** 删除事件 */
  const onDelete = (value) => {
    const { libraryName, id } = value;
    YRModal.confirm({
      title: '删除',
      content: `确定要删除【${libraryName}】`,
      okText: '确定',
      cancelText: '取消',
      onOk() {
        deleteRequest({
          id
        });
      },
      onCancel() {
        setIsOperate(false);
      }
    });
  };

  /** 树的格式化 */
  const tree: any = useMemo(() => {
    const loop = (data: DataNode[]): DataNode[] =>
      data?.map((item: any) => {
        const sceneType = item?.sceneType;
        if (item?.flowParamLibrarys) {
          const title = getOptions(flowType, sceneType);
          const titleComponent = <YRItem title={title} />;
          return {
            title: titleComponent,
            key: sceneType,
            selectable: false,
            children: loop(item.flowParamLibrarys),
            ...item
          };
        }
        const sonTitle = (
          <YRItem
            title={item?.libraryName}
            key={item?.id}
            onClick={() => {
              setSelectedIds([item?.id]);
              setActivity(item?.id);
            }}
            operateRender={[
              <YRLink
                type={'primary'}
                onClick={(e) => {
                  handleEdit(item);
                }}
              >
                修改
              </YRLink>,
              <YRLink
                type={'primary'}
                onClick={(e) => {
                  onDelete(item);
                }}
              >
                删除
              </YRLink>
            ]}
          />
        );
        return {
          ...item,
          title: sonTitle,
          key: item?.id,
          isChildren: true
        };
      });
    return loop(treeData);
  }, [treeData, flowType]);

  return (
    <YRSpace direction="vertical">
      <YRInput.Search
        size={'small'}
        placeholder="请输入参数库名称"
        onChange={(e) => onSearch(e.target.value)}
        allowClear
      />
      <YRSpin spinning={typeLaoding} style={{ margin: '20px 0 0 0 ' }} wrapperClassName={styles.organTreeLoading}>
        {tree?.length > 0 ? (
          <YRTree
            blockNode
            defaultExpandAll
            onExpand={onExpand}
            selectedKeys={selectedIds}
            expandedKeys={expandedKeys}
            autoExpandParent={autoExpandParent}
            treeData={tree}
            switcherIcon={<CaretDownOutlined />}
            // onSelect={(selectedKeys, _e) => {
            //   // 1. 判断 selectedKeys 是否为空
            //   // 2. 如果为空，判断当前是否为操作,如果是操作则不赋值，如果是操作则赋值
            //   // 3. 如果不为空，判断是否为操作，如果是操作，则判断点击的参数库是否为当前参数库，如果不是的话，则不修改 selected
            //   console.log(selectedKeys, selectedIds, 'selectedKeys');
            //   if (selectedKeys?.length !== 0) {
            //     // setSelectedIds(selectedKeys);
            //     // setActivity(selectedKeys[0]);
            //   }
            // }}
          />
        ) : (
          <YREmpty />
        )}
      </YRSpin>
      <ParamLibraryAddModal
        title={'修改参数库'}
        visible={libraryVisible}
        currentLibrary={currentLibrary}
        onOkCallback={onEditLibraryOk}
        changeVisible={setLibraryVisible}
        setCurrentLibrary={setCurrentLibrary}
      />
    </YRSpace>
  );
});

export default Dict(['flow_scene_type'])(ParamList);
