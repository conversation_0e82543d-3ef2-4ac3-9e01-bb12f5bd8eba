/**
 * @Author: wangyw26123
 * @Description: 流程图节点面板使用的折叠面板
 * @Date: Created in 2022-04-25 16:08:03
 * @Modifed By:
 */

import React from 'react';
import { YRCollapse } from 'yrantd';

import styles from '@/pages/Flow/index.module.less';

const { Panel } = YRCollapse;

const XFlowCollapse = (props) => {
  const { children, ...rest } = props;
  return (
    <YRCollapse size="small" bordered={false} {...rest} className={styles.xFlowCollapse}>
      {children}
    </YRCollapse>
  );
};

XFlowCollapse.Panel = Panel;

export default XFlowCollapse;
