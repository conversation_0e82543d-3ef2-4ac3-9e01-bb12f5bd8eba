/*
 * @Author: liaokt
 * @E-mail: <EMAIL>
 * @Date: 2023-04-07 17:25:03
 * @Description: 节点高级配置
 */
import React from 'react';
import { checkAuth } from '@/utils/utils';
import XFlowCollapse from '@/pages/Flow/XFlow/components/XFlowCollapse';
import UserNodeConfig from './components/UserNodeConfig';
import NodeRuleConfig from './components/NodeRuleConfig';

const { Panel } = XFlowCollapse;

let components = [
  // 只在任务节点才显示
  {
    title: '用户节点执行程序',
    key: 'userNodeConfig',
    check: null,
    component(props) {
      return (
        <Panel forceRender key={this.key} header={this.title}>
          <UserNodeConfig {...props} />
        </Panel>
      );
    }
  },
  // 除了开始节点和结束节点都显示
  {
    title: '节点执行程序',
    key: 'ruleConfig',
    check: null,
    component(props) {
      return (
        <Panel forceRender key={this.key} header={this.title}>
          <NodeRuleConfig {...props} />
        </Panel>
      );
    }
  }
];

components = components.filter((item) => (item.check ? checkAuth(item.check) : true));

const HeightLevel = (props: any) => {
  const {
    targetData: { nodePanel }
  } = props;
  const panels = [...nodePanel];

  const coms = components.filter((item) => panels.includes(item.key)) || [];
  const nodePanels = coms.map((com) => com.component(props)) || [];

  return <XFlowCollapse defaultActiveKey={coms[0]?.key}>{nodePanels}</XFlowCollapse>;
};

export default HeightLevel;
