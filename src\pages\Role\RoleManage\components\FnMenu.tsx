/**
 * @Author: wangyw26123
 * @Description: 功能菜单
 * @Date: Created in 2022-12-20 15:15:07
 * @Modifed By:
 */

import React, { useEffect, useState } from 'react';
import {
  Y<PERSON>utton,
  YRCard,
  YREmpty,
  YRFlexPageLayout,
  YRForm,
  YRLink,
  YRModal,
  YRSelect,
  YRItem,
  YRSpace
} from 'yrantd';
import classNames from 'classnames';
import { queryPermitGroupList } from '@/services/setting';
import PermitTree from '@/pages/Permit/PermitTree';
import styles from './index.module.less';
import stylesPermit from '@/pages/Permit/index.module.less';
import { useLocation } from '../../../../../.ice';

interface PermitGroupItem {
  operatorId: string;
  ownOrganId: string;
  permitGroupId: string;
  permitGroupName: string;
}

interface FnMenuProps {
  mode: 'add' | 'edit' | 'readPretty';
  roleDetail: {
    permitGroupIdList: string[];
  };
  getPermitGroupIds: (keys: string[]) => void;
}

const FnMenu = (props: FnMenuProps) => {
  const { getPermitGroupIds, roleDetail, mode } = props;
  const [form] = YRForm.useForm();
  const [visible, setVisible] = useState<boolean>(false);
  const [allPermitGroup, setAllPermitGroup] = useState<PermitGroupItem[]>([]);
  const [selectPermitGroupList, setSelectPermitGroup] = useState<PermitGroupItem[]>([]);
  const [activePermitGroup, setActivePermitGroup] = useState<PermitGroupItem>({
    operatorId: '',
    ownOrganId: '',
    permitGroupId: '',
    permitGroupName: ''
  });
  const { query } = useLocation() as any;

  useEffect(() => {
    queryPermitGroupList().then((res) => {
      if (res.success) {
        setAllPermitGroup(res.data);
      }
    });
  }, []);

  useEffect(() => {
    const permitGroupIdList: string[] = roleDetail.permitGroupIdList || [];
    const list = allPermitGroup.filter((group) => permitGroupIdList.includes(group.permitGroupId));
    setSelectPermitGroup(list);
    setActivePermitGroup(list[0] || {});
  }, [roleDetail.permitGroupIdList, allPermitGroup]);

  useEffect(() => {
    getPermitGroupIds?.(selectPermitGroupList.map((group) => group.permitGroupId));
  }, [selectPermitGroupList]);

  const onsubmit = () => {
    form.validateFields().then((values: any) => {
      const permitGroupIds = values.permitGroupIds;
      const list = allPermitGroup.filter((item) => permitGroupIds.includes(item.permitGroupId));
      setSelectPermitGroup(list);
      setActivePermitGroup(list[0] || {});
      setVisible(false);
    });
  };

  const onDelGroup = () => {
    const index = selectPermitGroupList.findIndex((group) => group.permitGroupId === activePermitGroup.permitGroupId);

    if (index > -1) {
      const groupList = [...selectPermitGroupList];
      groupList.splice(index, 1);

      setSelectPermitGroup(groupList);
      setActivePermitGroup(groupList[0] || {});
    }
  };

  const renderPermitGroup = () => {
    if (!selectPermitGroupList.length) {
      return (
        <div className={styles.empty}>
          <YREmpty />
        </div>
      );
    }

    return selectPermitGroupList.map((item) => {
      return (
        <YRItem
          key={item.permitGroupId}
          title={item.permitGroupName}
          onClick={() => setActivePermitGroup(item)}
          className={classNames(stylesPermit.groupListItem, {
            [stylesPermit.active]: activePermitGroup.permitGroupId === item.permitGroupId
          })}
        />
      );
    });
  };

  return (
    <YRCard title="功能菜单" id="fnMenu" bodyStyle={{ padding: 1 }}>
      <YRFlexPageLayout style={{ minHeight: 300, height: 'inherit' }}>
        {query.mode !== 'readPretty' && (
          <YRFlexPageLayout.Sider
            title="权限组列表"
            extra={
              mode !== 'readPretty' && (
                <YRLink
                  type="primary"
                  onClick={() => {
                    form.setFieldValue(
                      'permitGroupIds',
                      selectPermitGroupList.map((item) => item.permitGroupId)
                    );
                    setVisible(true);
                  }}
                >
                  添加权限
                </YRLink>
              )
            }
          >
            {renderPermitGroup()}
          </YRFlexPageLayout.Sider>
        )}
        <YRFlexPageLayout.Main
          title={
            mode !== 'readPretty' && (
              <span className="ellipsis" style={{ maxWidth: '95%' }} title={activePermitGroup.permitGroupName}>
                {activePermitGroup.permitGroupName}
              </span>
            )
          }
        >
          {activePermitGroup.permitGroupId || query?.acrossTenantFlag === 'Y' ? (
            <YRSpace direction="vertical">
              {mode !== 'readPretty' && (
                <YRButton size="small" danger onClick={onDelGroup}>
                  删除
                </YRButton>
              )}
              <PermitTree
                roleDetail={roleDetail}
                disabled
                form={form}
                permitGroupId={activePermitGroup.permitGroupId}
              />
            </YRSpace>
          ) : (
            <div className={styles.empty}>
              <YREmpty description="未选择左侧数据" />
            </div>
          )}
        </YRFlexPageLayout.Main>
      </YRFlexPageLayout>

      <YRModal
        open={visible}
        title="添加权限组"
        onCancel={() => setVisible(false)}
        footer={[
          <YRButton key="cancel" onClick={() => setVisible(false)}>
            取消
          </YRButton>,
          <YRButton key="confirm" type="primary" onClick={() => onsubmit()}>
            确定
          </YRButton>
        ]}
      >
        <YRForm form={form}>
          <YRForm.Row>
            <YRForm.Item label="权限组" name="permitGroupIds" column="block">
              <YRSelect
                showSearch
                optionFilterProp="permitGroupName"
                placeholder="请选择权限组"
                mode="multiple"
                options={allPermitGroup}
                fieldNames={{
                  label: 'permitGroupName',
                  value: 'permitGroupId'
                }}
              />
            </YRForm.Item>
          </YRForm.Row>
        </YRForm>
      </YRModal>
    </YRCard>
  );
};

export default FnMenu;
