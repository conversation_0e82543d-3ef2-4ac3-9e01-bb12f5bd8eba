/**
 * @Author: wangyw26123
 * @Description: 组织机构
 * @Date: Created in 2022-12-28 14:52:12
 * @Modifed By:
 */
// import { queryAcctOrganTree } from '@/services/setting';
import { M010101 } from '@permit/organ';
import { checkAuth } from '@/utils/utils';
import queryAcctOrganTreeJson from '@/pages/Organ/mock/queryAcctOrganTree.json';
import { queryParentOrganList } from '@/services/setting';

const { queryUserOrganTree, queryHrOrganList } = M010101.interfaces;
const queryAcctOrganTree = () => Promise.resolve(queryAcctOrganTreeJson);

export default {
  state: {
    organTreeList: [],
    hrList: [] as { hrOrganId: string; hrOrganName: string }[], // HR机构
    acctOrganTreeList: [],
    activeOrganId: '',
    pageLoading: false,
    orgaParentList: []
  },
  effects: () => ({
    // 获取机构树列表
    getOrganTreeList() {
      if (!checkAuth('/organ-group/organ/organ-manage')) return;

      const that = this as any;
      that.setPageLoading(true);
      queryUserOrganTree().then((res) => {
        if (res.success && res.data) {
          const organTree = res.data.organTreeDto ? [res.data.organTreeDto] : [];
          that.setOrganTreeList(organTree);
        }
        that.setPageLoading(false);
      });
    },
    // 获取核算机构树列表
    getAcctOrganTree() {
      queryAcctOrganTree().then((res) => {
        const that = this as any;
        if (res.success && res.data) {
          const organTreeDto = res.data.organTreeDto ? [res.data.organTreeDto] : [];
          that.setAcctOrganTree(organTreeDto);
        }
      });
    },
    // 获取hr机构
    async getHrOrganList(organName) {
      try {
        const res = await queryHrOrganList({ organName });
        if (res.success) {
          this.setHrList(res.data);
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log(error);
      }
    },
    // 获取父级机构列表
    async getOrgaParentList(organId) {
      const res = await queryParentOrganList({ organId });
      if (res?.success) {
        this.getOrgaParentList(res.data || []);
      }
    }
  }),
  reducers: {
    setOrganTreeList(state, data) {
      state.organTreeList = data;
    },
    setActiveOrganId(state, data) {
      state.activeOrganId = data;
    },
    setPageLoading(state, data) {
      state.pageLoading = data;
    },
    setAcctOrganTree(state, data) {
      state.acctOrganTreeList = data;
    },
    setHrList(state, data) {
      state.hrList = data;
    },
    setOrganParetList(state, data) {
      state.orgaParentList = data;
    }
  }
};
