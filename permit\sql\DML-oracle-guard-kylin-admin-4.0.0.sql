
-- ----------------------------
-- 更新时间:2023/1/11 17:19:07
-- 负责人: 王亚威26123
-- 邮  箱: <EMAIL>
-- 更新内容: 综合信贷系统-后台配置
-- ----------------------------
DELETE FROM GUARD.GUARD_PERMIT_INFO WHERE TENANT_ID = '000' AND PERMIT_ID LIKE 'kylin-admin%';
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin','000',sysdate,sysdate,'综合信贷系统-后台配置',null,'kylin-admin','1', 'N', '0','010','Y',1);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M01','000',sysdate,sysdate,'设置',null,'/setting','1', 'N', 'kylin-admin','010','Y',1);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0101','000',sysdate,sysdate,'机构管理',null,'/setting/organ','1', 'N', 'kylin-admin-M01','010','Y',1);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010101','000',sysdate,sysdate,'机构管理',null,'/setting/organ/organ-manage','1', 'N', 'kylin-admin-M0101','010','Y',1);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010101I1','000',sysdate,sysdate,'查询机构树',null,'/hsjry/guard/base/IOrganQuery/queryOrganTree','4', 'N', 'kylin-admin-M010101','010','Y',0);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010101I2','000',sysdate,sysdate,'修改机构状态',null,'/hsjry/guard/base/IOrgan/changeOrganStatus','4', 'N', 'kylin-admin-M010101','010','Y',2);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010101I3','000',sysdate,sysdate,'机构新增',null,'/hsjry/guard/base/IOrgan/addOrgan','4', 'N', 'kylin-admin-M010101','010','Y',4);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010101E01','000',sysdate,sysdate,'机构新增',null,'/hsjry/guard/base/IOrgan/addOrgan','2', 'N', 'kylin-admin-M010101','010','Y',1);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010101E02','000',sysdate,sysdate,'机构修改',null,'/hsjry/guard/base/IOrgan/modifyOrgan','2', 'N', 'kylin-admin-M010101','010','Y',2);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010101E02I1','000',sysdate,sysdate,'修改机构',null,'/hsjry/guard/base/IOrgan/modifyOrgan','4', 'N', 'kylin-admin-M010101E02','010','Y',0);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010101E03','000',sysdate,sysdate,'机构删除',null,'/hsjry/guard/base/IOrgan/delOrgan','2', 'N', 'kylin-admin-M010101','010','Y',3);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010101E03I1','000',sysdate,sysdate,'机构停启用',null,'/hsjry/guard/base/IOrgan/delOrgan','4', 'N', 'kylin-admin-M010101E03','010','Y',0);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010101E04','000',sysdate,sysdate,'机构启用',null,'/hsjry/guard/base/IOrgan/changeOrganStatus/start','2', 'N', 'kylin-admin-M010101','010','Y',4);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010101E05','000',sysdate,sysdate,'机构停用',null,'/hsjry/guard/base/IOrgan/changeOrganStatus/disabled','2', 'N', 'kylin-admin-M010101','010','Y',5);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010101E06','000',sysdate,sysdate,'机构恢复',null,'/hsjry/guard/base/IOrgan/changeOrganStatus/restore','2', 'N', 'kylin-admin-M010101','010','Y',6);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010101E07','000',sysdate,sysdate,'调整层级',null,'/hsjry/guard/base/changeOrganLevelApprove','2', 'N', 'kylin-admin-M010101','010','Y',7);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010101E07I1','000',sysdate,sysdate,'机构层级调整',null,'/hsjry/guard/management/changeOrganLevelApprove','4', 'N', 'kylin-admin-M010101E07','010','Y',0);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010101E08','000',sysdate,sysdate,'复制现有机构',null,'/hsjry/guard/base/IOrgan/copyOrgan','2', 'N', 'kylin-admin-M010101','010','Y',8);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010101E09','000',sysdate,sysdate,'新增子机构',null,'/hsjry/guard/base/IOrgan/addSubOrgan','2', 'N', 'kylin-admin-M010101','010','Y',9);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010101E10','000',sysdate,sysdate,'机构详情',null,'/hsjry/guard/base/IOrganQuery/queryOrganInfoByOrganId','2', 'N', 'kylin-admin-M010101','010','Y',10);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010101E10I1','000',sysdate,sysdate,'查询机构详情',null,'/hsjry/guard/base/IOrganQuery/queryOrganInfoByOrganId','4', 'N', 'kylin-admin-M010101E10','010','Y',0);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010102','000',sysdate,sysdate,'账务机构映射',null,'/setting/organ/financial-organ','1', 'N', 'kylin-admin-M0101','010','Y',2);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010102I1','000',sysdate,sysdate,'查询账务机构映射',null,'/hsjry/guard/base/IAcctOrganQuery/queryOrganRelationPage','4', 'N', 'kylin-admin-M010102','010','Y',0);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010102E01','000',sysdate,sysdate,'新增账务机构映射',null,'/hsjry/guard/base/IOrganAcctRelate/addOrgRelateList','2', 'N', 'kylin-admin-M010102','010','Y',1);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010102E01I1','000',sysdate,sysdate,'新增账务机构映射',null,'/hsjry/guard/base/IOrganAcctRelate/addOrgRelateList','4', 'N', 'kylin-admin-M010102E01','010','Y',0);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010102E02','000',sysdate,sysdate,'修改账务机构映射',null,'/hsjry/guard/base/IOrganAcctRelate/updateOrgRelateList','2', 'N', 'kylin-admin-M010102','010','Y',2);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010102E02I1','000',sysdate,sysdate,'更新账务机构映射',null,'/hsjry/guard/base/IOrganAcctRelate/updateOrgRelateList','4', 'N', 'kylin-admin-M010102E02','010','Y',0);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010102E03','000',sysdate,sysdate,'删除账务机构映射',null,'/hsjry/guard/base/IOrganAcctRelate/delOrgRelate','2', 'N', 'kylin-admin-M010102','010','Y',3);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010102E03I1','000',sysdate,sysdate,'删除账务机构映射',null,'/hsjry/guard/base/IOrganAcctRelate/delOrgRelate','4', 'N', 'kylin-admin-M010102E03','010','Y',0);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0102','000',sysdate,sysdate,'功能权限',null,'/setting/permit','1', 'N', 'kylin-admin-M01','010','Y',2);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0102I1','000',sysdate,sysdate,'查询当前权限下的权限组列表',null,'/hsjry/guard/base/IPermitQuery/queryPermitGroupList','4', 'N', 'kylin-admin-M0102','010','Y',0);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0102I2','000',sysdate,sysdate,'查询权限组权限',null,'/hsjry/guard/base/IPermitQuery/queryPermitGroupWithPermit','4', 'N', 'kylin-admin-M0102','010','Y',2);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0102E01','000',sysdate,sysdate,'新增权限组',null,'/hsjry/guard/base/IPermit/addPermitGroup','2', 'N', 'kylin-admin-M0102','010','Y',1);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0102E01I1','000',sysdate,sysdate,'新增权限组',null,'/hsjry/guard/base/IPermit/addPermitGroup','4', 'N', 'kylin-admin-M0102E01','010','Y',0);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0102E02','000',sysdate,sysdate,'删除权限组',null,'/hsjry/guard/base/IPermit/deletePermitGroup','2', 'N', 'kylin-admin-M0102','010','Y',2);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0102E02I1','000',sysdate,sysdate,'删除权限组',null,'/hsjry/guard/base/IPermit/deletePermitGroup','4', 'N', 'kylin-admin-M0102E02','010','Y',0);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0102E03','000',sysdate,sysdate,'修改权限组',null,'/hsjry/guard/base/IPermit/modifyPermitGroup','2', 'N', 'kylin-admin-M0102','010','Y',3);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0102E03I1','000',sysdate,sysdate,'修改权限组',null,'/hsjry/guard/base/IPermit/modifyPermitGroup','4', 'N', 'kylin-admin-M0102E03','010','Y',0);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0103','000',sysdate,sysdate,'角色管理',null,'/setting/role','1', 'N', 'kylin-admin-M01','010','Y',3);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010301','000',sysdate,sysdate,'角色管理',null,'/setting/role/role-manage','1', 'N', 'kylin-admin-M0103','010','Y',1);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010301I1','000',sysdate,sysdate,'角色查询',null,'/hsjry/guard/base/IRoleQuery/queryPage','4', 'N', 'kylin-admin-M010301','010','Y',0);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010301I2','000',sysdate,sysdate,'修改角色状态',null,'/hsjry/guard/base/IRole/changeRoleStatus','4', 'N', 'kylin-admin-M010301','010','Y',2);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010301E01','000',sysdate,sysdate,'新增角色',null,'/hsjry/guard/base/IRole/addOrUpdateRole','2', 'N', 'kylin-admin-M010301','010','Y',1);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010301E01I1','000',sysdate,sysdate,'角色新增/修改',null,'/hsjry/guard/base/IRole/addOrUpdateRole','4', 'N', 'kylin-admin-M010301E01','010','Y',0);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010301E02','000',sysdate,sysdate,'修改角色',null,'/hsjry/guard/base/IRole/addOrUpdateRole-modify','2', 'N', 'kylin-admin-M010301','010','Y',2);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010301E02I1','000',sysdate,sysdate,'角色新增/修改',null,'/hsjry/guard/base/IRole/addOrUpdateRole','4', 'N', 'kylin-admin-M010301E02','010','Y',0);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010301E03','000',sysdate,sysdate,'删除角色',null,'/hsjry/guard/base/IRole/delRole','2', 'N', 'kylin-admin-M010301','010','Y',3);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010301E03I1','000',sysdate,sysdate,'角色删除',null,'/hsjry/guard/base/IRole/delRole','4', 'N', 'kylin-admin-M010301E03','010','Y',0);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010301E04','000',sysdate,sysdate,'启用角色',null,'/hsjry/guard/base/IRole/changeRoleStatus/start','2', 'N', 'kylin-admin-M010301','010','Y',4);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010301E05','000',sysdate,sysdate,'停用角色',null,'/hsjry/guard/base/IRole/changeRoleStatus/disabled','2', 'N', 'kylin-admin-M010301','010','Y',5);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010301E06','000',sysdate,sysdate,'恢复角色',null,'/hsjry/guard/base/IRole/changeRoleStatus/restore','2', 'N', 'kylin-admin-M010301','010','Y',6);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010301E07','000',sysdate,sysdate,'添加成员',null,'/hsjry/guard/base/IRole/allotRole','2', 'N', 'kylin-admin-M010301','010','Y',7);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010301E07I1','000',sysdate,sysdate,'添加修改成员',null,'/hsjry/guard/base/IRole/allotRole','4', 'N', 'kylin-admin-M010301E07','010','Y',0);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010301E07I2','000',sysdate,sysdate,'查询当前操作人权限下的用户',null,'/hsjry/guard/base/IUserQuery/queryUserList','4', 'N', 'kylin-admin-M010301E07','010','Y',2);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010301E08','000',sysdate,sysdate,'历史记录',null,'/hsjry/guard/base/IRoleQuery/queryRoleLogPage','2', 'N', 'kylin-admin-M010301','010','Y',8);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010301E08I1','000',sysdate,sysdate,'角色记录查询',null,'/hsjry/guard/base/IRoleQuery/queryRoleLogPage','4', 'N', 'kylin-admin-M010301E08','010','Y',0);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010301E09','000',sysdate,sysdate,'角色详情',null,'/hsjry/guard/base/IRoleQuery/detail','2', 'N', 'kylin-admin-M010301','010','Y',9);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M010301E09I1','000',sysdate,sysdate,'角色详情查询',null,'/hsjry/guard/base/IRoleQuery/detail','4', 'N', 'kylin-admin-M010301E09','010','Y',0);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0104','000',sysdate,sysdate,'用户管理',null,'/setting/user','1', 'N', 'kylin-admin-M01','010','Y',4);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0105','000',sysdate,sysdate,'流程管理',null,'/setting/flow','1', 'N', 'kylin-admin-M01','010','Y',5);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0105I1','000',sysdate,sysdate,'查询流程图列表',null,'/hsjry/flow-designer/v1/flowModelResourceQuery/selectPageForMaxVersion','4', 'N', 'kylin-admin-M0105','010','Y',0);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0105I2','000',sysdate,sysdate,'详情查询流程图',null,'/hsjry/flow-designer/v1/flowDiagramResourceQuery/queryFlowDiagramResourceDetail','4', 'N', 'kylin-admin-M0105','010','Y',2);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0105I3','000',sysdate,sysdate,'查询岗位树',null,'/hsjry/flow-management/IFlowApprovalStation/queryFlowApprovalStationList','4', 'N', 'kylin-admin-M0105','010','Y',4);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0105I4','000',sysdate,sysdate,'查询当前操作人权限下的用户',null,'/hsjry/guard/base/IUserQuery/queryUserList','4', 'N', 'kylin-admin-M0105','010','Y',6);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0105E01','000',sysdate,sysdate,'新增流程图',null,'/hsjry/flow-designer/v1/flowModelResourceQuery/selectPageForMaxVersion','2', 'N', 'kylin-admin-M0105','010','Y',1);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0105E01I1','000',sysdate,sysdate,'新增流程图',null,'/hsjry/flow-designer/v1/flowDiagramResource/addFlowDiagramResource','4', 'N', 'kylin-admin-M0105E01','010','Y',0);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0105E02','000',sysdate,sysdate,'删除流程图',null,'/hsjry/flow-designer/v1/flowDiagramResource/deleteFlowDiagramResource','2', 'N', 'kylin-admin-M0105','010','Y',2);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0105E02I1','000',sysdate,sysdate,'删除流程图',null,'/hsjry/flow-designer/v1/flowDiagramResource/deleteFlowDiagramResource','4', 'N', 'kylin-admin-M0105E02','010','Y',0);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0105E03','000',sysdate,sysdate,'更新流程图',null,'/hsjry/flow-designer/v1/flowDiagramResource/modifyFlowDiagramResource','2', 'N', 'kylin-admin-M0105','010','Y',3);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0105E03I1','000',sysdate,sysdate,'更新流程图',null,'/hsjry/flow-designer/v1/flowDiagramResource/modifyFlowDiagramResource','4', 'N', 'kylin-admin-M0105E03','010','Y',0);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0105E03I2','000',sysdate,sysdate,'新增流程图',null,'/hsjry/flow-designer/v1/flowDiagramResource/addFlowDiagramResource','4', 'N', 'kylin-admin-M0105E03','010','Y',2);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0105E04','000',sysdate,sysdate,'部署流程图',null,'/hsjry/flow-designer/v1/manage-process/deployProcess','2', 'N', 'kylin-admin-M0105','010','Y',4);
INSERT INTO GUARD.GUARD_PERMIT_INFO  (PERMIT_ID,TENANT_ID,CREATE_TIME,UPDATE_TIME,PERMIT_NAME,PERMIT_DESC,URI,PERMIT_TYPE,DATA_PERMISSION,PARENT_PERMIT_ID,PERMIT_STATUS,PERMIT_CHECK,SORT)
VALUES ('kylin-admin-M0105E04I1','000',sysdate,sysdate,'部署流程图',null,'/hsjry/flow-designer/v1/manage-process/deployProcess','4', 'N', 'kylin-admin-M0105E04','010','Y',0);
COMMIT;
