/*
 * @Author: liaokt
 * @E-mail: <EMAIL>
 * @Date: 2023-04-10 09:11:34
 * @Description: 高级配置-用户节点规则配置
 */
import { useDebounceEffect } from 'ahooks';
import React, { useEffect, useState } from 'react';
import { YRDropAndSearchSelector, YREditableTable, YRForm, YRSpace } from 'yrantd';
import { queryUserTaskExecutionListener } from '@/services/flow';

interface ItemDataProps {
  event: string;
  className: string;
}

const eventOptions = [
  {
    value: 'start',
    label: '节点启动后'
  },
  {
    value: 'end',
    label: '节点结束时'
  },
  {
    value: 'take',
    label: '节点转移时'
  }
];

// const classOptions = [
//   {
//     value: '1',
//     label: '测试1'
//   },
//   {
//     value: '2',
//     label: '测试2'
//   },
//   {
//     value: '3',
//     label: '测试3'
//   }
// ];

const NodeRuleConfig = (props: any) => {
  const {
    globalDisabled,
    targetData: { nodeFormData },
    globalFormData,
    targetData,
    form
  } = props;
  const [dataSource, setDataSource] = useState<any>([]);
  const [classOptions, setClassOptions] = useState<any>([]);
  // 获取当前数据，用于赋值到 form 上
  // const executionlisteners = YRForm.useWatch('executionlisteners', form);

  // 监听表格变化，如果新增加了新的任务监听器，则赋值再 ['properties', 'executionlisteners']
  // useDebounceEffect(() => {
  //   form.setFieldsValue({ properties: { executionlisteners } });
  // }, [executionlisteners]);

  // 获取到任务监听器的详情之后，进行数据赋值，并且反显,并同步
  useDebounceEffect(() => {
    if (nodeFormData) {
      const { properties } = nodeFormData;
      const executionDetail = properties?.executionlisteners;
      executionDetail && setDataSource([...executionDetail]);
    }
  }, [nodeFormData]);
  const getUserTask = async () => {
    const res = await queryUserTaskExecutionListener();
    if (res?.success) {
      const newData = res?.data?.map((item) => item?.className)?.map((item: any) => {
        return {
          label: item?.display,
          value: item?.value
        };
      });
      setClassOptions(newData);
    }
  };
  useEffect(() => {
    getUserTask();
  }, []);
  const defaultColumns = [
    {
      title: '时机',
      dataIndex: 'event',
      width: 130,
      renderFormItem: (_form, { record }) => {
        return (
          <YRDropAndSearchSelector
            disabled={globalDisabled}
            style={{ width: '100%' }}
            options={eventOptions}
            valueField={'value'}
            labelField={'label'}
            placeholder={'请选择时机'}
          />
        );
      }
    },
    {
      title: '执行程序',
      dataIndex: 'className',
      width: 130,
      renderFormItem: (_form, { record }) => {
        return (
          <YRDropAndSearchSelector
            disabled={globalDisabled}
            style={{ width: '100%' }}
            options={classOptions}
            valueField={'value'}
            labelField={'label'}
            placeholder={'请选择时机'}
          />
        );
      }
    }
  ];

  // 新增事件
  const onAdd = () => {
    const list = [...dataSource];
    list.push({
      event: '',
      className: ''
    });
    setDataSource([...list]);
  };

  // 删除操作
  const onDelete = (index: number) => {
    const list = [...dataSource];
    list.splice(index, 1);
    setDataSource([...list]);
  };

  return (
    <YRSpace block direction={'vertical'}>
      {/* <YRForm.Item name={['properties', 'executionlisteners']} hidden /> */}
      <YREditableTable
        form={form}
        name={['properties', 'executionlisteners']}
        showIndex={false}
        loading={!nodeFormData}
        rowKey={(row, index) => index}
        scroll={{ y: '246px' }}
        dataSource={dataSource}
        columns={defaultColumns}
        hiddenToolbar={globalDisabled}
        onBeforeAdd={() => onAdd()}
        onAfterDelete={(index) => {
          onDelete(index);
        }}
        toolbar={{
          buttonsProps: {
            add: {
              text: '新增',
              type: 'primary'
            }
          }
        }}
      />
    </YRSpace>
  );
};

export default NodeRuleConfig;
