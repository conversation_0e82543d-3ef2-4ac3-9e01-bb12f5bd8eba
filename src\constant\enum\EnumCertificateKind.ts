/*
 * yunrong.cn Inc. Copyright (c) 2014-2021 All Rights Reserved
 */

/**
 * 证件类型
 */
const EnumCertificateKind = {
  ORGANIZATION_CODE_CERT: { code: '610001', desc: '全国组织机构代码' },
  MILITARY_NOTICE_APPROVAL: { code: '610003', desc: '银行机构代码' },
  BUSINESS_LICENSE: { code: '610005', desc: '企业法人营业执照号码' },
  NATION_TAX_REGIST_CERT: { code: '610007', desc: '国税登记证号码' },
  FINANCE_LICENCE: { code: '610009', desc: '金融许可证号码' },
  BASE_ACCOUNT_CERT: { code: '610011', desc: '基本存款账户开户登记证号码' },
  GOV_PERSONAL_CERT: { code: '610013', desc: '政府人事部门批文号码' },
  COMMIT_CERT: { code: '610015', desc: '编制委员会批文号码' },
  GOV_COMMIT_PART: { code: '610017', desc: '政府人事部门登记证书号码' },
  OPENING_CERT: { code: '610019', desc: '编制委员会登记证书号码' },
  ARMED_POLICE_NOTICE_APPROVAL: { code: '610021', desc: '军队、武警财务部门开户证明号码' },
  SOCIAL_GROUPS_REGIST_CERT: { code: '610023', desc: '社会团体登记证书号码' },
  PER_UNENTERPRISE_REGIST_CERT: { code: '610025', desc: '民办非企业登记证书号码' },
  SECURITIES_LICENSE: { code: '610027', desc: '外地常设机构驻在地政府主管部门批文号码' },
  GOV_PART_ORG_CERT: { code: '610029', desc: '国家主管部门颁外国驻华机构批文号码' },
  GOV_REGISTER_OVERSEAS_REGIST_CERT: { code: '610031', desc: '国家登记机关颁外资企业驻华代表、办事处登记证号码' },
  CHARGE_PART_LIVE_CERT: { code: '610033', desc: '主管部门颁居民、村民、社区委员会批文号码' },
  ACCOUNT_ORG_CERT: { code: '610035', desc: '独立核算的附属机构批文号码' },
  CHARGE_PART_CERT: { code: '610037', desc: '主管部门批文号码' },
  FINANCE_PART_CERT: { code: '610039', desc: '财政部门证明号码' },
  SECURITY_INVEST_ACCESS_CERT: { code: '610041', desc: '证券投资业务许可证号码' },
  TMP_MANAGE_PART_CERT: { code: '610043', desc: '临时经营地工商行政管理部门批文号码' },
  ENT_NAME_NOTICE_CERT: { code: '610045', desc: '企业名称预先核准通知书号码' },
  BUSINESS_LICENCE_CERT: { code: '610047', desc: '企业营业执照号码' },
  PRIVATE_BUSINESS_LICENCE_CERT: { code: '610049', desc: '个体工商户营业执照号码' },
  LOCAL_TAX_REGIST_CERT: { code: '610051', desc: '地税登记证号码' },
  RELIGION_CERT: { code: '610053', desc: '宗教事务管理部门的批文或证明号码' },
  LOAN_CONTRACT_CERT: { code: '610055', desc: '借款合同号码' },
  FOREX_PAER_CERT: { code: '610057', desc: '国家外汇管理部门的批复文件号码' },
  GOV_PART_ACCESS_CERT: { code: '610059', desc: '主管部门许可证号码' },
  BUILD_CERT: { code: '610061', desc: '建筑施工及安装合同号码' },
  ORG_PLACEHOLDER_CERT: { code: '619996', desc: '组织机构占位号码' },
  BRANCH_ORG_CERT: { code: '619997', desc: '分支机构替代号码' },
  SYSTEM_ORG_CERT: { code: '619998', desc: '系统产生组织证件号码' },
  OTHER_CERT: { code: '619999', desc: '其他机构证件标识' },
  SOCIAL_CREDIT_CODE: { code: '610063', desc: '统一社会信用代码' },
  BUSINESS_REGIST_CERT: { code: '610081', desc: '商业登记证' },
  IMPORT_EXPORT_LICENSE: { code: '610087', desc: '外贸许可证号' },
  SPECIAL_TRADE_LICENSE: { code: '610089', desc: '特种行业许可证' },
  NOT_STANDARD_CERT: { code: 'Z00000', desc: '移植的不规范证件号码' },
  RESIDENT_IDENTITY_CARD: { code: '110001', desc: '居民身份证' },
  TEMP_RESIDENT_IDENTITY_CARD: { code: '110003', desc: '临时居民身份证' },
  RESIDENCE_BOOKLET: { code: '110005', desc: '户口簿' },
  PEOPLE_ARM_CERTIFICATE: { code: '110007', desc: '中国人民解放军军人身份证件' },
  POLICE_CERTIFICATE: { code: '110009', desc: '中国人民武装警察身份证件' },
  RETIRED_CADRES: { code: '110011', desc: '离休干部荣誉证' },
  RETIRED_OFFICER: { code: '110013', desc: '军官退休证' },
  TYPE_E: { code: '110015', desc: '文职干部退休证' },
  TYPE_D: { code: '110017', desc: '军事院校学员证' },
  HKMA_TO_MAINLAND_PASS: { code: '110019', desc: '港澳居民往来内地通行证' },
  TAIWAN_TO_MAINLAND_PASS: { code: '110021', desc: '台湾同胞往来大陆通行证' },
  PASSPORT: { code: '110023', desc: '中华人民共和国因私护照' },
  PASSPORT_2: { code: '110025', desc: '中华人民共和国因公护照' },
  FOR_PASSPORT: { code: '110027', desc: '外国护照' },
  ALIEN_RESIDENCE_PERMIT: { code: '110029', desc: '外国人居留证' },
  NOT_EXIST: { code: '119998', desc: '系统使用的个人证件识别标识,在现实中不存在' },
  OFFICERS_CERTIFICATE: { code: '119011', desc: '军官证' },
  SOLDIER_CERTIFICATE: { code: '119013', desc: '士兵证' },
  HONGKONG_IDENTITY_CARD: { code: '119023', desc: '香港身份证' },
  MACAO_IDENTITY_CARD: { code: '119025', desc: '澳门身份证' },
  TAIWAN_IDENTITY_CARD: { code: '119027', desc: '台湾身份证' },
  OTHER_DOCUMENTS: { code: '119999', desc: '其他个人证件识别标识' }
};

export { EnumCertificateKind };
