@import "~@/global.less";

.steps-action {
  margin-top: 16px;
  text-align: right;
}
.steps-content {
  margin-top: 16px;
}
.space {
  margin-left: 12px;
}
.lh {
  line-height: 32px;
}
.test-result {
  .title {
    margin-bottom: 8px;
    font-weight: bolder;
  }
  .result {
    max-height: 300px;
    padding: 4px 8px;
    overflow-y: auto;
    color: fade(#1f2e4a, 70%);
    font-size: 12px;
    word-break: break-all;
    background-color: fade(color(~`colorPalette("#1f2e4a", 1) `), 10%);
    border-radius: 4px;
  }
}

.LittleModal_little-modal-cont__BjQXV .ant-form-item-control .ant-form-explain {
  display: block;
}

.LittleModal_little-modal-cont__Ly9z0 .ant-form-item-control .ant-form-explain {
  display: block;
}
