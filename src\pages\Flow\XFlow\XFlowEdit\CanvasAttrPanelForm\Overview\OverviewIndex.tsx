/**
 * @Author: wangyw26123
 * @Description: 画布面板-概览
 * @Date: Created in 2022-04-27 13:49:31
 * @Modifed By:
 */

import React, { useEffect, useState } from 'react';
import { YREmpty } from 'yrantd';
import { XFlowNodeCommands } from '@yr/xflow';
import styles from '@/pages/Flow/index.module.less';
import XflowInstance from '@/pages/Flow/XFlow/XflowInstance';
import CustomeNode from '@/pages/Flow/XFlow/components/CustomeNode';

const GlobalConfigIndex = () => {
  const [allNodes, setAllNodes] = useState([]);
  const [initLoad, setInitLoad] = useState(() => XflowInstance.initLoad);

  useEffect(() => {
    // 使用开关属性，解决页面首次加载无法获取到节点数据问题
    if (initLoad) {
      const timer = setTimeout(() => {
        XflowInstance.getGraphData().then((res) => {
          setAllNodes(res.nodes);
          setInitLoad(false);
          clearTimeout(timer);
        });
      }, 600);
    } else {
      XflowInstance.getGraphData().then((res) => {
        setAllNodes(res.nodes);
      });
    }
  }, [initLoad]);

  const onCenter = (node) => {
    // 节点居中
    XflowInstance.app.executeCommand(XFlowNodeCommands.CENTER_NODE.id, {
      nodeConfig: {
        ...node
      }
    });
  };

  return (
    <div className={styles.overviewIndex}>
      {allNodes.length ? (
        allNodes.map((node) => {
          const data = { ...node, width: '100%', height: 60 };
          return (
            <div onClick={() => onCenter(node)}>
              <CustomeNode isOverview overviewData={data} />
            </div>
          );
        })
      ) : (
        <YREmpty />
      )}
    </div>
  );
};

export default GlobalConfigIndex;
