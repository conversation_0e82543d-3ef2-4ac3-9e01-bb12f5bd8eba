import React from 'react';
import { runApp } from 'ice';
import { YRIconSVGBuilder } from 'yrantd';
import type { IAppConfig } from 'ice';
import { isInIcestark } from '@ice/stark-app';
import { PageLoading } from '@yr/pro-layout';
import { register } from '@/utils/micro-utils';
import GlobalWrapper from '@/components/GlobalWrapper';
import packageInfo from '../package.json';
import 'yrantd/lib/style/yr-style/classname.less';
import 'yrantd/dist/yrantd.css';
import 'yrantd/dist/antd.variable.css';

const appConfig: IAppConfig = {
  app: {
    rootId: 'ice-container',
    addProvider: (props) => <GlobalWrapper {...props} />
  },
  icestark: {
    type: 'child'
  },
  router: {
    type: 'browser',
    fallback: <PageLoading />,
    basename: isInIcestark() ? `/${packageInfo.name}` : '/', // 如果是集成到基座运行需要添加{appId}作为前缀
    modifyRoutes: register
  }
};

// 本应用会嵌套在基座中运行，所以生产环境中可以不执行；
if (process.env.NODE_ENV === 'development') {
  YRIconSVGBuilder(window);

  // 初始化 Stagewise Toolbar
  import('@stagewise/toolbar-react').then(({ StagewiseToolbar }) => {
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const ReactDOM = require('react-dom');
    const stagewiseConfig = {
      plugins: []
    };

    // 创建独立的容器元素
    const toolbarContainer = document.createElement('div');
    toolbarContainer.id = 'stagewise-toolbar-container';
    document.body.appendChild(toolbarContainer);

    // 渲染工具栏到独立容器
    ReactDOM.render(
      React.createElement(StagewiseToolbar, { config: stagewiseConfig }),
      toolbarContainer
    );
  }).catch((error) => {
    // eslint-disable-next-line no-console
    console.warn('Failed to load Stagewise Toolbar:', error);
  });
}

runApp(appConfig);
