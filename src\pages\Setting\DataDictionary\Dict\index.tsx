/*
 * @Author: 伍晶晶
 * @Description: 字典
 * @Date: 2023-03-20 11:14:48
 * @LastEditors: 伍晶晶
 * @LastEditTime: 2023-03-29 19:08:22
 */
import { useRequest } from 'ahooks';
import React from 'react';
import { YRButton, YREasyUseModal, YRLink, YRMessage, YRSpace, YRTable, YRTableProps } from 'yrantd';
import { useAntd } from '../Context';
import AddModal from './AddModal';
import { useVT } from 'virtualizedtableforantd4';

import { addDict, modifyDict } from '@/services/setting';

const Dict = () => {
  const antd = useAntd();

  const [, setSelectState] = antd.selectedState;

  const [vt] = useVT(() => ({ scroll: { y: 600 } }), []);
  const { run } = useRequest(modifyDict, {
    manual: true,
    onSuccess: (res) => {
      if (res.success) {
        YRMessage.success('修改成功');
      }
    }
  });

  const columns: YRTableProps['columns'] = [
    {
      dataIndex: 'dictName',
      title: '字典中文名',
      width: 200,
      render: (value, record) => {
        if (value) {
          return (
            <YRLink type="primary" onClick={() => setSelectState(record)}>
              {value}
            </YRLink>
          );
        }
        return CONST.null;
      }
    },
    { dataIndex: 'dictKey', title: '字典英文名' },
    { dataIndex: 'updateTime', title: '更新时间' },
    { dataIndex: 'createTime', title: '创建时间' },
    {
      title: '操作',
      fixed: 'right',
      dataIndex: 'action',
      width: 100,
      render: (v, record) => (
        <YRButton.Overflow>
          <YRLink type="primary" onClick={() => setSelectState(record)}>
            详情
          </YRLink>
          <YRLink type="primary" onClick={() => YREasyUseModal.show(AddModal, { type: 'edit', row: record }).then(run)}>
            编辑
          </YRLink>
        </YRButton.Overflow>
      )
    }
  ];

  const { run: runAdd } = useRequest(addDict, {
    manual: true,
    onSuccess: (res) => {
      if (res.success) {
        YRMessage.success('新增成功');
        antd.antdTreeProps.refresh();
      }
    }
  });

  return (
    <YRSpace direction="vertical" block>
      <YRButton
        key="add"
        type="primary"
        onClick={() =>
          YREasyUseModal.show(AddModal, { type: 'add' }).then((values: any) => {
            runAdd(values);
          })
        }
      >
        新增
      </YRButton>
      <YRTable
        {...antd.antdTreeProps}
        rowKey="dictKey"
        components={vt}
        dataSource={antd.antdTreeProps.data || []}
        onRow={(record) => ({
          onDoubleClick: () => {
            setSelectState(record);
          },
          style: { cursor: 'pointer' }
        })}
        columns={columns}
      />
    </YRSpace>
  );
};

export default Dict;
