import React, { useEffect, useState } from 'react';
import { YREasyUseModal, YRForm, YRModal, YRRow, YRCheckbox, YRButton, YRCol } from 'yrantd';
import { useRequest } from 'ahooks';
import { businessStageItem, businessTacheItem } from '../config';
import { sceneConfig } from '@/services/setting';
import styles from '../index.module.less';

const SceneModal = (props) => {
  const modal = YREasyUseModal.useModal();
  const [form] = YRForm.useForm();
  const { okCallback, defaultValue } = props;
  const [businessTacheOption, setBusinessTacheOption] = useState([]) as any;
  const selectedTache = YRForm.useWatch('businessTache', form);

  // // 查询业务环节
  // const { run: sceneQueryRun, loading } = useRequest(sceneConfig, {
  //   manual: true,
  //   onSuccess: (result, params: any) => {
  //     const { errorMessage, data } = result as any;
  //     if (errorMessage === null) {
  //       if (data && data?.length > 0) {
  //         const tacheList = data.reduce((item, next) => {
  //           const mapList = (next?.businessTacheList || []).map((i) => {
  //             return {
  //               value: i,
  //               label: businessTacheItem[i]
  //             };
  //           });
  //           return [...(item || []), ...mapList];
  //         }, []);
  //         setBusinessTacheOption(tacheList);
  //       }
  //     }
  //   }
  // });

  useEffect(() => {
    if (defaultValue) {
      const { businessStage } = defaultValue;
      const tacheList = (businessStage || []).reduce((item, next) => {
        return [...(item || []), ...businessTacheItem[next]];
      }, []);
      setBusinessTacheOption(tacheList);
      form.setFieldsValue(defaultValue);
    }
  }, [defaultValue]);

  // 确认选择
  const submit = () => {
    form.validateFields().then((values) => {
      okCallback(values);
      modal.remove();
    });
  };

  // 根据所选阶段展示对应环节
  const stageChange = (checkedValues) => {
    // 对所选阶段进行排序
    const sortValues = (checkedValues || []).sort((a, b) => a - b);
    const tacheList = (sortValues || []).reduce((item, next) => {
      return [...(item || []), ...businessTacheItem[next]];
    }, []);
    setBusinessTacheOption(tacheList);
    // 筛选已选择的环节
    const filterTache = (selectedTache || []).filter((item) => {
      return tacheList.find((v) => v?.value === item);
    });
    form.setFieldValue('businessTache', filterTache);
  };

  return (
    <YRModal
      title="业务阶段以及环节选择"
      onOk={submit}
      onCancel={modal.remove}
      open={modal.visible}
      destroyOnClose
      maskClosable={false}
      width={'40%'}
    >
      <div className={styles.wizardModal}>
        <YRForm form={form} layout={'vertical'}>
          <YRRow>
            <YRCol span={12}>
              <YRForm.Item
                label={
                  <YRRow style={{ width: '100%' }} justify={'space-between'}>
                    <span>业务阶段</span>
                    <div>
                      <YRButton
                        size="small"
                        type="link"
                        onClick={() => {
                          const allStage = businessStageItem.map((item) => item?.value);
                          stageChange(allStage);
                          form.setFieldValue('businessStage', allStage);
                        }}
                      >
                        全选
                      </YRButton>
                      <YRButton
                        size="small"
                        type="link"
                        onClick={() => {
                          stageChange([]);
                          form.setFieldValue('businessStage', []);
                        }}
                      >
                        清空
                      </YRButton>
                    </div>
                  </YRRow>
                }
                name="businessStage"
                rules={[{ required: true, message: '请选择业务阶段' }]}
              >
                <YRCheckbox.Group onChange={stageChange}>
                  {businessStageItem.map((item) => {
                    return (
                      <YRRow key={item?.value}>
                        <YRCheckbox value={item?.value}>{item?.label}</YRCheckbox>
                      </YRRow>
                    );
                  })}
                </YRCheckbox.Group>
              </YRForm.Item>
            </YRCol>
            <YRCol span={12}>
              <YRForm.Item
                label={
                  <YRRow style={{ width: '100%' }} justify={'space-between'}>
                    <span>业务环节</span>
                    <div>
                      <YRButton
                        size="small"
                        type="link"
                        onClick={() => {
                          const allTache = businessTacheOption.map((item) => item?.value);
                          form.setFieldValue('businessTache', allTache);
                        }}
                      >
                        全选
                      </YRButton>
                      <YRButton
                        size="small"
                        type="link"
                        onClick={() => {
                          form.setFieldValue('businessTache', []);
                        }}
                      >
                        清空
                      </YRButton>
                    </div>
                  </YRRow>
                }
                name="businessTache"
                rules={[{ required: true, message: '请选择业务环节' }]}
              >
                <YRCheckbox.Group>
                  {businessTacheOption.map((item: any) => {
                    return (
                      <YRRow key={item?.value}>
                        <YRCheckbox value={item?.value}>{item?.label}</YRCheckbox>
                      </YRRow>
                    );
                  })}
                </YRCheckbox.Group>
              </YRForm.Item>
            </YRCol>
          </YRRow>
        </YRForm>
      </div>
    </YRModal>
  );
};

export default YREasyUseModal.create(SceneModal);
