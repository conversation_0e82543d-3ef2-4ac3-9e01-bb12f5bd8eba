/**
 * 页面描述: 规则条目参数组件(比较符左侧)
 * @文件名 ParameterValue.tsx
 * @filePath \src\pages\Setting\RuleManager\Entry\components\ParameterValue.tsx
 * @Date 2023-08-08 16:48:55
 * @Email <EMAIL>
 * <AUTHOR>
 */
import React from 'react';
import style from '../index.module.less';
import Parameter from './Parameter';

const ParameterValue = (props) => {
  const { leftValue, changeType } = props;
  // 参数设置值
  const setValue = (active) => {
    if (leftValue && leftValue.variableCode === active.variableCode) {
      return;
    }
    // 设置与改变值
    changeType('leftVariableRuleDto', {
      ...(active || {}),
      variableType: '001'
    });
  };

  return (
    <>
      {leftValue && Object.keys(leftValue).length > 0 ? (
        <>
          <span className={style['parameter-value']}>
            <Parameter
              setValue={setValue}
              selectedId={leftValue.variableCode}
              selectedLabel={leftValue.variableName}
            />
          </span>
        </>
      ) : (
        <>
          <Parameter setValue={setValue} selectedLabel={'请选择参数'} />
        </>
      )}
    </>
  );
};

export default ParameterValue;
