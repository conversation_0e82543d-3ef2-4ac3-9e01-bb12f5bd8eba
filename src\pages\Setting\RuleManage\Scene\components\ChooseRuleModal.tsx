/**
 * 页面描述: 规则条目选择弹框
 * @文件名 ChooseRuleModal.tsx
 * @filePath \src\pages\Setting\RuleManage\Scene\components\ChooseRuleModal.tsx
 * @Date 2023-08-17 16:06:08
 * @Email <EMAIL>
 * <AUTHOR>
 */
import React, { useState } from 'react';
import { YRModal, YRTable, YRForm, YREasyUseModal, YRTreeSelect, YRMessage, YRTooltip } from 'yrantd';
import { useAntdTable, useRequest } from 'ahooks';
import { ruleItemPage, queryRuleTree } from '@/services/setting';

const ChooseRuleModal = (props) => {
  const modal = YREasyUseModal.useModal();
  const [form] = YRForm.useForm();
  const { okCallback, selectedList } = props;
  const [treeData, setTreeData] = useState([]);
  const [selectedIds, setSelectedIds] = useState([]);
  const [selectedItems, setSelectedItems] = useState([]);

  // 查询规则条目列表接口
  const { run: queryTree } = useRequest(queryRuleTree, {
    onSuccess: (res) => {
      const { errorMessage, data } = res as any;
      if (data && data.length > 0) {
        setTreeData(data);
      }
    },
    defaultParams: [{ type: '001' }],
    debounceWait: 500
  });

  const { tableProps, run, params } = useAntdTable((p) => {
    delete p.extra;
    delete p.filters;
    return ruleItemPage({
      ...p,
      pageNum: p.current,
      status: '010'
    }).then((res) => {
      return res.success
        ? { list: res?.data?.list || [], total: res?.data?.total || 0 }
        : {
          list: [],
          total: 0
        };
    });
  }, {});

  const rowSelection = {
    type: 'checkBox',
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedIds(selectedRowKeys);
      setSelectedItems(selectedRows);
    },
    selectedRowKeys: selectedIds
  };

  const generateColumns = () => {
    const columns = [
      {
        title: '树名称',
        dataIndex: 'treeName',
        render: (value) => {
          return (
            <YRTooltip title={value}>
              <div className="ellipsis">{value}</div>
            </YRTooltip>
          );
        }
      },
      {
        title: '规则条目名称',
        dataIndex: 'itemName',
        render: (value) => {
          return (
            <YRTooltip title={value}>
              <div className="ellipsis">{value}</div>
            </YRTooltip>
          );
        }
      },
      {
        title: '规则条目描述',
        dataIndex: 'description',
        render: (value) => {
          return (
            <YRTooltip title={value}>
              <div className="ellipsis">{value}</div>
            </YRTooltip>
          );
        }
      }
    ];

    return columns;
  };

  // 查询条件
  const formItemList = [
    {
      placeholder: '树编号',
      key: 'treeId',
      type: 'component',
      position: 'show',
      width: 250,
      component: (
        <YRTreeSelect
          showSearch
          style={{ width: '250px' }}
          dropdownStyle={{ width: 250, maxHeight: 500, overflow: 'auto' }}
          placeholder="请选择树编号"
          allowClear
          fieldNames={{ label: 'treeName', value: 'treeId', children: 'treeDtoList' }}
          filterTreeNode={(inputValue, treeNode) =>
            treeNode.treeName.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
          }
          treeData={treeData}
          onSelect={(value) => {
            run({ current: 1, pageSize: 10, treeId: value });
          }}
        />
      )
    }
  ];

  // 确认选择
  const submit = () => {
    // 重复选择的条目
    const repeatList = (selectedList || []).filter((i) => {
      return selectedItems.find((v: any) => v?.itemId === i?.itemId);
    });
    if (!selectedIds || selectedIds.length === 0 || selectedItems.length === 0) {
      YRMessage.error('请选择规则条目');
      return;
    }
    if (repeatList.length > 0) {
      const repeatNames = repeatList.map((i) => i?.itemName).join(',');
      YRMessage.warning(`请勿选择重复条目: ${repeatNames}`);
      return;
    }
    okCallback(selectedItems);
    modal.remove();
  };

  return (
    <YRModal
      title="选择规则条目"
      onOk={submit}
      onCancel={modal.remove}
      open={modal.visible}
      destroyOnClose
      maskClosable={false}
      width={'60%'}
    >
      <YRForm form={form}>
        <YRTable
          {...tableProps}
          form={form}
          formItemList={formItemList}
          handleSearch={() => {
            const formValues = form.getFieldsValue() || {};
            run({ current: 1, pageSize: 10, ...formValues });
          }}
          rowKey={'itemId'}
          rowSelection={rowSelection}
          columns={generateColumns()}
        />
      </YRForm>
    </YRModal>
  );
};

export default YREasyUseModal.create(ChooseRuleModal);
