/**
 * 页面描述: 变量列表信息
 * @文件名 VariableInfo.tsx
 * @filePath D:\work\kylin-admin\src\pages\Setting\VariableManage\components\VariableInfo.tsx
 * @Date 2023-08-11 10:27:11
 * @Email <EMAIL>
 * <AUTHOR>
 */
import React, { useEffect } from 'react';
import {
  YRTable,
  YRForm,
  YRButton,
  YREasyUseModal,
  YRSpace,
  YRLink,
  YRTooltip,
  YRMessage,
  YRConfirmBtn
} from 'yrantd';
import { useAntdTable } from 'ahooks';
import type { YRTableProps } from 'yrantd';
import { variableQuery, deleteVariable } from '@/services/setting';
import VariableModal from './VariableModal';

const VariableInfo = (props) => {
  const { activeNode = {} } = props;
  const { treeId, treeName, treeDtoList = [] } = activeNode;
  const addIf = !(treeDtoList && treeDtoList?.length > 0);
  const [form] = YRForm.useForm();

  const {
    run: queryVariableList,
    refresh,
    tableProps,
    params
  } = useAntdTable(
    (param) =>
      variableQuery({
        ...param,
        pageNum: param.current
      }).then((res) => {
        if (res?.success) {
          return {
            list: res.data.list || [],
            total: res.data.total || 0
          };
        } else {
          return {
            list: [],
            total: 0
          };
        }
      }),
    { manual: true }
  );

  useEffect(() => {
    if (treeId) {
      queryVariableList({
        treeId,
        current: 1,
        pageSize: 10
      });
    }
  }, [treeId]);

  const deleteItem = (variableId) => {
    deleteVariable({ variableId }).then((res) => {
      if (res?.success) {
        YRMessage.success('删除成功');
        refresh();
      }
    });
  };

  const getTableColumns: YRTableProps['columns'] = [
    {
      title: '变量code',
      dataIndex: 'variableCode',
      width: 200,
      render: (value: string) => {
        return (
          <YRTooltip onlyShow title={value}>
            <div className="ellipsis">{value || CONST.null}</div>
          </YRTooltip>
        );
      }
    },
    {
      title: '变量名称',
      dataIndex: 'variableName',
      width: 150,
      render: (value: string) => {
        return (
          <YRTooltip onlyShow title={value}>
            <div className="ellipsis">{value || CONST.null}</div>
          </YRTooltip>
        );
      }
    },
    {
      title: '操作',
      dataIndex: 'operate',
      width: 120,
      fixed: 'right',
      render: (value, row: any) => (
        <YRButton.Overflow>
          <YRLink
            type={'primary'}
            onClick={() => {
              YREasyUseModal.show(VariableModal, { defaultData: row, refresh, type: 'edit' });
            }}
          >
            修改
          </YRLink>
          <YRConfirmBtn msg="删除后无法恢复，确定进行删除吗？" type="pop" onConfirm={() => deleteItem(row?.variableId)}>
            删除
          </YRConfirmBtn>
        </YRButton.Overflow>
      )
    }
  ];

  const renderExtAction = (
    <YRSpace>
      <YRButton
        key="add"
        type="primary"
        onClick={() => {
          YREasyUseModal.show(VariableModal, {
            defaultData: { treeId, treeName },
            refresh,
            type: 'add'
          });
        }}
      >
        新增
      </YRButton>
    </YRSpace>
  );

  return (
    <YREasyUseModal.Provider>
      <YRTable
        {...tableProps}
        form={form}
        rowKey={(row) => row?.variableId}
        columns={getTableColumns}
        extAction={addIf ? renderExtAction : null}
      />
    </YREasyUseModal.Provider>
  );
};
export default VariableInfo;
