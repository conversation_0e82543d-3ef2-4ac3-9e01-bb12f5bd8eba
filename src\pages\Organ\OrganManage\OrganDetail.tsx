/*
 * @Author: DJscript
 * @Date: 2023-02-20 14:29:50
 * @LastEditTime: 2023-02-21 16:01:14
 * @FilePath: /kylin-admin/src/pages/Organ/OrganManage/OrganDetail.tsx
 * @Description: ...
 */
/**
 * @Author: wangyw26123
 * @Description: 机构详情
 * @Date: Created in 2022-12-15 10:49:38
 * @Modifed By:
 */
import React, { useEffect, useState } from 'react';
import {YRButton, YRForm, YRClassificationLayout, YRSpin, YRSpace} from 'yrantd';
import { QueryString } from '@yr/util';
import { openNewTab } from '@/utils/utils';
import { M010101 } from '@permit/organ';
import OrganInfoEdit from './components/OrganInfoEdit';
import FinancialOrganInfo from './components/FinancialOrganInfo';
import OrganChangeRecord from './components/OrganChangeRecord';

const { queryOrganInfoByOrganId } = M010101.E10.interfaces;

interface OrganDetailProps {
  parentOrganId: string;
}

const OrganDetail = (props: OrganDetailProps) => {
  const { parentOrganId } = props;
  const [form] = YRForm.useForm();
  const [pageLoading, setPageLoading] = useState<boolean>(false);
  const [organDetail, setOrganDetail] = useState<any>();
  const [forceRenderForm, setForceRenderForm] = useState(0);

  useEffect(() => {
    if (!parentOrganId) return;
    setPageLoading(true);
    queryOrganInfoByOrganId({
      organId: parentOrganId
    }).then((res) => {
      setPageLoading(false);
      if (res.success) {
        setOrganDetail(res.data);
        setForceRenderForm(forceRenderForm + 1);
        form.resetFields();
      }
    });
  }, [parentOrganId]);

  const right = (
    <YRButton
      type="primary"
      check={M010101.E02}
      onClick={() => {
        openNewTab({
          pathname: '/setting/organ/organ-manage/edit',
          query: {
            mode: 'edit',
            parentOrganId
          }
        });
      }}
    >
      修改
    </YRButton>
  );

  const organLevelChangeInfo = organDetail?.organLevelChangeInfo || [];
  const organRelationList = organDetail?.organRelationList || [];

  return (
    <YRSpin spinning={pageLoading}>
      <YRSpace direction="vertical" block>
        {right}
        <YRClassificationLayout.Space>
          <OrganInfoEdit
            key={`001_${forceRenderForm}`}
            form={form}
            mode="readPretty"
            parentOrganId={parentOrganId}
            detail={organDetail || {}}
          />
          <FinancialOrganInfo list={organRelationList} />
          <OrganChangeRecord key={`003_${forceRenderForm}`} list={organLevelChangeInfo} />
        </YRClassificationLayout.Space>
      </YRSpace>
    </YRSpin>
  );
};

export default QueryString(OrganDetail);
