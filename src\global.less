@import (reference) 'yrantd/lib/style/variable.less';
@import './common.less';

html,
body,
#ice-container {
  height: 100%;
}

body {
  -webkit-font-smoothing: antialiased;
}

.highlight-old .djs-visual > :nth-child(1),
.highlight-old .djs-visual > circle {
  stroke: #faad14 !important;
  fill: #fde4c3 !important;
}

.highlight-line .djs-visual > :nth-child(1) {
  stroke: #faad14 !important;
}

.highlight-current .djs-visual > :nth-child(1),
.highlight-current .djs-visual > circle {
  stroke: #1d8f80 !important;
  fill: #a6d74b !important;
}

.highlight-error .djs-visual > :nth-child(1),
.highlight-error .djs-visual > circle {
  stroke: #d90008 !important;
  fill: #f5222d !important;
}
