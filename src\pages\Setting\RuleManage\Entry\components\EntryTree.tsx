/**
 * 页面描述: 规则条目目录
 * @文件名 EntryTree.tsx
 * @filePath \src\pages\Setting\RuleManager\Entry\components\EntryTree.tsx
 * @Date 2023-08-01 16:44:32
 * @Email <EMAIL>
 * <AUTHOR>
 */
import React, { useMemo, useState } from 'react';
import { YREasyUseModal, YRItem, YRSpace, YRSpin, YRTree, YREmpty } from 'yrantd';
import { DataNode } from 'antd/lib/tree';
import { CaretDownOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { queryRuleTree } from '@/services/setting';

const EntryTree = (props) => {
  const { setActiveNode } = props as any;
  /** ==== 基本数据 ==== */
  const [treeData, setTreeData] = useState<any>([]);
  /** 当前选中节点 */
  const [selectedIds, setSelectedIds] = useState<any>([]);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [searchValue, setSearchValue] = useState('');

  // 查询规则条目列表接口
  const { loading: queryLoading, run: queryTree } = useRequest(queryRuleTree, {
    onSuccess: (res) => {
      const { errorMessage, data } = res as any;
      if (data && data.length > 0) {
        setTreeData(data);
        setExpandedKeys([data[0]?.treeId]);
        setSelectedIds([data[0]?.treeId]);
        setActiveNode(data[0]);
      }
    },
    defaultParams: [{ type: '001' }],
    debounceWait: 500
  });

  /** ==== 方法 ==== */

  /** 扩展方法 */
  const onExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys);
    setAutoExpandParent(false);
  };

  /** 树的格式化 */
  const tree: any = useMemo(() => {
    const loop = (data: DataNode[], parent?: any): DataNode[] =>
      data?.map((item: any) => {
        const { treeName, treeId } = item;
        const sonTitle = (
          <YRItem
            title={treeName}
            key={treeId}
            highlightText={searchValue}
            onClick={() => {
              setSelectedIds([treeId]);
              setActiveNode(item);
            }}
          />
        );
        return {
          ...item,
          title: sonTitle,
          key: treeId,
          children: loop(item.treeDtoList, item),
          parent: parent || null
        };
      });
    return loop(treeData);
  }, [treeData, searchValue]);

  return (
    <YREasyUseModal.Provider>
      <YRSpace direction="vertical">
        {/* <YRInput.Search
          size={'small'}
          placeholder="请输入规则条目名称"
          onChange={(e) => setSearchValue(e.target.value)}
          allowClear
        /> */}
        <YRSpin spinning={queryLoading} style={{ margin: '20px 0px 0px 0px' }}>
          {tree?.length > 0 ? (
            <YRTree
              blockNode
              defaultExpandAll
              onExpand={onExpand}
              selectedKeys={selectedIds}
              expandedKeys={expandedKeys}
              autoExpandParent={autoExpandParent}
              treeData={tree}
              switcherIcon={<CaretDownOutlined />}
            />
          ) : (
            <YREmpty />
          )}
        </YRSpin>
      </YRSpace>
    </YREasyUseModal.Provider>
  );
};

export default EntryTree;
