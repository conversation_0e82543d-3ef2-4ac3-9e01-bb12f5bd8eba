/*
 * @Author: 伍晶晶
 * @Description: 字典类型定义
 * @Date: 2023-03-27 19:00:01
 * @LastEditors: 伍晶晶
 * @LastEditTime: 2023-03-27 20:17:31
 */

type IEnableStatus = 'Y' | 'N';

export interface IData {
  itemId: string;
  dictKey: string;
  createTime: string;
  updateTime: string;
  enableStatus: IEnableStatus;
  parentItemKey: string;
  itemKey: string;
  itemName: string;
}

export interface TreeNode {
  dictId?: string;
  dictKey: string;
  dictName: string;
  dictType?: string;
  isChildNode: string;
  tenantId?: string;
  createTime?: string;
  updateTime?: string;
}

export enum KeyEnum {
  KEY = 'dictKey',
  LABEL = 'dictName'
}

export const DefaultNode = { dictKey: 'all', dictName: '全部' };
