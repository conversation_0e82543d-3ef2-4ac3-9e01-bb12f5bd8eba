/**
 * 页面描述: 规则条目历史版本弹框
 * @文件名 HistoryModal.tsx
 * @filePath \src\pages\Setting\RuleManage\Entry\components\HistoryModal.tsx
 * @Date 2023-08-14 20:06:58
 * @Email <EMAIL>
 * <AUTHOR>
 */
import React from 'react';
import {
  YRModal,
  YRTable,
  YRForm,
  YREasyUseModal,
  YRTimeDescription
} from 'yrantd';
import { historyRecordQuery } from '@/services/setting';
import { useAntdTable } from 'ahooks';

const HistoryModal = (props: any) => {
  const modal = YREasyUseModal.useModal();
  const [form] = YRForm.useForm() as any;
  const { itemId } = props;
  const { tableProps, run, params, loading } = useAntdTable((p) => {
    delete p.extra;
    delete p.filters;
    return historyRecordQuery({
      ...p,
      pageNum: p.current,
      itemId
    }).then((res) => {
      return res.success
        ? { list: res?.data || [], total: res?.data?.length || 0 }
        : {
          list: [],
          total: 0
        };
    });
  }, {});

  const generateColumns = () => {
    const columns = [
      {
        title: '规则条目编号',
        dataIndex: 'itemId'
      },
      {
        title: '规则条目版本',
        dataIndex: 'version',
        render: (value) => {
          return value ? `${value}.0` : null;
        }
      },
      {
        title: '修订时间',
        dataIndex: 'createTime',
        width: 120,
        render: (value: string) => <YRTimeDescription time={value} needMillisecond isSmartMode={false} />
      }
    ];

    return columns;
  };

  return (
    <YRModal
      title="历史版本记录"
      width="60%"
      destroyOnClose
      footer={null}
      bodyStyle={{}}
      onOk={modal.remove}
      onCancel={modal.remove}
      open={modal.visible}
    >
      <YRTable
        {...tableProps}
        loading={loading}
        scroll={{ y: 'calc(60vh)' }}
        rowKey={'index'}
        columns={generateColumns()}
      />
    </YRModal>
  );
};

export default YREasyUseModal.create(HistoryModal);
