/*
 * @Description: file content
 * @Author: your name
 * @Date: 2020-10-13 14:02:44
 * @LastEditors: your name
 * @LastEditTime: 2020-11-17 17:28:50
 */
import React, { Fragment, useEffect, useState } from 'react';
import { Y<PERSON><PERSON>on, Y<PERSON><PERSON>s, YREmpty, <PERSON>RPopcon<PERSON>rm, YRS<PERSON>, YRSelect, YRUpload, YRMessage, YRLink } from 'yrantd';
import { useModalChange } from '@/utils/utils';
import ContentBlock from '@/components/ContentBlock';
import TooltipText from '@/components/TooltipText';
import TipContent from '@/components/TooltipText/TipContent';
import BaseInformation from './BaseInformation';
import RequestParams from './RequestParams';
import ResponseParams from './ResponseParams';
import MockParams from './MockParams';
import ModalCommon from './ModalBoxs/ModalCommon';
import './index.less';
import ModalAdd from './ModalBoxs/ModalAdd';
import ModalTest from './ModalBoxs/ModalTest';
import {
  queryByComCode,
  dcDict,
  threeConfPage,
  threeQueryTree,
  queryRespConf,
  threeConfRemove,
  threeConfSwitch,
  threeConfSetMock,
  addThreeConf,
  threeConfSave,
  threeComModify,
  modifyRespConf
} from '@/services/setting';
import { sysFieldList } from '@/utils/data-dictionary';

const { TabPane } = YRTabs;
const { Option } = YRSelect;

function CostInterface(props) {
  const { supplierList, currentSupplier, refreshSupplier } = props;
  const [tableData, setTableData] = useState([]);
  const [currentInterface, setCurrentInterface] = useState({});
  const [respObj, setRespObj] = useState([]);
  const [treeList, setTreeList] = useState([]);
  const [searchCode, setSearchCode] = useState('');
  const [allInterfaceList, setAllInterfaceList] = useState([]);
  const [refresh, setRefresh] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const isUpdate = false;
  const { commonParamList = [] } = currentSupplier;
  const { handleShow: handleShowAdd, handleCancel: handleCancelAdd, modalMessage: modalMessageAdd } = useModalChange();
  const {
    handleShow: handleShowTest,
    handleCancel: handleCancelTest,
    modalMessage: modalMessageTest
  } = useModalChange();

  useEffect(() => {
    if (currentSupplier.comCode) {
      setIsLoading(true);
      queryByComCode({ comCode: currentSupplier.comCode }).then((res) => {
        setIsLoading(false);
        if (res?.data) {
          const data = res?.data;
          setTableData(data);
          setSearchCode('');
          setCurrentInterface(data[0] || {});
        }
      });
    }
  }, [currentSupplier.comCode, refresh]);

  useEffect(() => {
    threeConfPage().then((res) => {
      // this.setState({ loading: false });
      if (res?.data) {
        setAllInterfaceList(res.data);
      }
    });
  }, []);

  useEffect(() => {
    if (currentInterface.comPathCode) {
      getResponses();
      // threeQueryTree({ comPathCode: currentInterface.comPathCode }).then((res) => {
      //   // this.setState({ loading: false });
      //   if (res?.data) {
      //     setTreeList(typeof res.data === 'object' ? [res.data] : []);
      //   }
      // });
    }
  }, [currentInterface.comPathCode]);

  function getResponses() {
    queryRespConf({ comPathCode: currentInterface.comPathCode }).then((res) => {
      if (res?.data) {
        setRespObj(typeof res.data === 'object' ? res.data : {});
      }
    });
  }
  function changeInterface(values) {
    setCurrentInterface(values);
  }

  function deleteInterface() {
    threeConfRemove({ comPathCode: currentInterface.comPathCode }).then((res) => {
      if (!res?.errorMessage) {
        YRMessage.success('删除成功');
        setRefresh(!refresh);
      }
    });
  }

  function switchFun() {
    threeConfSwitch({
      comPathCode: currentInterface.comPathCode,
      mockSwitch: currentInterface.mockSwitch === 0 ? 1 : 0
    }).then((res) => {
      if (!res?.errorMessage) {
        setRefresh(!refresh);
      }
    });
  }

  function saveMockFun(value) {
    threeConfSetMock({ comPathCode: currentInterface.comPathCode, mockData: value }).then((res) => {
      if (!res?.errorMessage) {
        YRMessage.success('操作成功');
        // refreshSupplier();
      }
    });
  }

  function getResponseList() {
    let fieldList = [...(respObj?.fieldList || [])];
    fieldList = fieldList.map((item) => {
      const { type, confType, ...rest } = item;
      let transferType = type;
      if (type === 0 && confType === 2) {
        transferType = 2;
      }
      return { ...rest, confType: transferType };
    });
    return { list: fieldList, pieces: fieldList.length };
  }

  function getRequestList() {
    const { clientType } = currentInterface;
    const requestParams = currentInterface.requestParams || [];
    const accessParams = currentInterface.accessParams || [];
    const requestHeaders = currentInterface.requestHeaders || [];
    const sdkParams = currentInterface.sdkParams || [];
    let requestList;
    if (clientType === 'http') {
      requestList = [...requestParams, ...requestHeaders, ...accessParams];
    } else {
      requestList = sdkParams.map((item) => {
        return {
          paramNameCn: item.sdkParamName,
          paramName: item.sdkParam,
          val: item.val,
          valWay: item.valWay,
          id: item.id,
          type: 2,
          isRequired: item.isRequired,
          isInParam: item.isInParam
        };
      });
      requestList = [...requestList, ...accessParams];
    }
    return { list: requestList, pieces: requestList.length };
  }

  function handleOk(type, params) {
    if (type === 'add') {
      addThreeConf({ ...params }).then((res) => {
        if (!res?.errorMessage) {
          YRMessage.success('新增成功');
          setRefresh(!refresh);
        }
      });
    } else {
      modifyInterface(params);
    }
    handleCancelAdd();
  }

  function modifyInterface(params) {
    const threeDataConf = {
      ...params,
      requestHeaders: currentInterface.requestHeaders,
      requestParams: currentInterface.requestParams,
      sdkParams: currentInterface.sdkParams
    };
    const threeRespConf = {
      comCode: currentSupplier.comCode,
      comPathCode: currentInterface.comPathCode
    };
    params = {
      threeDataConf,
      threeRespConf
    };
    threeConfSave({ ...params }).then((res) => {
      if (!res?.errorMessage) {
        // this.setState({ submitState: false });
        YRMessage.success('保存成功');
        setRefresh(!refresh);
      }
    });
  }

  function saveCommonParams(type, values) {
    let newList = [...commonParamList];
    if (type === 'add') {
      newList.push(values);
    } else if (type === 'edit') {
      const index = newList.findIndex((item) => {
        return item.fieldCode === values.fieldCode;
      });
      if (index > -1) {
        newList[index] = {
          ...newList[index],
          ...values
        };
      }
    } else if (type === 'del') {
      newList = newList.filter((item) => {
        return item.fieldCode !== values.fieldCode;
      });
    }
    threeComModify({
      comCode: currentSupplier.comCode,
      comName: currentSupplier.comName,
      id: currentSupplier.id,
      commonParamList: newList.map((item) => {
        return { ...item, description: '' };
      })
    }).then((res) => {
      if (!res?.errorMessage) {
        YRMessage.success('操作成功');
      }
      refreshSupplier();
    });
  }

  function showTest() {
    handleShowTest('test', currentInterface);
  }

  function searchInterface(value) {
    setSearchCode(value);
    const found = tableData.find((item) => {
      return item.comPathCode === value;
    });
    if (found) {
      setCurrentInterface(found);
    }
  }
  function getTableList() {
    let data = tableData;
    if (searchCode) {
      data = tableData.filter((item) => {
        return item.comPathCode === searchCode;
      });
    }
    return data;
  }

  // 保存请求参数
  function saveRequestParams(modalType, values) {
    const { isCache } = values;
    // 判断是否需要缓存 改变cacheKey的值
    if (modalType !== 'del') {
      if (isCache) {
        let newCache = currentInterface.cacheKey ? currentInterface.cacheKey.split(',') : [];
        newCache.push(values.paramName);
        newCache = newCache.filter((item, i, self) => item && self.indexOf(item) === i);
        currentInterface.cacheKey = newCache.join(',');
      } else {
        let newCache = currentInterface.cacheKey ? currentInterface.cacheKey.split(',') : [];
        newCache = newCache?.filter((val) => {
          return val !== values.paramName;
        });
        currentInterface.cacheKey = newCache.join(',');
      }
    }
    let { requestHeaders, requestParams, sdkParams, ...params } = currentInterface;
    if (!requestHeaders) {
      requestHeaders = [];
    }
    sdkParams = sdkParams || [];
    if (currentInterface.clientType === 'http') {
      // http类型的操作
      if (values.type === 0 || values.type === '0') {
        // 请求头的增加或修改删除
        if (modalType === 'add') {
          requestHeaders.push(values);
        } else {
          const index = requestHeaders.findIndex((ele) => {
            return ele.paramName === values.paramName;
          });
          if (index > -1) {
            if (modalType === 'del') {
              requestHeaders.splice(index, 1);
            } else {
              requestHeaders[index] = values;
            }
          }
        }
        currentInterface.requestHeaders = requestHeaders;
      } else {
        // 路径参数的增加或修改删除
        if (modalType === 'add') {
          requestParams.push(values);
        } else {
          const index = requestParams.findIndex((ele) => {
            return ele.paramName === values.paramName;
          });
          if (index > -1) {
            if (modalType === 'del') {
              requestParams.splice(index, 1);
            } else {
              requestParams[index] = values;
            }
          }
        }
        currentInterface.requestParams = requestParams;
      }
    } else {
      values.sdkParam = values.paramName;
      values.sdkParamName = values.paramNameCn;
      // SDK的操作
      if (modalType === 'add') {
        sdkParams.push(values);
      } else {
        const index = sdkParams.findIndex((ele) => {
          return ele.sdkParam === values.sdkParam;
        });
        if (index > -1) {
          if (modalType === 'del') {
            sdkParams.splice(index, 1);
          } else {
            sdkParams[index] = values;
          }
        }
      }
      currentInterface.sdkParams = sdkParams;
    }
    modifyInterface({ ...params });
  }
  // 删除请求参数
  function deleteRequestParams(values) {
    saveRequestParams('del', values);
  }
  /**
   * @description: 保存响应参数
   * 选中表达式 type=0 conf_type=1;选中脚本  type=1 conf_type=1;预配置  type=0 conf_type=2
   */
  function saveResponseParams(type, values) {
    const { confType, ...rest } = values;
    const params = {
      ...rest,
      type: confType === 1 ? 1 : 0,
      confType: confType === 2 ? 2 : 1
    };
    const fieldList = [...(respObj?.fieldList || [])];

    if (type === 'add') {
      fieldList.push(params);
    } else {
      const index = fieldList.findIndex((item) => {
        return item.fieldName === rest.fieldName;
      });
      if (index > -1) {
        fieldList.splice(index, 1, params);
      }
    }
    doResponseParams(fieldList);
  }

  function doResponseParams(fieldList) {
    modifyRespConf({ comCode: currentInterface.comCode, comPathCode: currentInterface.comPathCode, fieldList }).then(
      (res) => {
        if (!res?.errorMessage) {
          YRMessage.success('操作成功');
          getResponses();
        }
      }
    );
  }
  // 删除响应参数
  function deleteResponseParams(values) {
    const fieldList = [...(respObj?.fieldList || [])];
    const index = fieldList.findIndex((item) => {
      return item.fieldName === values.fieldName;
    });
    if (index > -1) {
      fieldList.splice(index, 1);
    }
    doResponseParams(fieldList);
  }

  const actions = (
    <Fragment>
      <YRSelect
        placeholder="接口名称"
        style={{ width: 200, marginRight: 10 }}
        onChange={searchInterface}
        showSearch
        allowClear
        value={searchCode}
        filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
      >
        {tableData.map((item) => {
          return (
            <Option value={item.comPathCode} key={item.comPathCode}>
              {item.comPathName}
            </Option>
          );
        })}
      </YRSelect>
      <YRLink
        type="primary"
        onClick={() => {
          handleShowAdd('add', {
            comCode: currentSupplier.comCode,
            mockSwitch: 0
          });
        }}
      >
        新增
      </YRLink>
    </Fragment>
  );
  const actions1 = (
    <ModalCommon handleOk={saveCommonParams} title="新增属性" type="add">
      <a>新增属性</a>
    </ModalCommon>
  );
  const requestObj = getRequestList();
  const responseObj = getResponseList();
  const tableList = getTableList();

  return (
    <Fragment>
      <div style={{ margin: '0 0 0 -12px' }}>
        <ContentBlock title="接口" style={{ width: '80%', height: 'calc(100vh - 133px)' }} actions={actions}>
          {tableList.length === 0 ? (
            <YREmpty image={YREmpty.PRESENTED_IMAGE_SIMPLE} style={{ paddingTop: '100px' }} />
          ) : (
            <YRSpin spinning={isLoading || isUpdate}>
              <div className="interface-cont">
                <div className="column-list">
                  {tableList.map((item) => {
                    return (
                      <div
                        className={`row ${item.comPathCode === currentInterface.comPathCode ? 'active' : ''}`}
                        key={item.comPathCode}
                        onClick={() => {
                          changeInterface(item);
                        }}
                      >
                        <div className={item.mockSwitch === 0 ? 'mock-gray' : 'mock-blue'}>MOCK</div>
                        <div className="row-right">
                          <div className="title">
                            <TooltipText value={item.comPathName} title={item.comPathName} />
                          </div>
                          <div className="code">
                            <TooltipText value={`ID:${item.comPathCode}`} title={`ID:${item.comPathCode}`} />
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
                <div className="column-detail">
                  <div className="column-detail-top">
                    <div className="left">
                      <div>{currentInterface.comPathName}</div>
                      <div className="text">
                        <div className={currentInterface.mockSwitch === 0 ? 'mock-gray' : 'mock-blue'}>MOCK</div>
                        <span>ID:{currentInterface.comPathCode}</span>
                      </div>
                    </div>
                    <div className="right">
                      <YRPopconfirm title="确定要删除吗？" onConfirm={deleteInterface}>
                        <YRButton>删除</YRButton>
                      </YRPopconfirm>
                      {/* <YRButton onClick={showTest} className="space">
                        测试
                      </YRButton> */}
                      <YRButton
                        type="primary"
                        onClick={() => {
                          handleShowAdd('edit', currentInterface);
                        }}
                        className={'space'}
                      >
                        修改
                      </YRButton>
                    </div>
                  </div>
                  <YRTabs defaultActiveKey="1" size={window.size}>
                    <TabPane tab="基本信息" key="1">
                      <BaseInformation currentInterface={currentInterface} allInterfaceList={allInterfaceList} />
                    </TabPane>
                    <TabPane tab={`请求参数(${requestObj.pieces})`} key="2">
                      <RequestParams
                        data={requestObj.list}
                        sysFieldList={sysFieldList}
                        cacheKey={currentInterface.cacheKey}
                        clientType={currentInterface.clientType}
                        handleOk={saveRequestParams}
                        handleDelete={deleteRequestParams}
                      />
                    </TabPane>
                    <TabPane tab={`响应参数(${responseObj.pieces})`} key="3">
                      <ResponseParams
                        data={responseObj.list}
                        treeList={treeList}
                        handleOk={saveResponseParams}
                        handleDelete={deleteResponseParams}
                      />
                    </TabPane>
                    <TabPane tab="MOCK" key="4">
                      <MockParams
                        data={{
                          mockSwitch: currentInterface.mockSwitch,
                          mockData: currentInterface.mockData
                        }}
                        switchFun={switchFun}
                        saveMockFun={saveMockFun}
                        comPathCode={currentInterface.comPathCode}
                      />
                    </TabPane>
                  </YRTabs>
                </div>
              </div>
            </YRSpin>
          )}
        </ContentBlock>
        <ContentBlock
          title="公共入参"
          style={{
            width: 'calc(20% - 36px)',
            height: 'calc(100vh - 133px)',
            marginLeft: '0',
            marginRight: '0'
          }}
          actions={actions1}
        >
          <div className={'params-list'}>
            {commonParamList.map((item) => {
              const oper = (
                <Fragment>
                  <ModalCommon handleOk={saveCommonParams} title="修改属性" type="edit" initValues={item}>
                    <a style={{ marginRight: 5 }}>修改</a>
                  </ModalCommon>
                  <YRPopconfirm
                    title="确定要删除吗？"
                    onConfirm={() => {
                      saveCommonParams('del', { fieldCode: item.fieldCode });
                    }}
                  >
                    <a>删除</a>
                  </YRPopconfirm>
                </Fragment>
              );
              return (
                <TipContent title={item.fieldName} value={item.fieldCode} key={item.fieldCode} actions={oper}>
                  <div className={'params-cont'}>
                    <div className={'row'}>参数名:{item.fieldCode}</div>
                    <div className={'title'}>参数说明:{item.fieldName}</div>
                    <div className={'row'}>
                      <span className={'left'}>属性值：</span>
                      <span className={'right'}>{item.fieldVal}</span>
                    </div>
                  </div>
                </TipContent>
              );
            })}
          </div>
        </ContentBlock>
      </div>
      {modalMessageAdd.isShow && (
        <ModalAdd
          handleOk={handleOk}
          handleCancel={() => {
            handleCancelAdd(modalMessageAdd.editType);
          }}
          visible={modalMessageAdd.isShow}
          type={modalMessageAdd.editType}
          initValues={modalMessageAdd.editObj}
          supplierList={supplierList}
          interfaceList={allInterfaceList}
          sysFieldList={sysFieldList}
          treeList={treeList}
        />
      )}
      {modalMessageTest.isShow ? (
        <ModalTest
          handleCancel={() => {
            handleCancelTest(modalMessageTest.editType);
          }}
          visible={modalMessageTest.isShow}
          initValues={modalMessageTest.editObj}
        />
      ) : null}
    </Fragment>
  );
}

export default CostInterface;
