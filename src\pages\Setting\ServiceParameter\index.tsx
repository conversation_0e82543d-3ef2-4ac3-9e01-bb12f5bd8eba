/*
 * @Author: 杜江
 * @Date: 2023-03-06 14:14:04
 * @LastEditTime: 2023-03-10 09:43:04
 * @FilePath: /kylin-admin/src/pages/Setting/ServiceParameter/index.tsx
 * @Description: 业务参数
 */
import { changeBizParamStatus, deleteBizParam, selectPageIMdataBizParamQuery } from '@/services/setting';
import { setDict } from '@/utils/utils';
import MultiViewTable, { ViewTableProps } from '@yr/multi-view-table';
import { SearchListItemTypeEnum, SearchProps } from '@yr/multi-view-table/lib/ViewTable/type';
import { useQueryMultiViewTableData } from '@yr/util';
import { useRequest } from 'ahooks';
import React from 'react';
import { YRButton, YRConfirmBtn, YRDict, YREasyUseModal, YRIndexPageLayout, YRLink, YRMessage } from 'yrantd';
import Add from './Modal/Add';

const BIZ_LINE_TYPE = [
  { itemName: '公共业务参数', itemKey: '001' },
  { itemName: '公司业务参数', itemKey: '002' },
  { itemName: '零售业务参数', itemKey: '003' },
  { itemName: '数字业务参数', itemKey: '004' },
  { itemName: '金市业务参数', itemKey: '005' }
];
setDict('业务条线类型', BIZ_LINE_TYPE);

const ServiceParameter = () => {
  // 查询表格数据
  const { tableProps, refresh, query } = useQueryMultiViewTableData(selectPageIMdataBizParamQuery);

  const { run: runDel } = useRequest(deleteBizParam, {
    manual: true,
    onSuccess: (res) => {
      if (res.success) {
        YRMessage.success('删除成功');
        refresh();
      }
    }
  });

  const { run: runEnableStatus, params: paramsEnableStatus } = useRequest(changeBizParamStatus, {
    manual: true,
    onSuccess: (res) => {
      if (res.success) {
        YRMessage.success(`${paramsEnableStatus[0]?.enableStatus === 'N' ? '禁用' : '启用'}成功`);
        refresh();
      }
    }
  });

  const columns: ViewTableProps['columns'] = [
    { dataIndex: 'paramCode', title: '参数code' },
    { dataIndex: 'paramValue', title: '参数值' },
    { dataIndex: 'paramName', title: '参数名称' },
    {
      dataIndex: 'bizLineType',
      title: '业务条线类型',
      render: (v) => <YRDict.Text dictkey="业务条线类型" defaultValue={v} />
    },
    {
      title: '操作',
      fixed: 'right',
      render: (v, row) => (
        <YRButton.Space>
          <YRLink type="primary">
            <YRConfirmBtn
              msg={`确认${row.enableStatus === 'Y' ? '禁用' : '启用'}"业务参数"吗？`}
              type="pop"
              onConfirm={() =>
                runEnableStatus({ paramId: row.paramId, enableStatus: row.enableStatus === 'Y' ? 'N' : 'Y' })
              }
            >
              {row.enableStatus === 'Y' ? '禁用' : '启用'}
            </YRConfirmBtn>
          </YRLink>
          <YRLink type="primary" onClick={() => YREasyUseModal.show(Add, { type: 'edit', row }).then(refresh)}>
            修改
          </YRLink>
          <YRLink type="primary">
            <YRConfirmBtn msg='确认删除"业务参数"吗？' type="pop" onConfirm={() => runDel({ paramId: row.paramId })}>
              删除
            </YRConfirmBtn>
          </YRLink>
        </YRButton.Space>
      )
    }
  ];

  // 查询条件
  const searchList: SearchProps['searchList'] = [
    { key: 'paramCode', label: '参数code', type: SearchListItemTypeEnum.search },
    {
      key: 'bizLineType',
      label: '业务条线类型',
      type: SearchListItemTypeEnum.select,
      options: BIZ_LINE_TYPE.map((item) => ({ label: item.itemName, value: item.itemKey }))
    }
  ];

  // 操作栏渲染
  const operationRender = (
    <YRButton.Space>
      <YRButton type="primary" onClick={() => YREasyUseModal.show(Add, { type: 'add' }).then(refresh)}>
        新增
      </YRButton>
    </YRButton.Space>
  );

  return (
    <YRIndexPageLayout>
      <MultiViewTable
        business="home"
        viewOpt={{ title: '业务参数' }}
        {...tableProps}
        searchOpt={{ searchList }}
        onSubmit={query}
        operation={operationRender}
        columns={columns}
      />
    </YRIndexPageLayout>
  );
};

export default ServiceParameter;
