/**
 * @Author: liufh23623
 * @Description: 流程管理模块
 * @Date: Created in 2022-12-20 11:30:00
 * @Modifed By:
 */

import { request } from '@yr/util';

/**
 * 查询流程图列表
 * @param param
 */
export function selectPageForMaxVersion(param?: any) {
  return request('/v1/flowModelResourceQuery/selectPageForMaxVersion', {
    param,
    module: MODULES.flowDesigner,
    serviceScene: SCENES.query,
    serviceName: '查询流程图列表'
  });
}

/**
 * 新增流程图基本信息
 * @param param
 */
export function addFlowDiagramResource(param?: any) {
  return request('/v1/flowModelResource/addFlowModelResource', {
    param,
    module: MODULES.flowDesigner,
    serviceScene: SCENES.apply,
    serviceName: '新增流程图'
  });
}

/**
 * 删除流程图
 * @param param
 */
export function deleteFlowDiagramResource(param?: any) {
  return request('/v1/flowModelResource/deleteFlowModelResource', {
    param,
    module: MODULES.flowDesigner,
    serviceScene: SCENES.apply,
    serviceName: '删除流程图'
  });
}

/**
 * 详情查询流程图
 * @param param
 */
export function queryFlowDiagramResourceDetail(param?: any) {
  return request('/v1/flowModelResourceQuery/queryFlowModelResourceDetail', {
    param,
    module: MODULES.flowDesigner,
    serviceScene: SCENES.apply,
    serviceName: '详情查询流程图'
  });
}

/**
 * 更新流程图
 * @param param
 */
export function modifyFlowDiagramResource(param?: any) {
  return request('/v1/flowModelResource/modifyFlowModelResource', {
    param,
    module: MODULES.flowDesigner,
    serviceScene: SCENES.apply,
    serviceName: '更新流程图'
  });
}

/**
 * 部署流程图
 * @param param
 */
export function deployProcess(param?: any) {
  return request('/v1/manage-model/deployProcess', {
    param,
    module: MODULES.flowDesigner,
    serviceScene: SCENES.apply,
    serviceName: '部署流程图'
  });
}

/**
 * 部署决策表
 * @param param
 */
export function deployDmn(param?: any) {
  return request('/v1/manage-model/deployDmn', {
    param,
    module: MODULES.flowDesigner,
    serviceScene: SCENES.apply,
    serviceName: '部署决策表'
  });
}

/**
 * 查询岗位树
 * @param param
 */
export function queryFlowApprovalStationList(param?: any) {
  return request('/IFlowApprovalStation/queryFlowApprovalStationList', {
    param,
    module: MODULES.flowManagement,
    serviceScene: SCENES.query,
    serviceName: '查询岗位树'
  });
}
/**
 * 查询流程版本
 * @param param
 */
export function queryFlowDiagramResourceVersion(param?: any) {
  return request('/v1/flowModelResourceQuery/queryFlowModelResourceVersion', {
    param,
    module: MODULES.flowDesigner,
    serviceScene: SCENES.apply,
    serviceName: '查询流程版本'
  });
}

/**
 * 查询子流程引用元素
 * @param param
 */
export function queryFlowDiagramAndVersion(param?: any) {
  return request('/v1/flowModelResourceQuery/queryFlowModelAndVersion', {
    param,
    module: MODULES.flowDesigner,
    serviceScene: SCENES.apply,
    serviceName: '查询子流程引用元素'
  });
}

/** 查询参数库列表 */
export function queryFlowParamLibraryList(param?: any) {
  return request('/IFlowParamLibrary/queryFlowParamLibraryList', {
    param,
    module: MODULES.flowManagement,
    serviceScene: SCENES.query,
    serviceName: '查询参数库列表'
  });
}

/** 修改参数库 */
export function modifyFlowParamLibrary(param?: any) {
  return request('/IFlowParamLibrary/modifyFlowParamLibrary', {
    param,
    module: MODULES.flowManagement,
    serviceScene: SCENES.query,
    serviceName: '修改参数库'
  });
}

/** 删除参数库 */
export function deleteFlowParamLibrary(param?: any) {
  return request('/IFlowParamLibrary/deleteFlowParamLibrary', {
    param,
    module: MODULES.flowManagement,
    serviceScene: SCENES.query,
    serviceName: '删除参数库'
  });
}

/** 新增参数库 */
export function addFlowParamLibrary(param?: any) {
  return request('/IFlowParamLibrary/addFlowParamLibrary', {
    param,
    module: MODULES.flowManagement,
    serviceScene: SCENES.query,
    serviceName: '新增参数库'
  });
}

/** 查询参数项详情列表 */
export function selectPage(param?: any) {
  return request('/IFlowParamItem/selectPage', {
    param,
    module: MODULES.flowManagement,
    serviceScene: SCENES.query,
    serviceName: '查询参数项详情列表'
  });
}

/** 新增参数项 */
export function addFlowParamItem(param?: any) {
  return request('/IFlowParamItem/addFlowParamItem', {
    param,
    module: MODULES.flowManagement,
    serviceScene: SCENES.query,
    serviceName: '新增参数项'
  });
}

/** 修改参数项 */
export function modifyFlowParamItem(param?: any) {
  return request('/IFlowParamItem/modifyFlowParamItem', {
    param,
    module: MODULES.flowManagement,
    serviceScene: SCENES.query,
    serviceName: '修改参数项'
  });
}

/** 删除参数项 */
export function deleteFlowParamItem(param?: any) {
  return request('/IFlowParamItem/deleteFlowParamItem', {
    param,
    module: MODULES.flowManagement,
    serviceScene: SCENES.query,
    serviceName: '删除参数项'
  });
}

/** 查询流程版本状态列表 */
export function queryFlowState(param?: any) {
  return request('/IProcess/queryFlowState', {
    param,
    module: MODULES.flowManagement,
    serviceScene: SCENES.query,
    serviceName: '查询流程版本状态列表'
  });
}

/** 挂起流程版本状态 */
export function suspendProcessDefinitionByDeployId(param?: any) {
  return request('/IProcess/suspendProcessDefinitionByDeployId', {
    param,
    module: MODULES.flowManagement,
    serviceScene: SCENES.query,
    serviceName: '挂起流程版本状态'
  });
}

/** 激活流程版本状态 */
export function activateProcessDefinitionByDeployId(param?: any) {
  return request('/IProcess/activateProcessDefinitionByDeployId', {
    param,
    module: MODULES.flowManagement,
    serviceScene: SCENES.query,
    serviceName: '激活流程版本状态'
  });
}

/** 查询服务节点节点配置信息 */
export function queryServiceTaskDelegates(param?: any) {
  return request('/IFlowDiagramConfig/queryServiceTaskDelegates', {
    param,
    module: MODULES.flowManagement,
    serviceScene: SCENES.query,
    serviceName: '查询服务节点节点配置信息'
  });
}

/** 条件查询参数库列表信息 */
export function selectPageParamLibrary(param?: any) {
  return request('/IFlowParamLibrary/selectPageParamLibrary', {
    param,
    module: MODULES.flowManagement,
    serviceScene: SCENES.query,
    serviceName: '条件查询参数库列表信息'
  });
}

/** 绑定参数库 */
export function addFlowParamLibraryRef(param?: any) {
  return request('/IFlowParamLibraryRef/addFlowParamLibraryRef', {
    param,
    module: MODULES.flowManagement,
    serviceScene: SCENES.query,
    serviceName: '绑定参数库'
  });
}

/** 查询参数项 */
export function selectList(param?: any) {
  return request('/IFlowParamItem/selectList', {
    param,
    module: MODULES.flowManagement,
    serviceScene: SCENES.query,
    serviceName: '查询参数项'
  });
}

/** 查询参数项,只查 fields */
export function selectListByParamLibraryId(param?: any) {
  return request('/IFlowParamItem/selectListByParamLibraryId', {
    param,
    module: MODULES.flowManagement,
    serviceScene: SCENES.query,
    serviceName: '查询参数项,只查 fields'
  });
}

/**
 * 导入流程定义
 * @param param
 */
export function fileImport(param?: any) {
  return request('/v1/flowModelResource/fileImport', {
    param,
    module: MODULES.flowDesigner,
    serviceScene: SCENES.query,
    serviceName: '导入流程定义'
  });
}

/**
 * 导出流程定义
 * @param param
 */
export function fileExport(param?: any) {
  return request('/v1/flowModelResource/fileExport', {
    param,
    module: MODULES.flowDesigner,
    serviceScene: SCENES.query,
    serviceName: '导出流程定义'
  });
}
// 用户节点执行程序
export function queryUserTaskTaskListener(param?: any) {
  return request('/IFlowDiagramConfig/queryUserTaskTaskListener', {
    param,
    module: MODULES.flowManagement,
    serviceScene: SCENES.query,
    serviceName: '用户节点执行程序'
  });
}
// 节点执行程序
export function queryUserTaskExecutionListener(param?: any) {
  return request('/IFlowDiagramConfig/queryUserTaskExecutionListener', {
    param,
    module: MODULES.flowManagement,
    serviceScene: SCENES.query,
    serviceName: '节点执行程序'
  });
}
