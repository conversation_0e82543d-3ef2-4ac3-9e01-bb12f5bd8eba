/**
 * @Author: wangyw26123
 * @Description: 用户管理
 * @Date: Created in 2022-12-08 20:10:04
 * @Modifed By:
 */

import React, { createContext, useState } from 'react';
import { YRFlexPageLayout } from 'yrantd';
import OrganTree from '../../Organ/OrganManage/components/OrganTree';
import UserList from '../components/UserList';
import { useAntdTable } from 'ahooks';
import { queryUserPageInfo } from '../../../services/user';

export const TreeContext = createContext<{ isHasResetTree: Boolean; setIsHasResetTree: React.Dispatch<any> } | {}>({});

const UserMange = () => {
  const [currentOrganId, setCurrentOrganId] = useState('');
  const [isHasResetTree, setIsHasResetTree] = useState(true);
  const organTreeCallback = (val) => {
    const rootNode = val[0] || {};
    setCurrentOrganId(rootNode.organId);
  };
  const { run, tableProps } = useAntdTable(
    (param) =>
      queryUserPageInfo({
        ...param,
        pageNum: param.current || 1,
        current: param.current || 1,
        pageSize: param.pageSize || 10
      }).then((res) => {
        if (res?.success) {
          if (!param?.organId) {
            setIsHasResetTree(true);
          }
          return {
            list: res.data.list || [],
            total: res.data.total || 0
          };
        } else {
          return {
            list: [],
            total: 0
          };
        }
      }),
    {
      defaultParams: [
        {
          current: 1,
          pageNum: 1,
          pageSize: 10,
          organId: undefined
        }
      ]
    }
  );
  return (
    <YRFlexPageLayout>
      <TreeContext.Provider value={{ isHasResetTree, setIsHasResetTree }}>
        <YRFlexPageLayout.Sider title="机构列表">
          <OrganTree
            callback={organTreeCallback}
            fieldNames={{
              key: 'organId',
              title: 'organName',
              children: 'children'
            }}
            run={run}
          />
        </YRFlexPageLayout.Sider>
        <UserList currentOrganId={currentOrganId} run={run} tableProps={tableProps} />
      </TreeContext.Provider>
    </YRFlexPageLayout>
  );
};

export default UserMange;
