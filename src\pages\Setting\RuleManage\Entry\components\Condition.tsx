/**
 * 页面描述:条件组件
 * @文件名 Condition.tsx
 * @filePath \src\pages\Setting\RuleManager\Entry\components\Condition.tsx
 * @Date 2023-08-08 16:12:49
 * @Email <EMAIL>
 * <AUTHOR>
 */
import React, { useEffect, useState } from 'react';
import { useUnmount } from 'ahooks';
import { YRIcon } from 'yrantd';
import store from '@/store';
import { defaultLeft, defaultSpace, defaultWidth2, defaultTop, defaultHeight, defaultTopSpace } from '../config';
import style from '../index.module.less';
import ParameterValue from './ParameterValue';
import ComparisonOperator from './ComparisonOperator';

const SVG_NS = 'http://www.w3.org/2000/svg';

const Condition = (props) => {
  const { level, number, parentNumber, svgId = 'ifSvg', id, parentId, removeItem } = props;
  const [ruleState, ruleDispatch] = store.useModel('rule');
  const { type, ruleDataMap } = ruleState;
  const itemData = ruleDataMap[id] || {};
  const [path, setPath] = useState(null) as any;
  const left = (level - 1) * defaultSpace + defaultLeft - 44;

  useEffect(() => {
    if (number > 1) {
      draw(level, parentNumber, number);
    }
  }, [level, parentNumber, number]);

  useUnmount(() => {
    const svg = document.getElementById(`${svgId}`);
    if (path && path.parentNode === svg) {
      svg && svg.removeChild && svg.removeChild(path);
    }
  });

  // 绘制路径
  const draw = (drawLevel, drawParentNumber, drawNumber) => {
    const start = {
      x: (drawLevel - 2) * defaultSpace + defaultWidth2 + defaultLeft,
      y: (drawParentNumber - 1) * defaultHeight + defaultTop + defaultTopSpace
    };
    const end = {
      x: (drawLevel - 1) * defaultSpace + defaultLeft,
      y: (drawNumber - 1) * defaultHeight + defaultHeight / 2 + defaultTopSpace
    };
    const pos = getPos(start, end);
    if (path === null) {
      const newPath = document.createElementNS(SVG_NS, 'path');
      newPath.setAttribute('fill', 'none');
      newPath.setAttribute('stroke-width', '2px');
      newPath.setAttribute('stroke', 'rgba(207, 219, 230, 1)');
      const svgDom = document.getElementById(`${svgId}`);
      svgDom && svgDom.appendChild(newPath);
      newPath.setAttribute('d', pos);
      setPath(newPath);
    } else {
      path.setAttribute('d', pos);
    }
  };

  // 获取连线坐标
  const getPos = (start, end) => {
    return `M${start.x},${start.y} C${start.x},${end.y} ${start.x},${end.y} ${end.x},${end.y}`;
  };

  // 改变类型
  const changeType = (key, value) => {
    const newItem = {
      ...(ruleDataMap[id] || {}),
      [key]: value
    };
    // 比较符是为空或者不为空时，右侧参数不传
    if (key === 'op' && ['为空', '不为空'].includes(value) && newItem?.rightVariableRuleDto) {
      delete newItem?.rightVariableRuleDto;
    }
    const newData = {
      ...ruleDataMap,
      [id]: newItem
    } as any;
    ruleDispatch.setRuleDataMap(newData);
  };

  // 删除节点
  const remove = () => {
    removeItem(parentId, id);
  };

  return (
    <div className={style['if-item']}>
      <span className={style['if-item-rownum']}>{number}</span>
      <span style={{ paddingLeft: left }}>
        <ParameterValue leftValue={itemData.leftVariableRuleDto} changeType={changeType} />
        <ComparisonOperator op={itemData.op} changeType={changeType} rightValue={itemData.rightVariableRuleDto || {}} />
      </span>
      {type !== 'detail' && (
        <span className={style['if-item-remove']} onClick={remove}>
          <YRIcon icon="yunrongXXclose" fontSize={'14px'} />
        </span>
      )}
    </div>
  );
};

export default Condition;
