/**
 * @Author: 周国强
 * @Description: 全局颜色常量
 * @Date: Created in 2023-02-06 11:25:26
 * @Modifed By:
 */
import {
  generate,
  presetPalettes,
  presetDarkPalettes,
  presetPrimaryColors,
  red,
  volcano,
  orange,
  gold,
  yellow,
  lime,
  green,
  cyan,
  blue,
  geekblue,
  purple,
  magenta,
  gray
} from '@ant-design/colors';
import { EnumStatus } from './common';

// 组织机构

/**
 * @状态名称: Demo
 * @dictkey: 字典名称
 */

const EnumDemoColor = {
  '001': generate[3],
  '002': presetPalettes[3],
  '003': presetDarkPalettes[3],
  '004': presetPrimaryColors[3],
  '005': red[3],
  '006': volcano[3],
  '007': orange[3],
  '008': gold[3],
  '009': yellow[3],
  '010': lime[3],
  '011': green[3],
  '012': cyan[3],
  '013': blue[3],
  '014': geekblue[3],
  '015': magenta[3],
  '016': purple[3],
  '017': gray[3]
};

/**
 * @状态名称: 机构状态
 * @dictkey: EnumOrganStatus
 */
const EnumOrganStatusColor = {
  '001': gray[3], // 未启用
  '002': green[3], // 启用
  '003': gray[3] // 停用
};
/**
 * @状态名称: 机构类型
 * @dictkey: EnumOrganType
 */
const EnumOrganTypeColor = {
  '001': blue[4], // 机构
  '002': orange[4] // 部门
};

/**
 * @状态名称: 机构性质
 * @dictkey: EnumOrganNature
 */
const EnumOrganNatureColor = {
  '001': green[3], // 一般机构
  '002': volcano[3], // 放款中心
  '003': geekblue[3], // 集中作业中心
  '004': orange[3], // 审批中心
  '005': magenta[3] // 保全中心
};
/**
 * @状态名称: 机构级别
 * @dictkey: EnumOrganLevel
 */
const EnumOrganLevelColor = {
  '001': green[4], // 总行
  '002': volcano[4], // 总行部室
  '003': geekblue[4], // 省级区域
  '004': orange[4], // 市级区域
  '005': magenta[4], // 中心区域
  '006': cyan[4] // 营业机构
};
/**
 * @状态名称: 账务机构类型
 * @dictkey: EnumAcctOrganType
 */
const EnumAcctOrganTypelColor = {
  '001': blue[4], // 一般记账机构
  '002': volcano[4], // 国际业务记账机构
  '003': green[4] // 贴现业务记账机构
};

// 用户
/**
 * @状态名称: 用户状态
 * @dictkey: EnumGuardUserStatus
 */
const EnumGuardUserStatusColor = {
  '010': green[3], // 启用
  '020': gray[3], // 停用
  '030': red[3], // 注销
  '040': gray[3] // 未启用
};
// 角色
/**
 * @状态名称: 角色级别
 * @dictkey: EnumRoleLevel
 */
const EnumRoleLevelColor = {
  '001': green[3], // 总行
  '002': volcano[3], // 总行部室
  '003': red[3], // 省级区域
  '004': red[3], // 市级区域
  '005': geekblue[3], // 中心区域
  '006': magenta[3], // 营业机构
  '099': cyan[3] // 通用
};
/**
 * @状态名称: 角色类型
 * @dictkey: EnumGuardRoleType
 */
const EnumGuardRoleTypeColor = {
  '000': gold[4], // 审批角色
  '001': green[3], // 催收角色
  '002': volcano[3], // 业务角色
  '003': red[3], // 核算角色
  '004': lime[6], // 营销角色
  '005': geekblue[3], // 风控角色
  '006': blue[3], // 报表角色
  '007': orange[3], // 管理后台角色
  '008': magenta[3], // 权限组
  '009': cyan[3] // 机构管理员角色
};
/**
 * @状态名称: 角色所属条线
 * @dictkey: EnumRoleBizLine
 */
const EnumRoleBizLineColor = {
  '001': green[3], // 公司银行
  '002': volcano[3], // 零售银行
  '003': red[3], // 数字银行
  '004': blue[3], // 金融市场
  '005': magenta[3] // 通用
};
/**
 * @状态名称: 角色状态
 * @dictkey: EnumRoleStatus
 */
const EnumRoleStatusColor = {
  '001': gray[3], // 未启用
  '002': green[3], // 启用
  '003': gray[3] // 停用
};

// 风险探测
/**
 * @状态名称: 规则条目状态
 * @dictkey: EnumRuleItemStatus
 */
const EnumRuleItemStatusColor = {
  '010': green[3], // 启用
  '020': gray[3], // 停用
  '030': red[3] // 删除
};

/**
 * @状态名称: 场景状态
 * @dictkey: EnumSceneStatus
 */
const EnumSceneStatusColor = {
  '010': green[3], // 启用
  '020': gray[3], // 停用
  '030': red[3] // 删除
};


// 流程
/**
 * @状态名称: 流程类型
 * @dictkey: flowSceneType
 */
const flowSceneTypeColor = {
  1: green[3],
  2: volcano[3],
  3: red[3],
  4: blue[3],
  5: geekblue[3],
  6: lime[3],
  7: magenta[3],
  8: cyan[3],
  9: volcano[4],
  10: geekblue[5],
  11: red[5],
  12: blue[5],
  13: green[6],
  14: gray[3]
};

// 证件类型颜色枚举
const ENUM_CERTIFICATEKIND_COLOR = {
  '001': orange[3],
  '002': blue[3],
  '003': cyan[3],
  '004': orange[3],
  '005': red[3],
  '006': yellow[3],
  '007': purple[3],
  '008': geekblue[3]
};

export {
  EnumGuardUserStatusColor,
  EnumOrganStatusColor,
  EnumOrganLevelColor,
  EnumOrganNatureColor,
  EnumOrganTypeColor,
  flowSceneTypeColor,
  EnumRoleLevelColor,
  EnumGuardRoleTypeColor,
  EnumRoleBizLineColor,
  EnumAcctOrganTypelColor,
  EnumDemoColor,
  EnumRoleStatusColor,
  EnumRuleItemStatusColor,
  EnumSceneStatusColor,
  ENUM_CERTIFICATEKIND_COLOR
};
