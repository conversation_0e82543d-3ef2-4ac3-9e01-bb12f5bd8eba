import { registerMicroApp } from '@/services/config';
import { isInIcestark } from '@ice/stark-app';
import { processInfo } from '@/constant/workplat/processInfo';
import { YRMicroUtils } from 'yrantd';
import packageInfo from '../../package.json';

// 格式化menuData
export function formatter(data) {
  data = data.filter((o) => !o.redirect);
  return data.map((item) => {
    const result = { ...item };
    if (item.children) {
      result.children = formatter(item.children);
    }
    delete result.component;
    return result;
  });
}

/**
 * 构建路由参数
 * @param routes
 * @param pageType
 * @returns {*}
 */
export function buildRoutes(routes, pageType) {
  return routes.map((route) => {
    route.appId = packageInfo.name;
    route.appIdList = [packageInfo.name];
    route.pageType = pageType;
    if (route.permit) {
      route.menuId = typeof route.permit !== 'string' ? route.permit.id : route.permit;
    }
    if (route.children) {
      buildRoutes(route.children, pageType);
    }
    return route;
  });
}

/**
 * 构建纯净路由对象
 * @param routes
 * @returns {*}
 */
export function buildPureAppRoutes(routes) {
  return routes.map((route) => {
    let children = null;
    if (route.children) {
      children = buildPureAppRoutes(route.children);
    }
    return {
      ...route,
      children,
      component: undefined,
      routeWrappers: undefined
    };
  });
}

/**
 * 注册微应用
 * @param routes
 * @returns {*}
 */
export function register(routes) {
  YRMicroUtils.outputDeployInfo(DEPLOY);
  const processInfoData = Object.keys(processInfo).map((k) => ({
    ...processInfo[k],
    businessSign: packageInfo.businessSign
  }));
  const extra = {
    processInfo: processInfoData,
    deploy: DEPLOY
  };
  return YRMicroUtils.register(packageInfo, envType, PUBLIC_PATH, isInIcestark(), routes, extra, registerMicroApp);
}
