import React, { PureComponent } from 'react';
import { history } from 'ice';
import { isInIcestark } from '@ice/stark-app';
import { parse } from 'query-string';
import { PageLoading } from '@yr/pro-layout';
import store from '@/store';
import { router, changeTheme } from '@/utils/utils';

const { withModel } = store;

interface SecurityLayoutProps {
  [key: string]: any;
}

interface SecurityLayoutStata {
  isReady: boolean;
}

/**
 * 登录判断 以及菜单信息获取
 */
@withModel('user', ([, dispatchers]) => ({
  getMenuData: dispatchers.getMenuData
}))
@withModel('layout', ([state]) => ({
  initialState: state.initialState
}))
class SecurityLayout extends PureComponent<SecurityLayoutProps, SecurityLayoutStata> {
  constructor(props) {
    super(props);
    router.setRouter(history);
    this.state = {
      isReady: false
    };
    // 无用户信息跳转登录
    if (!isInIcestark() && !sessionStorage.userInfo) {
      window.location.href = '/login/login.html';
    }

    // 全局变量记录信息
    window['g_userInfo'] = sessionStorage.userInfo ? JSON.parse(sessionStorage.userInfo) : {};
  }

  componentDidMount = () => {
    const { getMenuData, initialState } = this.props;
    getMenuData().then(() => {
      this.setState({ isReady: true });
    });

    changeTheme(initialState.settings.primaryColor);
  };

  static getDerivedStateFromError() {
    return {
      isReady: false
    };
  }

  render() {
    const { children, location } = this.props;
    const { isReady } = this.state;
    // 修复location无query属性
    location.query = location.search ? parse(location.search) : {};
    if (!isReady) {
      return <PageLoading />;
    }
    return children;
  }
}

export default SecurityLayout;
