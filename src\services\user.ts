/**
 * @页面描述: 用户列表接口
 * @文件名 user.ts
 * @Path src\services\user.ts
 * @Date 2023-03-22 15:41:30
 * <AUTHOR>
 */

import { MODULES, request, SCENES } from '@yr/util';

/**
 * 查询所属hr机构下的业务机构
 * @param param
 */
export function queryBelongHrOrganList(param: any) {
  return request('/base/IOrganQuery/queryBelongHrOrganList', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '查询所属hr机构下的业务机构'
  });
}

/**
 * 用户注册
 * @param param
 */
export function registerUser(param: any) {
  return request('/base/IUser/registerUser', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '用户注册'
  });
}

/**
 * 用户分页查询
 * @param param
 */
export function queryUserPageInfo(param) {
  return request('/base/IUserQuery/queryUserPageInfo', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '用户分页查询'
  });
}

/**
 * 重置密码
 * @param param
 */
export function resetPassword(param) {
  return request('/base/IUser/resetPassword', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '重置密码'
  });
}

/**
 * 超管权限移交
 * @param param
 */
export function handleOverAdminPermission(param) {
  return request('/base/IUser/handOverSuperAdmin', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '超管权限移交'
  });
}

/**
 * 三方系统查询用户信息
 * @param param
 */
export function queryThirdUser(param) {
  return request('/base/IUserQuery/queryThirdUser', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '三方系统查询用户信息'
  });
}

/**
 * 新增用户
 * @param param
 */
export function addUser(param) {
  return request('/base/IUser/addUser', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '新增用户'
  });
}

/**
 * 用户详情查询
 * @param param
 */
export function queryUserDetailWithIdentify(param) {
  return request('/base/IUserQuery/queryUserDetailWithIdentify', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '用户详情查询'
  });
}

/**
 * 修改用户
 * @param param
 */
export function modifyUser(param) {
  return request('/base/IUser/modifyUser', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '修改用户'
  });
}

/**
 * 分配角色查询角色列表
 * @param param
 */
export function queryRoleList(param) {
  return request('/base/IRoleQuery/queryList', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '分配角色查询角色列表'
  });
}

/**
 * 用户状态修改
 * @param param
 */
export function changeUserStatus(param) {
  return request('/base/IUser/changeUserStatus', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '用户状态修改'
  });
}

/**
 * 用户删除
 * @param param
 */
export function deleteUser(param) {
  return request('/base/IUser/deleteUser', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '用户删除'
  });
}

/**
 * 用户机构变更
 * @param param
 */
export function changeUserOrgan(param) {
  return request('/base/IUser/changeUserOrgan', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '用户机构变更'
  });
}

/**
 * 用户机构变更审批
 * @param param
 */
export function changeUserOrganApproval(param) {
  return request('/management/changeUserOrganApproval', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '用户机构变更审批'
  });
}

/**
 * 查询用户下管护客户和业务数据
 * @param param
 */
export function queryUserCustomerAndBiz(param) {
  return request('/base/IUserQuery/queryUserCustomerAndBiz', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '查询用户下管护客户和业务数据'
  });
}

/**
 * 分配角色
 * @param param
 */
export function allocateUserRoles(param) {
  return request('/base/IUser/allocateUserRoles', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '分配角色'
  });
}

/**
 * 分页查询客户经理助理关联
 * @param param
 */
export function queryManagerAssisantPage(param) {
  return request('/base/IUserQuery/queryManagerAssisantPage', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '分页查询客户经理助理关联'
  });
}

/**
 * 新增客户经理助理关联
 * @param param
 */
export function addManagerAssisantRel(param) {
  return request('/base/IUser/addManagerAssisantRel', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '新增客户经理助理关联'
  });
}

/**
 * 删除客户经理助理关联
 * @param param
 */
export function deleteManagerAssisantRel(param) {
  return request('/base/IUser/deleteManagerAssisantRel', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '删除客户经理助理关联'
  });
}

/**
 * 查询用户注册时的机构树
 * @param param
 */
export function queryRegOrganTree(param) {
  return request('/base/IOrganQuery/queryOrganTree', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '查询用户注册时的机构树'
  });
}

/**
 * 删除用户身份
 * @param param
 */
export function deleteUserIdentity(param) {
  return request('/base/IUser/deleteUserIdentity', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.apply,
    serviceName: '删除用户身份'
  });
}

/**
 * 柜员分页查询
 * @param param
 */
export function queryTellerPage(param) {
  return request('/base/IUserQuery/queryTellerPage', {
    param,
    module: MODULES.guard,
    serviceScene: SCENES.query,
    serviceName: '柜员分页查询'
  });
}
