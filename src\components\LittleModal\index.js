/*
 * @Description: file content
 * @Author: your name
 * @Date: 2020-12-05 15:32:33
 * @LastEditors: your name
 * @LastEditTime: 2020-12-05 16:11:01
 */
/*
 * @Description: 小弹出框
 *  属性说明：
 *  title 名称
 *  placement 弹框位置
 *  actions 操作按钮
 *  content 弹框内容
 *  okText 保存文本
 *  cancelText 取消文本
 *  onOk 保存事件
 *  onCancel 取消事件
 */

import React, { useState } from 'react';
import { YRPopover, YRButton } from 'yrantd';
import styles from './index.module.less';

export default function LittleModal(props) {
  const {
    title,
    placement = 'bottom',
    children,
    actions,
    content,
    cancelText,
    okText,
    onCancel,
    onOk,
    footer,
    width,
    needCloseIcon,
    ...rest
  } = props;
  const [clicked, setClicked] = useState(false);
  function handleChange(visible) {
    if (onCancel && !visible) {
      onCancel();
    }
    changeVisible(visible);
  }

  function changeVisible(visible) {
    setClicked(visible);
  }
  function handleSave() {
    if (onOk) {
      onOk({ callback: changeVisible });
    }
  }

  function handleCancel() {
    if (onCancel) {
      onCancel();
    }
    changeVisible(false);
  }

  return (
    <YRPopover
      {...rest}
      maskClosable={false}
      content={
        <div className={styles['little-modal-cont']}>
          <div>{content}</div>
          {footer === null ? null : footer !== undefined ? (
            footer
          ) : (
            <div className={styles['little-modal-buttons']}>
              <YRButton onClick={handleCancel}>{cancelText || '取消'}</YRButton>
              <YRButton type="primary" onClick={handleSave} className={styles['space']}>
                {okText || '保存'}
              </YRButton>
            </div>
          )}
        </div>
      }
      title={
        <div className={styles['little-modal-title']}>
          <div className={styles['text']}>{title}</div>
          {actions && <div className={styles['oper']}>{actions}</div>}
          {needCloseIcon && (
            <YRIcon icon="yunrongXXclose" fontSize="14px" onClick={handleCancel} />
          )}
        </div>
      }
      trigger="click"
      visible={clicked}
      onVisibleChange={handleChange}
      placement={placement}
      overlayClassName={'little-modal'}
      overlayStyle={{ width: width || '400px' }}
    >
      {children}
    </YRPopover>
  );
}
