/**
 * 页面描述: 规则条目历史版本弹框
 * @文件名 HistoryModal.tsx
 * @filePath \src\pages\Setting\RuleManage\Entry\components\HistoryModal.tsx
 * @Date 2023-08-14 20:06:58
 * @Email <EMAIL>
 * <AUTHOR>
 */
import React from 'react';
import {
  YRModal,
  YRTable,
  YRForm,
  YRTooltip,
  YREasyUseModal
} from 'yrantd';
import { quoteRecord } from '@/services/setting';
import { useAntdTable } from 'ahooks';
import { businessStageNames, tacheNameAndStage } from '../../Scene/config';
import { setDict } from '@/utils/utils';
import { getDictName } from '@yr/util';

setDict('EnumOperateType', [
  { itemKey: '001', itemName: '不检查' },
  { itemKey: '002', itemName: '禁止类' },
  { itemKey: '003', itemName: '提示类' },
  { itemKey: '004', itemName: '确认类' }
]);
setDict('EnumRuleType', [
  { itemKey: '001', itemName: '通用规则' },
  { itemKey: '002', itemName: '产品规则' }
]);

const RelationModal = (props: any) => {
  const modal = YREasyUseModal.useModal();
  const [form] = YRForm.useForm() as any;
  const { itemId } = props;
  const { tableProps, run, params, loading } = useAntdTable((p) => {
    delete p.extra;
    delete p.filters;
    return quoteRecord({
      ...p,
      pageNum: p.current,
      itemId
    }).then((res) => {
      return res.success
        ? { list: res?.data || [], total: res?.data?.length || 0 }
        : {
          list: [],
          total: 0
        };
    });
  }, {});

  const generateColumns = () => {
    const columns = [
      {
        title: '场景编号',
        dataIndex: 'sceneId',
        render: (value) => {
          return (
            <YRTooltip onlyShow title={value}>
              <div className="ellipsis">{value || CONST.null}</div>
            </YRTooltip>
          );
        }
      },
      {
        title: '场景名称',
        dataIndex: 'sceneName',
        render: (value) => {
          return (
            <YRTooltip onlyShow title={value}>
              <div className="ellipsis">{value || CONST.null}</div>
            </YRTooltip>
          );
        }
      },
      {
        title: '规则分类',
        dataIndex: 'ruleType',
        width: 120,
        render: (value) => {
          const dictName = getDictName('EnumRuleType', value);
          return (
            <YRTooltip onlyShow title={dictName}>
              <div className="ellipsis">{dictName || CONST.null}</div>
            </YRTooltip>
          );
        }
      },
      {
        title: '业务阶段',
        dataIndex: 'businessStage',
        width: 120,
        render: (value) => {
          const dictName = businessStageNames[value];
          return (
            <YRTooltip onlyShow title={dictName}>
              <div className="ellipsis">{dictName || CONST.null}</div>
            </YRTooltip>
          );
        }
      },
      {
        title: '业务环节',
        dataIndex: 'businessTache',
        width: 120,
        render: (value) => {
          const dictName = tacheNameAndStage[value].name;
          return (
            <YRTooltip onlyShow title={dictName}>
              <div className="ellipsis">{dictName || CONST.null}</div>
            </YRTooltip>
          );
        }
      },
      {
        title: '校验策略',
        dataIndex: 'operateType',
        width: 120,
        render: (value) => {
          const dictName = getDictName('EnumOperateType', value);
          return (
            <YRTooltip onlyShow title={dictName}>
              <div className="ellipsis">{dictName || CONST.null}</div>
            </YRTooltip>
          );
        }
      }
    ];

    return columns;
  };

  return (
    <YRModal
      title="引用关系记录"
      width="60%"
      destroyOnClose
      footer={null}
      bodyStyle={{}}
      onOk={modal.remove}
      onCancel={modal.remove}
      open={modal.visible}
    >
      <YRTable
        {...tableProps}
        loading={loading}
        scroll={{ y: 'calc(60vh)' }}
        rowKey={'index'}
        columns={generateColumns()}
      />
    </YRModal>
  );
};

export default YREasyUseModal.create(RelationModal);
