/**
 * @Author: wangyw26123
 * @Description: 侧边栏机构树
 * @Date: Created in 2022-06-01 14:46:45
 * @Modifed By:
 */
import React, { useEffect, useState, forwardRef, useImperativeHandle, useRef, useContext } from 'react';
import { YRTree, YRSpin, YRItem } from 'yrantd';
import store from '@/store';
import styles from './index.module.less';
import { TreeContext } from '../../../User/Personal';

interface OrganTreeProps {
  callback?: (value: OrganTreeItem[], parents: OrganTreeItem[]) => void;
  fieldNames?: {
    key: string;
    title: string;
    children: string;
  };
  run?: (param: any) => void;
}

export interface OrganTreeItem {
  organId: string;
  organName: string;
  children: OrganTreeItem[];
}

const { TreeNode } = YRTree;
const flatTreeData: OrganTreeItem[] = [];
const defaultFieldNames = {
  key: 'key',
  title: 'title',
  children: 'children'
};

/**
 * 平铺机构树
 * @param sourceData
 */
const flatOrganTree = (sourceData: OrganTreeItem[]) => {
  const loop = (data) => {
    data?.forEach((item: OrganTreeItem): void => {
      flatTreeData.push({ ...item });
      loop(item.children);
    });
  };

  loop(sourceData);
};

const OrganTree = forwardRef((props: OrganTreeProps, ref) => {
  const { callback: propsCallBack, fieldNames = defaultFieldNames, run } = props;
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState<boolean>(false);
  const [searchValue] = useState<string>('');
  const clearTimeoutId = useRef<any>();
  const firstLoad = useRef<boolean>(true);
  const [organState, organDispatch] = store.useModel('organ');
  const { organTreeList, activeOrganId, pageLoading } = organState;
  // @ts-ignore
  const { isHasResetTree, setIsHasResetTree } = useContext(TreeContext);
  useImperativeHandle(ref, () => {
    return {
      setActiveNode
    };
  });

  useEffect(() => {
    organDispatch.getOrganTreeList();
    organDispatch.getAcctOrganTree();
  }, []);

  useEffect(() => {
    flatOrganTree(organTreeList);
    setSelectedKeys(!isHasResetTree ? [activeOrganId] : []);
    if (firstLoad.current) {
      propsCallBack?.(organTreeList, organTreeList);
      // 因为激活的id保存在store中，所以需要在切换菜单时重置
      firstLoad.current = false;
    } else if (activeOrganId) {
      organItemById(organTreeList, activeOrganId, (item, parents) => propsCallBack?.([item], parents));
    } else {
      propsCallBack?.(organTreeList, organTreeList);
    }
  }, [organTreeList, activeOrganId, isHasResetTree]);

  const organItemById = (organTree: OrganTreeItem[], id: string, fn) => {
    const loop = (data, key, callback, parents: OrganTreeItem[] = []) => {
      for (let i = 0; i < data.length; i++) {
        if (data[i][fieldNames.key] === key) {
          return callback(data[i], [...parents, data[i]]);
        }

        if (data[i][fieldNames.children]) {
          loop(data[i][fieldNames.children], key, callback, [...parents, data[i]]);
        }
      }
    };

    loop(organTree, id, (item, parents) => {
      fn(item, parents);
    });
  };

  const getExpandNodes = (id: string) => {
    const list: OrganTreeItem[] = [];

    organItemById(organTreeList, id, (item, parents) => {
      list.push(...parents);

      const loop = (data) => {
        for (let i = 0; i < data.length; i++) {
          if (data[i][fieldNames.children]) {
            list.push(data[i]);
            loop(data[i][fieldNames.children]);
          }
        }
      };

      loop([item]);
    });

    return list;
  };

  const onSelectParam = (keys, row) => {
    clearTimeout(clearTimeoutId.current);
    clearTimeoutId.current = setTimeout(() => {
      setActiveNode(row.node.key);
      // @ts-ignore
      run({ organId: keys?.[0] || undefined });
    }, 200);
  };

  const setActiveNode = (key) => {
    if (setIsHasResetTree) {
      setIsHasResetTree(false);
    }
    organItemById(organTreeList, key, (item, parents) => {
      setSelectedKeys([key]);
      propsCallBack?.([item], parents);
    });
  };

  const onExpand = (keys) => {
    setExpandedKeys(keys);
    setAutoExpandParent(false);
  };

  const getParentKey = (key, tree) => {
    let parentKey;
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i];
      if (node[fieldNames.children]) {
        if (node[fieldNames.children].some((item) => item[fieldNames.key] === key)) {
          parentKey = node[fieldNames.key];
        } else if (getParentKey(key, node[fieldNames.children])) {
          parentKey = getParentKey(key, node[fieldNames.children]);
        }
      }
    }
    return parentKey;
  };

  const loop = (data) => {
    return data.map((item) => {
      if (item[fieldNames.children]) {
        return (
          <TreeNode
            key={item[fieldNames.key]}
            title={
              <YRItem
                title={item[fieldNames.title]}
                highlightText={searchValue}
                count={item[fieldNames.children].length}
              />
            }
          >
            {loop(item[fieldNames.children])}
          </TreeNode>
        );
      }

      return (
        <TreeNode
          key={item[fieldNames.key]}
          title={<YRItem title={item[fieldNames.title]} highlightText={searchValue} />}
        />
      );
    });
  };

  return (
    <YRSpin spinning={pageLoading} wrapperClassName={styles.organTreeLoading}>
      <div className={styles.organTree}>
        <YRTree
          onExpand={onExpand}
          expandedKeys={expandedKeys}
          autoExpandParent={autoExpandParent}
          onSelect={onSelectParam}
          selectedKeys={selectedKeys}
          onDoubleClick={(e, row) => {
            clearTimeout(clearTimeoutId.current);
            // 双击展开子级节点
            setExpandedKeys([...new Set(getExpandNodes(row.key as string))].map((node) => node.organId));
          }}
        >
          {loop(organTreeList)}
        </YRTree>
      </div>
    </YRSpin>
  );
});

export default OrganTree;
