
-- ----------------------------
-- 更新时间:2023/1/11 17:19:07
-- 负责人: 王亚威26123
-- 邮  箱: <EMAIL>
-- 更新内容: 综合信贷系统-后台配置
-- ----------------------------
SET autocommit = 0;
SET @TENANT_ID = '000';
DELETE FROM guard_permit_info WHERE TENANT_ID = '000' AND permit_id LIKE 'kylin-admin%';
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin',@TENANT_ID,now(),now(),'综合信贷系统-后台配置',null,'kylin-admin','1','N','0','010','Y',1);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M01',@TENANT_ID,now(),now(),'设置',null,'/setting','1','N','kylin-admin','010','Y',1);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0101',@TENANT_ID,now(),now(),'机构管理',null,'/setting/organ','1','N','kylin-admin-M01','010','Y',1);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010101',@TENANT_ID,now(),now(),'机构管理',null,'/setting/organ/organ-manage','1','N','kylin-admin-M0101','010','Y',1);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010101I1',@TENANT_ID,now(),now(),'查询机构树',null,'/hsjry/guard/base/IOrganQuery/queryOrganTree','4','N','kylin-admin-M010101','010','Y',0);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010101I2',@TENANT_ID,now(),now(),'修改机构状态',null,'/hsjry/guard/base/IOrgan/changeOrganStatus','4','N','kylin-admin-M010101','010','Y',2);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010101I3',@TENANT_ID,now(),now(),'机构新增',null,'/hsjry/guard/base/IOrgan/addOrgan','4','N','kylin-admin-M010101','010','Y',4);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010101E01',@TENANT_ID,now(),now(),'机构新增',null,'/hsjry/guard/base/IOrgan/addOrgan','2','N','kylin-admin-M010101','010','Y',1);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010101E02',@TENANT_ID,now(),now(),'机构修改',null,'/hsjry/guard/base/IOrgan/modifyOrgan','2','N','kylin-admin-M010101','010','Y',2);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010101E02I1',@TENANT_ID,now(),now(),'修改机构',null,'/hsjry/guard/base/IOrgan/modifyOrgan','4','N','kylin-admin-M010101E02','010','Y',0);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010101E03',@TENANT_ID,now(),now(),'机构删除',null,'/hsjry/guard/base/IOrgan/delOrgan','2','N','kylin-admin-M010101','010','Y',3);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010101E03I1',@TENANT_ID,now(),now(),'机构停启用',null,'/hsjry/guard/base/IOrgan/delOrgan','4','N','kylin-admin-M010101E03','010','Y',0);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010101E04',@TENANT_ID,now(),now(),'机构启用',null,'/hsjry/guard/base/IOrgan/changeOrganStatus/start','2','N','kylin-admin-M010101','010','Y',4);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010101E05',@TENANT_ID,now(),now(),'机构停用',null,'/hsjry/guard/base/IOrgan/changeOrganStatus/disabled','2','N','kylin-admin-M010101','010','Y',5);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010101E06',@TENANT_ID,now(),now(),'机构恢复',null,'/hsjry/guard/base/IOrgan/changeOrganStatus/restore','2','N','kylin-admin-M010101','010','Y',6);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010101E07',@TENANT_ID,now(),now(),'调整层级',null,'/hsjry/guard/base/changeOrganLevelApprove','2','N','kylin-admin-M010101','010','Y',7);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010101E07I1',@TENANT_ID,now(),now(),'机构层级调整',null,'/hsjry/guard/management/changeOrganLevelApprove','4','N','kylin-admin-M010101E07','010','Y',0);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010101E08',@TENANT_ID,now(),now(),'复制现有机构',null,'/hsjry/guard/base/IOrgan/copyOrgan','2','N','kylin-admin-M010101','010','Y',8);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010101E09',@TENANT_ID,now(),now(),'新增子机构',null,'/hsjry/guard/base/IOrgan/addSubOrgan','2','N','kylin-admin-M010101','010','Y',9);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010101E10',@TENANT_ID,now(),now(),'机构详情',null,'/hsjry/guard/base/IOrganQuery/queryOrganInfoByOrganId','2','N','kylin-admin-M010101','010','Y',10);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010101E10I1',@TENANT_ID,now(),now(),'查询机构详情',null,'/hsjry/guard/base/IOrganQuery/queryOrganInfoByOrganId','4','N','kylin-admin-M010101E10','010','Y',0);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010102',@TENANT_ID,now(),now(),'账务机构映射',null,'/setting/organ/financial-organ','1','N','kylin-admin-M0101','010','Y',2);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010102I1',@TENANT_ID,now(),now(),'查询账务机构映射',null,'/hsjry/guard/base/IAcctOrganQuery/queryOrganRelationPage','4','N','kylin-admin-M010102','010','Y',0);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010102E01',@TENANT_ID,now(),now(),'新增账务机构映射',null,'/hsjry/guard/base/IOrganAcctRelate/addOrgRelateList','2','N','kylin-admin-M010102','010','Y',1);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010102E01I1',@TENANT_ID,now(),now(),'新增账务机构映射',null,'/hsjry/guard/base/IOrganAcctRelate/addOrgRelateList','4','N','kylin-admin-M010102E01','010','Y',0);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010102E02',@TENANT_ID,now(),now(),'修改账务机构映射',null,'/hsjry/guard/base/IOrganAcctRelate/updateOrgRelateList','2','N','kylin-admin-M010102','010','Y',2);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010102E02I1',@TENANT_ID,now(),now(),'更新账务机构映射',null,'/hsjry/guard/base/IOrganAcctRelate/updateOrgRelateList','4','N','kylin-admin-M010102E02','010','Y',0);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010102E03',@TENANT_ID,now(),now(),'删除账务机构映射',null,'/hsjry/guard/base/IOrganAcctRelate/delOrgRelate','2','N','kylin-admin-M010102','010','Y',3);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010102E03I1',@TENANT_ID,now(),now(),'删除账务机构映射',null,'/hsjry/guard/base/IOrganAcctRelate/delOrgRelate','4','N','kylin-admin-M010102E03','010','Y',0);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0102',@TENANT_ID,now(),now(),'功能权限',null,'/setting/permit','1','N','kylin-admin-M01','010','Y',2);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0102I1',@TENANT_ID,now(),now(),'查询当前权限下的权限组列表',null,'/hsjry/guard/base/IPermitQuery/queryPermitGroupList','4','N','kylin-admin-M0102','010','Y',0);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0102I2',@TENANT_ID,now(),now(),'查询权限组权限',null,'/hsjry/guard/base/IPermitQuery/queryPermitGroupWithPermit','4','N','kylin-admin-M0102','010','Y',2);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0102E01',@TENANT_ID,now(),now(),'新增权限组',null,'/hsjry/guard/base/IPermit/addPermitGroup','2','N','kylin-admin-M0102','010','Y',1);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0102E01I1',@TENANT_ID,now(),now(),'新增权限组',null,'/hsjry/guard/base/IPermit/addPermitGroup','4','N','kylin-admin-M0102E01','010','Y',0);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0102E02',@TENANT_ID,now(),now(),'删除权限组',null,'/hsjry/guard/base/IPermit/deletePermitGroup','2','N','kylin-admin-M0102','010','Y',2);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0102E02I1',@TENANT_ID,now(),now(),'删除权限组',null,'/hsjry/guard/base/IPermit/deletePermitGroup','4','N','kylin-admin-M0102E02','010','Y',0);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0102E03',@TENANT_ID,now(),now(),'修改权限组',null,'/hsjry/guard/base/IPermit/modifyPermitGroup','2','N','kylin-admin-M0102','010','Y',3);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0102E03I1',@TENANT_ID,now(),now(),'修改权限组',null,'/hsjry/guard/base/IPermit/modifyPermitGroup','4','N','kylin-admin-M0102E03','010','Y',0);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0103',@TENANT_ID,now(),now(),'角色管理',null,'/setting/role','1','N','kylin-admin-M01','010','Y',3);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010301',@TENANT_ID,now(),now(),'角色管理',null,'/setting/role/role-manage','1','N','kylin-admin-M0103','010','Y',1);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010301I1',@TENANT_ID,now(),now(),'角色查询',null,'/hsjry/guard/base/IRoleQuery/queryPage','4','N','kylin-admin-M010301','010','Y',0);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010301I2',@TENANT_ID,now(),now(),'修改角色状态',null,'/hsjry/guard/base/IRole/changeRoleStatus','4','N','kylin-admin-M010301','010','Y',2);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010301E01',@TENANT_ID,now(),now(),'新增角色',null,'/hsjry/guard/base/IRole/addOrUpdateRole','2','N','kylin-admin-M010301','010','Y',1);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010301E01I1',@TENANT_ID,now(),now(),'角色新增/修改',null,'/hsjry/guard/base/IRole/addOrUpdateRole','4','N','kylin-admin-M010301E01','010','Y',0);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010301E02',@TENANT_ID,now(),now(),'修改角色',null,'/hsjry/guard/base/IRole/addOrUpdateRole-modify','2','N','kylin-admin-M010301','010','Y',2);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010301E02I1',@TENANT_ID,now(),now(),'角色新增/修改',null,'/hsjry/guard/base/IRole/addOrUpdateRole','4','N','kylin-admin-M010301E02','010','Y',0);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010301E03',@TENANT_ID,now(),now(),'删除角色',null,'/hsjry/guard/base/IRole/delRole','2','N','kylin-admin-M010301','010','Y',3);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010301E03I1',@TENANT_ID,now(),now(),'角色删除',null,'/hsjry/guard/base/IRole/delRole','4','N','kylin-admin-M010301E03','010','Y',0);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010301E04',@TENANT_ID,now(),now(),'启用角色',null,'/hsjry/guard/base/IRole/changeRoleStatus/start','2','N','kylin-admin-M010301','010','Y',4);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010301E05',@TENANT_ID,now(),now(),'停用角色',null,'/hsjry/guard/base/IRole/changeRoleStatus/disabled','2','N','kylin-admin-M010301','010','Y',5);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010301E06',@TENANT_ID,now(),now(),'恢复角色',null,'/hsjry/guard/base/IRole/changeRoleStatus/restore','2','N','kylin-admin-M010301','010','Y',6);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010301E07',@TENANT_ID,now(),now(),'添加成员',null,'/hsjry/guard/base/IRole/allotRole','2','N','kylin-admin-M010301','010','Y',7);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010301E07I1',@TENANT_ID,now(),now(),'添加修改成员',null,'/hsjry/guard/base/IRole/allotRole','4','N','kylin-admin-M010301E07','010','Y',0);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010301E07I2',@TENANT_ID,now(),now(),'查询当前操作人权限下的用户',null,'/hsjry/guard/base/IUserQuery/queryUserList','4','N','kylin-admin-M010301E07','010','Y',2);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010301E08',@TENANT_ID,now(),now(),'历史记录',null,'/hsjry/guard/base/IRoleQuery/queryRoleLogPage','2','N','kylin-admin-M010301','010','Y',8);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010301E08I1',@TENANT_ID,now(),now(),'角色记录查询',null,'/hsjry/guard/base/IRoleQuery/queryRoleLogPage','4','N','kylin-admin-M010301E08','010','Y',0);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010301E09',@TENANT_ID,now(),now(),'角色详情',null,'/hsjry/guard/base/IRoleQuery/detail','2','N','kylin-admin-M010301','010','Y',9);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M010301E09I1',@TENANT_ID,now(),now(),'角色详情查询',null,'/hsjry/guard/base/IRoleQuery/detail','4','N','kylin-admin-M010301E09','010','Y',0);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0104',@TENANT_ID,now(),now(),'用户管理',null,'/setting/user','1','N','kylin-admin-M01','010','Y',4);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0105',@TENANT_ID,now(),now(),'流程管理',null,'/setting/flow','1','N','kylin-admin-M01','010','Y',5);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0105I1',@TENANT_ID,now(),now(),'查询流程图列表',null,'/hsjry/flow-designer/v1/flowModelResourceQuery/selectPageForMaxVersion','4','N','kylin-admin-M0105','010','Y',0);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0105I2',@TENANT_ID,now(),now(),'详情查询流程图',null,'/hsjry/flow-designer/v1/flowDiagramResourceQuery/queryFlowDiagramResourceDetail','4','N','kylin-admin-M0105','010','Y',2);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0105I3',@TENANT_ID,now(),now(),'查询岗位树',null,'/hsjry/flow-management/IFlowApprovalStation/queryFlowApprovalStationList','4','N','kylin-admin-M0105','010','Y',4);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0105I4',@TENANT_ID,now(),now(),'查询当前操作人权限下的用户',null,'/hsjry/guard/base/IUserQuery/queryUserList','4','N','kylin-admin-M0105','010','Y',6);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0105E01',@TENANT_ID,now(),now(),'新增流程图',null,'/hsjry/flow-designer/v1/flowModelResourceQuery/selectPageForMaxVersion','2','N','kylin-admin-M0105','010','Y',1);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0105E01I1',@TENANT_ID,now(),now(),'新增流程图',null,'/hsjry/flow-designer/v1/flowDiagramResource/addFlowDiagramResource','4','N','kylin-admin-M0105E01','010','Y',0);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0105E02',@TENANT_ID,now(),now(),'删除流程图',null,'/hsjry/flow-designer/v1/flowDiagramResource/deleteFlowDiagramResource','2','N','kylin-admin-M0105','010','Y',2);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0105E02I1',@TENANT_ID,now(),now(),'删除流程图',null,'/hsjry/flow-designer/v1/flowDiagramResource/deleteFlowDiagramResource','4','N','kylin-admin-M0105E02','010','Y',0);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0105E03',@TENANT_ID,now(),now(),'更新流程图',null,'/hsjry/flow-designer/v1/flowDiagramResource/modifyFlowDiagramResource','2','N','kylin-admin-M0105','010','Y',3);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0105E03I1',@TENANT_ID,now(),now(),'更新流程图',null,'/hsjry/flow-designer/v1/flowDiagramResource/modifyFlowDiagramResource','4','N','kylin-admin-M0105E03','010','Y',0);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0105E03I2',@TENANT_ID,now(),now(),'新增流程图',null,'/hsjry/flow-designer/v1/flowDiagramResource/addFlowDiagramResource','4','N','kylin-admin-M0105E03','010','Y',2);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0105E04',@TENANT_ID,now(),now(),'部署流程图',null,'/hsjry/flow-designer/v1/manage-process/deployProcess','2','N','kylin-admin-M0105','010','Y',4);
INSERT INTO guard_permit_info (permit_id, tenant_id, create_time, update_time, permit_name, permit_desc, uri,permit_type, data_permission, parent_permit_id, permit_status, permit_check, sort)
VALUES ('kylin-admin-M0105E04I1',@TENANT_ID,now(),now(),'部署流程图',null,'/hsjry/flow-designer/v1/manage-process/deployProcess','4','N','kylin-admin-M0105E04','010','Y',0);
COMMIT;
