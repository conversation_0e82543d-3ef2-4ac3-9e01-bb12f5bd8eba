import React, { Component } from 'react';
import style from '../index.module.less';

/**
 * 通用布局组件
 */

class CommLayout extends Component {
  render() {
    const { id = '', diffStyle } = this.props;

    return (
      <div className={style['comm-context']} id={id} style={diffStyle}>
        <div className={style['context']}>
          {this.props.children}
        </div>
        <div className={`${style['resizeR']} resizeR`} />
        <div className={`${style['resizeL']} resizeL`} />
      </div>
    );
  }
}

export default CommLayout;
