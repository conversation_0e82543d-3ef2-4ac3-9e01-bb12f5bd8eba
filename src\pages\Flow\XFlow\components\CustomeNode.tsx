/*
 * @Author: liaokt
 * @E-mail: <EMAIL>
 * @Date: 2023-03-10 10:20:38
 * @Description:
 */
/**
 * @Author: wangyw26123
 * @Description: 画布面板自定义节点
 * @Date: Created in 2022-05-10 14:40:00
 * @Modifed By:
 */

import React from 'react';
import { YRIcon, YRTag } from 'yrantd';
import { get } from 'lodash-es';
import styles from '../../index.module.less';
import { enumIconName, nodeTypeNames } from '@/pages/Flow/XFlow/constants';
import XflowInstance from '@/pages/Flow/XFlow/XflowInstance';

const CustomeNode = (props) => {
  const { isNodeTreePanel, isOverview, store, overviewData, data = {} } = props;
  let blockData;

  // 左侧自定义面板中的节点数据
  if (isNodeTreePanel || data.isCustom) {
    blockData = {
      ...data,
      // 画布中的节点要保证 边的4个连线端口与box的4边对其
      width: isNodeTreePanel ? 128 : data.width,
      name: data.label,
      nodebType: data.name,
      maxWidth: 106
    };
  } else if (isOverview) {
    // 右侧全局面板概览中的节点数据
    blockData = {
      width: overviewData.width,
      height: overviewData.height,
      name: overviewData.nodeFormData.name || overviewData.label,
      nodebType: overviewData.name,
      nodeFormData: overviewData.nodeFormData,
      maxWidth: 144
    };
  } else {
    // 画布中的节点数据
    const storeData = store.data.data;
    blockData = {
      width: storeData.width,
      height: storeData.height,
      name: storeData.nodeFormData.name || storeData.label,
      // 当前的审批节点，此处为配置入口，后面可能会用到
      currentNodeCode: storeData.nodeFormData.currentNodeCode,
      nodebType: storeData.name,
      nodeFormData: storeData.nodeFormData,
      maxWidth: 106
    };
  }

  // console.log('blockData', blockData, props);

  const orSign = get(blockData.nodeFormData, ['properties', 'multiinstance_condition'])?.includes('orSign');

  return (
    <div
      onMouseEnter={() => {
        if (!XflowInstance.isAddNode) {
          XflowInstance.isAddNode = true;
        }
      }}
      style={{ width: blockData.width, height: blockData.height }}
      className={`${styles.customeNode} customeNode`}
    >
      <div className={styles.icon}>
        <YRIcon icon={enumIconName[blockData.nodebType]} />
      </div>
      <div className={styles.name}>
        <strong className="ellipsis" style={{ maxWidth: blockData.maxWidth }} title={blockData.name}>
          {blockData.name}
        </strong>
        <span className="ellipsis" style={{ maxWidth: blockData.maxWidth }}>
          {blockData.nodeFormData?.id}
        </span>
      </div>
      <div className={styles.extra}>
        {blockData.currentNodeCode && <YRIcon icon="yunrongshenpijiedianbiaoshi" style={{ fontSize: 24 }} />}
        {blockData.nodebType === nodeTypeNames['UserTask'] && !isNodeTreePanel && (
          <YRTag
            className={styles.extraTag}
            style={{ margin: 0, padding: '0 2px', lineHeight: 'initial', transform: 'scale(0.9)' }}
            color={orSign ? 'orange' : 'blue'}
          >
            {orSign ? '或' : '会'}
          </YRTag>
        )}
      </div>
    </div>
  );
};

export default CustomeNode;
