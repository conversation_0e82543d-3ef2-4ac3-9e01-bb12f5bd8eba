/**
 * 页面描述: 场景列表
 * @文件名 SceneInfo.tsx
 * @filePath \src\pages\Setting\RuleManager\Scene\components\SceneInfo.tsx
 * @Date 2023-08-10 10:52:50
 * @Email <EMAIL>
 * <AUTHOR>
 */
import React, { useEffect } from 'react';
import {
  YRTable,
  YRForm,
  YRButton,
  YREasyUseModal,
  YRSpace,
  YRBadge,
  YRDict,
  YRLink,
  YRMessage,
  YRTooltip,
  YRConfirmBtn
} from 'yrantd';
import { useAntdTable, useRequest } from 'ahooks';
import type { FormItemListProps, YRTableProps } from 'yrantd';
import { EnumSceneStatusColor } from '@/constant/StyleConst';
import { scenePage, sceneStatusChange } from '@/services/setting';
import { setDict, openNewTab } from '@/utils/utils';
import { getDictName } from '@yr/util';
import SceneModal from './SceneModal';

setDict('EnumSceneStatus', [
  { itemKey: '010', itemName: '启用' },
  { itemKey: '020', itemName: '停用' },
  { itemKey: '030', itemName: '删除' }
]);

const SceneInfo = (props) => {
  const { activeNode = {} } = props;
  const { treeId, circleFlag, circleType } = activeNode;
  const [form] = YRForm.useForm();

  // 场景启用停用删除
  const { run: changeSceneRun } = useRequest(sceneStatusChange, {
    manual: true,
    onSuccess: (result, params: any) => {
      const { errorMessage } = result as any;
      const status = params[0]?.status;
      if (errorMessage === null) {
        YRMessage.success(`${getDictName('EnumSceneStatus', status)}成功`);
        refresh();
      }
    }
  });

  const {
    run: querySceneList,
    refresh,
    tableProps,
    params
  } = useAntdTable(
    (param) =>
      scenePage({
        ...param,
        pageNum: param.current,
        statusList: ['010', '020']
      }).then((res) => {
        if (res?.success) {
          return {
            list: res.data.list || [],
            total: res.data.total || 0
          };
        } else {
          return {
            list: [],
            total: 0
          };
        }
      }),
    { manual: true }
  );

  useEffect(() => {
    if (treeId) {
      querySceneList({
        treeId,
        current: 1,
        pageSize: 10
      });
    }
  }, [treeId]);

  const formItemList: FormItemListProps[] = [
    {
      placeholder: '场景名称',
      key: 'sceneName',
      type: 'input',
      position: 'show'
    }
  ];

  const getTableColumns: YRTableProps['columns'] = [
    {
      title: '场景编号',
      dataIndex: 'sceneId',
      width: 200,
      render: (value: string) => {
        return (
          <YRTooltip title={value}>
            <div className="ellipsis">{value || CONST.null}</div>
          </YRTooltip>
        );
      }
    },
    {
      title: '场景码',
      dataIndex: 'sceneCode',
      width: 150,
      render: (value: string) => {
        return (
          <YRTooltip title={value}>
            <div className="ellipsis">{value || CONST.null}</div>
          </YRTooltip>
        );
      }
    },
    {
      title: '场景名称',
      dataIndex: 'sceneName',
      width: 150,
      render: (value: string) => {
        return (
          <YRTooltip title={value}>
            <div className="ellipsis">{value || CONST.null}</div>
          </YRTooltip>
        );
      }
    },
    {
      title: '场景描述',
      dataIndex: 'sceneDescription',
      width: 200,
      render: (value: string) => {
        return (
          <YRTooltip title={value}>
            <div className="ellipsis">{value || CONST.null}</div>
          </YRTooltip>
        );
      }
    },
    {
      title: '登记人',
      dataIndex: 'operatorName',
      width: 100,
      render: (value: string) => {
        return (
          <YRTooltip title={value}>
            <div className="ellipsis">{value || CONST.null}</div>
          </YRTooltip>
        );
      }
    },
    {
      title: '登记机构',
      dataIndex: 'ownOrganName',
      width: 100,
      render: (value: string) => {
        return (
          <YRTooltip title={value}>
            <div className="ellipsis">{value || CONST.null}</div>
          </YRTooltip>
        );
      }
    },
    {
      title: '场景状态',
      dataIndex: 'status',
      width: 100,
      fixed: 'right',
      render: (value: string) => {
        return value ? (
          <YRBadge
            color={EnumSceneStatusColor[value]}
            text={<YRDict.Text dictkey="EnumSceneStatus" defaultValue={value} />}
          />
        ) : (
          CONST.null
        );
      }
    },
    {
      title: '操作',
      dataIndex: 'operate',
      width: 120,
      fixed: 'right',
      render: (value, row: any) => (
        <YRButton.Overflow>
          {row?.status === '020' && (
            <YRLink type={'primary'} onClick={() => jumpPage(row?.sceneId, 'edit')}>
              编辑
            </YRLink>
          )}
          <YRLink type={'primary'} onClick={() => jumpPage(row?.sceneId, 'readPretty')}>
            详情
          </YRLink>
          {row?.status === '020' && (
            <YRConfirmBtn
              type={'pop'}
              msg="删除后无法恢复，确定进行删除吗？"
              confirmProps={{ zIndex: 9999 }}
              onConfirm={() => {
                changeSceneRun({ sceneId: row?.sceneId, status: '030' });
              }}
            >
              删除
            </YRConfirmBtn>
          )}

          {row?.status === '020' && (
            <YRConfirmBtn
              type={'pop'}
              msg="请确认是否启用"
              confirmProps={{ zIndex: 9999 }}
              onConfirm={() => {
                changeSceneRun({ sceneId: row?.sceneId, status: '010' });
              }}
            >
              启用
            </YRConfirmBtn>
          )}
          {row?.status === '010' && (
            <YRConfirmBtn
              type={'pop'}
              msg="请确认是否停用"
              confirmProps={{ zIndex: 9999 }}
              onConfirm={() => {
                changeSceneRun({ sceneId: row?.sceneId, status: '020' });
              }}
            >
              停用
            </YRConfirmBtn>
          )}
          <YRLink type={'primary'} onClick={() => jumpPage(row?.sceneId, 'copy')}>
            复制
          </YRLink>
        </YRButton.Overflow>
      )
    }
  ];

  // 新增场景
  const addScene = (values) => {
    openNewTab({
      pathname: '/rule-manage/scene-detail/add',
      query: {
        ...(values || {}),
        treeId
      }
    });
  };

  // 修改详情跳转页面
  const jumpPage = (sceneId, type) => {
    const pathname = `/rule-manage/scene-detail/${type}`;
    openNewTab({
      pathname,
      query: {
        sceneId,
        treeId
      }
    });
  };

  const renderExtAction = (
    <YRSpace>
      <YRButton
        key="add"
        type="primary"
        onClick={() => {
          YREasyUseModal.show(SceneModal, { okCallback: addScene });
        }}
      >
        新增
      </YRButton>
    </YRSpace>
  );

  return (
    <YREasyUseModal.Provider>
      <YRTable
        {...tableProps}
        form={form}
        rowKey={(row) => row?.sceneId}
        columns={getTableColumns}
        formItemList={formItemList}
        extAction={renderExtAction}
        handleSearch={() => {
          const formValues = form.getFieldsValue() || {};
          querySceneList({
            ...params[0],
            ...formValues,
            current: 1,
            pageSize: 10
          });
        }}
      />
    </YREasyUseModal.Provider>
  );
};
export default SceneInfo;
