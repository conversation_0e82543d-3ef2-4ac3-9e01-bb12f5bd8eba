/* eslint-disable @typescript-eslint/indent */
/* eslint-disable no-nested-ternary */
/*
 * @Author: liaokt
 * @Description: 规则新增弹窗
 * @Date: 2024-02-27 16:10:32
 * @LastEditors: liaokt
 * @LastEditTime: 2024-10-25 08:58:29
 */
import {
  YRButton,
  YRClassificationLayout,
  YRCol,
  YRDatePicker,
  YRDict,
  YREasyUseModal,
  YREmpty,
  YRForm,
  YRInput,
  YRLink,
  YRList,
  YRMessage,
  YRModal,
  YRRow,
  YRSelect,
  YRSpace,
  YRText
} from 'yrantd';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import React, { useEffect, useState } from 'react';
import { uuid } from 'yr-loan-antd/lib/util';
import { getDictItems } from '@yr/util';
import { useRequest, useSetState } from 'ahooks';
import { M0105 } from '@permit/organ';
import { COMMON } from '@permit/common';
import { addBaseAuthInfo } from '@/services/auth';
import moment from 'moment';

const AddRoleTree = (props) => {
  const { mode = 'add', params, okCallback } = props;

  const { queryDimensionLoadByAuthCode, queryAuthInfoByAuthNo, updateBaseAuthInfo } = M0105.interfaces;
  const { productQueryList } = COMMON.interfaces;

  const [form] = YRForm.useForm();
  const [detailData, setDetailData] = useState({} as any);
  const [dimensions, setDimensions] = useState([] as Array<{}>);

  const [firstDict, setFirstDict] = useState([]);
  const [secondDict, setSecondDict] = useSetState({});
  const [productData, setProductData] = useState([]);

  const modal = YREasyUseModal.useModal();

  useEffect(() => {
    if (mode === 'add') {
      const { parentOrgName, authBaseId, roleName, roleId, authCategory } = params;
      form.setFieldsValue({
        orgName: parentOrgName,
        authSourceType: authBaseId,
        roleName: roleName || roleId
      });

      getDict(authCategory);
    } else {
      const { authBaseId, authCategory } = props;

      detailRun({ authBaseId });
      getDict(authCategory);
    }
  }, [params]);

  // 查询第二维度的 map
  const { run: query } = useRequest(queryDimensionLoadByAuthCode, {
    manual: true,
    onSuccess: (res: any, param) => {
      const { authCode } = param[0];
      const {
        data: { showExtResponses }
      } = res;
      const { paramList } = showExtResponses[0];
      //   const obj = secondDict;
      //   console.log(obj, '----old----');
      //   console.log(obj, '----xxxx----');

      setSecondDict((prev) => ({
        ...prev,
        [authCode]: paramList
      }));

      //   Object.assign(obj, { [authCode]: paramList });
      //   setSecondDict(obj);
    }
  });

  // 查询第二维度的产品
  const { run: queryProduct } = useRequest(productQueryList, {
    manual: true,
    onSuccess: (res: any) => {
      const {
        data: { list }
      } = res;
      setProductData(list);
    }
  });

  // 提交事件
  const { run: createRule, loading: createLoading } = useRequest(addBaseAuthInfo, {
    manual: true,
    onSuccess: (res: any) => {
      if (!res.errorMessage) {
        YRMessage.success('新增基本授权成功', 0.5, () => {
          okCallback();
          modal.remove();
        });
      }
    }
  });

  // 提交事件
  const { run: updateRule, loading: updateLoading } = useRequest(updateBaseAuthInfo, {
    manual: true,
    onSuccess: (res: any) => {
      if (!res.errorMessage) {
        YRMessage.success('修改基本授权成功', 0.5, () => {
          okCallback();
          modal.remove();
        });
      }
    }
  });

  // 查询详情
  const { run: detailRun, loading: detailLoading } = useRequest(queryAuthInfoByAuthNo, {
    manual: true,
    onSuccess: (res: any) => {
      const { data } = res;
      if (!res.errorMessage) {
        const { dimInfoList, effectBeginDate, effectEndDate } = data;
        const list = dimInfoList.map((item) => {
          //   query({ authCode: item.authCode });
          const { resultType, authValue } = item;
          let authCode = item.authCode;
          if (authCode === 'productCollect') {
            item.authValue = JSON.parse(authValue);
            queryProduct({ pageNum: 1, pageSize: 500, productStatus: '020' });
          } else {
            if (resultType === 'creditResultType' || resultType === 'loanResultType') {
              item.authCode = resultType;
              item.authValue = authCode;
              item.resultType = authValue;
              authCode = resultType;
            }

            if (['productCollect', 'guarMethodCollect'].includes(item.authCode)) {
              item.authValue = JSON.parse(authValue);
            }

            if (mode !== 'detail') {
              queryDimensionLoadByAuthCode({ authCode }).then((result) => {
                const {
                  data: { showExtResponses }
                } = result;
                const { paramList } = showExtResponses[0];
                //   const obj = secondDict;
                //   console.log(obj, '----xxxx----');

                setSecondDict((prev) => ({
                  ...prev,
                  [authCode]: paramList
                }));
              });
            }
          }

          return { ...item, id: uuid() };
        });
        setDetailData(data);
        setDimensions(list);
        form.setFieldsValue({
          ...data,
          dimensionList: list,
          effectBeginDate: moment(effectBeginDate),
          effectEndDate: moment(effectEndDate)
        });
      }
    }
  });

  // 获取数据字典
  const getDict = (authCategory: string) => {
    getDictItems({ dictKeys: [authCategory] }).then((res) => {
      const { data } = res;
      const dict = data[authCategory]?.map((item) => {
        const { itemKey, itemName } = item as { itemKey: string; itemName: string };
        return { itemKey, itemName };
      });
      setFirstDict(dict);
    });
  };

  // 提交事件
  const onSubmit = () => {
    if (['add'].includes(mode)) {
      form.validateFields().then((values: any) => {
        const { orgId, roleId, authCategory } = params;
        const { dimensionList, effectBeginDate, effectEndDate } = values;
        const param = {
          ...values,
          effectBeginDate: moment(effectBeginDate).format('YYYY-MM-DD'),
          effectEndDate: moment(effectEndDate).format('YYYY-MM-DD'),
          orgId,
          roleId,
          authCategory,
          authStatus: '010',
          dimensionList: dimensionList?.map((item, index) => {
            // 判断是否为结果类型，如果是结果类型， resultType 使用 authValue 的值，authValue 使用 ResultType 的值也就是两者相反
            const { authValue, authCode, resultType } = item;
            if (authCode === 'creditResultType' || authCode === 'loanResultType') {
              return { ...item, authCode: authValue, resultType: authCode, authValue: resultType, sortNo: index };
            } else {
              return { ...item, sortNo: index };
            }
          })
        };
        createRule(param);
        // run({ ...params });
      });
    } else if (mode === 'edit') {
      form.validateFields().then((values: any) => {
        const { dimensionList, effectBeginDate, effectEndDate } = values;

        delete detailData.dimInfoList;

        updateRule({
          ...detailData,
          ...values,
          effectBeginDate: moment(effectBeginDate).format('YYYY-MM-DD'),
          effectEndDate: moment(effectEndDate).format('YYYY-MM-DD'),
          authStatus: '010',
          dimensionList: dimensionList?.map((item, index) => {
            // 判断是否为结果类型，如果是结果类型， resultType 使用 authValue 的值，authValue 使用 ResultType 的值也就是两者相反
            const { authValue, authCode, resultType } = item;
            if (authCode === 'creditResultType' || authCode === 'loanResultType') {
              return { ...item, authCode: authValue, resultType: authCode, authValue: resultType, sortNo: index };
            } else {
              return { ...item, sortNo: index };
            }
          })
        });
      });
    } else {
      modal.remove();
    }
  };

  const addDimension = () => {
    setDimensions([...dimensions, { id: uuid(), name: '', type: '', detail: '' }]);
  };

  const updateDimensionValue = (index, key, value) => {
    const updatedDimensions = [...dimensions];
    updatedDimensions[index][key] = value;
    setDimensions(updatedDimensions);
  };

  const onDragEnd = (result) => {
    // 检查是否有拖拽结果
    if (!result.destination) {
      return;
    }

    // 处理拖拽结果
    const { source, destination } = result;
    if (source.index !== destination.index) {
      // 更新 dimensions 的顺序
      const updatedDimensions = Array.from(dimensions);
      const [movedItem] = updatedDimensions.splice(source.index, 1);
      updatedDimensions.splice(destination.index, 0, movedItem);

      // 更新状态
      setDimensions(updatedDimensions);
      form.setFieldsValue({ dimensionList: updatedDimensions });
    }
  };

  const onDelete = (index: number) => {
    const updatedDimensions = form.getFieldValue(['dimensionList']) || [];
    updatedDimensions.splice(index, 1);
    setDimensions(updatedDimensions);
    form.setFieldsValue({ dimensionList: updatedDimensions });
  };

  return (
    <YRModal
      title={`规则关系${mode === 'add' ? '新增' : '详情'}`}
      open={modal.visible}
      onCancel={modal.hide}
      afterClose={modal.remove}
      confirmLoading={createLoading}
      okText="确定"
      width={'60%'}
      onOk={onSubmit}
      destroyOnClose
    >
      <YRForm mode={mode === 'detail' ? 'readPretty' : mode} form={form}>
        <YRClassificationLayout.Space>
          <YRClassificationLayout title="基本信息">
            <YRForm.Row column={2}>
              {/* <YRForm.Item label="基础授权编号" name="authNo">
                <YRInput placeholder="请输入基础授权编号" disabled />
              </YRForm.Item>
              <YRForm.Item label="授权目录编号" name="authSourceType">
                <YRInput placeholder="请输入授权目录编号" disabled />
              </YRForm.Item> */}
              <YRForm.Item label="机构" name="orgName">
                <YRInput placeholder="请选择机构" disabled />
              </YRForm.Item>
              <YRForm.Item label="角色" name="roleName">
                <YRInput placeholder="请选择角色" disabled />
              </YRForm.Item>
              <YRForm.Item label="业务条线" name="bizLine" rules={[{ required: true, message: '请选择业务条线' }]}>
                <YRDict.Select dictkey="BIZ_LINE" placeholder="请选择业务条线" />
              </YRForm.Item>
              <YRForm.Item
                label="生效日期"
                name="effectBeginDate"
                rules={[{ required: true, message: '请输入生效日期' }]}
              >
                <YRDatePicker placeholder="请输入生效日期" disabled={mode === 'detail'} />
              </YRForm.Item>
              <YRForm.Item
                label="失效日期"
                name="effectEndDate"
                rules={[{ required: true, message: '请输入失效日期' }]}
              >
                <YRDatePicker placeholder="请输入失效日期" disabled={mode === 'detail'} />
              </YRForm.Item>
              {/* <YRForm.Item label="登记日期" name="signDate">
                <YRDatePicker placeholder="请输入登记日期" disabled />
              </YRForm.Item> */}
              <YRForm.Item label="登记人" name="operatorName">
                <YRInput placeholder="请输入登记人" disabled />
              </YRForm.Item>
              <YRForm.Item label="登记机构" name="orgName">
                <YRInput placeholder="请输入登记机构" disabled />
              </YRForm.Item>
              <YRForm.Item label="授权编号" name="authSourceType" hidden>
                <YRInput />
              </YRForm.Item>
            </YRForm.Row>
          </YRClassificationLayout>
          <YRClassificationLayout title="维度名称">
            <YRSpace direction="vertical">
              {mode !== 'detail' && (
                <YRButton type="primary" onClick={addDimension}>
                  + 新增维度
                </YRButton>
              )}
              {dimensions?.length ? (
                mode === 'detail' ? (
                  <YRList bordered size="small">
                    {dimensions.map((item, index) => (
                      <YRList.Item>
                        <YRRow gutter={24} style={{ width: '100%' }}>
                          <YRCol span={3} style={{ display: 'flex', alignItems: 'center' }}>
                            <YRText>维度名称</YRText>
                          </YRCol>
                          <YRCol span={7}>{(item as any)?.authCodeName}</YRCol>
                          <YRCol span={7}>{(item as any)?.authValueName}</YRCol>
                          <YRCol span={7}>{(item as any)?.resultType}</YRCol>
                        </YRRow>
                      </YRList.Item>
                    ))}
                  </YRList>
                ) : (
                  <DragDropContext onDragEnd={onDragEnd}>
                    <Droppable droppableId={'droppable'}>
                      {(provided) => (
                        <div ref={provided.innerRef} {...provided.droppableProps}>
                          <YRList bordered size="small">
                            {dimensions.map((item, index) => (
                              <Draggable key={item.id} draggableId={item.id} index={index}>
                                {(_provided) => (
                                  <div
                                    ref={_provided.innerRef}
                                    {..._provided.draggableProps}
                                    {..._provided.dragHandleProps}
                                  >
                                    <YRList.Item
                                      actions={[
                                        <YRLink
                                          type="primary"
                                          onClick={() => {
                                            onDelete(index);
                                          }}
                                        >
                                          删除
                                        </YRLink>
                                      ]}
                                    >
                                      <YRRow gutter={24} style={{ width: '100%' }}>
                                        <YRCol span={3} style={{ display: 'flex', alignItems: 'center' }}>
                                          <YRText>维度名称</YRText>
                                        </YRCol>
                                        <YRCol span={7}>
                                          <YRForm.Item
                                            label="第一维度"
                                            name={['dimensionList', index, 'authCode']}
                                            noStyle
                                          >
                                            <YRSelect
                                              style={{ width: '100%' }}
                                              options={firstDict}
                                              fieldNames={{ label: 'itemName', value: 'itemKey' }}
                                              onSelect={(e, option) => {
                                                const { itemName } = option;
                                                updateDimensionValue(index, 'authCode', e);
                                                form.setFieldValue(['dimensionList', index, 'authCodeName'], itemName);

                                                if (e === 'productCollect') {
                                                  // 如果是产品组则调查询产品接口，并且第二维度可以多选
                                                  queryProduct({ pageNum: 1, pageSize: 500, productStatus: '020' });
                                                } else {
                                                  // 查询第二维度的数据字典
                                                  query({ authCode: e });
                                                }

                                                form.resetFields([['dimensionList', index, 'authValue']]);
                                                form.resetFields([['dimensionList', index, 'resultType']]);
                                              }} // 清除第三维度
                                              disabled={mode === 'detail'}
                                            />
                                          </YRForm.Item>
                                        </YRCol>
                                        <YRCol span={7}>
                                          <YRForm.Item
                                            label="第二维度"
                                            name={['dimensionList', index, 'authValue']}
                                            noStyle
                                          >
                                            <YRSelect
                                              mode={
                                                ['productCollect', 'guarMethodCollect'].includes(
                                                  form.getFieldValue(['dimensionList', index, 'authCode'])
                                                )
                                                  ? 'multiple'
                                                  : undefined
                                              }
                                              style={{ width: '100%' }}
                                              options={
                                                form.getFieldValue(['dimensionList', index, 'authCode']) ===
                                                'productCollect'
                                                  ? productData
                                                  : secondDict[form.getFieldValue(['dimensionList', index, 'authCode'])]
                                              }
                                              fieldNames={
                                                form.getFieldValue(['dimensionList', index, 'authCode']) ===
                                                'productCollect'
                                                  ? { label: 'productName', value: 'productId' }
                                                  : { label: 'value', value: 'key' }
                                              }
                                              onSelect={(e, option) => {
                                                const { value = '', productName } = option;
                                                const code = form.getFieldValue(['dimensionList', index, 'authCode']);

                                                if (code === 'productCollect') {
                                                  const product: string[] =
                                                    form.getFieldValue(['dimensionList', index, 'authValueName']) || [];
                                                  if (!product?.includes(productName)) {
                                                    product.push(productName);
                                                    form.setFieldValue(
                                                      ['dimensionList', index, 'authValueName'],
                                                      product
                                                    );
                                                  }
                                                } else if (code === 'guarMethodCollect') {
                                                  const guarantee: string[] =
                                                    form.getFieldValue(['dimensionList', index, 'authValueName']) || [];
                                                  if (!guarantee?.includes(value as string)) {
                                                    guarantee?.push(value as string);
                                                    form.setFieldValue(
                                                      ['dimensionList', index, 'authValueName'],
                                                      guarantee
                                                    );
                                                  }
                                                } else {
                                                  form.setFieldValue(['dimensionList', index, 'authValueName'], value);
                                                }

                                                updateDimensionValue(index, 'authValue', e);
                                              }}
                                              disabled={mode === 'detail'}
                                            />
                                          </YRForm.Item>
                                        </YRCol>
                                        <YRCol span={7}>
                                          {['creditResultType', 'loanResultType'].includes(
                                            form.getFieldValue(['dimensionList', index, 'authCode'])
                                          ) && (
                                            <YRForm.Item
                                              label="第三维度"
                                              name={['dimensionList', index, 'resultType']}
                                              noStyle
                                            >
                                              <YRInput
                                                style={{ width: '100%' }}
                                                onBlur={(e) => {
                                                  const { value } = e.target;
                                                  updateDimensionValue(index, 'resultType', value);
                                                }}
                                                disabled={mode === 'detail'}
                                              />
                                            </YRForm.Item>
                                          )}
                                        </YRCol>
                                        <YRForm.Item
                                          label="第一维度名称"
                                          name={['dimensionList', index, 'authCodeName']}
                                          noStyle
                                          hidden
                                        >
                                          <YRInput
                                            style={{ width: '100%' }}
                                            onChange={(e) => {
                                              updateDimensionValue(index, 'authCodeName', e);
                                            }}
                                            disabled={mode === 'detail'}
                                          />
                                        </YRForm.Item>
                                        <YRForm.Item
                                          label="第二维度名称"
                                          name={['dimensionList', index, 'authValueName']}
                                          noStyle
                                          hidden
                                        >
                                          <YRInput
                                            style={{ width: '100%' }}
                                            onChange={(e) => {
                                              updateDimensionValue(index, 'authValueName', e);
                                            }}
                                            disabled={mode === 'detail'}
                                          />
                                        </YRForm.Item>
                                      </YRRow>
                                    </YRList.Item>
                                    {_provided.placeholder}
                                  </div>
                                )}
                              </Draggable>
                            ))}
                          </YRList>
                        </div>
                      )}
                    </Droppable>
                  </DragDropContext>
                )
              ) : (
                <YREmpty style={{ border: '1px solid #ddd', padding: '10px', margin: '0px -1px' }} />
              )}
            </YRSpace>
          </YRClassificationLayout>
        </YRClassificationLayout.Space>
      </YRForm>
    </YRModal>
  );
};

export default YREasyUseModal.create(AddRoleTree);
