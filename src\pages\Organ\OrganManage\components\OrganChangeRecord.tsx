/**
 * @Author: wangyw26123
 * @Description: 机构管理-机构层级调整记录
 * @Date: Created in 2022-12-15 11:18:18
 * @Modifed By:
 */
import React, { useState } from 'react';
import { YRBadge, YRClassificationLayout, YRDict, YRTable, YRTableRowContent } from 'yrantd';
import { countTableWidth } from '@yr/util';
import type { YRTableProps } from 'yrantd';
import { EnumOrganStatusColor } from '@/constant/organ';

const OrganChangeRecord = (props) => {
  const { list: dataSource } = props;
  const [pageLoading] = useState<boolean>(false);

  const getTableColumns: YRTableProps['columns'] = [
    {
      title: '机构信息',
      dataIndex: 'organId',
      width: 220,
      render: (value: string, row: any) => {
        return (
          <div>
            <YRTableRowContent title="机构编号">{value || CONST.null}</YRTableRowContent>
            <YRTableRowContent title="机构名称">{row.organName || CONST.null}</YRTableRowContent>
          </div>
        );
      }
    },
    {
      title: '上级机构',
      dataIndex: 'beforeParentOrganName',
      width: 220,
      render: (value: string, row: any) => {
        return (
          <div>
            <YRTableRowContent title="调整前">{value || CONST.null}</YRTableRowContent>
            <YRTableRowContent title="调整后">{row.afterParentOrganName || CONST.null}</YRTableRowContent>
          </div>
        );
      }
    },
    {
      title: '登录账号',
      dataIndex: 'organLevel',
      width: 220,
      render: (value: string) => value || CONST.null
    },
    {
      title: '级别',
      dataIndex: 'beforeLevel',
      width: 220,
      render: (value: string, row: any) => {
        return (
          <div>
            <YRTableRowContent title="调整前">
              {value ? <YRDict.Text dictkey="EnumOrganLevel" defaultValue={value} /> : CONST.null}
            </YRTableRowContent>
            <YRTableRowContent title="调整后">
              {row.afterLevel ? <YRDict.Text dictkey="EnumOrganLevel" defaultValue={row.afterLevel} /> : CONST.null}
            </YRTableRowContent>
          </div>
        );
      }
    },
    {
      title: '操作信息',
      dataIndex: 'operatorUserName',
      width: 220,
      render: (value: string, row: any) => {
        return (
          <div>
            <YRTableRowContent title="操作人">{value || CONST.null}</YRTableRowContent>
            <YRTableRowContent title="操作机构">{row.operatorOrganName || CONST.null}</YRTableRowContent>
            <YRTableRowContent title="调整时间">{row.operatorDate || CONST.null}</YRTableRowContent>
          </div>
        );
      }
    },
    {
      title: '状态',
      dataIndex: 'organStatus',
      width: 220,
      render: (value: string) => {
        if (!value) return CONST.null;

        return (
          <YRBadge
            status={EnumOrganStatusColor[value]}
            text={<YRDict.Text dictkey="EnumOrganStatus" defaultValue={value} />}
          />
        );
      }
    }
  ];

  return (
    <YRClassificationLayout title="机构层级调整记录">
      <YRTable
        rowKey="organId"
        loading={pageLoading}
        dataSource={dataSource}
        columns={getTableColumns}
        scroll={{
          y: document.body.clientHeight - 258,
          x: countTableWidth(getTableColumns)
        }}
      />
    </YRClassificationLayout>
  );
};

export default OrganChangeRecord;
