/**
 * 页面描述: 场景管理
 * @文件名 index.tsx
 * @filePath \src\pages\Setting\RuleManager\Scene\index.tsx
 * @Date 2023-08-10 10:23:36
 * @Email <EMAIL>
 * <AUTHOR>
 */
import React, { useRef, useState } from 'react';
import { YRFlexPageLayout, YREmpty, YRLink, YRSpace } from 'yrantd';
import SceneTree from './components/SceneTree';
import SceneInfo from './components/SceneInfo';

const Scene = () => {
  const [activeNode, setActiveNode] = useState({}) as any;

  return (
    <YRFlexPageLayout>
      <YRFlexPageLayout.Sider title="场景目录">
        <SceneTree setActiveNode={setActiveNode} />
      </YRFlexPageLayout.Sider>
      <YRFlexPageLayout.Main title="场景信息">
        {activeNode?.treeId ? <SceneInfo activeNode={activeNode} /> : <YREmpty style={{ marginTop: '60px' }} />}
      </YRFlexPageLayout.Main>
    </YRFlexPageLayout>
  );
};

export default Scene;
