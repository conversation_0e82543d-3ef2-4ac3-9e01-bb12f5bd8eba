.common-left-slider {
  background-color: #fff;
  margin: 16px;
  margin-right: 0;
  position: relative;
  .select-wrap {
    padding: 10px;
    width: 100%;
    .add-wrap {
      margin-left: 5px;
      margin-top: 3px;
      width: 26px;
      height: 26px;
      border-radius: 16px;
      line-height: 32px;
      padding: 1px 5px 5px 2px;
      color: rgba(0, 0, 0, 0.3);
      transition: all 0.2s !important;
    }
    .add-wrap:hover {
      color: var(--primary-5);
      background: rgba(249, 249, 249, 1);
      cursor: pointer;
    }
  }
  &.ant-layout-sider {
    width: auto !important;
    flex: 0 0 auto !important;
    max-width: none !important;
    min-width: 200px !important;
  }
  .list-box {
    height: calc(100vh - 170px);
    overflow-y: auto;
    overflow-x: hidden;
    &::-webkit-scrollbar {
      width: 4px; /*高宽分别对应横竖滚动条的尺寸*/
      height: 4px;
    }
    &::-webkit-scrollbar-track {
      background-color: #fff;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 2px;
      background-color: rgba(0, 0, 0, 0.1);
      /* -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3); */
    }
  }
  .ant-list-item {
    display: flex;
    padding: 10px;
    padding-right: 0;
    border-left: 2px solid transparent;
    cursor: pointer;
    width: 100%;
    transition: all 0.3s;
    .icon-menu {
      width: 20px;
      height: 20px;
      text-align: center;
      line-height: 20px;
      display: inline-block;
      border-radius: 50%;
      transition: all 0.3s;
      color: #ddd;
      font-weight: 600;
      &:hover {
        background: rgba(0, 0, 0, 0.03);
        cursor: pointer;
        color: #1890ff;
      }
    }
    .box {
      flex: 1;
      width: 80%;
    }
    &.active {
      background-color: #e6f7ff;
      border-left-color: #1890ff;
      color: #1890ff;
      .list-item-desc {
        color: #1890ff;
      }
    }
    &:hover {
      background-color: #e6f7ff;
      border-left-color: #1890ff;
      color: #1890ff;
      .list-item-desc {
        color: #1890ff;
      }
    }
  }
  .left-content-title {
    margin-right: auto;
    line-height: 32px;
    font-weight: 600;
    color: #000;
    white-space: nowrap;
  }
  .list-item-title {
    position: relative;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    font-weight: 500;
    font-size: 13px;
    // width: 80%;
    line-height: 1.5;
  }
  .list-item-desc {
    padding-top: 5px;
    width: 100%;
    font-size: 12px;
    line-height: 1.5;
    color: var(--neutral-7);
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    height: 23px;
  }
  .ant-list-item-action {
    align-self: flex-start;
    margin-left: 0;
    flex-shrink: 0;
  }
}
// topbar 样式
.common-left-slider {
  .top-bar-box {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    padding: 10px;
  }
  .top-bar-box > span {
    width: 32px;
    height: 32px;
    border-radius: 16px;
    line-height: 32px;
    padding-left: 10px;
    padding-right: 10px;
    color: rgba(0, 0, 0, 0.3);
    transition: all 0.2s !important;
  }
  .top-bar-box > span i {
    font-size: 14px;
  }
  .top-bar-box .ant-input-affix-wrapper .ant-input:not(:last-child) {
    padding-right: 16px;
  }
  .top-bar-box > span.change-show {
    width: 100%;
    color: var(--primary-5);
    /* background: rgba(249, 249, 249, 1); */
    background: none !important;
    border-bottom: solid 1px #1890ff;
    border-radius: 0;
    padding: 0 4px 0 10px;
  }
  .top-bar-box > span.edit-search {
    transition-property: width, background !important;
  }
  .top-bar-box .ant-input-affix-wrapper .ant-input-suffix {
    right: 0px;
    font-size: 12px;
    color: rgba(153, 153, 153, 0.65);
  }
  .top-bar-box .ant-input-affix-wrapper .ant-input-suffix:hover {
    color: #1890ff;
  }
  .top-bar-box > span.change-show ~ span {
    display: none;
  }
  .top-bar-box {
    margin-right: 0 !important;
  }
  .top-bar-box > span:hover {
    color: var(--primary-5);
    background: rgba(249, 249, 249, 1);
    cursor: pointer;
  }
  .top-bar-box span.upload-icon:hover i {
    color: var(--primary-5) !important;
  }
  .top-bar-box .ant-input {
    border: none;
    border-radius: 0;
    box-sizing: border-box;
    background: none;
    transition: all 0.6s ease-in-out 0.2s !important;
  }
  .top-bar-box .ant-input-affix-wrapper {
    transition: all 0.6s ease-in-out 0.2s !important;
  }
  .top-bar-box .ant-input:focus {
    border-color: 1px solid var(--primary-5);
    outline: 0;
    box-shadow: none;
  }
  // tree 样式
  .var-text-overhidden {
    text-overflow: ellipsis;
    max-width: 192px;
    white-space: nowrap;
    overflow: hidden;
    vertical-align: top;
    display: inline-block;
  }
  .variable_set_sider {
    background: #fff;
  }
  .variable_set_sider .sider_content {
    height: calc(100vh - 114px);
    overflow-y: auto;
    overflow-x: auto;
    padding: 10px;
    background: #fff;
  }
  .variable_set_content_main {
    margin: 24px 24px 0;
    padding: 24px;
    background: #fff;
  }
  .variable_set_content_main .ant-table-thead > tr > th {
    background: none;
  }
  .variable_set_sider.ant-layout-sider {
    width: 280px !important;
    max-width: 280px !important;
    min-width: 280px !important;
    margin: 16px 0 16px 16px;
    overflow: hidden;
  }
  .search-tree_title {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    line-height: 22px;
    padding-bottom: 8px;
  }
  .variable_set_tree .ant-tree li .ant-tree-node-content-wrapper {
    transition: none;
  }
  .search-tree_box {
    margin-bottom: 8px;
    display: flex;
  }
  .search-tree_box .search {
    flex: 1 1 auto;
  }
  .search-tree_box .icon {
    font-size: 20px;
    flex: 0 0 auto;
    margin-top: 6px;
    margin-left: 8px;
  }
  .search-tree_box .icon:hover {
    color: #1890ff;
  }
  .variable_set_tree li .ant-tree-node-content-wrapper {
    width: calc(100% - 21px);
    height: 32px;
    line-height: 32px;
    border-radius: 4px;
  }
  /* 展开/折叠图标 */
  .variable_set_tree .ant-tree.ant-tree-show-line li span.ant-tree-switcher {
    height: 32px;
    line-height: 32px;
    z-index: 1;
    background: none !important;
    font-size: 13px;
    color: rgba(0, 0, 0, 0.2);
  }

  .variable_set_tree
    .ant-tree
    li
    .ant-tree-node-content-wrapper.ant-tree-node-selected
    .ant-tree-title {
    color: #1890ff;
  }
  /* hover样式 */
  .variable_set_tree
    .ant-tree
    > li
    span.ant-tree-node-content-wrapper:hover::before,
  .variable_set_tree
    .ant-tree
    .ant-tree-child-tree
    > li
    span.ant-tree-node-content-wrapper:hover::before {
    background-color: #e6f7ff;
    border-left-color: #1890ff;
  }
  /* 选中样式 */
  .variable_set_tree
    .ant-tree
    .ant-tree-child-tree
    > li.ant-tree-treenode-selected
    > span.ant-tree-node-content-wrapper::before {
    background-color: #e6f7ff;
    border-left-color: #1890ff;
  }
  .variable_set_tree
    .ant-tree
    li
    .ant-tree-node-content-wrapper.ant-tree-node-selected {
    color: #fff !important;
    background: none;
  }
  .variable_set_tree
    .ant-tree
    li
    .ant-tree-treenode-selected
    .ant-tree-switcher {
    color: #1890ff !important;
  }
  .variable_set_tree .ant-tree li .ant-tree-node-content-wrapper:hover {
    background: none;
    color: #1890ff;
  }
  .variable_set_tree
    .ant-tree-treenode-switcher-close:hover
    .ant-tree-switcher {
    color: #1890ff !important;
  }
  /* 节点标题 */
  .variable_set_tree
    .ant-tree
    li
    .ant-tree-node-content-wrapper
    > .ant-tree-title {
    display: inline-block;
    max-width: 160px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 13px;
  }
  .variable_set_tree .ant-tree li .ant-tree-node-content-wrapper > span {
    position: relative;
    z-index: 1;
  }
  /* 去除边线 */
  .variable_set_tree .ant-tree.ant-tree-show-line li:not(:last-child)::before {
    border: none;
  }
  /* 隐藏默认图标 */
  .variable_set_tree
    .ant-tree.ant-tree-show-line
    li
    .ant-tree-switcher-noop
    > i {
    visibility: hidden;
  }
  .variable_set_tree {
    width: 100%;
    height: calc(100vh - 174px);
    overflow-y: scroll;
    overflow-x: hidden;
    padding-right: 12px;
  }
  .variable_set_tree .ant-tree li {
    padding: 2px 0;
  }
  .variable_set_tree::-webkit-scrollbar {
    width: 4px; /*对垂直流动条有效*/
    height: 4px; /*对水平流动条有效*/
  }
  /*定义滚动条的轨道颜色、内阴影及圆角*/
  .variable_set_tree::-webkit-scrollbar-track {
    background-color: white;
  }
  /*定义滑块颜色、内阴影及圆角*/
  .variable_set_tree::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: rgba(0, 0, 0, 0.1);
  }
  /*定义两端按钮的样式*/
  .variable_set_tree::-webkit-scrollbar-button {
    background-color: white;
  }
  /*定义右下角汇合处的样式*/
  .variable_set_tree::-webkit-scrollbar-corner {
    background: white;
  }

  .variable_set_tree .ant-tree > li span.ant-tree-node-content-wrapper::before,
  .variable_set_tree
    .ant-tree
    .ant-tree-child-tree
    > li
    span.ant-tree-node-content-wrapper::before {
    position: absolute;
    right: -4px;
    width: 500px;
    height: 32px;
    border-left: 2px solid #fff;
    /* border-radius: 4px; */
    content: "";
    z-index: 0;
  }
}
