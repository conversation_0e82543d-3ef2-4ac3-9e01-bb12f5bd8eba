/**
 *模块功能路由
 */
import { EnumPageType } from '@yr/util';
import { buildRoutes } from '@/utils/micro-utils';
import setting from './setting';
import organ from '@/routes/part/organ';
import flow from '@/routes/part/flow';

const partRoutes = [
  {
    path: '/',
    exact: true,
    redirect: '/organ-group'
  },
  ...organ,
  ...flow,
  ...setting
];

export default buildRoutes(partRoutes, EnumPageType.PART_PAGE.code);
