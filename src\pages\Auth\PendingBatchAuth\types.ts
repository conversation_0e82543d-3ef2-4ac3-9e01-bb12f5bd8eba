/**
 * 待复核-批量授权管理相关类型定义
 */

// 查询参数接口
export interface PendingBatchQueryParams {
  pageNum: number;                    // 页码（必填）
  pageSize: number;                   // 每页大小（必填）
  batchAuthNo?: string;               // 批量授权编号
  authStatus?: string;                // 授权状态（参考EnumAuthBaseStatus枚举）
}

// API响应数据结构
export interface ApiResponse<T = any> {
  rpcResult: "SUCCESS" | "FAIL";
  data: T;
  errorMessage?: string;
}

// 分页响应数据
export interface PageResponse<T = any> {
  pageNum: number;
  pageSize: number;
  total: number;
  pages: number;
  list: T[];
}

// 后端API返回的待复核批量授权信息
export interface PendingBatchAuthDto {
  batchAuthBaseId: string;          // 批量授权编号
  tenantId: string;                 // 租户号
  authStatus: string;               // 授权状态
  registerTime: string;             // 登记时间 (ISO格式)
  registerId: string;               // 登记人ID
  registerName: string;             // 登记人名称
  registerOrgId: string;            // 登记机构ID
  registerOrgName: string;          // 登记机构名称
  fileName: string;                 // 文件名
  successNum: number;               // 成功笔数
  failNum: number;                  // 失败笔数
  flowInstanceId?: string;          // 流程实例ID
  remark?: string;                  // 备注
}

// 前端使用的待复核批量授权信息（映射后的数据结构）
export interface PendingBatchAuthInfo {
  batchAuthId: string;              // 批量授权ID（映射自batchAuthBaseId）
  batchAuthNo: string;              // 批量授权编号（映射自batchAuthBaseId）
  fileName: string;                 // 导入文件名称
  authStatus: string;               // 授权状态
  operatorName: string;             // 登记人（映射自registerName）
  operatorId: string;               // 登记人ID（映射自registerId）
  ownOrganName: string;             // 登记机构（映射自registerOrgName）
  ownOrganId: string;               // 登记机构ID（映射自registerOrgId）
  inputTime: string;                // 登记日期（格式化后的registerTime）
  updateTime: string;               // 更新时间
  totalCount: number;               // 总记录数（successNum + failNum）
  successCount: number;             // 成功记录数（映射自successNum）
  failCount: number;                // 失败记录数（映射自failNum）
  flowInstanceId?: string;          // 流程实例ID
  remark?: string;                  // 备注
}

// 复核操作参数
export interface BatchReviewParams {
  batchAuthId: string;              // 批量授权ID
  reviewResult: 'PASS' | 'REJECT';  // 复核结果：通过|退回
  reviewRemark?: string;            // 复核意见
}

// 批量授权详情信息
export interface BatchAuthDetailInfo {
  batchAuthId: string;              // 批量授权ID
  batchAuthNo: string;              // 批量授权编号
  fileName: string;                 // 导入文件名称
  authStatus: string;               // 授权状态
  operatorName: string;             // 登记人
  ownOrganName: string;             // 登记机构
  inputTime: string;                // 登记时间
  totalCount: number;               // 总记录数
  successCount: number;             // 成功记录数
  failCount: number;                // 失败记录数
  authDetails?: AuthDetailItem[];   // 授权明细列表
}

// 授权明细项
export interface AuthDetailItem {
  authNo: string;                   // 授权编号
  orgName: string;                  // 机构名称
  roleName: string;                 // 角色名称
  authCategory: string;             // 授权类别
  authStatus: string;               // 授权状态
  errorMessage?: string;            // 错误信息（如果有）
}
