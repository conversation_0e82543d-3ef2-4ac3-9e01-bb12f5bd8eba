import { NsEdgeCmd, NsNodeCmd, XFlowEdgeCommands, XFlowNodeCommands } from '@yr/xflow';
import { nodeTypeNames } from './constants';
import { YRMessage } from 'yrantd';

let App: any = null;
let Instance: any = null;

/**
 * xflow实例/app，便于全局访问
 */
class XflowInstance {
  instance: any;
  app: any;
  isAddNode: boolean;
  initLoad: boolean;

  constructor() {
    this.isAddNode = false;
    this.initLoad = false;
  }

  // 不要动这行代码，用于实时保存的函数，初始化时会被覆盖掉。
  setRealTimeSave() {}

  setApp(app, instance) {
    App = app;
    Instance = instance;
    this.app = app;
    this.instance = instance;
  }

  getApp() {
    return this.app;
  }

  getGraphData() {
    return this.app.getGraphData();
  }

  getAllNodes() {
    return this.app.getAllNodes();
  }

  /**
   * 更新边
   * @param edge edgeConfig
   */
  updateEdge(edge) {
    return this.app.commandService.executeCommand(XFlowEdgeCommands.UPDATE_EDGE.id, { edgeConfig: edge });
  }

  /**
   * 高亮边
   * @param values {edgeId,strokeColor,strokeWidth }
   */
  highlightEdge(values: NsEdgeCmd.HighlightEdge.IArgs) {
    return this.app.commandService.executeCommand(XFlowEdgeCommands.HIGHLIGHT_EDGE.id, { ...values });
  }

  /**
   * 更新节点
   * @param node nodeConfig
   */
  updateNode(node) {
    return this.app.commandService.executeCommand(XFlowNodeCommands.UPDATE_NODE.id, { nodeConfig: node });
  }

  /**
   * 点亮节点
   * @param values {nodeId,stroke,strokeWidth}
   */
  highlightNode(values: NsNodeCmd.HighlightNode.IArgs) {
    return this.app.commandService.executeCommand(XFlowNodeCommands.HIGHLIGHT_NODE.id, { ...values });
  }
}

export default new XflowInstance();
/**
 * 提供查找node/edge的基本能力
 */
export class XflowNodes {
  edges: any;
  nodes: any;
  constructor(props) {
    this.nodes = props.nodes;
    this.edges = props.edges;
  }

  /**
   * 根据节点类型查找所有节点
   * @param type 节点类型
   * @returns {*} [node]
   */
  findNodes(type) {
    return this.nodes?.filter((node) => node.name === nodeTypeNames[type]);
  }

  /**
   * 根据节点id查找节点
   * @param nodeId 节点Id
   * @returns {*}
   */
  findNode(nodeId) {
    return this.nodes?.find((item) => item.id === nodeId);
  }

  /**
   * 根据边id查找边
   * @param edgeId 边Id
   * @returns {*}
   */
  findEdge(edgeId) {
    return this.edges?.find((item) => item.id === edgeId);
  }

  /**
   * 获取一个节点的进出边
   * @param incomingEdges 进线集合
   * @param outgoingEdges 出线集合
   * @returns {{outs: *[], ins: *[]}}
   */
  getEdges(incomingEdges, outgoingEdges) {
    const ins: any = [];
    const outs: any = [];
    if (Array.isArray(incomingEdges)) {
      incomingEdges?.forEach((item) => {
        const { id } = this.findEdge(item.id)?.edgeFormData || {};
        if (id) {
          ins.push({
            value: id
          });
        }
      });
    }

    if (Array.isArray(outgoingEdges)) {
      outgoingEdges?.forEach((item) => {
        const { id } = this.findEdge(item.id)?.edgeFormData || {};
        if (id) {
          outs.push({
            value: id
          });
        }
      });
    }

    return { ins, outs };
  }

  /**
   * 检查是否有独立节点(没有连接线的节点)
   * @returns {boolean|*}
   */
  checkSingleNode() {
    for (const node of this.nodes) {
      const { incomingEdges, outgoingEdges } = node;
      if ((!incomingEdges || !incomingEdges.length) && (!outgoingEdges || !outgoingEdges.length)) {
        return node;
      }
    }
    return false;
  }

  checkNode() {
    // 检查是否包含多个开始节点
    const startNodes = this.findNodes('START');
    if (startNodes.length === 0) {
      YRMessage.warning('请添加开始节点');
      return false;
    } else if (startNodes.length > 1) {
      YRMessage.warning('包含多个开始节点，请检查');
      return false;
    }

    // 检查是否包含多个结束节点
    const endNodes = this.findNodes('END');
    if (endNodes.length === 0) {
      YRMessage.warning('请添加结束节点');
      return false;
    } else if (endNodes.length > 1) {
      YRMessage.warning('包含多个结束节点，请检查');
      return false;
    }

    // 检查是否是独立节点(没有任何连接线)
    const node = this.checkSingleNode();
    if (node) {
      Instance.resetSelection(node.id);
      App.executeCommand(XFlowNodeCommands.CENTER_NODE.id, {
        nodeConfig: { id: node.id }
      });

      YRMessage.warning(`连接线不完整，请检查节点: ${node.label}`);

      return false;
    }

    return true;
  }
}
