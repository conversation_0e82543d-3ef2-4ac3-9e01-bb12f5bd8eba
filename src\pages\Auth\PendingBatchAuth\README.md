# 待复核-批量授权管理页面

## 页面概述

待复核-批量授权管理页面用于处理处于复核中状态的批量授权记录，支持查询、详情查看、复核通过和复核退回等功能。

## 功能特性

### 1. 查询功能
- **批量授权编号**: 支持按批量授权编号精确查询
- **授权状态**: 支持按授权状态下拉选择查询（从数据字典获取）
- **分页查询**: 支持分页显示，提高查询性能
- **状态过滤**: 默认只显示状态为"复核中"(020)的批量授权记录

### 2. 列表展示
按照要求显示以下字段：
- **批量授权编号**: 唯一标识，固定在左侧
- **导入文件名称**: 批量导入的文件名
- **授权状态**: 当前授权状态（显示中文名称）
- **登记人**: 批量授权的登记人
- **登记机构**: 批量授权的登记机构
- **登记日期**: 批量授权的登记日期

### 3. 操作功能
每条记录固定提供以下操作按钮：
- **详情**: 查看批量授权详细信息（弹窗展示）
- **复核退回**: 将批量授权退回给申请人重新处理
- **复核通过**: 通过复核，批量授权生效

## 数据结构

### 待复核批量授权信息 (PendingBatchAuthInfo)
```typescript
interface PendingBatchAuthInfo {
  batchAuthId: string;      // 批量授权ID
  batchAuthNo: string;      // 批量授权编号
  fileName: string;         // 导入文件名称
  authStatus: string;       // 授权状态
  operatorName: string;     // 登记人
  ownOrganName: string;     // 登记机构
  inputTime: string;        // 登记日期
  totalCount: number;       // 总记录数
  successCount: number;     // 成功记录数
  failCount: number;        // 失败记录数
  // ... 其他字段
}
```

## 技术实现

### 组件结构
```
PendingBatchAuth/
├── index.tsx           # 主组件
├── types.ts           # 类型定义
├── useIndex.tsx       # 表格配置和工具函数
└── README.md          # 文档说明
```

### 主要依赖
- **yrantd**: UI组件库
- **ahooks**: React Hooks工具库
- **@yr/util**: 工具函数库和数据字典

### 权限控制
- 页面权限: `M010504` (待复核-批量)
- 接口权限: 通过批量授权相关接口获取

## API接口

### 1. 查询接口
- **接口**: `queryPendingBatchAuthList`
- **路径**: `/authBaseInfoBatchQueryController/queryPendingPage`
- **功能**: 分页查询待复核批量授权列表

### 2. 复核通过接口
- **接口**: `approveBatchAuth`
- **路径**: `/authBaseInfoBatchController/approve`
- **功能**: 批量授权复核通过

### 3. 复核退回接口
- **接口**: `rejectBatchAuth`
- **路径**: `/authBaseInfoBatchController/reject`
- **功能**: 批量授权复核退回

### 4. 详情查询接口
- **接口**: `queryBatchAuthDetail`
- **路径**: `/authBaseInfoBatchQueryController/queryDetail`
- **功能**: 查询批量授权详细信息

## 使用说明

### 1. 查询流程
1. 在查询条件区域输入批量授权编号或选择授权状态
2. 点击"查询"按钮执行查询
3. 点击"重置"按钮清空查询条件

### 2. 复核流程
1. 在列表中找到需要复核的批量授权记录
2. 点击"详情"按钮查看详细信息
3. 根据业务规则决定复核结果：
   - 点击"复核通过"：批量授权生效
   - 点击"复核退回"：退回给申请人修改

### 3. 查询条件说明
- **批量授权编号**: 支持精确匹配查询
- **授权状态**: 下拉选择器，从数据字典 'AUTH_STATUS' 获取
- **默认过滤**: 页面默认只显示"复核中"状态的记录

## 业务规则

### 1. 数据过滤
- 页面只显示状态为"复核中"(020)的批量授权记录
- 支持按批量授权编号和授权状态进行筛选查询

### 2. 操作权限
- 只有具有复核权限的用户才能进行复核操作
- 复核操作需要记录操作日志

### 3. 状态流转
- 复核通过：状态从"复核中"变为"已生效"
- 复核退回：状态从"复核中"变为"复核退回"

## 数据映射

### API响应字段映射
| API字段 | 前端字段 | 说明 |
|---------|----------|------|
| batchAuthBaseId | batchAuthNo | 批量授权编号 |
| fileName | fileName | 导入文件名称 |
| authStatus | authStatus | 授权状态 |
| registerName | operatorName | 登记人 |
| registerOrgName | ownOrganName | 登记机构 |
| registerTime | inputTime | 登记日期（格式化） |
| successNum | successCount | 成功笔数 |
| failNum | failCount | 失败笔数 |

## 注意事项

1. **数据安全**: 复核操作需要严格的权限控制
2. **操作审计**: 所有复核操作需要记录详细日志
3. **业务规则**: 严格按照业务流程进行状态流转
4. **用户体验**: 提供清晰的操作反馈和错误提示
5. **性能优化**: 大数据量时需要考虑分页和查询优化

## 与单笔复核的区别

1. **数据来源**: 批量复核处理的是批量导入的授权数据
2. **复核粒度**: 以批次为单位进行复核，而非单条记录
3. **详情展示**: 显示批量导入的统计信息（成功/失败笔数）
4. **操作影响**: 复核操作影响整个批次的所有授权记录

## 更新日志

- 2024-12-19: 初始版本创建
- 实现基础查询和复核功能
- 完成页面布局和交互逻辑
- 集成权限控制和数据字典
- 参考PendingSingleAuth页面实现模式
