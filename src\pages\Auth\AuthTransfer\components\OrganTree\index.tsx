/* eslint-disable react/jsx-indent */
/*
 * @Author: liaokt
 * @Description: 一级机构树
 * @Date: 2024-10-16 10:04:40
 * @LastEditors: liaokt
 * @LastEditTime: 2024-10-24 15:48:32
 */
import React, { useEffect, useMemo, useState } from 'react';
import { YRSpin, YRTree, YRItem, YRLink, YRModal, YREmpty, YRMessage } from 'yrantd';
import { useRequest } from 'ahooks';
import { M0105 } from '@permit/organ';
import { getDictItems } from '@yr/util';

const OrganTree = (props: { data: any[]; loading: boolean; setData: any; height: number; okCallback: () => void }) => {
  const { data, loading, setData, height, okCallback } = props;

  const { delOrgAuthCategoryRoleInfo } = M0105.interfaces;

  const [authCategory, setAuthCategory] = useState({
    creditResultType: '授信业务审批授权',
    loanResultType: '放款业务审批授权'
  });

  useEffect(() => {
    getDictItems({ dictKeys: 'AUTH_CATEGORY' }).then((res) => {
      const { data: resData } = res;
      // eslint-disable-next-line @iceworks/best-practices/recommend-polyfill
      const dict = Object.fromEntries(
        resData['AUTH_CATEGORY']?.map((item: { itemKey: string; itemName: string }) => {
          const { itemKey, itemName } = item as { itemKey: string; itemName: string };
          return [itemKey, itemName]; // 返回一个键值对数组
        }) || [] // 确保在没有数据时返回空数组
      ) as any;
      setAuthCategory(dict);
    });
  }, []);

  const mergeCategoryToChildren = (nodes: any[]): any[] => {
    return nodes.map((node) => {
      // 将 categoryList 转换为 TreeNode 类型
      const categoryChildren = (node.categoryList || []).map(
        (category: { orgId: any; authCategory: string | number }) => ({
          orgId: category.orgId,
          orgName: authCategory[category.authCategory],
          // 父的机构登记
          orgLvl: node.orgLvl || '',
          // 父的机构 id
          relativeOrgId: node.orgId,
          // 是否为授权
          isAuth: true,
          authCategory: category.authCategory,
          parentOrgName: node.orgName
        })
      );

      // 合并 categoryList 转换的节点和 children
      const mergedChildren = [...categoryChildren, ...(node.children || [])];

      return {
        ...node,
        children: mergedChildren.length > 0 ? mergeCategoryToChildren(mergedChildren) : undefined // 递归处理子节点
      };
    });
  };

  const { loading: deleteLoading, run: onDelete } = useRequest(delOrgAuthCategoryRoleInfo, {
    manual: true,
    onSuccess: (res: any) => {
      if (res?.errorMessage === null) {
        YRMessage.success('删除成功', 0.5, () => {
          okCallback();
        });
      }
    }
  });

  const treeData = useMemo(() => {
    return mergeCategoryToChildren(data);
  }, [data]);

  return (
    <>
      <YRSpin spinning={loading} style={{ marginTop: '20px' }}>
        {data.length > 0 ? (
          <YRTree.DirectoryTree
            height={height}
            treeData={treeData}
            showIcon={false}
            titleRender={(nodeData) => {
              const { orgName, orgId, authCategory, parentOrgName } = nodeData as any;
              return (
                <YRItem
                  title={parentOrgName ? `* ${orgName}` : orgName}
                  key={orgId}
                  onClick={() => {
                    const { relativeOrgId, isAuth, orgLvl } = nodeData;
                    if (isAuth) {
                      // 只有授信权限采取查询关联角色
                      setData({ orgId: relativeOrgId, authCategory, orgLvl, parentOrgName });
                    }
                  }}
                  operateRender={
                    parentOrgName
                      ? [
                          <YRLink
                            type={'primary'}
                            onClick={() => {
                              YRModal.confirm({
                                title: '删除',
                                okText: '确定',
                                cancelText: '取消',
                                content: '确定要删除此授权吗?',
                                okButtonProps: {
                                  loading: deleteLoading
                                },
                                onOk: () => {
                                  onDelete({ orgId, authCategory });
                                }
                              });
                            }}
                          >
                            删除
                          </YRLink>
                        ]
                      : []
                  }
                />
              );
            }}
          />
        ) : (
          <YREmpty style={{ marginTop: '80px' }} />
        )}
      </YRSpin>
    </>
  );
};

export default OrganTree;
