import React, { useState, useEffect } from 'react';
import { YRTree, YRTooltip } from 'yrantd';

const { TreeNode } = YRTree;

const Tree = (props) => {
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [initExpandedKey, setInitExpandedKey] = useState('');
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [treeDataList, setTreeDataList] = useState([]);
  const { searchValue, treeData, fieldNames, setCurrentItem, initExpandedKey: expandedKey, search, ...rest } = props;
  //   const { name, key } = fieldNames;
  // 初始化展开默认节点
  const keys = expandedKeys && expandedKeys.length ? expandedKeys : [initExpandedKey];
  useEffect(() => {
    getFlatTreeDataList(treeData);
    setInitExpandedKey(expandedKey);
  }, [expandedKey]);
  useEffect(() => {
    onChange(searchValue);
  }, [searchValue]);

  // 展开或收起节点
  const onExpand = (expandedKeys) => {
    setExpandedKeys(expandedKeys);
    setAutoExpandParent(false);
    setInitExpandedKey('');
  };

  // 搜索框输入值改变
  const onChange = (value) => {
    const expandedKeys = treeDataList
      .map((item) => {
        if (item.title.indexOf(value) > -1) {
          return getParentKey(item.key, treeData);
        }
        return null;
      })
      .filter((item, i, self) => item && self.indexOf(item) === i);

    setExpandedKeys(expandedKeys);
    setAutoExpandParent(true);
  };

  const getFlatTreeDataList = (data) => {
    for (let i = 0; i < data.length; i++) {
      const node = data[i];
      treeDataList.push({ key: node.key, title: node.title });
      if (node.children) {
        getFlatTreeDataList(node.children);
      }
    }
  };

  const getParentKey = (key, tree) => {
    // 获取当前节点所在的父节点key值
    let parentKey;
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i];
      if (node.children) {
        if (node.children.some((item) => item.key === key)) {
          parentKey = node.key;
        } else {
          const recursive = getParentKey(key, node.children);
          if (recursive) {
            parentKey = recursive;
          }
        }
        if (parentKey) {
          break;
        }
      }
    }
    return parentKey;
  };

  function generateTreeNodes(treeData) {
    if (treeData.length > 0) {
      return treeData.map((item) => {
        const { title } = item;
        const index = title.indexOf(searchValue);
        const beforeStr = title.substr(0, index);
        const afterStr = title.substr(index + searchValue.length);
        const tls = (
          <YRTooltip placement="bottomLeft" title={item.title}>
            {index > -1 ? (
              <span>
                {beforeStr}
                <span style={{ color: '#f50' }}>{searchValue}</span>
                <span>{afterStr}</span>
                {/* <YRTooltip title={afterStr}>
                    {afterStr}
                  </YRTooltip> */}
              </span>
            ) : (
              <span>{item.title}</span>
            )}
          </YRTooltip>
        );

        if (item.children) {
          return (
            <TreeNode key={item.key} title={tls} raw={item}>
              {generateTreeNodes(item.children)}
            </TreeNode>
          );
        }
        return <TreeNode key={item.key} title={tls} raw={item} />;
      });
    }
  }
  return (
    <div style={{ padding: '10px' }}>
      <div className="variable_set_tree">
        <YRTree onExpand={onExpand} expandedKeys={keys} autoExpandParent={autoExpandParent} {...rest}>
          {generateTreeNodes(treeData)}
        </YRTree>
      </div>
    </div>
  );
};

export default Tree;
