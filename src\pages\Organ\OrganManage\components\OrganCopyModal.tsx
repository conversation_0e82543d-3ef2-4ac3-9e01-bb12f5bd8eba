/**
 * @Author: wangyw26123
 * @Description: 机构管理-机构复制
 * @Date: Created in 2022-12-15 16:58:45
 * @Modifed By:
 */
import React, { useEffect } from 'react';
import { YRForm, YRModal, YRButton, YRTreeSelect } from 'yrantd';
import { checkAuth, openNewTab } from '@/utils/utils';
import { M010101 } from '@permit/organ';

interface AdjustmentOrderProps {
  visible: boolean;
  treeData: any[];
  organInfo: any;
  onCancel: () => void;
}

const OrganCopyModal = (props: AdjustmentOrderProps) => {
  const { visible, treeData, onCancel, organInfo } = props;
  const [form] = YRForm.useForm();

  useEffect(() => {
    visible && form.resetFields();
  }, [organInfo, visible]);

  const onSubmit = () => {
    if (!checkAuth(M010101.E08)) return;

    form.validateFields().then((values) => {
      openNewTab({
        pathname: '/setting/organ/organ-manage/edit',
        query: {
          mode: 'add',
          organCopyId: values.organId
        }
      });
      onCancel();
    });
  };

  return (
    <YRModal
      title="机构复制"
      open={visible}
      onCancel={onCancel}
      footer={[
        <YRButton key="cancel" onClick={onCancel}>
          取消
        </YRButton>,
        <YRButton key="submit" type="primary" check={M010101.E08} onClick={onSubmit}>
          确定
        </YRButton>
      ]}
    >
      <YRForm form={form} bordered>
        <YRForm.Row>
          <YRForm.Item
            name="organId"
            label="机构"
            column="block"
            initialValue={organInfo.organId}
            rules={[{ required: true, message: '请选择机构' }]}
          >
            <YRTreeSelect
              treeData={treeData}
              fieldNames={{ label: 'organName', value: 'organId' }}
              placeholder="请选择机构"
            />
          </YRForm.Item>
        </YRForm.Row>
      </YRForm>
    </YRModal>
  );
};

export default OrganCopyModal;
