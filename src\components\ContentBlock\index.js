/*
 * @Description: 分块内容
 *
 *  属性说明：
 *    title 顶部名称
 *    actions 顶部右侧操作按钮
 *
 * @Author: your name
 * @Date: 2020-10-16 16:41:38
 * @LastEditors: your name
 * @LastEditTime: 2020-10-17 11:19:50
 */

import React from 'react';
import './index.less';

export default function ContentBlock(props) {
  const { title, actions, children, ...rest } = props;
  return (
    <div className={'content'} {...rest}>
      <div className={'content-top'}>
        <span className={'title'}>{title}</span>
        {actions && <span>{actions}</span>}
      </div>
      {children}
    </div>
  );
}
