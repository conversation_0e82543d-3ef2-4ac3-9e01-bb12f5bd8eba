Auth模块接口文档

## 概述

本文档详细描述了auth模块提供的所有REST API接口，包括基础授权管理、授权分类管理、授权规则执行等功能模块。

## 接口前缀说明

所有接口的URL前缀为：`RestConstant.MODULE_CORP_PREFIX`（具体值需要根据部署环境确定）

## 通用响应结构

### CommonResponse<T>
```json
{
  "rpcResult": "string",      // RPC调用结果
  "httpStatus": "integer",    // HTTP状态码
  "responseType": "string",   // 响应类型
  "errorCode": "string",      // 错误码
  "errorMessage": "string",   // 错误信息
  "data": "T"                // 业务数据
}
```

### PageInfo<T>
```json
{
  "pageNum": "integer",       // 当前页码
  "pageSize": "integer",      // 每页大小
  "total": "long",           // 总记录数
  "pages": "integer",        // 总页数
  "list": "List<T>"          // 数据列表
}
```

## 枚举值说明

### 授权状态 (EnumAuthBaseStatus)
- `010` - 待提交：总行内部授权岗在所选角色下新增授权配置保存后，或者复制基础授权保存后的状态
- `020` - 复核中：总行内部授权岗将待提交或复核退回的数据提交至复核阶段时的状态
- `050` - 复核退回：授权复核不通过时的状态
- `040` - 已生效：授权复核通过后的状态；对已暂停的授权记录操作重启后的状态
- `030` - 已失效：操作失效后的状态；若基础授权中设置了失效日期，则在到期后自动更新为此状态
- `060` - 已暂停：对已生效的基础授权操作暂停后的状态

### 授权类型 (EnumAuthType)
- `001` - 产品阈值
- `002` - 综合阈值  
- `003` - 专项阈值

### 授权类别 (EnumAuthCategory)
- `creditResultType` - 授信业务审批授权
- `loanResultType` - 放款审查授权

### 机构授权标识 (EnumOrgAuthFlag)
- `001` - 机构
- `002` - 角色

### 审批状态 (EnumApproveStatus)
- `010` - 待发起
- `020` - 审批中
- `030` - 已审批
- `050` - 驳回
- `060` - 取消

### 布尔值枚举 (EnumBoolean)
- `Y` - 是
- `N` - 否

---

## 1. 基础授权管理模块

### 1.1 基础授权操作接口 (AuthBaseInfoController)

**接口前缀**: `/authBaseInfo`

#### 1.1.1 保存基础授权

**接口路径**: `POST /authBaseInfo/saveBaseInfo`

**功能描述**: 新增或更新基础授权信息

**请求参数**: AuthBaseInfoSaveCommand
```json
{
  "authBaseId": "string",                    // 基础授权ID（更新时必传）
  "authCatalogRoleId": "string",            // 授权类别角色关联ID（必填）
  "effectBeginDate": "2024-01-01",          // 生效起日（必填，格式：yyyy-MM-dd）
  "effectEndDate": "2024-12-31",            // 生效止日（必填，格式：yyyy-MM-dd）
  "authType": "string",                     // 授权类型（必填，参考EnumAuthType）
  "dimInfoList": [                          // 维度信息列表
    {
      "authDimType": "string",              // 授权维度类型
      "authDimValue": "string",             // 授权维度值
      "firstDimType": "string"              // 一级维度类型
    }
  ]
}
```

**响应结果**: CommonResponse<String>
```json
{
  "rpcResult": "SUCCESS",
  "data": "AUTH_BASE_ID_12345"              // 返回基础授权ID
}
```

#### 1.1.2 删除基础授权

**接口路径**: `POST /authBaseInfo/deleteBaseInfo`

**功能描述**: 删除基础授权（仅限待提交状态）

**请求参数**: AuthBaseInfoDeleteCommand
```json
{
  "authBaseId": "string"                    // 基础授权ID（必填）
}
```

**响应结果**: CommonResponse<Void>

#### 1.1.3 失效基础授权

**接口路径**: `POST /authBaseInfo/cancelBaseInfo`

**功能描述**: 将基础授权状态设置为已失效

**请求参数**: AuthBaseInfoCancelCommand
```json
{
  "authBaseId": "string"                    // 基础授权ID（必填）
}
```

**响应结果**: CommonResponse<Void>

#### 1.1.4 暂停基础授权

**接口路径**: `POST /authBaseInfo/stopBaseInfo`

**功能描述**: 暂停已生效的基础授权

**请求参数**: AuthBaseInfoStopCommand
```json
{
  "authBaseId": "string",                   // 基础授权ID（必填）
  "pauseDeadlineDate": "2024-06-30"        // 暂停止日（必填，格式：yyyy-MM-dd）
}
```

**响应结果**: CommonResponse<Void>

#### 1.1.5 重启基础授权

**接口路径**: `POST /authBaseInfo/restartBaseInfo`

**功能描述**: 重启已暂停的基础授权

**请求参数**: AuthBaseInfoRestartCommand
```json
{
  "authBaseId": "string"                    // 基础授权ID（必填）
}
```

**响应结果**: CommonResponse<Void>

#### 1.1.6 保存转授权

**接口路径**: `POST /authBaseInfo/addTransferBaseInfo`

**功能描述**: 基于现有基础授权创建转授权

**请求参数**: AuthTransferBaseInfoAddCommand
```json
{
  "authBaseId": "string",                   // 原基础授权ID（必填）
  "bizLine": "string",                      // 业务条线（必填）
  "effectBeginDate": "2024-01-01",          // 生效起日（必填，格式：yyyy-MM-dd）
  "effectEndDate": "2024-12-31",            // 生效止日（必填，格式：yyyy-MM-dd）
  "orgId": "string",                        // 机构ID（必填）
  "orgName": "string",                      // 机构名称（必填）
  "orgAuthFlag": "string",                  // 机构授权标识（必填，001-机构 002-角色）
  "roleList": [                             // 角色列表（当orgAuthFlag=002时必传）
    {
      "roleId": "string",                   // 角色ID
      "roleName": "string"                  // 角色名称
    }
  ],
  "dimInfoList": [                          // 维度信息列表
    {
      "authDimType": "string",              // 授权维度类型
      "authDimValue": "string",             // 授权维度值
      "firstDimType": "string"              // 一级维度类型
    }
  ]
}
```

**响应结果**: CommonResponse<String>
```json
{
  "rpcResult": "SUCCESS",
  "data": "TRANSFER_AUTH_ID_12345"          // 返回转授权ID
}
```

#### 1.1.7 导出基础授权信息

**接口路径**: `POST /authBaseInfo/export`

**功能描述**: 导出基础授权信息到Excel文件

**请求参数**: AuthBatchInfoExportCommand
```json
{
  "orgList": ["string"],                    // 机构ID列表
  "effectBeginDate": "2024-01-01"           // 生效日期（格式：yyyy-MM-dd）
}
```

**响应结果**: CommonResponse<String>
```json
{
  "rpcResult": "SUCCESS",
  "data": "file_download_url"               // 返回文件下载地址
}
```

### 1.2 基础授权查询接口 (AuthBaseInfoQueryController)

**接口前缀**: `/authBaseInfoQuery`

#### 1.2.1 分页查询基础授权列表

**接口路径**: `POST /authBaseInfoQuery/queryPage`

**功能描述**: 分页查询基础授权列表

**请求参数**: AuthBaseInfoPageQuery
```json
{
  "pageNum": 1,                             // 页码（必填）
  "pageSize": 10,                           // 每页大小（必填）
  "authCatalogRoleId": "string",            // 授权类别角色关联ID（配置页面角色点击时必传）
  "authStatus": "string",                   // 授权状态（参考EnumAuthBaseStatus）
  "pauseDeadlineDate": "2024-06-30",        // 暂停止日（格式：yyyy-MM-dd）
  "transferAuthFlag": "string",             // 转授权标识（转授权：1）
  "bizLine": "string"                       // 业务条线
}
```

**响应结果**: CommonResponse<PageInfo<AuthBaseInfoPagerDto>>
```json
{
  "rpcResult": "SUCCESS",
  "data": {
    "pageNum": 1,
    "pageSize": 10,
    "total": 100,
    "pages": 10,
    "list": [
      {
        "authBaseId": "string",             // 基础授权ID
        "authCategory": "string",           // 授权类别
        "authCatalogRoleId": "string",      // 授权类别角色关联ID
        "flowInstanceId": "string",         // 流程实例ID
        "updateOperatorId": "string",       // 修改人ID
        "updateOrganId": "string",          // 修改机构ID
        "authStatus": "string",             // 授权状态
        "orgAuthFlag": "string",            // 机构授权标识
        "roleName": "string",               // 角色名称
        "roleId": "string",                 // 角色ID
        "orgName": "string",                // 机构名称
        "orgId": "string",                  // 机构ID
        "bizLine": "string",                // 业务条线
        "effectBeginDate": "2024-01-01",    // 生效起日
        "effectEndDate": "2024-12-31",      // 生效止日
        "pauseDeadlineDate": "2024-06-30",  // 暂停止日
        "operatorName": "string",           // 操作人
        "operatorId": "string",             // 操作人ID
        "ownOrganName": "string",           // 机构名称
        "ownOrganId": "string",             // 机构ID
        "transferAuthFlag": "string",       // 转授权标识
        "authType": "string"                // 授权类型
      }
    ]
  }
}
```

#### 1.2.2 查询基础授权详情

**接口路径**: `POST /authBaseInfoQuery/queryDetail`

**功能描述**: 根据基础授权ID查询详细信息

**请求参数**: AuthBaseInfoDetailQuery
```json
{
  "authBaseId": "string"                    // 基础授权ID（必填）
}
```

**响应结果**: CommonResponse<AuthBaseInfoDto>
```json
{
  "rpcResult": "SUCCESS",
  "data": {
    "authBaseId": "string",                 // 基础授权ID
    "authCategory": "string",               // 授权类别
    "authCatalogRoleId": "string",          // 授权类别角色关联ID
    "flowInstanceId": "string",             // 流程实例ID
    "updateOperatorId": "string",           // 修改人ID
    "updateOrganId": "string",              // 修改机构ID
    "authStatus": "string",                 // 授权状态
    "orgAuthFlag": "string",                // 机构授权标识
    "roleName": "string",                   // 角色名称
    "roleId": "string",                     // 角色ID
    "orgName": "string",                    // 机构名称
    "orgId": "string",                      // 机构ID
    "bizLine": "string",                    // 业务条线
    "effectBeginDate": "2024-01-01",        // 生效起日
    "effectEndDate": "2024-12-31",          // 生效止日
    "pauseDeadlineDate": "2024-06-30",      // 暂停止日
    "operatorName": "string",               // 操作人
    "operatorId": "string",                 // 操作人ID
    "ownOrganName": "string",               // 机构名称
    "ownOrganId": "string",                 // 机构ID
    "transferAuthFlag": "string",           // 转授权标识
    "authType": "string",                   // 授权类型
    "dimInfoList": [                        // 维度信息列表
      {
        "authDimType": "string",            // 授权维度类型
        "authDimValue": "string",           // 授权维度值
        "firstDimType": "string"            // 一级维度类型
      }
    ]
  }
}
```

#### 1.2.3 基础授权维度动态加载查询

**接口路径**: `POST /authBaseInfoQuery/queryDimensionLoadByAuthCode`

**功能描述**: 根据授权维度码动态加载授权参数配置信息

**请求参数**: AuthDimensionLoadQuery
```json
{
  "authCode": "string"                      // 授权维度码（必填）
}
```

**响应结果**: CommonResponse<AuthDimensionLoadDto>
```json
{
  "rpcResult": "SUCCESS",
  "data": {
    "resultTypeFlag": true,                 // 结果类型标识
    "showExtResponses": [                   // 授权参数展示扩展表
      {
        "serialNo": "string",               // 流水号
        "authCode": "string",               // 授权维度编号
        "paramCode": "string",              // 参数码CODE
        "dataShowType": "string",           // 数据显示类型
        "defaultParamValue": "string",      // 默认参数值
        "valueDataType": "string",          // 值数据类型
        "chooseShowType": "string",         // 选择展示类型
        "event": "string",                  // 事件标记
        "visibleFlag": "string",            // 是否可见
        "sortNo": 1,                        // 排序号
        "paramList": [                      // 下拉枚举值
          {
            "key": "string",                // 键
            "value": "string"               // 值
          }
        ]
      }
    ]
  }
}
```

### 1.3 基础授权审批接口 (AuthBaseInfoApproveController)

**接口前缀**: `/authBaseInfoApprove`

#### 1.3.1 发起基础授权工作流

**接口路径**: `POST /authBaseInfoApprove/startFlow`

**功能描述**: 发起基础授权审批工作流

**请求参数**: AuthBaseInfoStartFlowCommand
```json
{
  "authBaseId": "string"                    // 基础授权ID（必填）
}
```

**响应结果**: CommonResponse<Void>

#### 1.3.2 基础授权工作流退回

**接口路径**: `POST /authBaseInfoApprove/returnFlow`

**功能描述**: 将基础授权工作流退回到指定节点

**请求参数**: AuthBaseInfoFlowReturnCommand
```json
{
  "returnType": "string",                   // 退回类型（必填）
  "taskId": "string",                       // 任务ID
  "targetActivityId": "string",             // 目标节点ID
  "opinion": "string",                      // 审批意见（020提交,021有条件提交,040同意,041有条件同意,042续议,043否决）
  "postType": "string",                     // 岗位类型
  "approveNodeId": "string",                // 审批岗位ID
  "approveNodeName": "string",              // 审批岗位名称
  "opinionDesc": "string"                   // 意见说明
}
```

**响应结果**: CommonResponse<Void>

#### 1.3.3 基础授权工作流取消

**接口路径**: `POST /authBaseInfoApprove/cancelFlow`

**功能描述**: 取消基础授权工作流

**请求参数**: AuthBaseInfoFlowCancelCommand
```json
{
  "authBaseId": "string",                   // 基础授权ID（必填）
  "opinion": "string",                      // 审批意见
  "postType": "string",                     // 岗位类型
  "approveNodeId": "string",                // 审批岗位ID
  "approveNodeName": "string",              // 审批岗位名称
  "opinionDesc": "string"                   // 意见说明
}
```

**响应结果**: CommonResponse<Void>

#### 1.3.4 基础授权工作流提交审批

**接口路径**: `POST /authBaseInfoApprove/submitApprove`

**功能描述**: 提交基础授权审批，推送工单至下一节点

**请求参数**: AuthBaseInfoSubmitCommand
```json
{
  "authBaseId": "string",                   // 基础授权ID（必填）
  "taskId": "string",                       // 审批任务ID（必填）
  "endFlag": "string",                      // 是否最后一岗（Y-是 N-否）
  "opinion": "string",                      // 审批意见（020提交,021有条件提交,040同意,041有条件同意,042续议,043否决）
  "postType": "string",                     // 岗位类型
  "approveNodeId": "string",                // 审批岗位ID
  "approveNodeName": "string",              // 审批岗位名称
  "opinionDesc": "string"                   // 意见说明
}
```

**响应结果**: CommonResponse<Void>

### 1.4 批量授权查询接口 (AuthBaseInfoBatchQueryController)

**接口前缀**: `/authBaseInfoBatchQueryController`

#### 1.4.1 查询批量授权列表

**接口路径**: `POST /authBaseInfoBatchQueryController/queryInfoPage`

**功能描述**: 分页查询批量授权导入信息列表

**请求参数**: AuthBaseInfoBatchQuery
```json
{
  "pageNum": 1,                             // 页码（必填）
  "pageSize": 10,                           // 每页大小（必填）
  "authStatus": "string"                    // 授权状态（参考EnumAuthBaseStatus）
}
```

**响应结果**: CommonResponse<PageInfo<AuthFileImportInfoBatchDto>>
```json
{
  "rpcResult": "SUCCESS",
  "data": {
    "pageNum": 1,
    "pageSize": 10,
    "total": 50,
    "pages": 5,
    "list": [
      {
        "batchAuthBaseId": "string",        // 批量授权编号
        "tenantId": "string",               // 租户号
        "authStatus": "string",             // 授权状态
        "registerTime": "2024-01-01T10:00:00", // 登记时间
        "registerId": "string",             // 登记人ID
        "registerName": "string",           // 登记人名称
        "registerOrgId": "string",          // 登记机构ID
        "registerOrgName": "string",        // 登记机构名称
        "fileName": "string",               // 文件名
        "successNum": 10,                   // 成功笔数
        "failNum": 2                        // 失败笔数
      }
    ]
  }
}
```

#### 1.4.2 查询批量授权导入详情列表

**接口路径**: `POST /authBaseInfoBatchQueryController/queryDetailPage`

**功能描述**: 分页查询批量授权导入详情列表

**请求参数**: AuthBaseDetailBatchQuery
```json
{
  "pageNum": 1,                             // 页码（必填）
  "pageSize": 10,                           // 每页大小（必填）
  "batchAuthBaseId": "string"               // 批量授权编号（必填）
}
```

**响应结果**: CommonResponse<PageInfo<AuthFileImportDetailBatchDto>>
```json
{
  "rpcResult": "SUCCESS",
  "data": {
    "pageNum": 1,
    "pageSize": 10,
    "total": 12,
    "pages": 2,
    "list": [
      {
        "batchAuthBaseId": "string",        // 批量授权编号
        "tenantId": "string",               // 租户号
        "serialNo": "string",               // 序号
        "authCategory": "string",           // 授权类别
        "orgName": "string",                // 机构名称
        "roleName": "string",               // 角色名称
        "authType": "string",               // 授权类型
        "effectBeginDate": "2024-01-01",    // 生效起日
        "effectEndDate": "2024-12-31",      // 生效止日
        "importStatus": "string",           // 导入状态
        "failReason": "string"              // 失败原因
      }
    ]
  }
}
```

#### 1.4.3 授权批量文件上传

**接口路径**: `POST /authBaseInfoBatchQueryController/importFileUpload`

**功能描述**: 上传批量授权Excel文件

**请求参数**: MultipartFile
- 文件类型：Excel文件(.xlsx, .xls)
- 文件大小：不超过10MB

**响应结果**: CommonResponse<String>
```json
{
  "rpcResult": "SUCCESS",
  "data": "BATCH_AUTH_ID_12345"             // 返回批量授权ID
}
```

#### 1.4.4 授权批量文件解析

**接口路径**: `POST /authBaseInfoBatchQueryController/parseExcelFile`

**功能描述**: 解析已上传的批量授权Excel文件

**请求参数**: AuthBatchExcelParseCommand
```json
{
  "batchAuthBaseId": "string"               // 批量授权ID（必填）
}
```

**响应结果**: CommonResponse<AuthFileImportInfoBatchDto>
```json
{
  "rpcResult": "SUCCESS",
  "data": {
    "batchAuthBaseId": "string",            // 批量授权编号
    "tenantId": "string",                   // 租户号
    "authStatus": "string",                 // 授权状态
    "registerTime": "2024-01-01T10:00:00",  // 登记时间
    "registerId": "string",                 // 登记人ID
    "registerName": "string",               // 登记人名称
    "registerOrgId": "string",              // 登记机构ID
    "registerOrgName": "string",            // 登记机构名称
    "fileName": "string",                   // 文件名
    "successNum": 10,                       // 成功笔数
    "failNum": 2                            // 失败笔数
  }
}
```

#### 1.4.5 下载失败原因

**接口路径**: `POST /authBaseInfoBatchQueryController/exportExcelFile`

**功能描述**: 导出批量授权失败详情Excel文件

**请求参数**: AuthBatchDetailExportCommand
```json
{
  "batchAuthBaseId": "string"               // 批量授权ID（必填）
}
```

**响应结果**: CommonResponse<String>
```json
{
  "rpcResult": "SUCCESS",
  "data": "file_download_url"               // 返回文件下载地址
}
```

---

## 2. 授权分类管理模块

### 2.1 授权分类操作接口 (AuthCatalogController)

**接口前缀**: `/authCatalog`

#### 2.1.1 保存授权分类

**接口路径**: `POST /authCatalog/saveAuthCatalog`

**功能描述**: 新增或更新授权分类配置

**请求参数**: AuthCatalogSaveCommand
```json
{
  "authCatalogId": "string",                // 授权类型ID（更新时必传）
  "orgId": "string",                        // 机构ID（必填）
  "orgName": "string",                      // 机构名称（必填）
  "authCategory": "string",                 // 授权类别（必填，参考EnumAuthCategory）
  "orgAuthFlag": "string",                  // 授权方式（必填，001-机构 002-角色）
  "roleList": [                             // 角色列表
    {
      "roleId": "string",                   // 角色ID
      "roleName": "string"                  // 角色名称
    }
  ]
}
```

**响应结果**: CommonResponse<String>
```json
{
  "rpcResult": "SUCCESS",
  "data": "AUTH_CATALOG_ID_12345"           // 返回授权类型ID
}
```

#### 2.1.2 删除授权分类

**接口路径**: `POST /authCatalog/deleteAuthCatalog`

**功能描述**: 删除授权分类配置

**请求参数**: AuthCatalogDeleteCommand
```json
{
  "authCatalogId": "string"                 // 授权类型ID（必填）
}
```

**响应结果**: CommonResponse<Void>

### 2.2 授权分类查询接口 (AuthCatalogQueryController)

**接口前缀**: `/authCatalogQuery`

#### 2.2.1 查询机构授权树

**接口路径**: `POST /authCatalogQuery/queryAuthCatalogOrgTree`

**功能描述**: 查询总行、总行部门及全行的一级机构授权树结构

**请求参数**: AuthCatalogOrgTreeQuery
```json
{
  "queryAuthFlag": "string"                 // 是否查询机构类别数据标识（Y-是 N-否）
}
```

**响应结果**: CommonResponse<AuthCatalogOrgTreeDto>
```json
{
  "rpcResult": "SUCCESS",
  "data": {
    "orgId": "string",                      // 机构编号
    "orgName": "string",                    // 机构名称
    "orgLvl": "string",                     // 机构级别
    "relativeorgId": "string",              // 对应的宁波地区一级支行或分行的ID
    "categoryList": [                       // 授权配置列表
      {
        "authCatalogId": "string",          // 授权类型ID
        "orgId": "string",                  // 机构ID
        "orgName": "string",                // 机构名称
        "authCategory": "string",           // 授权类别
        "orgAuthFlag": "string"             // 授权方式
      }
    ],
    "children": [                           // 子机构列表（递归结构）
      {
        "orgId": "string",
        "orgName": "string",
        "orgLvl": "string",
        "relativeorgId": "string",
        "categoryList": [],
        "children": []
      }
    ]
  }
}
```

### 2.3 授权分类角色操作接口 (AuthCatalogRoleController)

**接口前缀**: `/authCatalogRole`

#### 2.3.1 保存角色列表

**接口路径**: `POST /authCatalogRole/saveRole`

**功能描述**: 为授权分类添加角色配置

**请求参数**: AuthCatalogRoleAddCommand
```json
{
  "authCatalogId": "string",                // 授权类型ID（必填）
  "roleList": [                             // 角色列表（必填）
    {
      "roleId": "string",                   // 角色ID
      "roleName": "string"                  // 角色名称
    }
  ]
}
```

**响应结果**: CommonResponse<Void>

#### 2.3.2 删除角色列表

**接口路径**: `POST /authCatalogRole/deleteRole`

**功能描述**: 删除授权分类角色配置

**请求参数**: AuthCatalogRoleDelCommand
```json
{
  "authCatalogRoleId": "string"             // 授权类别角色关联ID（必填）
}
```

**响应结果**: CommonResponse<Void>

### 2.4 授权分类角色查询接口 (AuthCatalogRoleQueryController)

**接口前缀**: `/authCatalogRoleQuery`

#### 2.4.1 查询机构授权类别对应的角色列表

**接口路径**: `POST /authCatalogRoleQuery/queryAuthRoleList`

**功能描述**: 根据授权类型和机构授权标识查询角色列表

**请求参数**: AuthRoleListQuery
```json
{
  "authCatalogId": "string",                // 授权类型ID
  "orgAuthFlag": "string"                   // 机构授权标识（001-机构 002-角色）
}
```

**响应结果**: CommonResponse<List<AuthCatalogRoleDto>>
```json
{
  "rpcResult": "SUCCESS",
  "data": [
    {
      "authCatalogRoleId": "string",        // 授权类别角色关联ID
      "authCatalogId": "string",            // 授权类型ID
      "roleId": "string",                   // 角色ID
      "roleName": "string",                 // 角色名称
      "orgAuthFlag": "string",              // 机构授权标识
      "ownOrganId": "string",               // 机构ID
      "operatorId": "string",               // 操作人ID
      "operatorName": "string"              // 操作人
    }
  ]
}
```

#### 2.4.2 查询角色信息

**接口路径**: `POST /authCatalogRoleQuery/queryRole`

**功能描述**: 根据机构ID和机构级别查询角色信息

**请求参数**: AuthQueryRoleQuery
```json
{
  "orgId": "string",                        // 机构编号（必填）
  "orgLvlList": ["string"]                  // 机构级别列表（必填）
}
```

**响应结果**: CommonResponse<List<AuthRoleQueryDto>>
```json
{
  "rpcResult": "SUCCESS",
  "data": [
    {
      "pkId": "string",                     // 主键ID
      "roleNo": "string",                   // 角色编号
      "sortNo": "string",                   // 排序号
      "roleName": "string",                 // 角色名称
      "roleDesc": "string",                 // 角色描述
      "roleSts": "string",                  // 角色状态（1-正常 0-停用）
      "roleLayr": "string",                 // 角色分层（00201-总行 00202-总分支行 00203-一级机构层级 00204-二级机构层级）
      "canNfLineSets": "string",            // 可配置条线集合
      "isRelaStanQualLice": "string",       // 是否关联岗位资格证书（1-是 0-否）
      "roleEndRemind": "string",            // 角色到期前N天提醒
      "isAllwOusrPersonUse": "string"       // 是否允许外包人员使用（1-是 0-否）
    }
  ]
}
```

---

## 3. 授权规则执行模块

### 3.1 授权规则执行接口 (AuthRuleController)

**接口前缀**: `/authRule`

#### 3.1.1 执行授权规则

**接口路径**: `POST /authRule/execute`

**功能描述**: 执行授权规则验证，检查当前用户是否具有指定授权

**请求参数**: AuthRuleExecuteCommand
```json
{
  "authCateGory": "string",                 // 授权类别（必填，参考EnumAuthCategory）
  "params": {                               // 业务参数（必填）
    "key1": "value1",                       // 业务参数键值对
    "key2": "value2",
    "resultType": "decimal_value"           // 结果类型（用于阈值比较）
  }
}
```

**响应结果**: CommonResponse<AuthExecuteResultDto>
```json
{
  "rpcResult": "SUCCESS",
  "data": {
    "finalResult": true,                    // 最终执行结果（true-通过 false-不通过）
    "authExecuteDetailDtoS": [              // 每个基础授权规则详情
      {
        "result": true,                     // 基础授权执行结果
        "authName": "string",               // 基础授权名称
        "detail": [                         // 规则执行详细信息
          "规则执行详情描述1",
          "规则执行详情描述2"
        ]
      }
    ]
  }
}
```

**业务参数说明**:
- `guarantArryDHRiskWindow`: 担保组单户敞口（数值类型，用于<=比较）
- `guarMethodCollect`: 担保方式（字符串类型，用于contains比较）
- `productCollect`: 产品组（字符串类型，用于contains比较）
- `underLimitFlag`: 是否额度项下（布尔类型）
- `businessRisk`: 是否低风险业务（布尔类型）
- `dbRiskWindow`: 单笔敞口（数值类型，用于<=比较）
- `dhRiskWindow`: 单户敞口（数值类型，用于<=比较）
- `resultType`: 结果类型（数值类型，用于阈值比较）

**使用示例**:
```json
{
  "authCateGory": "creditResultType",
  "params": {
    "resultType": 1000000,
    "guarMethodCollect": "抵押",
    "productCollect": "个人消费贷款",
    "underLimitFlag": true,
    "businessRisk": false,
    "dbRiskWindow": 500000,
    "dhRiskWindow": 2000000
  }
}
```

---

## 4. 错误码说明

### 4.1 通用错误码
- `0000` - 成功
- `0001` - 请求头为空
- `0002` - 客户请求头为空
- `0003` - 鉴权失败
- `0004` - 参数转换异常
- `0009` - 参数校验失败

### 4.2 业务错误码
- `AUTH_BASE_INFO_STATUS_FAIL` - 基础授权状态不符合操作要求
- `AUTH_CATALOG_NOT_FOUND` - 授权分类不存在
- `AUTH_ROLE_NOT_FOUND` - 授权角色不存在
- `AUTH_RULE_EXECUTE_FAIL` - 授权规则执行失败

---

## 5. 接口调用注意事项

### 5.1 请求头要求
所有接口调用都需要在HTTP请求头中包含以下信息：
- `Content-Type`: application/json
- `RestConstant.RPC_HSJRY_REQUEST`: 基础请求信息（加密）
- `RestConstant.RPC_HSJRY_USER_REQUEST`: 用户请求信息（加密）

### 5.2 分页参数
所有分页查询接口都继承自PageParam，包含以下基础参数：
- `pageNum`: 页码（从1开始）
- `pageSize`: 每页大小（建议10-100之间）

### 5.3 日期格式
所有日期字段统一使用以下格式：
- 日期：`yyyy-MM-dd`（如：2024-01-01）
- 日期时间：`yyyy-MM-ddTHH:mm:ss`（如：2024-01-01T10:00:00）

### 5.4 状态流转规则
基础授权状态流转规则：
1. 新建 → 待提交(010)
2. 待提交(010) → 复核中(020) [提交审批]
3. 复核中(020) → 已生效(040) [审批通过]
4. 复核中(020) → 复核退回(050) [审批退回]
5. 复核退回(050) → 复核中(020) [重新提交]
6. 已生效(040) → 已失效(030) [失效操作]
7. 已生效(040) → 已暂停(060) [暂停操作]
8. 已暂停(060) → 已生效(040) [重启操作]

### 5.5 权限控制
- 基础授权的增删改操作需要相应的业务权限
- 审批操作需要审批权限
- 查询操作根据数据权限过滤结果

---

## 6. 附录

### 6.1 完整接口列表

| 模块 | Controller | 接口路径 | 方法 | 功能描述 |
|------|------------|----------|------|----------|
| 基础授权管理 | AuthBaseInfoController | /authBaseInfo/saveBaseInfo | POST | 保存基础授权 |
| 基础授权管理 | AuthBaseInfoController | /authBaseInfo/deleteBaseInfo | POST | 删除基础授权 |
| 基础授权管理 | AuthBaseInfoController | /authBaseInfo/cancelBaseInfo | POST | 失效基础授权 |
| 基础授权管理 | AuthBaseInfoController | /authBaseInfo/stopBaseInfo | POST | 暂停基础授权 |
| 基础授权管理 | AuthBaseInfoController | /authBaseInfo/restartBaseInfo | POST | 重启基础授权 |
| 基础授权管理 | AuthBaseInfoController | /authBaseInfo/addTransferBaseInfo | POST | 保存转授权 |
| 基础授权管理 | AuthBaseInfoController | /authBaseInfo/export | POST | 导出基础授权信息 |
| 基础授权查询 | AuthBaseInfoQueryController | /authBaseInfoQuery/queryPage | POST | 分页查询基础授权列表 |
| 基础授权查询 | AuthBaseInfoQueryController | /authBaseInfoQuery/queryDetail | POST | 查询基础授权详情 |
| 基础授权查询 | AuthBaseInfoQueryController | /authBaseInfoQuery/queryDimensionLoadByAuthCode | POST | 基础授权维度动态加载查询 |
| 基础授权审批 | AuthBaseInfoApproveController | /authBaseInfoApprove/startFlow | POST | 发起基础授权工作流 |
| 基础授权审批 | AuthBaseInfoApproveController | /authBaseInfoApprove/returnFlow | POST | 基础授权工作流退回 |
| 基础授权审批 | AuthBaseInfoApproveController | /authBaseInfoApprove/cancelFlow | POST | 基础授权工作流取消 |
| 基础授权审批 | AuthBaseInfoApproveController | /authBaseInfoApprove/submitApprove | POST | 基础授权工作流提交审批 |
| 批量授权查询 | AuthBaseInfoBatchQueryController | /authBaseInfoBatchQueryController/queryInfoPage | POST | 查询批量授权列表 |
| 批量授权查询 | AuthBaseInfoBatchQueryController | /authBaseInfoBatchQueryController/queryDetailPage | POST | 查询批量授权导入详情列表 |
| 批量授权查询 | AuthBaseInfoBatchQueryController | /authBaseInfoBatchQueryController/importFileUpload | POST | 授权批量文件上传 |
| 批量授权查询 | AuthBaseInfoBatchQueryController | /authBaseInfoBatchQueryController/parseExcelFile | POST | 授权批量文件解析 |
| 批量授权查询 | AuthBaseInfoBatchQueryController | /authBaseInfoBatchQueryController/exportExcelFile | POST | 下载失败原因 |
| 授权分类管理 | AuthCatalogController | /authCatalog/saveAuthCatalog | POST | 保存授权分类 |
| 授权分类管理 | AuthCatalogController | /authCatalog/deleteAuthCatalog | POST | 删除授权分类 |
| 授权分类查询 | AuthCatalogQueryController | /authCatalogQuery/queryAuthCatalogOrgTree | POST | 查询机构授权树 |
| 授权分类角色管理 | AuthCatalogRoleController | /authCatalogRole/saveRole | POST | 保存角色列表 |
| 授权分类角色管理 | AuthCatalogRoleController | /authCatalogRole/deleteRole | POST | 删除角色列表 |
| 授权分类角色查询 | AuthCatalogRoleQueryController | /authCatalogRoleQuery/queryAuthRoleList | POST | 查询机构授权类别对应的角色列表 |
| 授权分类角色查询 | AuthCatalogRoleQueryController | /authCatalogRoleQuery/queryRole | POST | 查询角色信息 |
| 授权规则执行 | AuthRuleController | /authRule/execute | POST | 执行授权规则 |

### 6.2 版本信息
- 文档版本：v1.0
- 最后更新：2025年6月
- 适用系统版本：loanV4.0
