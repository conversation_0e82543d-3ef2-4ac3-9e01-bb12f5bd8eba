*, *:after, *:before {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}
html, body {
    height: 100%;
    width: 100%;
    margin: 0;
    overflow: hidden;
}
.login-content {
    height: 100vh;
}
.login-header {
    height: 100vh;
    position: relative;
    width: 100%;
    background-image:linear-gradient(to top, #30cfd0 0%, #330867 100%);
    overflow: hidden;
    background-size: cover;
    background-position: center center;
    z-index: 1;
}
#canvasId {
    height: 100%;
}
.login_box {
    width: 400px;
    height: 500px;
    padding: 35px;
    color: #EEE;
    position: absolute;
    left: 49%;
    top: 45%;
    margin-left: -200px;
    margin-top: -250px;
}
.login_box h3 {
    text-align: center;
    height: 20px;
    font: 30px "microsoft yahei",Helvetica,Tahoma,Arial,"Microsoft jhengHei",sans-serif;
    color: #FFFFFF;
    height: 20px;
    line-height: 20px;
    padding: 0 0 35px 0;
}
.input-outer {
    height: 46px;
    padding: 0 5px;
    margin-bottom: 30px;
    border-radius: 5px;
    position: relative;
    border: 1px solid #fff;
}
.u-user {
    width: 25px;
    height: 25px;
    background: url(./img/login_ico.png);
    background-position: -125px 0;
    position: absolute;
    margin: 10px 13px;
}
.u-tenant {
  width: 50px;
  height: 25px;
  font-size: 13px;
  position: absolute;
  margin: 13px 10px;
}
.text {
    width: 220px;
    height: 46px;
    outline: none;
    display: inline-block;
    font: 14px "microsoft yahei",Helvetica,Tahoma,Arial,"Microsoft jhengHei";
    margin-left: 50px;
    border: none;
    background: none;
}
.u-password {
    width: 25px;
    height: 25px;
    background-image: url(./img/login_ico.png);
    background-position: -125px -34px;
    position: absolute;
    margin: 10px 13px;
}
.mb2 {
    margin-bottom: 20px;
}
.mb2 a {
    text-decoration: none;
    outline: none;
    color: #fff;
}
.act-but {
    line-height: 20px;
    text-align: center;
    font-size: 20px;
    border-radius: 5px;
    background: rgb(33, 204, 208);
}
.submit {
    padding: 15px;
    margin-top: 20px;
    display: block;
}
.input-outer input {
    color: #FFFFFF;
}
.act-but:hover {
    background: #2ba1b8;
}
.input-outer input::-webkit-input-placeholder {
    color: #aab2bd;
}
.input-outer input::-moz-placeholder { /* Mozilla Firefox 4 to 18 */
    color:    #aab2bd;
 }
 .input-outer input:-moz-placeholder { /* Mozilla Firefox 19+ */
    color:    #aab2bd;
 }
 .input-outer input:-ms-input-placeholder { /* Internet Explorer 10-11 */
    color:    #aab2bd;
 }
 .input-err {
    font-size: 15px;
    color: #f5222d;
 }
 .has-err {
     border: #f5222d solid 1px!important;
 }

 input:-webkit-autofill,
 input:-webkit-autofill:hover,
 input:-webkit-autofill:focus,
 input:-webkit-autofill:active {
     -webkit-transition-delay: 99999s;
     -webkit-transition: color 99999s ease-out, background-color 99999s ease-out;
 }
 .login-err {
    color: #ff4646;
    text-align: center;
    font-size: 15px;
 }
 .pic-code {
    display: flex;
    padding-right: 0;
    display: none;
 }
 .pic-code > img {
    width: auto;
    height: 100%;
    vertical-align: top;
 }
 .pic-code > input {
    flex: 1;
    vertical-align: top;
 }
