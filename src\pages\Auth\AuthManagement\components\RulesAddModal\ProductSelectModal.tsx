import React, { useEffect, useState } from 'react';
import { YRButton, YRModal, YRTable, YRMessage } from 'yrantd';
import { queryProductList } from '@/services/comm';

// 产品数据接口
interface ProductInfo {
  productId: string;
  productName: string;
  bizLine: string;
  [key: string]: any;
}

interface ProductSelectModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (selectedProducts: ProductInfo[]) => void;
  selectedProductIds: string[];
}

const ProductSelectModal: React.FC<ProductSelectModalProps> = ({
  visible,
  onCancel,
  onOk,
  selectedProductIds = []
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [productList, setProductList] = useState<ProductInfo[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>(selectedProductIds);

  // 添加日志以验证组件挂载
  useEffect(() => {
    console.log('ProductSelectModal组件挂载');
    return () => {
      console.log('ProductSelectModal组件卸载');
    };
  }, []);

  // 添加日志以验证visible属性变化
  useEffect(() => {
    console.log('ProductSelectModal visible变化:', visible);
    console.log('selectedProductIds:', selectedProductIds);
  }, [visible, selectedProductIds]);

  // 加载产品列表
  const loadProductList = async () => {
    console.log('开始加载产品列表');
    setLoading(true);
    try {
      const params = {
        pageNum: 1,
        pageSize: 100,
        productStatus: '020',
        logPermitId: 'kylin-operation-I9',
        logOperateName: '查询产品列表'
      };
      
      const res = await queryProductList(params);
      console.log('产品列表API响应:', res);
      
      if (res?.rpcResult === 'SUCCESS' && res?.data?.list) {
        setProductList(res.data.list);
        console.log('成功获取产品列表数量:', res.data.list.length);
      } else {
        console.error('获取产品列表失败:', res?.errorMessage);
        YRMessage.error(res?.errorMessage || '获取产品列表失败');
      }
    } catch (error) {
      console.error('获取产品列表异常:', error);
      YRMessage.error('获取产品列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 当弹窗打开时加载产品列表
  useEffect(() => {
    if (visible) {
      loadProductList();
      // 初始化已选中的产品ID
      setSelectedRowKeys(selectedProductIds);
    }
  }, [visible, selectedProductIds]);

  // 表格列配置
  const columns = [
    {
      title: '产品ID',
      dataIndex: 'productId',
      key: 'productId',
      width: 150
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName'
    },
    {
      title: '业务线',
      dataIndex: 'bizLine',
      key: 'bizLine',
      width: 150
    }
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      console.log('选择变化:', newSelectedRowKeys);
      setSelectedRowKeys(newSelectedRowKeys as string[]);
    }
  };

  // 确认选择
  const handleOk = () => {
    console.log('确认选择产品, selectedRowKeys:', selectedRowKeys);
    const selectedProducts = productList.filter((product) =>
      selectedRowKeys.includes(product.productId)
    );
    console.log('选中的产品:', selectedProducts);
    onOk(selectedProducts);
  };

  return (
    <YRModal
      title="选择产品"
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={[
        <YRButton key="cancel" onClick={onCancel}>
          取消
        </YRButton>,
        <YRButton key="ok" type="primary" onClick={handleOk}>
          确定
        </YRButton>
      ]}
    >
      <YRTable
        rowSelection={rowSelection}
        columns={columns}
        dataSource={productList}
        rowKey="productId"
        loading={loading}
        pagination={false}
        scroll={{ y: 400 }}
      />
    </YRModal>
  );
};

export default ProductSelectModal; 