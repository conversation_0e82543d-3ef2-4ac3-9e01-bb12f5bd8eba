/**
 * @Author: wangyw26123
 * @Description: 机构管理-新增账务机构映射弹窗
 * @Date: Created in 2022-12-15 16:58:45
 * @Modifed By:
 */
import React, { useEffect, useState } from 'react';
import {
  YRForm,
  YRInput,
  YRModal,
  YRButton,
  YRTreeSelect,
  YRMessage,
  YRClassificationLayout,
  YREditableTable
} from 'yrantd';
import { jsonParse, uuid } from '@yr/util';
import { EditableColumnType, ValueTypeEnum, ToolbarProps } from 'yrantd/lib/yr-editable-table/declare';
import { queryUserOrganTree } from '@/services/setting';
import { EnumMsg } from '@/constant/common';
import { M010102 } from '@permit/organ';
import { checkAuth } from '@/utils/utils';
import queryAcctOrganTreeJson from '../mock/queryAcctOrganTree.json';

interface DataType {
  acctOrganType?: string;
  acctOrganName?: string;
  acctOrganId?: string;
}

const { addOrgRelateList } = M010102.E01.interfaces;
const { updateOrgRelateList } = M010102.E02.interfaces;
const queryAcctOrganTree = () => Promise.resolve(queryAcctOrganTreeJson);

const defaultOrganItem = {
  id: uuid(),
  acctOrganType: undefined,
  acctOrganName: undefined,
  acctOrganId: undefined
};

/**
 * 根据机构Id获取机构项及父级机构
 * @param treeData 机构树
 * @param organId 机构Id
 * @param fn 回调函数，找到时触发
 */
const getOrganItemById = (treeData, organId, fn, fieldNames?: { key?: string; children?: string }) => {
  const userFieldNames = Object.assign(
    {
      key: 'organId',
      children: 'children'
    },
    fieldNames
  );

  const loop = (data, id, _parent, _callback) => {
    for (let i = 0; i < data.length; i++) {
      if (data[i][userFieldNames.key] === id) {
        return _callback(data[i], _parent);
      }

      if (data[i][userFieldNames.children]) {
        loop(data[i][userFieldNames.children], id, data[i], _callback);
      }
    }
  };

  loop(treeData, organId, null, (item, parentOrgan) => {
    fn(item, parentOrgan);
  });
};

const OrganMapModal = (props) => {
  const { visible, organInfo, onCancel: cancelProps, callback, mode } = props;
  const [formPrefix] = useState<string>('acctOrganMappings');
  const [treeData, setTreeData] = useState<any[]>([]);
  const [acctOrganTreeData, setAcctOrganTreeData] = useState<any[]>([]);
  const [title, setTitle] = useState<string>('');
  const [formDisabled, setFormDisabled] = useState<boolean>(false);
  const [btnLoading, setBtnLoading] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<DataType[]>([]);
  const [tableForceUpdate, setTableForceUpdate] = useState<number>(0);
  const [form] = YRForm.useForm();

  useEffect(() => {
    if (mode === 'edit') {
      setTitle('修改机构映射');
      setFormDisabled(true);
    } else {
      setTitle('新增机构映射');
      setFormDisabled(false);
    }
  }, [mode]);

  useEffect(() => {
    // 重置编辑表格数据
    if (visible) {
      if (mode === 'edit') {
        setDataSource([{ ...organInfo, acctOrganName: organInfo.acctOrganId, id: uuid() }]);
      } else {
        setDataSource([{ ...defaultOrganItem }]);
      }

      // 解决修改时表单不更新问题
      setTableForceUpdate(tableForceUpdate + 1);
      const timer = setTimeout(() => {
        form.resetFields();

        clearTimeout(timer);
      }, 300);
    }
  }, [visible, organInfo]);

  useEffect(() => {
    queryUserOrganTree().then((res: any) => {
      if (res.success && res.data) {
        const userInfo = jsonParse(window.sessionStorage.getItem('userInfo') || '') || {};
        const organTreeDto = res.data.organTreeDto ? [res.data.organTreeDto] : [];
        // 只能选择用户所在机构及下级机构
        getOrganItemById(organTreeDto, userInfo.organId, (item) => {
          setTreeData([item]);
        });
      }
    });
    queryAcctOrganTree().then((res: any) => {
      if (res.success && res.data) {
        const organTreeDto = res.data.organTreeDto ? [res.data.organTreeDto] : [];
        setAcctOrganTreeData(organTreeDto);
      }
    });
  }, []);

  const onSelectOrgan = (value) => {
    form.setFieldValue('organId', value);
    form.validateFields(['organId']);
  };

  const onSubmit = () => {
    form.validateFields().then((values) => {
      const params = { ...values };

      const acctOrganMappings = params.acctOrganMappings?.filter(Boolean);

      if (!Array.isArray(acctOrganMappings) || !acctOrganMappings.length) {
        YRMessage.warn('请至少新增一条账务机构信息！');
        return;
      }

      params.acctOrganMappings = acctOrganMappings.map((item) => {
        let acctOrganName = '';
        getOrganItemById(
          acctOrganTreeData,
          item.acctOrganId,
          (_item) => {
            acctOrganName = _item.acctOrganName;
          },
          { key: 'acctOrganId' }
        );
        return {
          acctOrganId: item.acctOrganId,
          acctOrganType: item.acctOrganType,
          acctOrganName
        };
      });

      setBtnLoading(true);
      if (mode === 'add') {
        addOrgRelateList(params).then((res) => {
          setBtnLoading(false);
          if (res.success) {
            YRMessage.success(EnumMsg.add);
            callback();
          }
        });
      } else {
        updateOrgRelateList({
          organId: params.organId,
          acctOrganId: params.acctOrganMappings?.[0].acctOrganId,
          acctOrganType: params.acctOrganMappings?.[0].acctOrganType,
          acctOrganName: params.acctOrganMappings?.[0].acctOrganName
        }).then((res) => {
          setBtnLoading(false);
          if (res.success) {
            YRMessage.success(EnumMsg.modify);
            callback();
          }
        });
      }
    });
  };

  const onCancel = () => cancelProps();

  const onOrganChange = (key, node, row) => {
    const getAcctOrganMappings = form.getFieldsValue().acctOrganMappings?.[row.rowIndex];
    form.setFieldValue([formPrefix, getAcctOrganMappings ? row.rowIndex : row.rowIndex + 1, 'acctOrganId'], key);
    form.validateFields([[formPrefix, getAcctOrganMappings ? row.rowIndex : row.rowIndex + 1, 'acctOrganId']]);
  };

  const defaultColumns: EditableColumnType[] = [
    {
      title: '账务机构类型',
      dataIndex: 'acctOrganType',
      width: 180,
      required: true,
      valueType: ValueTypeEnum.dictSelect,
      componentProps: {
        dictkey: 'EnumAcctOrganType',
        disabled: formDisabled,
        placeholder: '请选择账务机构类型'
      },
      formItemProps: {
        rules: [{ required: true, message: '请选择账务机构类型' }]
      }
    },
    {
      title: '账务机构名称',
      dataIndex: 'acctOrganName',
      width: 180,
      required: true,
      formItemProps: {
        rules: [{ required: true, message: '请选择账务机构名称' }]
      },
      renderFormItem: (_, row) => {
        return (
          <YRTreeSelect
            showSearch
            treeNodeFilterProp="acctOrganName"
            onSelect={(key, node) => onOrganChange(key, node, row)}
            treeData={acctOrganTreeData}
            fieldNames={{ label: 'acctOrganName', value: 'acctOrganId' }}
            placeholder="请选择账务机构名称"
          />
        );
      }
    },
    {
      title: '账务机构编号',
      dataIndex: 'acctOrganId',
      width: 160,
      required: true,
      formItemProps: {
        rules: [{ required: true, message: '请输入' }]
      },
      componentProps: {
        disabled: true,
        placeholder: '请输入账务机构编号'
      }
    }
  ];

  const create = () => ({ ...defaultOrganItem, id: uuid() });

  const toolbar: ToolbarProps = {
    buttonsProps: {
      ghost: true,
      size: 'small',
      add: {
        text: '新增',
        type: 'primary'
      }
    }
  };

  return (
    <YRModal
      size="middle"
      title={title}
      open={visible}
      onCancel={onCancel}
      footer={[
        <YRButton key="cancel" onClick={onCancel}>
          取消
        </YRButton>,
        (checkAuth(M010102.E01) || checkAuth(M010102.E02)) && (
          <YRButton key="confirm" type="primary" disabled={btnLoading} loading={btnLoading} onClick={onSubmit}>
            确定
          </YRButton>
        )
      ]}
    >
      <YRClassificationLayout.Space key={tableForceUpdate}>
        <YRClassificationLayout title="业务机构信息">
          <YRForm form={form}>
            <YRForm.Row column={1}>
              <YRForm.Item name="organName" label="业务机构名称" initialValue={organInfo.organId}>
                <YRTreeSelect
                  showSearch
                  treeNodeFilterProp="organName"
                  disabled={formDisabled}
                  treeData={treeData}
                  onSelect={onSelectOrgan}
                  fieldNames={{ label: 'organName', value: 'organId' }}
                  placeholder="请选择业务机构"
                />
              </YRForm.Item>
              <YRForm.Item
                name="organId"
                label="业务机构编号"
                initialValue={organInfo.organId}
                rules={[{ required: true, message: '请输入业务机构编号' }]}
              >
                <YRInput disabled />
              </YRForm.Item>
            </YRForm.Row>
          </YRForm>
        </YRClassificationLayout>
        <YRClassificationLayout title="账务机构信息">
          <YREditableTable
            form={form}
            rowKey="id"
            name={formPrefix}
            recordCreator={create}
            dataSource={dataSource}
            columns={defaultColumns}
            hiddenToolbar={formDisabled}
          />
        </YRClassificationLayout>
      </YRClassificationLayout.Space>
    </YRModal>
  );
};

export default OrganMapModal;
