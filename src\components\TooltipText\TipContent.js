/*
 * @Description: 文本块显示，带提示框
 *  属性说明：
 *  value 值
 *  title 文本
 *  actions 操作按钮
 *  children 提示内容
 */

import React, { useState } from 'react';
import { YRTooltip } from 'yrantd';
import './index.less';

export default function TipContent(props) {
  const [hoverVisible, setHoverVisible] = useState(false);
  const { title, value, actions, placement = 'left', children } = props;

  const handleEnter = () => {
    setHoverVisible(true);
  };

  const handleLeave = () => {
    setHoverVisible(false);
  };

  const titleNode = <div>{children}</div>;
  return (
    <YRTooltip title={titleNode} placement={placement} overlayClassName="tip-content" visible={hoverVisible}>
      <div className="tip-content-base">
        <div className="real-cont" onMouseEnter={handleEnter} onMouseLeave={handleLeave}>
          <div className="text">{value}</div>
          <div className="title">{title}</div>
        </div>
        {actions && <div className="actions">{actions}</div>}
      </div>
    </YRTooltip>
  );
}
