/**
 * @Author: wangyw26123
 * @Description: 机构管理
 * @Date: Created in 2022-12-08 20:10:04
 * @Modifed By:
 */

import React, { useRef, useState } from 'react';
import { YRFlexPageLayout, YRBreadcrumb, YRLink, YRSpace } from 'yrantd';
import store from '@/store';
import OrganTree, { OrganTreeItem } from './components/OrganTree';
import OrganList from './components/OrganList';
import OrganDetail from './OrganDetail';
import Dict from '../mock/getDict';

const OrganManage = () => {
  const [organList, setOrganList] = useState([]);
  const [organState, organDispatch] = store.useModel('organ');
  const [breadcrumb, setBreadcrumb] = useState<OrganTreeItem[]>([]);
  const organTreeRef = useRef<{ setActiveNode: (key: string) => void }>();
  const { activeOrganId } = organState;

  const organTreeCallbcack = (val, parents) => {
    const rootNode = val[0] || {};
    if (rootNode.organName?.trim()) {
      organDispatch.setActiveOrganId(rootNode.organId);
    }
    setBreadcrumb(parents);
    setOrganList(val);
  };

  return (
    <YRFlexPageLayout>
      <YRFlexPageLayout.Sider title="机构列表">
        <OrganTree
          callback={organTreeCallbcack}
          fieldNames={{
            key: 'organId',
            title: 'organName',
            children: 'children'
          }}
          ref={organTreeRef}
        />
      </YRFlexPageLayout.Sider>
      <YRFlexPageLayout.Main
        title={
          <YRBreadcrumb>
            {breadcrumb.map((node, index) => (
              <YRBreadcrumb.Item key={node.organId}>
                <YRLink
                  disabled={index === breadcrumb.length - 1}
                  onClick={() => {
                    organTreeRef?.current?.setActiveNode(node.organId);
                  }}
                >
                  {node.organName}
                </YRLink>
              </YRBreadcrumb.Item>
            ))}
          </YRBreadcrumb>
        }
      >
        <YRSpace direction="vertical">
          <OrganList organList={organList} />
          <OrganDetail parentOrganId={activeOrganId} />
        </YRSpace>
      </YRFlexPageLayout.Main>
    </YRFlexPageLayout>
  );
};

export default Dict([
  'EnumOrganLevel',
  'EnumOrganType',
  'EnumOrganStatus',
  'EnumOrganBizLine',
  'EnumOrganNature',
  'EnumAcctOrganType'
])(OrganManage);
