/**
 * @Author: wangyw26123
 * @Description: 节点数据
 * @Date: Created in 2022-04-29 16:25:30
 * @Modifed By:
 */

import { BaseNode, InsItem, NodeListener, NodeType, OutsItem, PropertyItem } from './Interface';

// 全局表单数据
class GlobalFormData {
  version: string;
  sort: string;
  remark: string;
  digest: string;
  transferSwitch: string;
  transferList: string;
  sceneName: string;
  triggerOperate: string;
  transferNode: string;
  transferCondition: string;
  transferType: string;
  transferToNode: string;
  differentSwitch: string;
  differentConfig: { differentId: string; differentNode: string }[];
  sameTeamSwitch: string;
  sameTeamConfig: { sameTeamId: string; sameTeamNode: string }[];
  endApproveInterceptor: string;
  exclusionGroups: [];
  businessManagerInterceptor: string;
  businessChangeSwitch: string;
  businessChangeInterceptor: string;
  flowNumber: string | undefined;
  flowName: string | undefined;
  flowDesc: string;
  sceneType: string;
  displayName: string;
  nodeType: NodeType;
  type: string;
  instanceUrl: string;
  approveDetail: string;
  approveNodeList: string;
  approveAuthType: string[];
  endApproveNodeList: string;
  customizeActuatorId: string;
  paramLibraryId: string[];
  approveAuthoritySwitch: string;
  signaldefinitions: string[];
  includeGroups: [];
  servicetaskclass: string | string[];
  params: [];
  paramLibraryName: string;

  properties: { exclusionGroups: string; includeGroups: string };

  constructor(options: BaseNode) {
    this.flowNumber = options?.flowNumber;
    this.flowName = options?.flowName;
    this.flowDesc = options?.flowDesc;
    this.sceneType = options?.sceneType;
    this.displayName = options?.displayName;
    this.nodeType = NodeType.PROCESS;
    this.version = options?.version;
    this.type = options?.type;
    this.instanceUrl = options?.instanceUrl;
    this.sort = options?.sort;
    this.paramLibraryId = options?.paramLibraryId;
    this.remark = options?.remark;
    this.digest = options?.digest;
    this.approveDetail = options?.approveDetail;
    this.approveNodeList = options?.approveNodeList;
    this.approveAuthType = options?.approveAuthType;
    this.customizeActuatorId = options?.customizeActuatorId;
    this.endApproveNodeList = options?.endApproveNodeList;
    this.differentSwitch = options?.differentSwitch;
    this.differentConfig = options?.differentConfig;
    this.sameTeamSwitch = options?.sameTeamSwitch;
    this.sameTeamConfig = options?.sameTeamConfig;
    this.transferSwitch = options?.transferSwitch;
    this.transferList = options?.transferList;
    this.sceneName = options?.sceneName;
    this.triggerOperate = options?.triggerOperate;
    this.transferNode = options?.transferNode;
    this.transferCondition = options?.transferCondition;
    this.transferType = options?.transferType;
    this.transferToNode = options?.transferToNode;
    this.endApproveInterceptor = options?.endApproveInterceptor;
    this.businessManagerInterceptor = options?.businessManagerInterceptor;
    this.businessChangeSwitch = options?.businessChangeSwitch;
    this.businessChangeInterceptor = options?.businessChangeInterceptor;
    this.approveAuthoritySwitch = options?.approveAuthoritySwitch;
    this.signaldefinitions = options?.signaldefinitions;
    this.exclusionGroups = options?.exclusionGroups;
    this.includeGroups = options?.includeGroups;
    this.servicetaskclass = options?.servicetaskclass;
    this.params = options?.params;
    this.paramLibraryName = options?.paramLibraryName;
  }
}

/**
 * 开始节点
 */
class StartNode {
  id: string;
  name: string;
  displayName: string;
  nodeType: NodeType;
  listeners: NodeListener[];
  properties: PropertyItem[];
  properties: { initiator: string };

  constructor(options: BaseNode) {
    this.id = options?.id;
    this.name = options?.name;
    this.displayName = options?.displayName;
    this.nodeType = NodeType.START_EVENT;
    this.listeners = options?.listeners;
    this.properties = options?.properties;
  }
}

/**
 * 结束节点
 */
class EndNode {
  id: string;
  name: string;
  displayName: string;
  nodeType: NodeType;
  listeners: NodeListener[];
  properties: PropertyItem[];
  ins: InsItem[];

  constructor(options: BaseNode) {
    this.id = options?.id;
    this.name = options?.name;
    this.displayName = options?.displayName;
    this.nodeType = NodeType.END_EVENT;
    this.listeners = options?.listeners;
    this.properties = options?.properties;
    this.ins = options?.ins;
  }
}

/**
 * 任务节点
 */
class TaskNode {
  id: string;
  name: string;
  displayName: string;
  nodeType: NodeType;
  listeners: NodeListener[];
  properties: PropertyItem[];
  assignMode: string;
  takeAging: string;
  approvalPage: string;
  approvalButton: string[];
  appointInfoId: string;
  sendMode: string;
  normDealTime: string;
  normDealOverTime: string;
  normDealWay: string;
  retentionTime: string;
  appointResource: string;
  designTime: string;
  allotOverTimeDeal: string;
  allotTimeOut: string;
  appointType: string;
  appointValue: string;
  approveInfo: string;
  approveChannel: string;
  retentionDealOverTime: string;
  counterSignResult: string;
  endApproveNodeList: string;
  counterSignMax: string;
  businessSwitch: string;
  appointResourceResult: string;
  approveNodeList: string;
  customizeActuatorId: string;
  assignMessageNotice: string;
  poolMessageNotice: string;
  approveAuthorityConfig: string;
  design: string;
  rate: string;
  designRule: string;
  counterSignMin: string;
  matchingRule: string;
  assignee: string;
  users: { name: string; id: string }[];
  roles: { name: string; id: string }[];
  assignType: string;
  screeningParty: string;
  performType: string;
  normDealTimeUnit: string;
  sendBackMode: string;

  constructor(options: BaseNode) {
    this.id = options?.id;
    this.name = options?.name;
    this.displayName = options?.displayName;
    this.nodeType = NodeType.USER_TASK;
    this.listeners = options?.listeners;
    this.properties = options?.properties;
    this.assignMode = options?.assignMode;
    this.takeAging = options?.takeAging;
    this.approvalPage = options?.approvalPage;
    this.approvalButton = options?.approvalButton;
    this.sendMode = options?.sendMode;
    this.appointInfoId = options?.appointInfoId;
    this.appointType = options?.appointType;
    this.appointValue = options?.appointValue;
    this.approveChannel = options?.approveChannel;
    this.approveInfo = options?.approveInfo;
    this.allotTimeOut = options?.allotTimeOut;
    this.allotOverTimeDeal = options?.allotOverTimeDeal;
    this.normDealTime = options?.normDealTime;
    this.normDealOverTime = options?.normDealOverTime;
    this.normDealWay = options?.normDealWay;
    this.retentionTime = options?.retentionTime;
    this.retentionDealOverTime = options?.retentionDealOverTime;
    this.appointResourceResult = options?.appointResourceResult;
    this.assignee = options?.assignee;
    this.appointResource = options?.appointResource;
    this.matchingRule = options?.matchingRule;
    this.businessSwitch = options?.businessSwitch;
    this.counterSignMin = options?.counterSignMin;
    this.counterSignMax = options?.counterSignMax;
    this.counterSignResult = options?.counterSignResult;
    this.design = options?.design;
    this.designRule = options?.designRule;
    this.designTime = options?.designTime;
    this.rate = options?.rate;
    this.approveAuthorityConfig = options?.approveAuthorityConfig;
    this.poolMessageNotice = options?.poolMessageNotice;
    this.assignMessageNotice = options?.assignMessageNotice;
    this.approveNodeList = options?.approveNodeList;
    this.customizeActuatorId = options?.customizeActuatorId;
    this.endApproveNodeList = options?.endApproveNodeList;
    this.users = options?.users;
    this.roles = options?.roles;
    this.assignType = options?.assignType;
    this.screeningParty = options?.screeningParty;
    this.performType = options?.performType;
    this.normDealTimeUnit = options?.normDealTimeUnit;
    this.sendBackMode = options?.sendBackMode;
  }
}

/**
 * 发起人
 */
class OrganNode {
  id: string;
  name: string;
  displayName: string;
  nodeType: NodeType;
  listeners: NodeListener[];
  properties: PropertyItem[];

  constructor(options: BaseNode) {
    this.id = options?.id;
    this.name = options?.name;
    this.displayName = options?.displayName;
    this.nodeType = NodeType.ORGAN_ISER;
    this.listeners = options?.listeners;
    this.properties = options?.properties;
  }
}

/**
 * 并行节点
 */
class ParallelGatewayNode {
  id: string;
  name: string;
  displayName: string;
  nodeType: NodeType;
  listeners: NodeListener[];
  properties: PropertyItem[];

  constructor(options: BaseNode) {
    this.id = options?.id;
    this.name = options?.name;
    this.displayName = options?.displayName;
    this.nodeType = NodeType.PARALLEL_GATEWAY;
    this.listeners = options?.listeners;
    this.properties = options?.properties;
  }
}

/**
 * 排他网关
 */
class ExclusiveGatewayNode {
  id: string;
  name: string;
  displayName: string;
  nodeType: NodeType;
  listeners: NodeListener[];
  properties: PropertyItem[];
  expr: string;
  def: string;
  lineType: string;

  constructor(options: BaseNode) {
    this.id = options?.id;
    this.name = options?.name;
    this.displayName = options?.displayName;
    this.nodeType = NodeType.EXCLUSIVE_GATEWAY;
    this.listeners = options?.listeners;
    this.properties = options?.properties;
    this.expr = options?.expr;
    this.def = options?.def;
    this.lineType = options?.lineType;
  }
}

/**
 * 决策表
 */
class DecisionTablesNode {
  id: string;
  name: string;
  displayName: string;
  nodeType: NodeType;
  listeners: NodeListener[];
  properties: PropertyItem[];

  constructor(options: BaseNode) {
    this.id = options?.id;
    this.name = options?.name;
    this.nodeType = NodeType.DECISION_TABLES;
    this.listeners = options?.listeners;
    this.properties = options?.properties;
  }
}

/**
 * 决策网关
 */
class DecisionGatewayNode {
  id: string;
  name: string;
  displayName: string;
  nodeType: NodeType;
  listeners: NodeListener[];
  properties: PropertyItem[];

  constructor(options: BaseNode) {
    this.id = options?.id;
    this.name = options?.name;
    this.nodeType = NodeType.INCLUSIVE_GATEWAY;
    this.listeners = options?.listeners;
    this.properties = options?.properties;
  }
}

/**
 * 包容网关
 */
class InclusionGatewayNode {
  id: string;
  name: string;
  displayName: string;
  nodeType: NodeType;
  listeners: NodeListener[];
  properties: PropertyItem[];

  constructor(options: BaseNode) {
    this.id = options?.id;
    this.name = options?.name;
    this.nodeType = NodeType.INCLUSIVE_GATEWAY;
    this.listeners = options?.listeners;
    this.properties = options?.properties;
  }
}

/**
 * 事件网关
 */
class EventBasedGatewayNode {
  id: string;
  name: string;
  displayName: string;
  nodeType: NodeType;
  listeners: NodeListener[];
  properties: PropertyItem[];

  constructor(options: BaseNode) {
    this.id = options?.id;
    this.name = options?.name;
    this.nodeType = NodeType.EVENT_GATEWAY;
    this.listeners = options?.listeners;
    this.properties = options?.properties;
  }
}

/**
 * 信号抛出节点
 */
class SignalThrowNode {
  id: string;
  name: string;
  displayName: string;
  nodeType: NodeType;
  listeners: NodeListener[];
  properties: PropertyItem[];

  constructor(options: BaseNode) {
    this.id = options?.id;
    this.name = options?.name;
    this.nodeType = NodeType.THROWSINGAL_EVENT;
    this.listeners = options?.listeners;
    this.properties = options?.properties;
  }
}

/**
 * 信号捕获节点
 */
class SignalCatchNode {
  id: string;
  name: string;
  displayName: string;
  nodeType: NodeType;
  listeners: NodeListener[];
  properties: PropertyItem[];

  constructor(options: BaseNode) {
    this.id = options?.id;
    this.name = options?.name;
    this.nodeType = NodeType.CATCHSIGNAL_EVENT;
    this.listeners = options?.listeners;
    this.properties = options?.properties;
  }
}

/**
 * 服务节点
 */
class ServiceNode {
  id: string;
  name: string;
  displayName: string;
  nodeType: NodeType;
  listeners: NodeListener[];
  properties: PropertyItem[];

  constructor(options: BaseNode) {
    this.id = options?.id;
    this.name = options?.name;
    this.nodeType = NodeType.SERVICE_TASK;
    this.listeners = options?.listeners;
    this.properties = options?.properties;
  }
}

/**
 * 子流程
 */
class SubprocessNode {
  id: string;
  name: string;
  displayName: string;
  nodeType: NodeType;
  listeners: NodeListener[];
  properties: PropertyItem[];

  constructor(options: BaseNode) {
    this.id = options?.id;
    this.name = options?.name;
    this.nodeType = NodeType.CAll_ACTIVITY;
    this.listeners = options?.listeners;
    this.properties = options?.properties;
  }
}

/**
 * 定时器
 */
class TimerNode {
  id: string;
  name: string;
  displayName: string;
  nodeType: NodeType;
  listeners: NodeListener[];
  properties: PropertyItem[];

  constructor(options: BaseNode) {
    this.id = options?.id;
    this.name = options?.name;
    this.nodeType = NodeType.CATCHTIMER_EVENT;
    this.displayName = options?.displayName;
    this.listeners = options?.listeners;
    this.properties = options?.properties;
  }
}

/**
 * 流程中断节点
 */
class IntermediateThrowNode {
  id: string;
  name: string;
  displayName: string;
  nodeType: NodeType;
  listeners: NodeListener[];
  properties: PropertyItem[];

  constructor(options: BaseNode) {
    this.id = options?.id;
    this.name = options?.name;
    this.displayName = options?.displayName;
    this.nodeType = NodeType.INTERMEDIATE_THROW_EVENT;
    this.listeners = options?.listeners;
    this.properties = options?.properties;
  }
}

class EdgeNode {
  sourceRef: string;
  nodeType: NodeType;
  targetRef: string;
  conditionExpress: string;
  id: string;
  name: string;
  displayName: string;
  lineType: string;
  defaultFlow: string;
  constructor(options: BaseNode) {
    this.sourceRef = options?.sourceRef;
    this.targetRef = options?.targetRef;
    this.conditionExpress = options?.conditionExpress;
    this.id = options?.id;
    this.name = options?.name;
    this.displayName = options?.displayName;
    this.nodeType = NodeType.SEQUENCE_FLOW;
    this.lineType = options?.lineType;
    this.defaultFlow = options?.defaultFlow;
  }
}

export {
  StartNode,
  EndNode,
  TaskNode,
  OrganNode,
  DecisionTablesNode,
  ParallelGatewayNode,
  ExclusiveGatewayNode,
  IntermediateThrowNode,
  GlobalFormData,
  EdgeNode,
  DecisionGatewayNode,
  InclusionGatewayNode,
  EventBasedGatewayNode,
  SignalThrowNode,
  SignalCatchNode,
  ServiceNode,
  SubprocessNode,
  TimerNode
};
