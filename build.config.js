/*
 * @Author: liufh
 * @Description:
 * @Date: 2023-01-11 14:46:45
 * @LastEditors: liaokt
 * @LastEditTime: 2024-10-16 17:43:39
 */
const MODULES = require('@yr/util/lib/constant/module').default;
const { SCENES } = require('@yr/util/lib/constant/request');
const CONST = require('@yr/util/lib/constant/system').default;
const themeConfig = require('./src/defaultSettings.ts').default;
const packageInfo = require('./package.json');

const { envType, publicPath, startMode, NODE_ENV } = process.env;

module.exports = {
  publicPath,
  devPublicPath: `${packageInfo.appEntry}/`, // 本机服务 基座调试时使用
  browserslist: {
    chrome: 49,
    ie: 11
  },
  define: {
    SCENES,
    MODULES: {
      ...MODULES
    },
    envType,
    startMode,
    CONST,
    BUSINESS_SIGN: `${packageInfo.businessSign}`,
    PUBLIC_PATH: publicPath,
    DEPLOY: null,
    packageInfo: null
  },
  sourceMap: NODE_ENV === 'development',
  postcssrc: true,
  hash: 'contenthash',
  plugins: [
    [
      'build-plugin-icestark',
      {
        type: 'child'
      }
    ],
    [
      'build-plugin-antd',
      {
        themeConfig: {
          'primary-color': themeConfig.primaryColor,
          'root-entry-name': 'variable'
        }
      }
    ],
    './build.plugin.js'
  ],
  modeConfig: {
    // 本地开发
    local: {
      proxy: {
        '/hsjry/corp': {
          target: 'http://localhost:10060/',
          changeOrigin: true
        },
        '/hsjry': {
          target: 'http://kylin-base-web.loan4-0-hnnx-dev.svc.cluster.local:8889/',
          changeOrigin: true
        }
      }
    }
  }
};
