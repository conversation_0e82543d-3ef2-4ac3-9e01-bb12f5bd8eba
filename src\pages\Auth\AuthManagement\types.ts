/**
 * 授权管理相关类型定义
 */

// 查询参数接口
export interface QueryParams {
  pageNum: number; // 页码（必填）
  pageSize: number; // 每页大小（必填）
  authCatalogRoleId?: string; // 授权类别角色关联ID（配置页面角色点击时必传）
  authStatus?: string; // 授权状态（参考EnumAuthBaseStatus枚举）
  customerType?: string; // 客户类型
  pauseDeadlineDate?: string; // 暂停止日（格式：yyyy-MM-dd）
  transferAuthFlag?: string; // 转授权标识（转授权：1）
  // bizLine?: string; // 业务条线 - 已移除
  roleId?: string; // 角色ID
  authCategory?: string; // 授权类别
  orgId?: string; // 机构ID
}

// API响应数据结构
export interface ApiResponse<T = any> {
  rpcResult: string;
  data: T;
  errorMessage?: string;
}

// 分页响应数据
export interface PageResponse<T = any> {
  pageNum: number;
  pageSize: number;
  total: number;
  pages: number;
  list: T[];
}

// 授权信息
export interface AuthBaseInfo {
  authBaseId: string; // 授权ID
  authCategory: string; // 授权类别
  authCatalogRoleId: string; // 授权类别角色关联ID
  flowInstanceId: string; // 流程实例ID
  updateOperatorId: string; // 修改人ID
  updateOrganId: string; // 修改机构ID
  authStatus: string; // 授权状态
  orgAuthFlag: string; // 机构授权标识
  roleName: string; // 角色名称
  roleId: string; // 角色ID
  orgName: string; // 机构名称
  orgId: string; // 机构ID
  bizLine: string; // 业务条线
  effectBeginDate: string; // 生效起日（yyyy-MM-dd格式）
  effectEndDate: string; // 生效止日（yyyy-MM-dd格式）
  pauseDeadlineDate: string; // 暂停止日（yyyy-MM-dd格式）
  operatorName: string; // 操作人
  operatorId: string; // 操作人ID
  ownOrganName: string; // 机构名称
  ownOrganId: string; // 机构ID
  transferAuthFlag: string; // 转授权标识
  authType: string; // 授权类型
  authNo?: string; // 授权编号
}

// 机构参数
export interface OrganParams {
  orgId: string;
  authCategory: string;
  authCatalogId: string;
  orgAuthFlag: string;
}

// 角色参数
export interface RoleParams {
  roleId: string;
  authNo: string;
}