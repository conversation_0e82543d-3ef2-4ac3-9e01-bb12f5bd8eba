/**
 * @Author: wangyw26123
 * @Description: 一级菜单-设置
 * @Date: Created in 2022-12-12 19:10:56
 * @Modifed By:
 */

import { lazy } from 'ice';
import {
  M0301,
  M0302,
  M0303,
  M0304,
  M0305,
  M0306,
  M0307,
  M0309,
  M0310,
  M030801,
  M030802,
  M0311
} from '@permit/setting';

const partRoutes = [
  {
    path: '/service-configuration',
    name: '设置',
    permit: 'M03',
    children: [
      {
        path: '/',
        exact: true,
        redirect: '/service-configuration/data-dictionary'
      },
      {
        path: '/data-dictionary',
        name: '数据字典',
        component: lazy(() => import('@/pages/Setting/DataDictionary')),
        permit: M0301
      },
      {
        path: '/service-parameter',
        name: '业务参数',
        component: lazy(() => import('@/pages/Setting/ServiceParameter')),
        permit: M0302
      },
      {
        path: '/exchange-rate',
        name: '汇率',
        component: lazy(() => import('@/pages/Setting/ExchangeRate')),
        permit: M0303
      },
      {
        path: '/holiday',
        name: '节假日',
        component: lazy(() => import('@/pages/Setting/Holiday')),
        permit: M0304
      },
      {
        path: '/intr_rate',
        name: '基准利率（LPR）',
        component: lazy(() => import('@/pages/Setting/IntrRate')),
        permit: M0305
      },
      // {
      //   path: '/risk-grouped',
      //   name: '风险探测分组',
      //   component: lazy(() => import('@/pages/Setting/RiskGrouped')),
      //   permit: M0306
      // },
      // {
      //   path: '/risk-scene',
      //   name: '风险探测场景',
      //   component: lazy(() => import('@/pages/Setting/RiskScene')),
      //   permit: M0307
      // },
      {
        path: '/rule-manage',
        name: '规则配置管理',
        permit: 'M0308',
        children: [
          {
            path: '/',
            exact: true,
            redirect: '/rule-manage/entry'
          },
          {
            path: '/entry',
            name: '规则条目管理',
            component: lazy(() => import('@/pages/Setting/RuleManage/Entry')),
            permit: M030801
          },
          {
            path: '/scene',
            name: '场景管理',
            component: lazy(() => import('@/pages/Setting/RuleManage/Scene')),
            permit: M030802
          }
        ]
      },
      {
        path: '/variable-manage',
        name: '变量管理',
        component: lazy(() => import('@/pages/Setting/VariableManage')),
        permit: M0309
      },
      {
        path: '/data-manage',
        name: '数据源管理',
        component: lazy(() => import('@/pages/Setting/DataManage')),
        permit: M0310
      },
      {
        path: '/form-setting-management',
        name: '表单配置管理',
        component: lazy(() => import('@/pages/Setting/FormSettingManagement')),
        permit: M0311
      }
    ]
  }
];

export default partRoutes;
