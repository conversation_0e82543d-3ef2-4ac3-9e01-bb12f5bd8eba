/**
 * 页面描述: 规则条目参数组件(比较符右侧)
 * @文件名 InputValue.tsx
 * @filePath \src\pages\Setting\RuleManager\Entry\components\InputValue.tsx
 * @Date 2023-08-09 14:58:20
 * @Email <EMAIL>
 * <AUTHOR>
 */
import React, { useEffect, useMemo, useState } from 'react';
import { YRSelect, YRDropdown, YRIcon, YRInput, YRMenu } from 'yrantd';
import style from '../index.module.less';
import Parameter from './Parameter';
import ArrayValue from './ArrayValue';
import { selectPage } from '@/services/setting';
import store from '@/store';
import { useRequest } from 'ahooks';

const InputValue = (props) => {
  const { rightValue, changeType } = props;
  const [ruleState, ruleDispatch] = store.useModel('rule');
  const { type } = ruleState;
  const { variableType, dictCode } = rightValue;
  const [isEdit, setIsEdit] = useState(false);
  const [dictItem, setDictItems] = useState([]);

  // 查询客户额度详情
  const { run: dictItemRun } = useRequest(selectPage, {
    manual: true,
    onSuccess: (result, params) => {
      const { errorMessage, data } = result as any;
      if (errorMessage === null) {
        if (data) {
          const list = data?.list || [];
          setDictItems(list);
        }
      }
    }
  });

  useEffect(() => {
    if (variableType === '003' && dictCode) {
      dictItemRun({ dictKey: dictCode, pageNum: 1, pageSize: 9999 });
    }
  }, [variableType, dictCode]);

  const arrayValue = useMemo(() => {
    if (variableType === '003') {
      const formatValue = rightValue?.value ? JSON.parse(rightValue?.value) : null;
      const split = formatValue ? formatValue.split(',') : [];
      return split;
    }
  }, [rightValue?.value, dictCode, variableType]);

  // 设置值类型
  const setType = ({ key }) => {
    if (!key) return;
    // 选中项
    if (rightValue && rightValue.variableType === key) return;
    const newData = {
      variableType: key
    };
    changeType('rightVariableRuleDto', newData);
    // 选择输入框默认显示输入框
    if (key === '002') {
      setTimeout(() => {
        setIsEdit(true);
      }, 100);
    }
  };

  // 输入框设置值
  const setInputValue = (e) => {
    const inputValue = e.target.value;
    const newData = {
      ...rightValue,
      value: inputValue ? JSON.stringify(inputValue) : ''
    };
    changeType('rightVariableRuleDto', newData);
    setIsEdit(false);
  };

  // 设置参数
  const setParameter = (obj) => {
    const data = {
      variableName: obj.variableName,
      variableCode: obj.variableCode,
      variableType: '001'
    };
    changeType('rightVariableRuleDto', data);
  };

  // 选择数组
  const setDict = (value) => {
    const data = {
      ...(rightValue || {}),
      dictCode: value,
      value: null
    };
    changeType('rightVariableRuleDto', data);
  };

  // 选择数组码值
  const setDictItem = (value) => {
    const joinValue = value ? value.join(',') : '';
    const data = {
      ...(rightValue || {}),
      value: joinValue ? JSON.stringify(joinValue) : null
    };
    changeType('rightVariableRuleDto', data);
  };

  // 值渲染判断
  const getValueContext = (data) => {
    switch (data.variableType) {
      case '001':
        return (
          <Parameter
            setValue={setParameter}
            selectedId={data?.variableCode}
            selectedLabel={data?.variableName || '请选择参数'}
          />
        );
      case '002':
        return !isEdit ? (
          <span
            onClick={() => {
              if (type !== 'detail') {
                setIsEdit(true);
              }
            }}
          >
            {data?.value ? JSON.parse(data?.value) : '请输入'}
          </span>
        ) : (
          <YRInput
            autoFocus
            defaultValue={data?.value ? JSON.parse(data?.value) : ''}
            onBlur={setInputValue}
            style={{ width: '200px' }}
            size="small"
            placeholder="输入值"
          />
        );
      case '003':
        return (
          <ArrayValue
            setDict={setDict}
            dictCode={data?.dictCode}
            value={arrayValue}
            dictItem={dictItem}
            setDictItem={setDictItem}
          />
        );
      default:
        return null;
    }
  };

  const valueTypeMenu = (
    <YRMenu onClick={setType}>
      <YRMenu.Item key="001">参数</YRMenu.Item>
      <YRMenu.Item key="002">输入值</YRMenu.Item>
      <YRMenu.Item key="003">数组</YRMenu.Item>
    </YRMenu>
  );

  return (
    <>
      <YRDropdown disabled={type === 'detail'} overlay={valueTypeMenu} placement="bottomLeft">
        {!rightValue ? (
          <span>选择值类型</span>
        ) : (
          <YRIcon icon="yunrongXXdown" fontSize="14px" className={`${style['icon-down']} ${style['hover-icon']}`} />
        )}
      </YRDropdown>
      {rightValue && getValueContext(rightValue)}
    </>
  );
};

export default InputValue;
