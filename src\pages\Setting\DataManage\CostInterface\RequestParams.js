/*
 * @Description: file content
 * @Author: your name
 * @Date: 2020-10-22 19:10:03
 * @LastEditors: your name
 * @LastEditTime: 2020-12-16 11:27:25
 */
import React from 'react';
import { YRButton, YRPopconfirm } from 'yrantd';
import ModalRequest from './ModalBoxs/ModalRequest';
import { enumParamType, enumParamWay } from '@/utils/data-dictionary';
import './index.less';

export default function RequestParams(props) {
  const { data = [], sysFieldList = [], cacheKey, clientType, handleOk, handleDelete } = props;
  // 调用方入参
  const accessParams = data.filter((record) => record.type === 3);
  // 请求头
  const headerParams = data.filter((record) => record.type === 0);
  // 路径参数
  const requestParams = data.filter((record) => record.type === 1);
  // SDK参数
  const sdkParams = data.filter((record) => record.type === 2);
  // 优先显示调用方入参->请求头->请求参数
  const temData = [...accessParams, ...headerParams, ...requestParams, ...sdkParams];
  return (
    <div className={'request-cont'}>
      <ModalRequest
        handleOk={handleOk}
        title="新增请求参数"
        type="add"
        sysFieldList={sysFieldList}
        cacheKey={cacheKey}
        clientType={clientType}
        initValues={{}}
      >
        <YRButton icon="+ " style={{ width: '100%' }}>
          请求参数
        </YRButton>
      </ModalRequest>
      {temData.map((item, index) => {
        const { paramNameCn, paramName } = item;
        const valWayText = enumParamWay[item.valWay];
        let { val } = item;
        let typeText;
        if (item.type) {
          typeText = enumParamType[item.type];
        }
        if (clientType === 'http' && item.valWay === 0) {
          const found = sysFieldList.find((info) => {
            return info.itemVal === val;
          });
          if (found) {
            val = found.itemName;
          }
        }
        let color;
        if (valWayText === '表达式') {
          color = 'color-text3';
        } else if (valWayText === '系统参数') {
          color = 'color-text4';
        } else {
          color = 'color-text5';
        }
        return (
          <div className={'request-cont-block'} key={index}>
            <div className="row">
              <span className="text1">参数:</span>
              {typeText ? (
                <span className="text2">
                  <span className={`label ${typeText === '路径参数' ? 'color-text1' : 'color-text2'}`}>{typeText}</span>
                </span>
              ) : null}
              <span className="text3">
                <span>{paramName}</span>
                <span className="text3-space">{`[${paramNameCn}]`}</span>
              </span>
            </div>
            {item.type !== 3 ? (
              <div className={'row'}>
                <span className={'text1'}>值:</span>
                <span className={'text2'}>
                  <span className={`label ${color}`}>{valWayText}</span>
                </span>
                <span className={'text4'}>{val}</span>
              </div>
            ) : null}

            <div className={'actions'}>
              <ModalRequest
                handleOk={handleOk}
                title="修改请求参数"
                initValues={item}
                type="edit"
                sysFieldList={sysFieldList}
                cacheKey={cacheKey}
                clientType={clientType}
              >
                <a style={{ marginRight: 5 }}>修改</a>
              </ModalRequest>
              <YRPopconfirm
                title="确定要删除吗？"
                onConfirm={() => {
                  handleDelete(item);
                }}
              >
                <a>删除</a>
              </YRPopconfirm>
            </div>
          </div>
        );
      })}
    </div>
  );
}
