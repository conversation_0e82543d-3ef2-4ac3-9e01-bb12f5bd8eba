/**
 * 待复核-单笔授权管理相关类型定义
 */

// 查询参数接口
export interface PendingSingleQueryParams {
  pageNum: number;                    // 页码（必填）
  pageSize: number;                   // 每页大小（必填）
  authNo?: string;                    // 授权编号
  orgId?: string;                     // 机构
  deptId?: string;                    // 部门
  roleId?: string;                    // 角色
  authCategory?: string;              // 授权类别
}

// API响应数据结构
export interface ApiResponse<T = any> {
  rpcResult: string;
  data: T;
  errorMessage?: string;
}

// 分页响应数据
export interface PageResponse<T = any> {
  pageNum: number;
  pageSize: number;
  total: number;
  pages: number;
  list: T[];
}

// 待复核单笔授权信息
export interface PendingSingleAuthInfo {
  authBaseId: string;               // 授权ID
  authNo: string;                   // 授权编号
  orgId: string;                    // 机构ID
  orgName: string;                  // 机构名称
  deptId: string;                   // 部门ID
  deptName: string;                 // 部门名称
  roleId: string;                   // 角色ID
  roleName: string;                 // 角色名称
  authCategory: string;             // 授权类别
  creditCategory: string;           // 授信类别
  authStatus: string;               // 授权状态
  effectBeginDate: string;          // 生效日期（yyyy-MM-dd格式）
  effectEndDate: string;            // 失效日期（yyyy-MM-dd格式）
  operatorName: string;             // 操作人
  operatorId: string;               // 操作人ID
  ownOrganName: string;             // 登记机构
  ownOrganId: string;               // 登记机构ID
  inputTime: string;                // 登记时间
  updateTime: string;               // 更新时间
  flowInstanceId?: string;          // 流程实例ID
  remark?: string;                  // 备注
}

// 复核操作参数
export interface ReviewParams {
  authBaseId: string;               // 授权ID
  reviewResult: 'PASS' | 'REJECT';  // 复核结果：通过|退回
  reviewRemark?: string;            // 复核意见
}

// 机构树节点
export interface OrganTreeNode {
  orgId: string;
  orgName: string;
  children?: OrganTreeNode[];
}

// 部门信息
export interface DeptInfo {
  deptId: string;
  deptName: string;
  orgId: string;
}

// 角色信息
export interface RoleInfo {
  roleId: string;
  roleName: string;
  roleCode: string;
}
