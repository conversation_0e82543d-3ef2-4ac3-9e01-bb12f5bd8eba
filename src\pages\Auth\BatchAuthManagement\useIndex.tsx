import React from 'react';
import { ValueTypeEnum } from '@yr/multi-view-table';
import { FormItemListProps, YRTableProps, YRButton, YRLink, YRModal, YRMessage } from 'yrantd';
import type { BatchAuthInfo } from './types';

// 处理操作的通用函数
const handleAction = (
  actionName: string,
  confirmText: string,
  apiFunction: any,
  successText: string,
  batchAuthId: string,
  okCallback: () => void
) => {
  YRModal.confirm({
    title: actionName,
    okText: '确定',
    cancelText: '取消',
    content: confirmText,
    onOk: async () => {
      try {
        const res = await apiFunction({ batchAuthId });
        if (res?.errorMessage === null) {
          YRMessage.success(successText, 0.5, () => {
            okCallback();
          });
        } else {
          YRMessage.error(res?.errorMessage || `${actionName}失败`);
        }
      } catch (error) {
        YRMessage.error(`${actionName}失败`);
      }
    }
  });
};

// 表单项配置
const formItemList: FormItemListProps[] = [
  {
    placeholder: '授权状态',
    key: 'authStatus',
    type: 'dictSelect',
    position: 'show',
    dictkey: 'AUTH_STATUS'
  }
];

// 表格列定义
const columns: (
  okCallback: () => void,
  submitCallback: any,
  submitLoading: boolean,
  deleteCallback: any,
  deleteLoading: boolean,
  invalidCallback: any,
  invalidLoading: boolean
) => YRTableProps['columns'] = (
  okCallback,
  submitCallback,
  submitLoading,
  deleteCallback,
  deleteLoading,
  invalidCallback,
  invalidLoading
) => {
  return [
    {
      title: '批量授权编号',
      dataIndex: 'batchAuthNo',
      key: 'batchAuthNo',
      width: 200,
      ellipsis: true
    },
    {
      title: '导入文件名称',
      dataIndex: 'fileName',
      key: 'fileName',
      width: 200,
      ellipsis: true
    },
    {
      title: '授权状态',
      dataIndex: 'authStatus',
      key: 'authStatus',
      width: 120,
      valueType: ValueTypeEnum.dict,
      dictkey: 'AUTH_STATUS'
    },
    {
      title: '登记人',
      dataIndex: 'operatorName',
      key: 'operatorName',
      width: 120
    },
    {
      title: '登记机构',
      dataIndex: 'ownOrganName',
      key: 'ownOrganName',
      width: 180,
      ellipsis: true
    },
    {
      title: '登记日期',
      dataIndex: 'inputTime',
      key: 'inputTime',
      width: 120,
      valueType: ValueTypeEnum.date
    },
    {
      title: '操作',
      dataIndex: 'operator',
      valueType: ValueTypeEnum.operation,
      fixed: 'right',
      width: 200,
      render: (value, record) => {
        const { authStatus, batchAuthId } = record as BatchAuthInfo;
        
        // 根据授权状态显示不同的操作按钮
        const renderButtons = () => {
          switch (authStatus) {
            case '010': // 待提交
              return (
                <>
                  <YRLink
                    key={`detail-${batchAuthId}`}
                    type="primary"
                    onClick={() => {
                      // TODO: 打开详情弹窗
                      YRMessage.info('详情功能待实现');
                    }}
                  >
                    详情
                  </YRLink>
                  <YRLink
                    key={`delete-${batchAuthId}`}
                    type="primary"
                    onClick={() => 
                      handleAction('删除', '确定要删除此批量授权吗?', deleteCallback, '删除成功', batchAuthId, okCallback)
                    }
                  >
                    删除
                  </YRLink>
                  <YRLink
                    key={`submit-${batchAuthId}`}
                    type="primary"
                    onClick={() => {
                      YRModal.confirm({
                        title: '提交复核',
                        okText: '确定',
                        cancelText: '取消',
                        content: '确定要提交复核吗？',
                        okButtonProps: {
                          loading: submitLoading
                        },
                        onOk: () => {
                          submitCallback({ batchAuthId });
                        }
                      });
                    }}
                  >
                    提交复核
                  </YRLink>
                </>
              );
            case '020': // 复核中
              return (
                <YRLink
                  key={`detail-${batchAuthId}`}
                  type="primary"
                  onClick={() => {
                    // TODO: 打开详情弹窗
                    YRMessage.info('详情功能待实现');
                  }}
                >
                  详情
                </YRLink>
              );
            case '050': // 复核退回
              return (
                <>
                  <YRLink
                    key={`detail-${batchAuthId}`}
                    type="primary"
                    onClick={() => {
                      // TODO: 打开详情弹窗
                      YRMessage.info('详情功能待实现');
                    }}
                  >
                    详情
                  </YRLink>
                  <YRLink
                    key={`delete-${batchAuthId}`}
                    type="primary"
                    onClick={() => 
                      handleAction('删除', '确定要删除此批量授权吗?', deleteCallback, '删除成功', batchAuthId, okCallback)
                    }
                  >
                    删除
                  </YRLink>
                  <YRLink
                    key={`submit-${batchAuthId}`}
                    type="primary"
                    onClick={() => {
                      YRModal.confirm({
                        title: '提交复核',
                        okText: '确定',
                        cancelText: '取消',
                        content: '确定要提交复核吗？',
                        okButtonProps: {
                          loading: submitLoading
                        },
                        onOk: () => {
                          submitCallback({ batchAuthId });
                        }
                      });
                    }}
                  >
                    提交复核
                  </YRLink>
                </>
              );
            case '040': // 已生效
              return (
                <YRLink
                  key={`detail-${batchAuthId}`}
                  type="primary"
                  onClick={() => {
                    // TODO: 打开详情弹窗
                    YRMessage.info('详情功能待实现');
                  }}
                >
                  详情
                </YRLink>
              );
            default:
              return (
                <YRLink
                  key={`detail-${batchAuthId}`}
                  type="primary"
                  onClick={() => {
                    // TODO: 打开详情弹窗
                    YRMessage.info('详情功能待实现');
                  }}
                >
                  详情
                </YRLink>
              );
          }
        };

        return (
          <YRButton.Overflow>
            {renderButtons()}
          </YRButton.Overflow>
        );
      }
    }
  ];
};

export { columns, formItemList };
