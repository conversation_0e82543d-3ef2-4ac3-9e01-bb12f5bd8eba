/*
 * @Author: liaokt26196
 * @Description: 审批意见侧边栏
 * @Date: 2022-12-23 16:27:14
 * @LastEditors: liaokt
 * @LastEditTime: 2023-06-08 17:53:58
 */
import React, { useState } from 'react';
import { YRSpace, YRForm, YRRadio, YRTextAreaPlus } from 'yrantd';

const PostOpinion: React.FC = (props: any) => {
  const { form } = props;
  return (
    <YRSpace direction="vertical" style={{ width: '100%' }}>
      <YRForm form={form}>
        <YRForm.Row column={1}>
          <YRForm.Item
            label="审批结论"
            name={'approve'}
            rules={[{ required: true, message: '审批结论不能为空' }]}
            initialValue={'001'}
          >
            <YRRadio.Group>
              <YRRadio value={'001'} key={'001'}>
                同意
              </YRRadio>
              <YRRadio value={'002'} key={'002'}>
                拒绝
              </YRRadio>
            </YRRadio.Group>
          </YRForm.Item>
          <YRForm.Item label="意见说明" name={'remark'}>
            <YRTextAreaPlus maxLength={100} containerStyle={{ height: 120 }} placeholder="请输入意见说明" />
          </YRForm.Item>
        </YRForm.Row>
      </YRForm>
    </YRSpace>
  );
};

export default PostOpinion;
